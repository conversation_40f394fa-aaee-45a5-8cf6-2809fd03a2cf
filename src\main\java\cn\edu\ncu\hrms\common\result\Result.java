package cn.edu.ncu.hrms.common.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 后端统一返回结果
 * @param <T>
 */
@Data
public class Result<T> implements Serializable {

    private Integer code; // 编码：1成功，0失败，或自定义响应结果
    private String msg; // 错误信息
    private T data; // 数据(若有数据一般为json字符串)

    public static final Integer SUCCESS_CODE = 1;
    public static final Integer ERROR_CODE = 0;

    public static <T> Result<T> success() {
        Result result = new Result<T>();
        result.code = 1;
        return result;
    }

    public static <T> Result<T> success(String msg) {
        Result result = new Result<T>();
        result.code = 1;
        result.msg = msg;
        return result;
    }

    public static <T> Result<T> success(T obj) {
        Result result = new Result<T>();
        result.code = 1;
        result.data = obj;
        return result;
    }

    public static <T> Result<T> success(T object, String msg) {
        Result result = new Result<T>();
        result.data = object;
        result.code = 1;
        result.msg = msg;
        return result;
    }

    public static <T> Result<T> error(String msg) {
        Result result = new Result();
        result.msg = msg;
        result.code = 0; // Default error code
        return result;
    }

}
