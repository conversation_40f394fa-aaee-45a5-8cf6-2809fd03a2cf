package cn.edu.ncu.hrms.salary.mapper;

import cn.edu.ncu.hrms.salary.dto.SalaryPageQueryDTO;
import cn.edu.ncu.hrms.common.entity.SalaryReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 薪资报表Mapper接口
 */
@Mapper
public interface SalaryReportMapper {
    
    /**
     * 插入薪资报表
     * @param salaryReport 薪资报表
     * @return 影响行数
     */
    int insert(SalaryReport salaryReport);
    
    /**
     * 更新薪资报表
     * @param salaryReport 薪资报表
     * @return 影响行数
     */
    int update(SalaryReport salaryReport);
    
    /**
     * 根据ID删除薪资报表
     * @param id 薪资报表ID
     * @return 影响行数
     */
    int deleteById(Integer id);
    
    /**
     * 根据ID查询薪资报表
     * @param id 薪资报表ID
     * @return 薪资报表
     */
    SalaryReport selectById(Integer id);
    
    /**
     * 根据员工ID和月份查询薪资报表
     * @param employeeId 员工ID
     * @param month 月份(格式：yyyy-MM)
     * @return 薪资报表
     */
    SalaryReport selectByEmployeeIdAndMonth(@Param("employeeId") Integer employeeId, 
                                           @Param("month") String month);
    
    /**
     * 根据条件查询薪资报表列表 (配合 PageHelper 使用)
     * @param queryDTO 查询条件 DTO
     * @return 薪资报表列表
     */
    List<SalaryReport> selectByCondition(SalaryPageQueryDTO queryDTO);
    
    /**
     * 根据部门ID和月份查询所有薪资报表 (用于部门导出)
     * @param departmentId 部门ID
     * @param month 月份(格式：yyyy-MM)
     * @return 薪资报表列表
     */
    List<SalaryReport> selectByDepartmentAndMonth(@Param("departmentId") Integer departmentId,
                                                  @Param("month") String month);
    
    /**
     * 获取员工年度薪资总额
     * @param employeeId 员工ID
     * @param year 年份
     * @return 年度薪资总额，可能为 null
     */
    BigDecimal sumAnnualSalaryByEmployee(@Param("employeeId") Integer employeeId, @Param("year") Integer year);
    
    /**
     * 获取部门年度薪资总额
     * @param departmentId 部门ID
     * @param year 年份
     * @return 年度薪资总额，可能为 null
     */
    BigDecimal sumAnnualSalaryByDepartment(@Param("departmentId") Integer departmentId, @Param("year") Integer year);

}