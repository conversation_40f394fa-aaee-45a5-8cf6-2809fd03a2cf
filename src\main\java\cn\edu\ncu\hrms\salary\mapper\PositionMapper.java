package cn.edu.ncu.hrms.salary.mapper;

import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.salary.dto.PositionPageQueryDTO;
import cn.edu.ncu.hrms.salary.vo.PositionVO;
import cn.edu.ncu.hrms.system.dto.SystemPositionPageQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 职位Mapper接口
 */
@Mapper
public interface PositionMapper {

    /**
     * 根据ID查询职位
     *
     * @param id 职位ID
     * @return 职位信息
     */
    Position selectById(Integer id);

    /**
     * 查询职位列表
     *
     * @return 职位列表
     */
    List<Position> selectList();

    /**
     * 条件查询职位列表
     *
     * @return 职位列表
     */
    List<Position> selectListByCondition(SystemPositionPageQueryDTO queryDTO);

    /**
     * HR条件查询职位列表
     *
     * @return 职位列表
     */
    List<PositionVO> selectVOListByCondition(PositionPageQueryDTO queryDTO);

    /**
     * 新增职位
     *
     * @param position 职位信息
     * @return 影响行数
     */
    int insert(Position position);

    /**
     * 更新职位
     *
     * @param position 职位信息
     * @return 影响行数
     */
    int update(Position position);

    /**
     * 删除职位
     *
     * @param id 职位ID
     * @return 影响行数
     */
    int deleteById(Integer id);


    /**
     * 根据账套ID查询职位列表
     *
     * @param accountId 账套ID
     * @return 职位列表
     */
    List<Position> selectByAccountId(Integer accountId);


    /**
     * 查询职位名称列表
     * @return
     */
    List<String> selectNameList();


    Position selectByNameAndLevel(@Param("name") String name, @Param("level") Integer level);
}