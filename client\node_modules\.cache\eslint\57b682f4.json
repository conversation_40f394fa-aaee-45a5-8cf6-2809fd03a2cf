[{"D:\\Desktop\\GP\\HRMS\\client\\src\\main.js": "1", "D:\\Desktop\\GP\\HRMS\\client\\src\\permission.js": "2", "D:\\Desktop\\GP\\HRMS\\client\\src\\plugins\\element.js": "3", "D:\\Desktop\\GP\\HRMS\\client\\src\\App.vue": "4", "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\index.js": "5", "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\TreeSelect\\index.vue": "6", "D:\\Desktop\\GP\\HRMS\\client\\src\\router\\index.js": "7", "D:\\Desktop\\GP\\HRMS\\client\\src\\icons\\index.js": "8", "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\auth.js": "9", "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\getters.js": "10", "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\settings.js": "11", "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\user-info.js": "12", "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\app.js": "13", "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\user.js": "14", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\dashboard\\index.vue": "15", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\error\\404.vue": "16", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\register\\index.vue": "17", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\login\\index.vue": "18", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\attendance\\record\\index.vue": "19", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\attendance\\leave\\index.vue": "20", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\finance\\setting\\index.vue": "21", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\finance\\account\\index.vue": "22", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\employee\\profile\\index.vue": "23", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\finance\\report\\index.vue": "24", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\employee\\resign\\index.vue": "25", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\employee\\reward\\index.vue": "26", "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\websocket.js": "27", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\index.vue": "28", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\permission\\index.vue": "29", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\position\\index.vue": "30", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\user\\index.vue": "31", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\role\\index.vue": "32", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\department\\index.vue": "33", "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\SvgIcon\\index.vue": "34", "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\request.js": "35", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\user.js": "36", "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\validate.js": "37", "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\amap.js": "38", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\dashboard.js": "39", "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\index.js": "40", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\user.js": "41", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\attendance\\record.js": "42", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\attendance\\leave.js": "43", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\position.js": "44", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\role.js": "45", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\salary\\account.js": "46", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\employee.js": "47", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\department.js": "48", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\permission.js": "49", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\employee.js": "50", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\profile.js": "51", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\list.js": "52", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\reward.js": "53", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\resign.js": "54", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee.js": "55", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\common\\approval.js": "56", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\mixin\\ResizeHandler.js": "57", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\finance\\position.js": "58", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\finance\\account.js": "59", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\index.js": "60", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Navbar.vue": "61", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\AppMain.vue": "62", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\index.vue": "63", "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\Breadcrumb\\index.vue": "64", "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\Hamburger\\index.vue": "65", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\Logo.vue": "66", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\SidebarItem.vue": "67", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\Link.vue": "68", "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\Item.vue": "69", "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\finance\\report.js": "70", "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\test\\firefox-test.vue": "71"}, {"size": 1798, "mtime": 1748859408559, "results": "72", "hashOfConfig": "73"}, {"size": 4248, "mtime": 1745828455714, "results": "74", "hashOfConfig": "73"}, {"size": 258, "mtime": 1745673935850, "results": "75", "hashOfConfig": "73"}, {"size": 1237, "mtime": 1746196943459, "results": "76", "hashOfConfig": "73"}, {"size": 375, "mtime": 1746544239096, "results": "77", "hashOfConfig": "73"}, {"size": 5697, "mtime": 1745384124208, "results": "78", "hashOfConfig": "73"}, {"size": 5981, "mtime": 1748859468330, "results": "79", "hashOfConfig": "73"}, {"size": 317, "mtime": 1745380186780, "results": "80", "hashOfConfig": "73"}, {"size": 463, "mtime": 1745378278040, "results": "81", "hashOfConfig": "73"}, {"size": 964, "mtime": 1745755244646, "results": "82", "hashOfConfig": "73"}, {"size": 445, "mtime": 1745378325558, "results": "83", "hashOfConfig": "73"}, {"size": 3780, "mtime": 1745828507039, "results": "84", "hashOfConfig": "73"}, {"size": 1056, "mtime": 1745378319732, "results": "85", "hashOfConfig": "73"}, {"size": 3267, "mtime": 1745761991331, "results": "86", "hashOfConfig": "73"}, {"size": 131927, "mtime": 1748004384294, "results": "87", "hashOfConfig": "73"}, {"size": 4956, "mtime": 1745394553059, "results": "88", "hashOfConfig": "73"}, {"size": 10834, "mtime": 1745583207665, "results": "89", "hashOfConfig": "73"}, {"size": 8682, "mtime": 1745758228871, "results": "90", "hashOfConfig": "73"}, {"size": 101992, "mtime": 1747490883035, "results": "91", "hashOfConfig": "73"}, {"size": 49800, "mtime": 1747017729231, "results": "92", "hashOfConfig": "73"}, {"size": 11042, "mtime": 1747471661125, "results": "93", "hashOfConfig": "73"}, {"size": 13638, "mtime": 1747471639329, "results": "94", "hashOfConfig": "73"}, {"size": 114472, "mtime": 1747492848364, "results": "95", "hashOfConfig": "73"}, {"size": 21720, "mtime": 1747491171338, "results": "96", "hashOfConfig": "73"}, {"size": 46070, "mtime": 1746522099814, "results": "97", "hashOfConfig": "73"}, {"size": 39866, "mtime": 1747491043134, "results": "98", "hashOfConfig": "73"}, {"size": 13631, "mtime": 1746204861028, "results": "99", "hashOfConfig": "73"}, {"size": 1793, "mtime": 1745378425139, "results": "100", "hashOfConfig": "73"}, {"size": 11391, "mtime": 1747397557779, "results": "101", "hashOfConfig": "73"}, {"size": 11070, "mtime": 1746523538840, "results": "102", "hashOfConfig": "73"}, {"size": 89417, "mtime": 1746086470171, "results": "103", "hashOfConfig": "73"}, {"size": 16158, "mtime": 1745679391650, "results": "104", "hashOfConfig": "73"}, {"size": 33837, "mtime": 1747396790029, "results": "105", "hashOfConfig": "73"}, {"size": 1214, "mtime": 1745380181197, "results": "106", "hashOfConfig": "73"}, {"size": 5651, "mtime": 1746192176068, "results": "107", "hashOfConfig": "73"}, {"size": 1268, "mtime": 1745415670094, "results": "108", "hashOfConfig": "73"}, {"size": 1111, "mtime": 1745378290083, "results": "109", "hashOfConfig": "73"}, {"size": 7542, "mtime": 1748859451351, "results": "110", "hashOfConfig": "73"}, {"size": 565, "mtime": 1745751003418, "results": "111", "hashOfConfig": "73"}, {"size": 3999, "mtime": 1746516013932, "results": "112", "hashOfConfig": "73"}, {"size": 1372, "mtime": 1745412722167, "results": "113", "hashOfConfig": "73"}, {"size": 2521, "mtime": 1747472610952, "results": "114", "hashOfConfig": "73"}, {"size": 989, "mtime": 1746469272933, "results": "115", "hashOfConfig": "73"}, {"size": 1235, "mtime": 1746515518203, "results": "116", "hashOfConfig": "73"}, {"size": 1061, "mtime": 1745491012387, "results": "117", "hashOfConfig": "73"}, {"size": 902, "mtime": 1745585367717, "results": "118", "hashOfConfig": "73"}, {"size": 278, "mtime": 1746019216379, "results": "119", "hashOfConfig": "73"}, {"size": 1267, "mtime": 1745858867215, "results": "120", "hashOfConfig": "73"}, {"size": 954, "mtime": 1745483645757, "results": "121", "hashOfConfig": "73"}, {"size": 2634, "mtime": 1745854154354, "results": "122", "hashOfConfig": "73"}, {"size": 2599, "mtime": 1746262343425, "results": "123", "hashOfConfig": "73"}, {"size": 372, "mtime": 1746258941622, "results": "124", "hashOfConfig": "73"}, {"size": 2501, "mtime": 1746255831107, "results": "125", "hashOfConfig": "73"}, {"size": 1421, "mtime": 1746344845183, "results": "126", "hashOfConfig": "73"}, {"size": 1188, "mtime": 1745852541921, "results": "127", "hashOfConfig": "73"}, {"size": 222, "mtime": 1746344512312, "results": "128", "hashOfConfig": "73"}, {"size": 1235, "mtime": 1745378481015, "results": "129", "hashOfConfig": "73"}, {"size": 1078, "mtime": 1746526524649, "results": "130", "hashOfConfig": "73"}, {"size": 1450, "mtime": 1746514968727, "results": "131", "hashOfConfig": "73"}, {"size": 139, "mtime": 1745378436620, "results": "132", "hashOfConfig": "73"}, {"size": 10445, "mtime": 1747491756165, "results": "133", "hashOfConfig": "73"}, {"size": 654, "mtime": 1745378444939, "results": "134", "hashOfConfig": "73"}, {"size": 1877, "mtime": 1747485932798, "results": "135", "hashOfConfig": "73"}, {"size": 2158, "mtime": 1745378547268, "results": "136", "hashOfConfig": "73"}, {"size": 1045, "mtime": 1745378558275, "results": "137", "hashOfConfig": "73"}, {"size": 1504, "mtime": 1745732086060, "results": "138", "hashOfConfig": "73"}, {"size": 3269, "mtime": 1747483873023, "results": "139", "hashOfConfig": "73"}, {"size": 657, "mtime": 1745378529972, "results": "140", "hashOfConfig": "73"}, {"size": 896, "mtime": 1747484638953, "results": "141", "hashOfConfig": "73"}, {"size": 1102, "mtime": 1746628773624, "results": "142", "hashOfConfig": "73"}, {"size": 6140, "mtime": 1748858910736, "results": "143", "hashOfConfig": "73"}, {"filePath": "144", "messages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rurkg7", {"filePath": "146", "messages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "149", "messages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "151", "messages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "154", "messages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "156", "messages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "158", "messages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "162", "messages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "164", "messages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "166", "messages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "168", "messages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "170", "messages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "172", "messages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "174", "messages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "176", "messages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "178", "messages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "180", "messages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "182", "messages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "184", "messages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "186", "messages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "188", "messages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "190", "messages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "192", "messages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "194", "messages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "196", "messages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "198", "messages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "200", "messages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "206", "messages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "208", "messages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "210", "messages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "212", "messages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "214", "messages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "216", "messages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "218", "messages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "220", "messages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "224", "messages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "226", "messages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "228", "messages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "230", "messages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "232", "messages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "234", "messages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "236", "messages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "240", "messages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "242", "messages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "248", "messages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "250", "messages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "252", "messages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "254", "messages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "256", "messages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "258", "messages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "260", "messages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "262", "messages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "264", "messages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "266", "messages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "268", "messages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "270", "messages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "272", "messages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "274", "messages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "276", "messages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "278", "messages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "280", "messages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "282", "messages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "153"}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "148"}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Desktop\\GP\\HRMS\\client\\src\\main.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\permission.js", [], [], "D:\\Desktop\\GP\\HRMS\\client\\src\\plugins\\element.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\App.vue", [], [], "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\index.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\TreeSelect\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\router\\index.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\icons\\index.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\auth.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\getters.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\settings.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\user-info.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\app.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\store\\modules\\user.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\dashboard\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\error\\404.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\register\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\login\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\attendance\\record\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\attendance\\leave\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\finance\\setting\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\finance\\account\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\employee\\profile\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\finance\\report\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\employee\\resign\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\employee\\reward\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\websocket.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\permission\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\position\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\user\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\role\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\system\\department\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\SvgIcon\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\request.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\user.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\validate.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\amap.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\dashboard.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\utils\\index.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\user.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\attendance\\record.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\attendance\\leave.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\position.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\role.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\salary\\account.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\employee.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\department.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\system\\permission.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\employee.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\profile.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\list.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\reward.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee\\resign.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\employee.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\common\\approval.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\mixin\\ResizeHandler.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\finance\\position.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\finance\\account.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\index.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Navbar.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\AppMain.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\Breadcrumb\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\components\\Hamburger\\index.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\Logo.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\SidebarItem.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\Link.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\layout\\components\\Sidebar\\Item.vue", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\api\\finance\\report.js", [], "D:\\Desktop\\GP\\HRMS\\client\\src\\views\\test\\firefox-test.vue", []]