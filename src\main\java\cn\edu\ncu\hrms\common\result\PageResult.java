package cn.edu.ncu.hrms.common.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 封装分页查询结果
 */
@Data
@NoArgsConstructor
public class PageResult<T> implements Serializable {

    private long total; //总记录数

    private List<T> records; //当前页数据集合
    
    /**
     * 构造方法
     *
     * @param total 总记录数
     * @param records 分页数据
     */
    public PageResult(long total, List<T> records) {
        this.total = total;
        this.records = records;
    }
    
    /**
     * 创建成功的分页结果
     */
    public static <T> Result<PageResult<T>> success(long total, List<T> records) {
        PageResult<T> pageResult = new PageResult<>(total, records);
        return Result.success(pageResult);
    }


    public static <T> PageResult<T> empty() {
        return new PageResult<>(0L, new ArrayList<>());
    }
}
