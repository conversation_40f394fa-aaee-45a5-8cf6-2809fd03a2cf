import request from '@/utils/request'

// 查询角色列表
export function listRole(query) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params: query
  })
}

// 查询角色详细
export function getRole(id) {
  return request({
    url: '/system/role/' + id,
    method: 'get'
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/system/role',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole(id, data) {
  return request({
    url: '/system/role/' + id,
    method: 'put',
    data: data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: '/system/role/' + id,
    method: 'delete'
  })
}

// 获取角色权限
export function getRolePermissions(id) {
  return request({
    url: '/system/role/' + id + '/permissions',
    method: 'get'
  })
}

// 设置角色权限
export function setRolePermissions(id, data) {
  return request({
    url: '/system/role/' + id + '/permissions',
    method: 'post',
    data: data
  })
}
