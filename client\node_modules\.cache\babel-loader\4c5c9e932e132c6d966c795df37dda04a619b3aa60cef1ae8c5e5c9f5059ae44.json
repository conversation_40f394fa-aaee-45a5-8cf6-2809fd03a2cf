{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Vue from 'vue';\nimport VueRouter from 'vue-router';\n\n// 引入布局组件\nimport Layout from '@/layout';\nVue.use(VueRouter);\n\n// 公共路由，所有用户都可以访问\nexport const constantRoutes = [{\n  path: '/login',\n  component: () => import('@/views/login/index'),\n  hidden: true\n}, {\n  path: '/register',\n  component: () => import('@/views/register/index'),\n  hidden: true\n}, {\n  path: '/404',\n  component: () => import('@/views/error/404'),\n  hidden: true\n}, {\n  path: '/',\n  component: Layout,\n  redirect: '/dashboard',\n  children: [{\n    path: 'dashboard',\n    name: 'Dashboard',\n    component: () => import('@/views/dashboard/index'),\n    meta: {\n      title: '首页',\n      icon: 'el-icon-s-home'\n    }\n  }]\n},\n// Firefox兼容性测试页面（开发环境）\n{\n  path: '/firefox-test',\n  component: () => import('@/views/test/firefox-test'),\n  hidden: true\n}];\n\n// 动态路由，根据用户角色动态加载\nexport const asyncRoutes = [\n// 管理员路由 - 只对管理员可见\n{\n  path: '/department',\n  component: Layout,\n  name: 'Department',\n  meta: {\n    title: '部门管理',\n    icon: 'el-icon-office-building',\n    roles: ['ROLE_admin']\n  },\n  children: [{\n    path: 'index',\n    name: 'DepartmentIndex',\n    component: () => import('@/views/system/department/index'),\n    meta: {\n      title: '部门管理',\n      icon: 'el-icon-office-building'\n    }\n  }]\n}, {\n  path: '/position',\n  component: Layout,\n  name: 'Position',\n  meta: {\n    title: '职位管理',\n    icon: 'el-icon-suitcase',\n    roles: ['ROLE_admin']\n  },\n  children: [{\n    path: 'index',\n    name: 'PositionIndex',\n    component: () => import('@/views/system/position/index'),\n    meta: {\n      title: '职位管理',\n      icon: 'el-icon-suitcase'\n    }\n  }]\n}, {\n  path: '/permission',\n  component: Layout,\n  name: 'Permission',\n  meta: {\n    title: '权限管理',\n    icon: 'el-icon-key',\n    roles: ['ROLE_admin']\n  },\n  children: [{\n    path: 'index',\n    name: 'PermissionIndex',\n    component: () => import('@/views/system/permission/index'),\n    meta: {\n      title: '权限管理',\n      icon: 'el-icon-key'\n    }\n  }]\n}, {\n  path: '/role',\n  component: Layout,\n  name: 'Role',\n  meta: {\n    title: '角色管理',\n    icon: 'el-icon-s-check',\n    roles: ['ROLE_admin']\n  },\n  children: [{\n    path: 'index',\n    name: 'RoleIndex',\n    component: () => import('@/views/system/role/index'),\n    meta: {\n      title: '角色管理',\n      icon: 'el-icon-s-check'\n    }\n  }]\n}, {\n  path: '/user',\n  component: Layout,\n  name: 'User',\n  meta: {\n    title: '用户管理',\n    icon: 'el-icon-s-custom',\n    roles: ['ROLE_admin']\n  },\n  children: [{\n    path: 'index',\n    name: 'UserIndex',\n    component: () => import('@/views/system/user/index'),\n    meta: {\n      title: '用户管理',\n      icon: 'el-icon-s-custom'\n    }\n  }]\n},\n// 员工路由 - 只对员工可见\n{\n  path: '/employee',\n  component: Layout,\n  redirect: '/employee/profile',\n  name: 'Employee',\n  meta: {\n    title: '员工',\n    icon: 'el-icon-user-solid',\n    roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr']\n  },\n  children: [{\n    path: 'profile',\n    name: 'Profile',\n    component: () => import('@/views/employee/profile/index'),\n    meta: {\n      title: '档案管理',\n      icon: 'el-icon-folder'\n    }\n  }, {\n    path: 'reward',\n    name: 'Reward',\n    component: () => import('@/views/employee/reward/index'),\n    meta: {\n      title: '奖惩管理',\n      icon: 'el-icon-medal'\n    }\n  }, {\n    path: 'resign',\n    name: 'Resign',\n    component: () => import('@/views/employee/resign/index'),\n    meta: {\n      title: '离职管理',\n      icon: 'el-icon-switch-button'\n    }\n  }]\n},\n// 考勤路由 - 只对员工可见\n{\n  path: '/attendance',\n  component: Layout,\n  redirect: '/attendance/record',\n  name: 'Attendance',\n  meta: {\n    title: '考勤',\n    icon: 'el-icon-date',\n    roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr']\n  },\n  children: [{\n    path: 'record',\n    name: 'Record',\n    component: () => import('@/views/attendance/record/index'),\n    meta: {\n      title: '考勤管理',\n      icon: 'el-icon-alarm-clock'\n    }\n  }, {\n    path: 'leave',\n    name: 'Leave',\n    component: () => import('@/views/attendance/leave/index'),\n    meta: {\n      title: '请假管理',\n      icon: 'el-icon-stopwatch'\n    }\n  }]\n},\n// 财务路由 - 只对员工可见\n{\n  path: '/finance',\n  component: Layout,\n  redirect: '/finance/account',\n  name: 'Finance',\n  meta: {\n    title: '财务',\n    icon: 'el-icon-wallet',\n    roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr']\n  },\n  children: [{\n    path: 'account',\n    name: 'Account',\n    component: () => import('@/views/finance/account/index'),\n    meta: {\n      title: '账套管理',\n      icon: 'el-icon-bank-card',\n      roles: ['ROLE_hr']\n    }\n  }, {\n    path: 'setting',\n    name: 'Setting',\n    component: () => import('@/views/finance/setting/index'),\n    meta: {\n      title: '账套匹配',\n      icon: 'el-icon-connection',\n      roles: ['ROLE_hr']\n    }\n  }, {\n    path: 'report',\n    name: 'Report',\n    component: () => import('@/views/finance/report/index'),\n    meta: {\n      title: '薪资报表',\n      icon: 'el-icon-document'\n    }\n  }]\n},\n// 404页面必须放在最后\n{\n  path: '*',\n  redirect: '/404',\n  hidden: true\n}];\nconst originalPush = VueRouter.prototype.push;\nVueRouter.prototype.push = function push(location, onResolve, onReject) {\n  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);\n  return originalPush.call(this, location).catch(err => err);\n};\nconst createRouter = () => new VueRouter({\n  // mode: 'history', // 需要服务端支持\n  scrollBehavior: () => ({\n    y: 0\n  }),\n  routes: constantRoutes\n});\nconst router = createRouter();\n\n// 重置路由方法\nexport function resetRouter() {\n  const newRouter = createRouter();\n  router.matcher = newRouter.matcher;\n}\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Layout", "use", "constantRoutes", "path", "component", "hidden", "redirect", "children", "name", "meta", "title", "icon", "asyncRoutes", "roles", "originalPush", "prototype", "push", "location", "onResolve", "onReject", "call", "catch", "err", "createRouter", "scroll<PERSON>eh<PERSON>or", "y", "routes", "router", "resetRouter", "newRouter", "matcher"], "sources": ["D:/Desktop/GP/HRMS/client/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\n// 引入布局组件\nimport Layout from '@/layout'\n\nVue.use(VueRouter)\n\n// 公共路由，所有用户都可以访问\nexport const constantRoutes = [\n  {\n    path: '/login',\n    component: () => import('@/views/login/index'),\n    hidden: true\n  },\n  {\n    path: '/register',\n    component: () => import('@/views/register/index'),\n    hidden: true\n  },\n  {\n    path: '/404',\n    component: () => import('@/views/error/404'),\n    hidden: true\n  },\n  {\n    path: '/',\n    component: Layout,\n    redirect: '/dashboard',\n    children: [{\n      path: 'dashboard',\n      name: 'Dashboard',\n      component: () => import('@/views/dashboard/index'),\n      meta: { title: '首页', icon: 'el-icon-s-home' }\n    }]\n  },\n  // Firefox兼容性测试页面（开发环境）\n  {\n    path: '/firefox-test',\n    component: () => import('@/views/test/firefox-test'),\n    hidden: true\n  }\n]\n\n// 动态路由，根据用户角色动态加载\nexport const asyncRoutes = [\n  // 管理员路由 - 只对管理员可见\n  {\n    path: '/department',\n    component: Layout,\n    name: 'Department',\n    meta: { title: '部门管理', icon: 'el-icon-office-building', roles: ['ROLE_admin'] },\n    children: [\n      {\n        path: 'index',\n        name: 'DepartmentIndex',\n        component: () => import('@/views/system/department/index'),\n        meta: { title: '部门管理', icon: 'el-icon-office-building' }\n      }\n    ]\n  },\n  {\n    path: '/position',\n    component: Layout,\n    name: 'Position',\n    meta: { title: '职位管理', icon: 'el-icon-suitcase', roles: ['ROLE_admin'] },\n    children: [\n      {\n        path: 'index',\n        name: 'PositionIndex',\n        component: () => import('@/views/system/position/index'),\n        meta: { title: '职位管理', icon: 'el-icon-suitcase' }\n      }\n    ]\n  },\n  {\n    path: '/permission',\n    component: Layout,\n    name: 'Permission',\n    meta: { title: '权限管理', icon: 'el-icon-key', roles: ['ROLE_admin'] },\n    children: [\n      {\n        path: 'index',\n        name: 'PermissionIndex',\n        component: () => import('@/views/system/permission/index'),\n        meta: { title: '权限管理', icon: 'el-icon-key' }\n      }\n    ]\n  },\n  {\n    path: '/role',\n    component: Layout,\n    name: 'Role',\n    meta: { title: '角色管理', icon: 'el-icon-s-check', roles: ['ROLE_admin'] },\n    children: [\n      {\n        path: 'index',\n        name: 'RoleIndex',\n        component: () => import('@/views/system/role/index'),\n        meta: { title: '角色管理', icon: 'el-icon-s-check' }\n      }\n    ]\n  },\n  {\n    path: '/user',\n    component: Layout,\n    name: 'User',\n    meta: { title: '用户管理', icon: 'el-icon-s-custom', roles: ['ROLE_admin'] },\n    children: [\n      {\n        path: 'index',\n        name: 'UserIndex',\n        component: () => import('@/views/system/user/index'),\n        meta: { title: '用户管理', icon: 'el-icon-s-custom' }\n      }\n    ]\n  },\n\n  // 员工路由 - 只对员工可见\n  {\n    path: '/employee',\n    component: Layout,\n    redirect: '/employee/profile',\n    name: 'Employee',\n    meta: { title: '员工', icon: 'el-icon-user-solid', roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr'] },\n    children: [\n      {\n        path: 'profile',\n        name: 'Profile',\n        component: () => import('@/views/employee/profile/index'),\n        meta: { title: '档案管理', icon: 'el-icon-folder' }\n      },\n      {\n        path: 'reward',\n        name: 'Reward',\n        component: () => import('@/views/employee/reward/index'),\n        meta: { title: '奖惩管理', icon: 'el-icon-medal' }\n      },\n      {\n        path: 'resign',\n        name: 'Resign',\n        component: () => import('@/views/employee/resign/index'),\n        meta: { title: '离职管理', icon: 'el-icon-switch-button' }\n      }\n    ]\n  },\n\n  // 考勤路由 - 只对员工可见\n  {\n    path: '/attendance',\n    component: Layout,\n    redirect: '/attendance/record',\n    name: 'Attendance',\n    meta: { title: '考勤', icon: 'el-icon-date', roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr'] },\n    children: [\n      {\n        path: 'record',\n        name: 'Record',\n        component: () => import('@/views/attendance/record/index'),\n        meta: { title: '考勤管理', icon: 'el-icon-alarm-clock' }\n      },\n      {\n        path: 'leave',\n        name: 'Leave',\n        component: () => import('@/views/attendance/leave/index'),\n        meta: { title: '请假管理', icon: 'el-icon-stopwatch' }\n      }\n    ]\n  },\n\n  // 财务路由 - 只对员工可见\n  {\n    path: '/finance',\n    component: Layout,\n    redirect: '/finance/account',\n    name: 'Finance',\n    meta: { title: '财务', icon: 'el-icon-wallet', roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr'] },\n    children: [\n      {\n        path: 'account',\n        name: 'Account',\n        component: () => import('@/views/finance/account/index'),\n        meta: { title: '账套管理', icon: 'el-icon-bank-card', roles: ['ROLE_hr'] }\n      },\n      {\n        path: 'setting',\n        name: 'Setting',\n        component: () => import('@/views/finance/setting/index'),\n        meta: { title: '账套匹配', icon: 'el-icon-connection', roles: ['ROLE_hr'] }\n      },\n      {\n        path: 'report',\n        name: 'Report',\n        component: () => import('@/views/finance/report/index'),\n        meta: { title: '薪资报表', icon: 'el-icon-document' }\n      }\n    ]\n  },\n\n  // 404页面必须放在最后\n  { path: '*', redirect: '/404', hidden: true }\n]\n\nconst originalPush = VueRouter.prototype.push\nVueRouter.prototype.push = function push(location, onResolve, onReject) {\nif (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)\nreturn originalPush.call(this, location).catch(err => err)\n}\n\nconst createRouter = () => new VueRouter({\n  // mode: 'history', // 需要服务端支持\n  scrollBehavior: () => ({ y: 0 }),\n  routes: constantRoutes\n})\n\nconst router = createRouter()\n\n// 重置路由方法\nexport function resetRouter() {\n  const newRouter = createRouter()\n  router.matcher = newRouter.matcher\n}\n\nexport default router\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;;AAElC;AACA,OAAOC,MAAM,MAAM,UAAU;AAE7BF,GAAG,CAACG,GAAG,CAACF,SAAS,CAAC;;AAElB;AACA,OAAO,MAAMG,cAAc,GAAG,CAC5B;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;EAC9CC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;EAC5CC,MAAM,EAAE;AACV,CAAC,EACD;EACEF,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEJ,MAAM;EACjBM,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,CAAC;IACTJ,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,WAAW;IACjBJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC;IAClDK,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAiB;EAC9C,CAAC;AACH,CAAC;AACD;AACA;EACER,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;EACpDC,MAAM,EAAE;AACV,CAAC,CACF;;AAED;AACA,OAAO,MAAMO,WAAW,GAAG;AACzB;AACA;EACET,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEJ,MAAM;EACjBQ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,yBAAyB;IAAEE,KAAK,EAAE,CAAC,YAAY;EAAE,CAAC;EAC/EN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,iBAAiB;IACvBJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAA0B;EACzD,CAAC;AAEL,CAAC,EACD;EACER,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ,MAAM;EACjBQ,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,kBAAkB;IAAEE,KAAK,EAAE,CAAC,YAAY;EAAE,CAAC;EACxEN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,eAAe;IACrBJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAmB;EAClD,CAAC;AAEL,CAAC,EACD;EACER,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEJ,MAAM;EACjBQ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,aAAa;IAAEE,KAAK,EAAE,CAAC,YAAY;EAAE,CAAC;EACnEN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,iBAAiB;IACvBJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAc;EAC7C,CAAC;AAEL,CAAC,EACD;EACER,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEJ,MAAM;EACjBQ,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAEE,KAAK,EAAE,CAAC,YAAY;EAAE,CAAC;EACvEN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,WAAW;IACjBJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAkB;EACjD,CAAC;AAEL,CAAC,EACD;EACER,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEJ,MAAM;EACjBQ,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,kBAAkB;IAAEE,KAAK,EAAE,CAAC,YAAY;EAAE,CAAC;EACxEN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,WAAW;IACjBJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAmB;EAClD,CAAC;AAEL,CAAC;AAED;AACA;EACER,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ,MAAM;EACjBM,QAAQ,EAAE,mBAAmB;EAC7BE,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,oBAAoB;IAAEE,KAAK,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,SAAS;EAAE,CAAC;EACrGN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfK,IAAI,EAAE,SAAS;IACfJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAiB;EAChD,CAAC,EACD;IACER,IAAI,EAAE,QAAQ;IACdK,IAAI,EAAE,QAAQ;IACdJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB;EAC/C,CAAC,EACD;IACER,IAAI,EAAE,QAAQ;IACdK,IAAI,EAAE,QAAQ;IACdJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAwB;EACvD,CAAC;AAEL,CAAC;AAED;AACA;EACER,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEJ,MAAM;EACjBM,QAAQ,EAAE,oBAAoB;EAC9BE,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEE,KAAK,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,SAAS;EAAE,CAAC;EAC/FN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,QAAQ;IACdK,IAAI,EAAE,QAAQ;IACdJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAsB;EACrD,CAAC,EACD;IACER,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,OAAO;IACbJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAoB;EACnD,CAAC;AAEL,CAAC;AAED;AACA;EACER,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ,MAAM;EACjBM,QAAQ,EAAE,kBAAkB;EAC5BE,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,gBAAgB;IAAEE,KAAK,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,SAAS;EAAE,CAAC;EACjGN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfK,IAAI,EAAE,SAAS;IACfJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,mBAAmB;MAAEE,KAAK,EAAE,CAAC,SAAS;IAAE;EACvE,CAAC,EACD;IACEV,IAAI,EAAE,SAAS;IACfK,IAAI,EAAE,SAAS;IACfJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,oBAAoB;MAAEE,KAAK,EAAE,CAAC,SAAS;IAAE;EACxE,CAAC,EACD;IACEV,IAAI,EAAE,QAAQ;IACdK,IAAI,EAAE,QAAQ;IACdJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAmB;EAClD,CAAC;AAEL,CAAC;AAED;AACA;EAAER,IAAI,EAAE,GAAG;EAAEG,QAAQ,EAAE,MAAM;EAAED,MAAM,EAAE;AAAK,CAAC,CAC9C;AAED,MAAMS,YAAY,GAAGf,SAAS,CAACgB,SAAS,CAACC,IAAI;AAC7CjB,SAAS,CAACgB,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EACxE,IAAID,SAAS,IAAIC,QAAQ,EAAE,OAAOL,YAAY,CAACM,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,CAAC;EACxF,OAAOL,YAAY,CAACM,IAAI,CAAC,IAAI,EAAEH,QAAQ,CAAC,CAACI,KAAK,CAACC,GAAG,IAAIA,GAAG,CAAC;AAC1D,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAA,KAAM,IAAIxB,SAAS,CAAC;EACvC;EACAyB,cAAc,EAAEA,CAAA,MAAO;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChCC,MAAM,EAAExB;AACV,CAAC,CAAC;AAEF,MAAMyB,MAAM,GAAGJ,YAAY,CAAC,CAAC;;AAE7B;AACA,OAAO,SAASK,WAAWA,CAAA,EAAG;EAC5B,MAAMC,SAAS,GAAGN,YAAY,CAAC,CAAC;EAChCI,MAAM,CAACG,OAAO,GAAGD,SAAS,CAACC,OAAO;AACpC;AAEA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}