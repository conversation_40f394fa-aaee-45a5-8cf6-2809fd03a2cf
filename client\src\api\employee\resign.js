import request from '@/utils/request'

// 分页查询离职申请
export function listResign(query) {
  return request({
    url: '/employee/resign/page',
    method: 'get',
    params: query
  })
}

// 根据ID获取离职申请详情
export function getResign(id) {
  return request({
    url: `/employee/resign/${id}`,
    method: 'get'
  })
}

// 查询个人离职申请列表 - 使用个人申请接口
export function listPersonalResign() {
  return request({
    url: '/employee/resign/self',
    method: 'get'
  })
}

// 查询离职申请列表 - 使用分页查询接口
export function listResignPage(params) {
  return request({
    url: '/employee/resign/page',
    method: 'get',
    params
  })
}

// 提交离职申请
export function addResign(data) {
  return request({
    url: '/employee/resign/apply',
    method: 'post',
    data
  })
}

// 取消离职申请
export function withdrawResign(id) {
  return request({
    url: `/employee/resign/cancel/${id}`,
    method: 'put'
  })
}

// 审批离职申请
export function approveResign(id, data) {
  return request({
    url: `/employee/resign/approve/${id}`,
    method: 'put',
    data
  })
}

// 拒绝离职申请 - 使用审批接口，状态设为拒绝
export function rejectResign(id, data) {
  return request({
    url: `/employee/resign/approve/${id}`,
    method: 'put',
    data: {
      ...data,
      status: 'REJECTED'
    }
  })
}
