<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico?v=2">
    <title>HRMS</title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <!-- Firefox兼容性处理：仅针对高德地图统计错误 -->
    <script type="text/javascript">
      // 仅在Firefox中处理高德地图统计错误
      if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
        // 保存原始的错误处理器
        var originalOnError = window.onerror;

        window.onerror = function(message, source, lineno, colno, error) {
          // 只处理高德地图count统计请求的错误
          if (source && source.includes('webapi.amap.com/count?type=UIInit')) {
            console.warn('Firefox: 已忽略高德地图统计错误');
            return true; // 阻止错误冒泡
          }

          // 其他错误交给原始处理器处理
          if (originalOnError) {
            return originalOnError.call(this, message, source, lineno, colno, error);
          }
          return false;
        };
      }
    </script>

    <!-- 高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662"></script>
    <script type="text/javascript" src="https://webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>
  </body>
</html>
