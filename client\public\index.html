<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico?v=2">
    <title>HRMS</title>
    <!-- 添加安全策略，允许高德地图相关域名 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://webapi.amap.com https://restapi.amap.com; connect-src 'self' https://webapi.amap.com https://restapi.amap.com; img-src 'self' data: https://webapi.amap.com https://restapi.amap.com;">
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <!-- 高德地图API加载优化，添加错误处理和Firefox兼容性 -->
    <script type="text/javascript">
      // Firefox兼容性处理：捕获并忽略高德地图统计请求错误
      window.addEventListener('error', function(e) {
        // 检查是否是高德地图相关的错误
        if (e.filename && (e.filename.includes('webapi.amap.com') || e.filename.includes('count?type=UIInit'))) {
          console.warn('高德地图统计请求错误（已忽略）:', e.message);
          e.preventDefault();
          return true;
        }
      }, true);

      // 捕获未处理的Promise拒绝（主要针对高德地图API）
      window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.toString().includes('amap')) {
          console.warn('高德地图Promise错误（已忽略）:', e.reason);
          e.preventDefault();
        }
      });
    </script>

    <!-- 高德地图核心API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662&plugin=AMap.Scale,AMap.ToolBar,AMap.Geocoder,AMap.Geolocation" async defer onerror="console.warn('高德地图核心API加载失败')"></script>

    <!-- 高德地图UI组件（可选加载，避免Firefox错误） -->
    <script type="text/javascript">
      // 延迟加载UI组件，避免Firefox兼容性问题
      setTimeout(function() {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://webapi.amap.com/ui/1.1/main.js?v=1.1.1';
        script.async = true;
        script.onerror = function() {
          console.warn('高德地图UI组件加载失败，但不影响基础功能');
        };
        document.head.appendChild(script);
      }, 1000);
    </script>
  </body>
</html>
