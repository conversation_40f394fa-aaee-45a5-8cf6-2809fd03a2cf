<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico?v=2">
    <title>HRMS</title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <!-- Firefox兼容性处理：针对高德地图统计错误 -->
    <script type="text/javascript">
      // 仅在Firefox中处理高德地图相关错误
      if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
        // 保存原始的错误处理器
        var originalOnError = window.onerror;

        window.onerror = function(message, source, lineno, colno, error) {
          // 处理高德地图相关的所有错误
          if (source && (
            source.includes('webapi.amap.com/count?type=UIInit') ||
            source.includes('webapi.amap.com/ui/1.1/main.js')
          )) {
            console.warn('Firefox: 已忽略高德地图相关错误');
            return true; // 阻止错误冒泡
          }

          // 处理包含高德地图错误信息的错误
          if (message && (
            message.includes('webapi.amap.com/count?type=UIInit') ||
            message.includes('Script error for "http://webapi.amap.com/count') ||
            message.includes('requirejs.org/docs/errors.html#scripterror')
          )) {
            console.warn('Firefox: 已忽略高德地图脚本错误');
            return true; // 阻止错误冒泡
          }

          // 其他错误交给原始处理器处理
          if (originalOnError) {
            return originalOnError.call(this, message, source, lineno, colno, error);
          }
          return false;
        };

        // 额外处理：拦截RequireJS错误
        window.addEventListener('error', function(event) {
          if (event.target && event.target.src &&
              event.target.src.includes('webapi.amap.com')) {
            console.warn('Firefox: 已拦截高德地图资源加载错误');
            event.preventDefault();
            event.stopPropagation();
            return false;
          }
        }, true);
      }
    </script>

    <!-- 高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662"></script>

    <!-- 条件加载高德地图UI组件：Firefox中跳过以避免错误 -->
    <script type="text/javascript">
      // 只在非Firefox浏览器中加载UI组件
      if (navigator.userAgent.toLowerCase().indexOf('firefox') === -1) {
        // 非Firefox浏览器正常加载UI组件
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://webapi.amap.com/ui/1.1/main.js?v=1.1.1';
        document.head.appendChild(script);
      } else {
        // Firefox浏览器跳过UI组件加载
        console.warn('Firefox: 跳过高德地图UI组件加载以避免兼容性问题');
      }
    </script>
  </body>
</html>
