package cn.edu.ncu.hrms.attendance.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "管理员仪表盘 - 今日考勤统计视图对象")
public class DashboardAttendanceStatsVO {

    @ApiModelProperty("员工总数 (今日有考勤记录或应出勤的)")
    private Integer totalEmployees;

    @ApiModelProperty("状态正常人数")
    private Integer normalCount;

    @ApiModelProperty("今日请假人数")
    private Integer leaveCount;

    @ApiModelProperty("今日迟到人数")
    private Integer lateCount;

    @ApiModelProperty("今日早退人数")
    private Integer earlyLeaveCount;

    @ApiModelProperty("今日旷工人数")
    private Integer absentCount;

    // 可以根据需要添加其他字段，例如出勤率等

} 