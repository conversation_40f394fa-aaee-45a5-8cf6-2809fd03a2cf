package cn.edu.ncu.hrms.system.controller;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.SystemPositionPageQueryDTO;
import cn.edu.ncu.hrms.system.service.SystemPositionService;
import cn.edu.ncu.hrms.system.vo.PositionVO;
import cn.edu.ncu.hrms.system.dto.PositionAddDTO;
import cn.edu.ncu.hrms.system.dto.PositionUpdateDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统职位管理控制器
 */
@RestController
@RequestMapping("/system/position")
@Api(tags = "系统职位管理接口")
public class SystemPositionController {

    @Autowired
    private SystemPositionService systemPositionService;

    /**
     * 获取职位列表
     */
    @GetMapping("/list")
    @ApiOperation("获取职位列表")
    @PreAuthorize("@hrms.hasAuthority('system:position:list')")
    public Result<List<PositionVO>> getPositionList(SystemPositionPageQueryDTO queryDTO) {
        return systemPositionService.getPositionList(queryDTO);
    }

    /**
     * 分页获取职位列表
     * @param queryDTO
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("获取职位分页列表")
    @PreAuthorize("@hrms.hasAuthority('system:position:list')")
    public Result<PageResult<PositionVO>> getPositionPage(SystemPositionPageQueryDTO queryDTO) {
        PageInfo<PositionVO> positionPage = systemPositionService.getPositionPage(queryDTO);
        return PageResult.success(positionPage.getTotal(), positionPage.getList());
    }

    /**
     * 根据ID获取职位信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取职位信息")
    @ApiImplicitParam(name = "id", value = "职位ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:position:query')")
    public Result<PositionVO> getPositionById(@PathVariable Integer id) {
        return systemPositionService.getPositionVOById(id);
    }

    /**
     * 创建职位
     */
    @PostMapping
    @ApiOperation("创建职位")
    @PreAuthorize("@hrms.hasAuthority('system:position:add')")
    public Result<Void> createPosition(@Valid @RequestBody PositionAddDTO addDTO) {
        return systemPositionService.createPosition(addDTO);
    }

    /**
     * 更新职位信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新职位信息")
    @ApiImplicitParam(name = "id", value = "职位ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:position:edit')")
    public Result<Void> updatePosition(@PathVariable Integer id, @Valid @RequestBody PositionUpdateDTO updateDTO) {
        return systemPositionService.updatePosition(id, updateDTO);
    }

    /**
     * 删除职位
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除职位")
    @ApiImplicitParam(name = "id", value = "职位ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:position:delete')")
    public Result<Void> deletePosition(@PathVariable Integer id) {
        return systemPositionService.deletePosition(id);
    }


    /**
     * 根据账套ID查询职位列表
     */
    @GetMapping("/account/{accountId}")
    @ApiOperation("根据账套ID查询职位列表")
    @ApiImplicitParam(name = "accountId", value = "账套ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:position:query')")
    public Result<List<PositionVO>> getPositionsByAccountId(@PathVariable Integer accountId) {
        return systemPositionService.getPositionsByAccountId(accountId);
    }


    /**
     * 获取职位的员工列表
     */
    @GetMapping("/{id}/employees")
    @ApiOperation("获取职位的员工列表")
    @ApiImplicitParam(name = "id", value = "职位ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:position:query')")
    public Result<List<Employee>> getPositionEmployees(@PathVariable Integer id) {
        return systemPositionService.getPositionEmployees(id);
    }
} 