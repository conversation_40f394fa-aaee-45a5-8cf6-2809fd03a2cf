package cn.edu.ncu.hrms.employee.vo;

import cn.edu.ncu.hrms.attendance.vo.AttendanceStatisticsVO;
import cn.edu.ncu.hrms.common.entity.Attendance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "员工仪表盘视图对象")
public class EmployeeDashboardVO {

    @ApiModelProperty("员工姓名")
    private String employeeName; // 可以考虑移除，前端可以通过 store 获取

    @ApiModelProperty("考勤统计信息 (例如：当月或指定时段)")
    private AttendanceStatisticsVO attendanceStats;

    @ApiModelProperty("今日考勤记录")
    private Attendance todayRecord;

    // 可以根据需要添加其他字段，如公告、待办事项等

} 