/**
 * 高德地图工具函数
 * Firefox兼容性优化版本
 */
/* eslint-disable no-undef */

// 检查AMap是否可用
function checkAMapAvailable() {
  if (typeof window === 'undefined' || !window.AMap) {
    console.warn('高德地图API未加载或不可用')
    return false
  }
  return true
}

// 初始化地图
export function initMap(containerId, options = {}) {
  if (!checkAMapAvailable()) {
    throw new Error('高德地图API未加载，请检查网络连接')
  }

  const defaultOptions = {
    zoom: 15,
    resizeEnable: true
  }

  try {
    return new AMap.Map(containerId, { ...defaultOptions, ...options })
  } catch (error) {
    console.error('初始化地图失败:', error)
    throw error
  }
}

// 创建标记
export function createMarker(position, map, options = {}) {
  if (!checkAMapAvailable()) {
    console.warn('高德地图API不可用，无法创建标记')
    return null
  }

  // 确保位置是有效的
  if (!position || (typeof position === 'object' && (isNaN(position.lng) || isNaN(position.lat)))) {
    console.error('创建标记失败：无效的位置', position)
    return null
  }

  const defaultOptions = {
    position,
    map,
    title: '标记点'
  }

  try {
    return new AMap.Marker({ ...defaultOptions, ...options })
  } catch (error) {
    console.error('创建标记失败：', error)
    return null
  }
}

// 创建用户位置标记（圆形标记）
export function createUserLocationMarker(position, map, options = {}) {
  const defaultOptions = {
    position,
    map,
    icon: new AMap.Icon({
      // 使用圆形图标
      image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
      size: new AMap.Size(20, 20),
      imageSize: new AMap.Size(20, 20)
    }),
    offset: new AMap.Pixel(-10, -10),
    title: '您的位置'
  }

  return new AMap.Marker({ ...defaultOptions, ...options })
}

// 创建圆形
export function createCircle(center, radius, map, options = {}) {
  const defaultOptions = {
    center,
    radius,
    strokeColor: '#409EFF',
    strokeWeight: 2,
    strokeOpacity: 0.8,
    fillColor: '#409EFF',
    fillOpacity: 0.2,
    map
  }

  return new AMap.Circle({ ...defaultOptions, ...options })
}

// 获取当前位置
export function getCurrentPosition() {
  return new Promise((resolve, reject) => {
    if (!checkAMapAvailable()) {
      reject(new Error('高德地图API不可用，无法获取位置'))
      return
    }

    try {
      const geolocation = new AMap.Geolocation({
        enableHighAccuracy: true,
        timeout: 10000,
        zoomToAccuracy: true
      })

      geolocation.getCurrentPosition((status, result) => {
        if (status === 'complete') {
          // 定位成功
          resolve(result)
        } else {
          // 定位失败
          let errorMessage = '获取位置信息失败'
          switch (result.message) {
            case 'Geolocation permission denied.':
              errorMessage = '用户拒绝了位置请求'
              break
            case 'Geolocation position unavailable.':
              errorMessage = '位置信息不可用'
              break
            case 'Geolocation timeout.':
              errorMessage = '获取位置超时'
              break
            default:
              errorMessage = result.message || '发生未知错误'
              break
          }
          reject(new Error(errorMessage))
        }
      })
    } catch (error) {
      console.error('创建定位实例失败:', error)
      reject(new Error('定位功能初始化失败'))
    }
  })
}

// 计算两点之间的距离（米）
export function calculateDistance(point1, point2) {
  const [lng1, lat1] = point1
  const [lng2, lat2] = point2

  const p1 = new AMap.LngLat(lng1, lat1)
  const p2 = new AMap.LngLat(lng2, lat2)

  return p1.distance(p2)
}

// 创建地理编码实例
export function createGeocoder(options = {}) {
  const defaultOptions = {
    city: '全国'
  }

  return new AMap.Geocoder({ ...defaultOptions, ...options })
}

// 地点搜索 - 使用高德地图Web API
export function searchLocation(keywords) {
  return new Promise((resolve, reject) => {
    // 高德地图WEB服务API密钥
    const key = '47d5e708a924649baf363b1e075fe14c'

    // 构建API请求URL
    const url = `https://restapi.amap.com/v5/place/text?key=${key}&keywords=${encodeURIComponent(keywords)}`

    // 发起请求
    fetch(url)
      .then(response => response.json())
      .then(data => {
        if (data.status === '1' && data.pois && data.pois.length > 0) {
          // 获取第一个POI的位置
          const location = data.pois[0].location
          if (location) {
            // 位置格式为"经度,纬度"，需要转换为AMap.LngLat对象
            const [lng, lat] = location.split(',').map(Number)
            resolve({
              lng,
              lat,
              name: data.pois[0].name,
              address: data.pois[0].address || '未知地址'
            })
          } else {
            reject(new Error('无法获取位置信息'))
          }
        } else {
          reject(new Error(data.info || '地点搜索失败，请尝试其他关键词'))
        }
      })
      .catch(error => {
        console.error('地点搜索请求失败:', error)
        reject(new Error('地点搜索请求失败，请检查网络连接'))
      })
  })
}

// 地点搜索 - 使用AMap.Geocoder (备用方法)
export function searchLocationWithGeocoder(address, geocoder) {
  return new Promise((resolve, reject) => {
    geocoder.getLocation(address, (status, result) => {
      if (status === 'complete' && result.info === 'OK') {
        resolve(result.geocodes[0].location)
      } else {
        reject(new Error('地点搜索失败，请尝试其他关键词'))
      }
    })
  })
}

// 逆地理编码（坐标转地址）- 使用AMap.Geocoder
export function getAddress(position, geocoder) {
  return new Promise((resolve, reject) => {
    geocoder.getAddress(position, (status, result) => {
      if (status === 'complete' && result.info === 'OK') {
        resolve(result.regeocode.formattedAddress)
      } else {
        reject(new Error('获取地址信息失败'))
      }
    })
  })
}

// 逆地理编码（坐标转地址）- 使用高德地图Web API
export function getAddressFromAPI(location) {
  return new Promise((resolve, reject) => {
    // 高德地图WEB服务API密钥（与Web端不同）
    const key = '47d5e708a924649baf363b1e075fe14c'

    // 如果location是数组，转换为逗号分隔的字符串
    let locationStr = location
    if (Array.isArray(location)) {
      locationStr = location.join(',')
    }

    // 构建API请求URL
    const url = `https://restapi.amap.com/v3/geocode/regeo?key=${key}&location=${locationStr}&extensions=base`

    // 发起请求
    fetch(url)
      .then(response => response.json())
      .then(data => {
        if (data.status === '1') {
          // 如果有格式化地址，直接使用
          if (data.regeocode && data.regeocode.formatted_address) {
            resolve(data.regeocode.formatted_address)
          }
          // 否则，从地址组件中构建地址
          else if (data.regeocode && data.regeocode.addressComponent) {
            const addressComponent = data.regeocode.addressComponent
            let address = ''

            // 从省份开始精确到街道
            if (addressComponent.province) {
              address += addressComponent.province
            }
            if (addressComponent.city) {
              address += addressComponent.city
            }
            if (addressComponent.district) {
              address += addressComponent.district
            }
            if (addressComponent.township) {
              address += addressComponent.township
            }
            if (addressComponent.street) {
              address += addressComponent.street
            }
            if (addressComponent.streetNumber) {
              address += addressComponent.streetNumber
            }

            resolve(address || '未知地点')
          } else {
            reject(new Error('无法解析地址信息'))
          }
        } else {
          reject(new Error(data.info || '获取地址信息失败'))
        }
      })
      .catch(error => {
        console.error('逆地理编码请求失败:', error)
        reject(error)
      })
  })
}
