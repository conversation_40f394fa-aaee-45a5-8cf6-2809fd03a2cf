import { getUserInfo } from '@/api/user'

const state = {
  userInfo: {},
  roles: [],
  permissions: []
}

const mutations = {
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  }
}

const actions = {
  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const response = await getUserInfo()
      if (response.code === 1 && response.data) {
        const { roles, permissions } = response.data
        
        // 保存用户信息
        commit('SET_USER_INFO', response.data)
        
        // 保存角色信息
        commit('SET_ROLES', roles || [])
        
        // 保存权限信息
        commit('SET_PERMISSIONS', permissions || [])
        
        return response.data
      } else {
        return Promise.reject(new Error(response.msg || '获取用户信息失败'))
      }
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // 重置用户信息
  resetUserInfo({ commit }) {
    commit('SET_USER_INFO', {})
    commit('SET_ROLES', [])
    commit('SET_PERMISSIONS', [])
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
