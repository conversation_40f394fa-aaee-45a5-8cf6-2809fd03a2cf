package cn.edu.ncu.hrms.system.vo;

// import cn.edu.ncu.hrms.common.entity.Department; // Consider using DepartmentVO if available
// import cn.edu.ncu.hrms.common.entity.Position;   // Consider using PositionVO if available
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
// import java.util.List;

/**
 * 用户信息视图对象 (VO) - 根据 User.java 实体调整
 */
@Data
@ApiModel(value="UserVO", description="用户信息视图对象")
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Integer id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "是否管理员")
    private Boolean isAdmin;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "关联的员工信息ID")
    private Integer employeeId;

} 