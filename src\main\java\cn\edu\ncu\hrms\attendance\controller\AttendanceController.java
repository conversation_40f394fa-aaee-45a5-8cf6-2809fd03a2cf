package cn.edu.ncu.hrms.attendance.controller;

import cn.edu.ncu.hrms.attendance.dto.AttendanceConfigDTO;
import cn.edu.ncu.hrms.attendance.dto.AttendancePageQueryDTO;
import cn.edu.ncu.hrms.attendance.dto.ClockDTO;
import cn.edu.ncu.hrms.attendance.service.AttendanceService;
import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.entity.Attendance;
import cn.edu.ncu.hrms.attendance.vo.AttendanceVO;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import cn.edu.ncu.hrms.attendance.dto.AttendanceUpdateDTO;
import cn.edu.ncu.hrms.attendance.vo.AttendanceStatisticsVO;
import javax.validation.Valid;

import java.time.LocalDate;
import java.util.List;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.attendance.vo.DashboardAttendanceStatsVO;

/**
 * 考勤管理控制器
 */
@RestController
@RequestMapping("/attendance")
@Api(tags = "考勤管理接口")
public class AttendanceController {

    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private SecurityUtil securityUtil;

    /**
     * 员工打卡统一接口
     */
    @PostMapping("/clock")
    @ApiOperation("员工打卡")
    @PreAuthorize("isAuthenticated()")
    public Result<AttendanceVO> clock(@RequestBody ClockDTO clockDTO) {
        return attendanceService.clock(clockDTO.getEmployeeId(), clockDTO.getType(), clockDTO.getLocation());
    }

    /**
     * 员工签到打卡
     */
    @PostMapping("/clock-in")
    @ApiOperation("员工签到打卡")
    @PreAuthorize("isAuthenticated()")
    public Result<AttendanceVO> clockIn(@RequestBody ClockDTO clockDTO) {
        return attendanceService.clock(clockDTO.getEmployeeId(), AttendanceConstant.CLOCK_IN, clockDTO.getLocation());
    }

    /**
     * 员工签退打卡
     */
    @PostMapping("/clock-out")
    @ApiOperation("员工签退打卡")
    @PreAuthorize("isAuthenticated()")
    public Result<AttendanceVO> clockOut(@RequestBody ClockDTO clockDTO) {
        return attendanceService.clock(clockDTO.getEmployeeId(), AttendanceConstant.CLOCK_OUT, clockDTO.getLocation());
    }

    /**
     * 获取考勤记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation("获取考勤记录详情")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:query')")
    public Result<AttendanceVO> getById(@PathVariable @ApiParam("考勤记录ID") Integer id) {
        return attendanceService.getVOById(id);
    }

    /**
     * 获取员工今日考勤记录
     */
    @GetMapping("/today")
    @ApiOperation("获取考勤记录详情")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:query')")
    public Result<AttendanceVO> getTodayAttendanceRecord() {
        return attendanceService.getTodayAttendanceRecord();
    }

    /**
     * 分页条件查询考勤记录
     */
    @GetMapping("/page")
    @ApiOperation("分页条件查询考勤记录")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:list')")
    public Result<PageInfo<AttendanceVO>> page(AttendancePageQueryDTO queryDTO) {
        return attendanceService.pageVOs(queryDTO);
    }



    /**
     * 更新考勤记录
     */
    @PutMapping("/{id}")
    @ApiOperation("更新考勤记录")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:edit')")
    public Result<Void> update(@PathVariable Integer id, @Valid @RequestBody AttendanceUpdateDTO updateDTO) {
        return attendanceService.update(id, updateDTO);
    }

    /**
     * 获取部门考勤配置
     */
    @GetMapping("/config")
    @ApiOperation("获取当前部门考勤配置")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:edit')")
    public Result<AttendanceConfigDTO> getDepartmentAttendanceConfig() {
        return attendanceService.getDepartmentAttendanceConfig();
    }


    /**
     * 更新部门考勤配置（Leader专用）
     */
    @PutMapping("/config")
    @ApiOperation("更新部门考勤配置")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:edit')")
    public Result<Void> updateDepartmentAttendanceConfig(@RequestBody AttendanceConfigDTO configDTO) {
        return attendanceService.updateDepartmentAttendanceConfig(configDTO);
    }


    /**
     * 获取考勤统计数据
     */
    @GetMapping("/statistics/employee/{employeeId}")
    @ApiOperation("获取员工考勤统计数据")
    @PreAuthorize("@hrms.hasAuthority('attendance:record:query')")
    public Result<AttendanceStatisticsVO> getStatistics(
            @PathVariable @ApiParam("员工ID") Integer employeeId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") @ApiParam("开始日期") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") @ApiParam("结束日期") LocalDate endDate) {
        return attendanceService.getStatistics(employeeId, startDate, endDate);
    }


    @GetMapping("/records/monthly")
    @ApiOperation("获取当前登录员工指定月份的考勤记录")
    @ApiImplicitParam(name = "month", value = "年月 (YYYY-MM)", required = true, dataType = "string", paramType = "query")
    @PreAuthorize("@hrms.hasAuthority('attendance:myrecord:query')")
    public Result<List<Attendance>> getMyMonthlyRecords(@RequestParam String month) {
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            return Result.error("无法获取当前用户信息，请检查登录状态");
        }
        return attendanceService.getEmployeeMonthlyRecords(currentEmployeeId, month);
    }

    @GetMapping("/dashboard/stats")
    @ApiOperation("获取管理员仪表盘今日考勤统计")
    @PreAuthorize("@hrms.hasAuthority('attendance:dashboard:query')")
    public Result<DashboardAttendanceStatsVO> getDashboardAttendanceStats() {
        return attendanceService.getDashboardAttendanceStats();
    }
} 