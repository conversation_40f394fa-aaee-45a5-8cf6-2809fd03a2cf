package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("请假申请")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeaveRequest {
    @ApiModelProperty(value = "申请ID", required = true, example = "901")
    private Integer id;

    @ApiModelProperty(value = "员工ID", required = true, example = "1001")
    private Integer employeeId;

    @ApiModelProperty(value = "请假类型", allowableValues = "ANNUAL,SICK,PERSONAL,MARRIAGE,MATERNITY,OTHER", example = "ANNUAL（年假）")
    private String leaveType;

    @ApiModelProperty(value = "开始日期", required = true, example = "2025-01-20")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "开始时段", allowableValues = "AM,PM", example = "AM", notes = "AM表示上午请假，PM表示下午请假")
    private String startPeriod;

    @ApiModelProperty(value = "结束日期", required = true, example = "2025-01-23")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "结束时段", allowableValues = "AM,PM", example = "PM", notes = "AM表示上午请假，PM表示下午请假")
    private String endPeriod;

    @ApiModelProperty(value = "请假原因", example = "家庭事务")
    private String reason;

    @ApiModelProperty(value = "申请状态", allowableValues = "PENDING,APPROVED,REJECTED,CANCELED", example = "PENDING")
    private String status;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
