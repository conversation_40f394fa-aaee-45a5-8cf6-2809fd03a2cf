<template>
  <div class="app-container">
    <!-- 页面标题卡片 -->
    <el-card class="page-header-card">
      <div class="page-header">
        <h2>账套管理</h2>
        <div class="header-actions">
          <el-button
            v-if="isHR"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddAccount"
          >新增账套</el-button>
        </div>
      </div>
    </el-card>

    <!-- 内容卡片 -->
    <el-card class="content-card">
      <!-- 搜索区域 -->
      <div class="filter-form">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="账套ID">
            <el-input v-model="queryParams.id" placeholder="请输入账套ID" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="账套名称">
            <el-input v-model="queryParams.name" placeholder="请输入账套名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="accountList"
        border
        style="width: 100%; margin-top: 15px;"
      >
        <el-table-column prop="id" label="账套ID" width="80" align="center" />
        <el-table-column prop="name" label="账套名称" min-width="150" align="center" />
        <el-table-column prop="baseSalary" label="基本工资" min-width="120" align="center">
          <template slot-scope="scope">
            {{ formatCurrency(scope.row.baseSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="performanceRate" label="绩效比例" min-width="120" align="center">
          <template slot-scope="scope">
            {{ formatPercentage(scope.row.performanceRate) }}
          </template>
        </el-table-column>
        <el-table-column prop="insuranceRate" label="社保比例" min-width="120" align="center">
          <template slot-scope="scope">
            {{ formatPercentage(scope.row.insuranceRate) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="isHR"
              size="mini"
              type="warning"
              icon="el-icon-edit"
              circle
              @click="handleEditAccount(scope.row)"
              title="编辑"
            ></el-button>
            <el-button
              v-if="isHR"
              size="mini"
              type="danger"
              icon="el-icon-delete"
              circle
              @click="handleDeleteAccount(scope.row)"
              title="删除"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        style="margin-top: 15px; text-align: right;"
      />
    </el-card>

    <!-- 账套编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="accountForm" :model="accountForm" :rules="rules" label-width="100px">
        <el-form-item label="账套名称" prop="name">
          <el-input v-model="accountForm.name" placeholder="请输入账套名称" />
        </el-form-item>
        <el-form-item label="基本工资" prop="baseSalary">
          <el-input-number v-model="accountForm.baseSalary" :min="0" :max="100000" :precision="2" :step="100" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="绩效比例(%)" prop="performanceRate">
          <el-input-number v-model="accountForm.performanceRate" :min="0" :max="100" :precision="2" :step="1" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="社保比例(%)" prop="insuranceRate">
          <el-input-number v-model="accountForm.insuranceRate" :min="0" :max="30" :precision="2" :step="1" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccountSetList, getAccountSetById, addAccountSet, updateAccountSet, deleteAccountSet } from '@/api/finance/account'
import { parseTime } from '@/utils/index'

export default {
  name: 'FinanceAccount',
  data() {
    return {
      // 是否为HR
      isHR: false,
      // 加载状态
      loading: false,
      // 账套列表
      accountList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        name: undefined
      },
      // 总记录数
      total: 0,
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      isAddMode: false,
      // 账套表单
      accountForm: {
        id: undefined,
        name: '',
        baseSalary: 0,
        performanceRate: 0,
        insuranceRate: 0
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入账套名称', trigger: 'blur' }
        ],
        baseSalary: [
          { required: true, message: '请输入基本工资', trigger: 'blur' }
        ],
        performanceRate: [
          { required: true, message: '请输入绩效比例', trigger: 'blur' }
        ],
        insuranceRate: [
          { required: true, message: '请输入社保比例', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getUserRole()
    this.getAccountList()
  },
  methods: {
    // 获取用户角色信息 - 从Vuex中获取，不再调用API
    getUserRole() {
      // 从Vuex中获取用户角色信息
      this.isHR = this.$store.getters['userInfo/hasRole']('ROLE_hr')

      // 如果不是HR角色，跳转到首页
      if (!this.isHR) {
        this.$router.push('/dashboard')
        this.$message.warning('您没有权限访问此页面')
      }
    },

    // 获取账套列表
    async getAccountList() {
      try {
        this.loading = true
        const response = await getAccountSetList(this.queryParams)

        console.log('获取账套列表响应:', response)

        if (response.code === 1) {
          this.accountList = response.data.list || []
          this.total = response.data.total || 0
          console.log('账套列表数据:', this.accountList)
        } else {
          this.$message.error(response.msg || '获取账套列表失败')
          this.accountList = []
          this.total = 0
        }
        this.loading = false
      } catch (error) {
        console.error('获取账套列表失败', error)
        this.$message.error('获取账套列表失败')
        this.loading = false
      }
    },



    // 格式化货币
    formatCurrency(value) {
      if (value === undefined || value === null) return '¥0.00'
      return `¥${parseFloat(value).toFixed(2)}`
    },

    // 格式化百分比
    formatPercentage(value) {
      if (value === undefined || value === null) return '0%'
      return `${parseFloat(value).toFixed(2)}%`
    },

    // 格式化日期时间
    formatDateTime(time) {
      if (!time) return ''

      // 检查时间格式，如果是字符串格式的日期时间，直接返回
      if (typeof time === 'string' && time.includes('-') && time.includes(':')) {
        // 将 "2025-05-04 15:14:44" 格式转换为 "2025-05-04 15:14:44"
        return time.replace('T', ' ').split('.')[0]
      }

      return parseTime(time)
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getAccountList()
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        name: undefined
      }
      this.getAccountList()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getAccountList()
    },

    // 处理每页条数变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getAccountList()
    },
    // 处理添加账套
    handleAddAccount() {
      this.dialogTitle = '新增账套'
      this.isAddMode = true
      this.accountForm = {
        id: undefined,
        name: '',
        baseSalary: 0,
        performanceRate: 0,
        insuranceRate: 0
      }
      this.dialogVisible = true
    },
    // 处理编辑账套
    async handleEditAccount(row) {
      try {
        this.loading = true
        const response = await getAccountSetById(row.id)

        if (response.code === 1 && response.data) {
          this.dialogTitle = '编辑账套'
          this.isAddMode = false
          this.accountForm = { ...response.data }
          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || '获取账套详情失败')
        }
        this.loading = false
      } catch (error) {
        console.error('获取账套详情失败', error)
        this.$message.error('获取账套详情失败')
        this.loading = false
      }
    },
    // 处理删除账套
    handleDeleteAccount(row) {
      this.$confirm(`确定要删除账套“${row.name}”吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteAccountSet(row.id)

          if (response.code === 1) {
            this.$message.success('删除成功')
            this.getAccountList()
          } else {
            this.$message.error(response.msg || '删除账套失败')
          }
        } catch (error) {
          console.error('删除账套失败', error)
          this.$message.error('删除账套失败')
        }
      }).catch(() => {
        // 取消删除
      })
    },
    // 提交表单
    submitForm() {
      this.$refs.accountForm.validate(async valid => {
        if (valid) {
          // 如果是编辑模式，弹出警告提示
          if (!this.isAddMode) {
            this.$confirm('修改账套会影响所有关联职位的薪资，并且本月立即生效，是否继续？', '警告', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              this.doSubmitForm()
            }).catch(() => {
              // 取消操作
              this.$message.info('已取消修改')
            })
          } else {
            // 新增模式直接提交
            this.doSubmitForm()
          }
        }
      })
    },

    // 实际提交表单的方法
    async doSubmitForm() {
      try {
        let response

        if (this.isAddMode) {
          // 新增账套
          const addData = {
            name: this.accountForm.name,
            baseSalary: this.accountForm.baseSalary,
            performanceRate: this.accountForm.performanceRate,
            insuranceRate: this.accountForm.insuranceRate
          }
          response = await addAccountSet(addData)
        } else {
          // 更新账套
          const updateData = {
            name: this.accountForm.name,
            baseSalary: this.accountForm.baseSalary,
            performanceRate: this.accountForm.performanceRate,
            insuranceRate: this.accountForm.insuranceRate
          }
          response = await updateAccountSet(this.accountForm.id, updateData)
        }

        if (response.code === 1) {
          this.$message.success(this.isAddMode ? '新增账套成功' : '更新账套成功')
          this.dialogVisible = false
          this.getAccountList()
        } else {
          this.$message.error(response.msg || (this.isAddMode ? '新增账套失败' : '更新账套失败'))
        }
      } catch (error) {
        console.error('提交账套信息失败', error)
        this.$message.error('提交账套信息失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header-card {
  margin-bottom: 20px;
}

.content-card {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
  }
}

.filter-form {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  height: 70px;
}
</style>