package cn.edu.ncu.hrms.attendance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;

@Data
@ApiModel(description = "更新考勤记录数据传输对象")
public class AttendanceUpdateDTO {

    @ApiModelProperty(value = "签到时间", example = "08:58:00")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime clockInTime;

    @ApiModelProperty(value = "上班签到状态", allowableValues = "NORMAL,LATE,ABSENT,LEAVE", example = "NORMAL")
    private String clockInStatus;

    @ApiModelProperty(value = "签退时间", example = "18:02:00")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime clockOutTime;

    @ApiModelProperty(value = "下班签退状态", allowableValues = "NORMAL,EARLY,ABSENT,LEAVE", example = "NORMAL")
    private String clockOutStatus;

     @ApiModelProperty(value = "扣款金额", example = "10.00")
     private java.math.BigDecimal deduction;

} 