import request from '@/utils/request'

// 查询员工列表
export function listEmployee(query) {
  return request({
    url: '/system/employee/list',
    method: 'get',
    params: query
  })
}

// 分页查询员工(包含部门职位名称)
export function pageEmployees(query) {
  return request({
    url: '/system/employee/page',
    method: 'get',
    params: query
  })
}

// 获取未注册用户的员工列表
export function getUnregisteredEmployees() {
  return request({
    url: '/system/employee/unregistered',
    method: 'get'
  })
}

// 查询员工详细
export function getEmployee(id) {
  return request({
    url: '/employee/' + id,
    method: 'get'
  })
}

// 查询当前用户关联的员工信息
export function getCurrentEmployee() {
  return request({
    url: '/employee/current',
    method: 'get'
  })
}

// 查询下属员工列表
export function listSubordinates() {
  return request({
    url: '/employee/subordinates',
    method: 'get'
  })
}

// 新增员工
export function addEmployee(data) {
  return request({
    url: '/employee',
    method: 'post',
    data: data
  })
}

// 创建员工（系统管理员用）
export function createEmployee(data) {
  return request({
    url: '/system/employee',
    method: 'post',
    data: data
  })
}

// 根据部门ID查询指定角色员工
export function getEmployeesByDepartmentIdAndRole(departmentId, roleKey) {
  return request({
    url: `/system/employee/department/${departmentId}`,
    method: 'get',
    params: { roleKey }
  })
}

// 修改员工
export function updateEmployee(data) {
  return request({
    url: '/employee',
    method: 'put',
    data: data
  })
}

// 更新员工状态
export function updateEmployeeStatus(id, status) {
  return request({
    url: `/system/employee/status/${id}`,
    method: 'put',
    params: { status }
  })
}

// 更新员工信息（系统管理员用）
export function updateSystemEmployee(data) {
  return request({
    url: '/system/employee',
    method: 'put',
    data: data
  })
}

// 删除员工
export function deleteEmployee(id) {
  return request({
    url: '/employee/' + id,
    method: 'delete'
  })
}

// 导入员工数据
export function importEmployee(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/employee/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出员工数据
export function exportEmployee(query) {
  return request({
    url: '/employee/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
