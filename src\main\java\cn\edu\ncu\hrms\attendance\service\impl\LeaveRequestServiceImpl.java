package cn.edu.ncu.hrms.attendance.service.impl;

import cn.edu.ncu.hrms.attendance.dto.LeaveApplyDTO;
import cn.edu.ncu.hrms.attendance.dto.LeaveApproveDTO;
import cn.edu.ncu.hrms.attendance.dto.LeavePageQueryDTO;
import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import cn.edu.ncu.hrms.common.mapper.ApprovalProcessMapper;
import cn.edu.ncu.hrms.attendance.mapper.LeaveRequestMapper;
// import cn.edu.ncu.hrms.attendance.service.LeaveApprovalService; // Consider integrating for pending queries
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.service.ApprovalProcessService;
import cn.edu.ncu.hrms.attendance.service.LeaveRequestService;
import cn.edu.ncu.hrms.attendance.vo.LeaveRequestVO;
import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.entity.*;
import cn.edu.ncu.hrms.common.result.Result;
// import cn.edu.ncu.hrms.common.service.EmailService; // Removed unused import
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.vo.ResignRequestVO;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import cn.edu.ncu.hrms.auth.mapper.UserMapper;

import static cn.edu.ncu.hrms.common.constant.AttendanceConstant.*;
import static java.time.temporal.ChronoUnit.DAYS;

/**
 * 请假服务实现类。
 * 提供员工请假申请、审批、查询、撤回和删除等核心业务逻辑。
 */
@Slf4j
@Service
public class LeaveRequestServiceImpl implements LeaveRequestService {

    @Autowired
    private LeaveRequestMapper leaveRequestMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;


    @Autowired
    private SecurityUtil securityUtil;

    @Autowired
    private ApprovalProcessMapper approvalProcessMapper;

    @Autowired
    private ApprovalProcessService approvalProcessService;
    

    /**
     * 员工提交请假申请。
     *
     * @param applyDTO 包含请假类型、时间、原因等信息的 DTO。
     * @return 包含操作结果的 Result 对象。
     */
    @Override
    @Transactional
    public Result<Void> apply(LeaveApplyDTO applyDTO) {
        log.debug("开始处理请假申请: {}", applyDTO);

        // 步骤 1: 获取当前申请人的员工 ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("请假申请失败：无法获取当前用户的员工信息");
            return Result.error("无法获取当前用户信息，请重新登录");
        }
        log.debug("申请人 Employee ID: {}", currentEmployeeId);

        // 步骤 2: 校验请假时间
        // 获取当前请假的开始和结束时间点
        LocalDateTime newStartDT = toDateTime(applyDTO.getStartDate(), applyDTO.getStartPeriod());
        LocalDateTime newEndDT = toDateTime(applyDTO.getEndDate(), applyDTO.getEndPeriod());
        // 查询该员工的所有请假记录
        List<LeaveRequest> existingRequests = leaveRequestMapper.selectByEmployeeId(currentEmployeeId);
        // 检查是否存在时间冲突的请假记录
        for (LeaveRequest existing : existingRequests) {
            // 过滤出 PENDING 或 APPROVED 的请假记录
            if (!STATUS_PENDING.equals(existing.getStatus()) && !STATUS_APPROVED.equals(existing.getStatus())) {
                continue;
            }
            LocalDateTime existStartDT = toDateTime(existing.getStartDate(), existing.getStartPeriod());
            LocalDateTime existEndDT = toDateTime(existing.getEndDate(), existing.getEndPeriod());
            // 判断两个时间段是否有交集
            boolean overlap = !(newEndDT.isBefore(existStartDT) || newStartDT.isAfter(existEndDT));
            if (overlap) {
                log.warn("请假申请失败：请假时间段与其他请假申请冲突");
                return Result.error("请假时间段与待审批或已批准的请假申请冲突，请调整请假时间");
            }
        }

        // 步骤 3: 构建 LeaveRequest 实体
        LeaveRequest leaveRequest = new LeaveRequest();
        BeanUtils.copyProperties(applyDTO, leaveRequest);
        leaveRequest.setEmployeeId(currentEmployeeId);
        leaveRequest.setStatus(STATUS_PENDING);

        // 步骤 4: 保存请假申请到数据库
        int rows = leaveRequestMapper.insert(leaveRequest);
        if (rows > 0) {
            log.debug("请假申请提交成功, Employee ID: {}, Request ID: {}", currentEmployeeId, leaveRequest.getId());
            // 步骤 5: 初始化审批流程节点
            List<ApprovalProcess> approvalProcesses = initLeaveApprovalProcess(leaveRequest.getId(), currentEmployeeId);
            if (approvalProcesses == null || approvalProcesses.isEmpty()) {
                log.error("请假审批流程初始化失败：无对应审批人");
                return Result.error("未配置审批人，请联系管理员");
            }
            int count = approvalProcessMapper.batchInsert(approvalProcesses);
            if (count != approvalProcesses.size()) {
                log.error("请假审批流程初始化失败：批量插入审批流程节点失败");
                return Result.error("审批流程初始化失败，请稍后重试");
            }
            // 发送审批通知消息给第一个审批人
            approvalProcessService.sendApprovalNotification(approvalProcesses.get(0).getApproverId(), leaveRequest.getId(), REQUEST_TYPE_LEAVE);
            return Result.success();
        } else {
            log.error("请假申请提交失败：数据库插入操作未成功, Employee ID: {}", currentEmployeeId);
            return Result.error("提交失败，请稍后重试");
        }
    }

    private LocalDateTime toDateTime(LocalDate date, String period) {
        if ("AM".equals(period)) {
            return date.atStartOfDay(); // 上午 → 00:00
        } else {
            return date.atTime(12, 0);  // 下午 → 12:00
        }
    }


    private List<ApprovalProcess> initLeaveApprovalProcess(Integer id, Integer employeeId) {
        // 请假需要两级审批：+1，HR，如果没有上级领导，则只需要HR审批
        List<ApprovalProcess> processList = new LinkedList<>();

        Employee employee = employeeMapper.selectById(employeeId);
        Integer departmentId = employee.getDepartmentId();
        if (departmentId != null) {
            Department department = departmentMapper.selectById(departmentId);
            Integer leaderId = department.getLeaderId();
            Integer firstApproverId = getFirstApproverId(employeeId, leaderId, department);
            if (firstApproverId != null) {
                addApprovalProcess(processList, id, firstApproverId, (byte) 1, STATUS_PENDING);
            }
        }
        addHRBPApprovalProcess(processList, id, employeeId);

        return processList;
    }

    private Integer getFirstApproverId(Integer employeeId, Integer leaderId, Department department) {
        if (employeeId.equals(leaderId)) {
            // 若员工是本部门领导，则一级审批人是上级部门领导
            Department parentDepartment = getParentDepartment(department.getParentId());
            return parentDepartment != null ? parentDepartment.getLeaderId() : null;
        }
        // 否则一级审批人是本部门领导
        return leaderId;
    }

    private Department getParentDepartment(Integer parentDeptId) {
        return parentDeptId != null ? departmentMapper.selectById(parentDeptId) : null;
    }

    private void addApprovalProcess(List<ApprovalProcess> processList, Integer requestId, Integer approverId, byte stepOrder, String status) {
        ApprovalProcess process = new ApprovalProcess();
        process.setRequestId(requestId);
        process.setApproverId(approverId);
        process.setStepOrder(stepOrder);
        process.setType(REQUEST_TYPE_LEAVE);
        process.setStatus(status);
        if (status.equals(STATUS_PENDING)) {
            process.setExpireTime(LocalDateTime.now().plus(1, DAYS));
        }
        processList.add(process);
    }

    private void addHRBPApprovalProcess(List<ApprovalProcess> processList, Integer requestId, Integer employeeId) {
        Integer hrbpId = employeeMapper.getHRBPId(employeeId);
        if (hrbpId == null) {
            log.error("员工 {} 未配置 HRBP", employeeId);
        } else {
            byte stepOrder = (byte) (processList.size() + 1);
            String status = processList.isEmpty() ? STATUS_PENDING : STATUS_NEW;
            addApprovalProcess(processList, requestId, hrbpId, stepOrder, status);
        }
    }


    /**
     * 审批请假申请。
     *
     * @param requestId  请假申请的 ID。
     * @param approveDTO 包含审批结果（同意/拒绝）和审批备注的 DTO。
     * @return 包含操作结果的 Result 对象。
     */
    @Override
    @Transactional
    public Result<Void> approve(Integer requestId, LeaveApproveDTO approveDTO) {
        log.debug("开始审批请假申请, Request ID: {}, 审批信息: {}", requestId, approveDTO);
        if (requestId == null) {
            log.warn("审批请假申请失败：申请 ID 为空");
            return Result.error("申请ID不能为空");
        }

        // 步骤 1: 获取当前审批人员工信息
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("审批请假申请失败：无法获取当前审批人信息");
            return Result.error("获取登录用户信息失败，请重新登录");
        }
        log.debug("审批人 Employee ID: {}", currentEmployeeId);

        // 步骤 2: 校验审批状态值是否合法 (必须是 'approved' 或 'rejected')
        String status = approveDTO.getStatus();
        if (!STATUS_APPROVED.equals(status) && !STATUS_REJECTED.equals(status)) {
            log.warn("审批请假申请失败：无效的审批状态 '{}'", status);
            return Result.error("无效的审批操作");
        }

        // 步骤 3: 查询有效的请假申请记录
        LeaveRequest leaveRequest = leaveRequestMapper.selectById(requestId);
        if (leaveRequest == null || leaveRequest.getDelFlag()) {
            log.warn("审批请假申请失败：申请不存在或已被删除, ID: {}", requestId);
            return Result.error("请假申请不存在或已被删除");
        }

        // 步骤 4: 检查申请当前状态是否为待审批 (防止重复处理)
        if (!STATUS_PENDING.equals(leaveRequest.getStatus())) {
             log.warn("审批请假申请失败：申请状态非待审批, 当前状态: '{}'", leaveRequest.getStatus());
            return Result.error("该申请已处理，请勿重复操作");
        }

        // 步骤 5: 获取请假申请的当前审批节点ID
        Integer processId = approvalProcessMapper.selectCurrentPending(requestId, REQUEST_TYPE_LEAVE);
        log.debug("请假申请ID: {}  当前审批节点 ID: {}", requestId, processId);

        // 步骤 6: 进行节点审批操作，并处理后续流程
        try {
            approvalProcessService.approve(processId, currentEmployeeId, status, approveDTO.getComment(), REQUEST_TYPE_LEAVE);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }

        return Result.success();
    }

    /**
     * 根据 ID 查询请假申请的详细信息，并转换为 VO 对象。
     * 注意：权限控制建议在 Controller 层使用 @PreAuthorize 或进行显式检查。
     *
     * @param id 请假申请的 ID。
     * @return 包含详细信息的 VO 对象或错误结果。
     */
    @Override
    public Result<LeaveRequestVO> getVOById(Integer id) {
        log.debug("查询请假申请详情 (VO), ID: {}", id);
        if (id == null) {
            return Result.error("ID 不能为空");
        }

        LeaveRequest leaveRequest = leaveRequestMapper.selectById(id);
        if (leaveRequest == null || leaveRequest.getDelFlag()) {
            log.debug("查询请假申请详情失败：申请不存在或已被删除, ID: {}", id);
            return Result.error("请假申请不存在或已被删除");
        }

        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("查询离职申请详情失败：无法获取当前用户信息");
            return Result.error("无法获取当前用户信息，请检查登录状态");
        }

        LeaveRequestVO vo = convertToLeaveRequestVO(leaveRequest, currentEmployeeId);
        log.debug("查询请假申请详情成功, ID: {}", id);
        return Result.success(vo);
    }

    /**
     * 分页查询请假申请列表，并转换为 VO 对象。
     * 支持按多种条件查询，并通过 @PreAuthorize 进行权限控制。
     *
     * @param queryDTO 包含查询条件（如员工ID、状态、时间范围）和分页参数（页码、每页数量）的 DTO。
     *                 特殊查询标志：queryAppliedByMe (查询我申请的), queryPendingForMe (查询待我审批的)。
     * @return 分页后的请假申请 VO 列表 (PageInfo<LeaveRequestVO>)。
     */
    @Override
    public PageInfo<LeaveRequestVO> pageVOs(LeavePageQueryDTO queryDTO) {
        log.debug("分页查询请假申请, 条件: {}", queryDTO);

        // 获取当前登录用户的员工ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("分页查询请假申请失败：无法获取当前用户信息");
            return null;
        }
        queryDTO.setCurrentEmployeeId(currentEmployeeId);

        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<LeaveRequest> list = leaveRequestMapper.selectByCondition(queryDTO);
        PageInfo<LeaveRequest> pageInfoSrc = new PageInfo<>(list);

        List<LeaveRequestVO> voList = list.stream()
                .map(request -> convertToLeaveRequestVO(request, currentEmployeeId))
                .collect(Collectors.toList());

        PageInfo<LeaveRequestVO> pageInfoVO = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfoSrc, pageInfoVO, "list");

        return pageInfoVO;
    }

    /**
     * 员工撤回自己提交的、且处于 "待审批" 状态的请假申请。
     *
     * @param requestId 请假申请的 ID。
     * @return 包含操作结果的 Result 对象。
     */
    @Override
    @Transactional
    public Result<Void> cancel(Integer requestId) {
        log.debug("开始撤回请假申请, Request ID: {}", requestId);
        if (requestId == null) {
            log.warn("撤回请假申请失败：申请 ID 为空");
            return Result.error("申请ID不能为空");
        }

        // 步骤 1: 获取当前操作用户的员工 ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("撤回请假申请失败：无法获取当前用户信息");
            return Result.error("无法获取当前用户信息");
        }
        log.debug("Operator Employee ID: {}", currentEmployeeId);

        // 步骤 2: 查询有效的请假申请记录
        LeaveRequest leaveRequest = leaveRequestMapper.selectById(requestId);
        if (leaveRequest == null || leaveRequest.getDelFlag()) {
            log.warn("撤回请假申请失败：申请不存在或已被删除, ID: {}", requestId);
            return Result.error("请假申请不存在或已被删除");
        }

        // 步骤 3: 校验是否为申请人本人操作
        if (!currentEmployeeId.equals(leaveRequest.getEmployeeId())) {
            log.warn("撤回请假申请失败：非申请人操作, Request ID: {}, Applicant Employee ID: {}, Operator Employee ID: {}",
                     requestId, leaveRequest.getEmployeeId(), currentEmployeeId);
            return Result.error("只能撤回自己的请假申请");
        }

        // 步骤 4: 检查申请状态是否允许撤回 (仅 PENDING 状态)
        if (!STATUS_PENDING.equals(leaveRequest.getStatus())) {
            log.warn("撤回请假申请失败：当前状态 '{}' 不允许撤回, Request ID: {}", leaveRequest.getStatus(), requestId);
            return Result.error("只有待审批状态的申请才能撤回");
        }

        // 步骤 5: 更新状态为已撤回
        int rows = leaveRequestMapper.updateStatus(requestId, STATUS_CANCELED);
        if (rows > 0) {
            Boolean success = approvalProcessService.cancelApprovalProcessByRequestId(requestId, REQUEST_TYPE_LEAVE);
            if (!success) {
                log.error("请假申请 {} 状态设置为已撤回，但审批流程状态设置失败", requestId);
                return Result.error("撤回失败，请稍后重试");
            }
            log.debug("请假撤回成功, Request ID: {}", requestId);
            return Result.success();
        } else {
            log.error("撤回请假申请失败：数据库更新操作未成功, Request ID: {}", requestId);
            return Result.error("撤回失败，请稍后重试");
        }
    }

    @Override
    public Result<PageResult<LeaveRequest>> getSelfLeavePage(PageQueryDTO queryDTO) {
        log.debug("分页查询个人请假申请, 条件: {}", queryDTO);
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("分页查询离职申请失败：无法获取当前用户信息");
            return Result.error("无法获取当前用户信息，请检查登录状态");
        }

        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<LeaveRequest> list =leaveRequestMapper.selectByEmployeeId(currentEmployeeId);
        PageInfo<LeaveRequest> pageInfo = new PageInfo<>(list);

        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }

    /**
     * 根据请假申请 ID 获取申请人的员工 ID。
     * 主要用于权限校验或关联查询。
     *
     * @param requestId 请假申请 ID。
     * @return 对应的员工 ID；如果申请不存在，则返回 null。
     */
    @Override
    public Integer getEmployeeIdByRequestId(Integer requestId) {
        if (requestId == null) {
            return null;
        }
        LeaveRequest req = leaveRequestMapper.selectById(requestId);
        Integer employeeId = (req != null) ? req.getEmployeeId() : null;
        log.debug("根据 Request ID {} 查询到 Employee ID: {}", requestId, employeeId);
        return employeeId;
    }

    // --- 私有辅助方法 ---

    /**
     * 将 LeaveRequest 实体转换为 LeaveRequestVO 视图对象。
     * 主要用于填充关联的员工姓名和部门名称。
     *
     * @param leaveRequest 请假申请实体对象。
     * @return 转换后的 VO 对象；如果输入为 null 则返回 null。
     */
    private LeaveRequestVO convertToLeaveRequestVO(LeaveRequest leaveRequest, Integer currentEmployeeId ) {
        if (leaveRequest == null) {
            return null;
        }
        LeaveRequestVO vo = new LeaveRequestVO();
        BeanUtils.copyProperties(leaveRequest, vo);

        // 填充关联信息
        if (leaveRequest.getEmployeeId() != null) {
            Employee employee = employeeMapper.selectById(leaveRequest.getEmployeeId());
            if (employee != null) {
                vo.setEmployeeId(employee.getId());
                vo.setEmployeeName(employee.getName());
                vo.setEmployeeWorkNo(employee.getWorkNo());
                vo.setDepartmentId(employee.getDepartmentId());
                if (employee.getDepartmentId() != null) {
                    Department department = departmentMapper.selectById(employee.getDepartmentId());
                    if (department != null) {
                        vo.setDepartmentName(department.getName());
                    }
                }
            }
        }

        // 填充状态名称
        vo.setStatusName(AttendanceConstant.getStatusName(leaveRequest.getStatus()));

        // 查询申请当前是否待我审批
        vo.setIsPendingByMe(approvalProcessMapper.selectIsPendingByMe(leaveRequest.getId(), REQUEST_TYPE_LEAVE, currentEmployeeId));

        return vo;
    }


} 