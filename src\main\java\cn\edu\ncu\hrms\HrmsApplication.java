package cn.edu.ncu.hrms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableScheduling // 开启定时任务
@EnableTransactionManagement // 开启事务
public class HrmsApplication {

    public static void main(String[] args) {
        // 启动Spring容器（加载application.properties配置）
        ConfigurableApplicationContext context = SpringApplication.run(HrmsApplication.class, args);
    }

}
