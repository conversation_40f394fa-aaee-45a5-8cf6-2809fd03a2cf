package cn.edu.ncu.hrms.employee.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 审批离职申请 DTO
 */
@Data
@ApiModel(value="ResignApproveDTO", description="审批离职申请数据")
public class ResignApproveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "审批操作", required = true, notes = "必须为 'APPROVED' 或 'REJECTED'")
    @NotBlank(message = "审批操作不能为空")
    // 可以添加 @Pattern 校验必须是 APPROVED 或 REJECTED
    private String status;

    @ApiModelProperty(value = "审批意见")
    @Size(max = 500, message = "审批意见长度不能超过500个字符")
    private String comment;
} 