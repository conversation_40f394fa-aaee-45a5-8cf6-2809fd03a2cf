<template>
  <div class="app-container">
    <div class="page-header">
      <h2>档案管理</h2>
      <div class="header-actions">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="self">个人档案</el-radio-button>
          <el-radio-button label="subordinates" v-if="hasSubordinates">员工档案</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 个人档案视图 -->
    <div v-if="viewMode === 'self'" class="profile-container">
      <div class="profile-header">
        <div class="profile-info">
          <h3 class="profile-title">个人档案信息</h3>
          <p class="profile-subtitle">{{ profile.name || '未设置姓名' }} | {{ profile.positionName || '未设置职位' }} | {{ 'L' + profile.level || '' }}</p>
        </div>
        <div class="profile-actions">
          <el-button
            type="primary"
            icon="el-icon-edit"
            v-if="canEditSelf"
            @click="handleEditProfile"
          >修改个人档案</el-button>
        </div>
      </div>

    <el-row :gutter="20" class="profile-content">
        <el-col :span="24">
          <el-card class="profile-card" shadow="hover">
            <div slot="header" class="card-header">
              <span><i class="el-icon-user"></i> 基本信息</span>
            </div>
            <el-row :gutter="20">
              <!-- 第一行：姓名、工号、性别 -->
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>姓名：</label>
                  <span>{{ profile.name || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>工号：</label>
                  <span>{{ profile.workNo || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>性别：</label>
                  <span>{{ profile.gender || '暂无' }}</span>
                </div>
              </el-col>

              <!-- 第二行：部门、职位、职级 -->
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>部门：</label>
                  <span class="department-path">{{ profile.departmentNames || profile.departmentName || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>职位：</label>
                  <span>{{ profile.positionName || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>职级：</label>
                  <span>{{ 'L' + profile.level || '暂无' }}</span>
                </div>
              </el-col>

              <!-- 第三行：入职日期、HRBP、HRBP工号 -->
              <el-col :xs="24" :sm="8" :md="8">
                <div class="profile-item">
                  <label>入职日期：</label>
                  <span>{{ profile.entryDate || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" v-if="profile.hrbpName">
                <div class="profile-item">
                  <label>HRBP：</label>
                  <span>{{ profile.hrbpName || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" v-if="profile.hrbpWorkNo">
                <div class="profile-item">
                  <label>HR工号：</label>
                  <span>{{ profile.hrbpWorkNo || '暂无' }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>


        <el-col :span="24" class="mt-20">
          <el-card class="profile-card" shadow="hover">
            <div slot="header" class="card-header">
              <span><i class="el-icon-phone"></i> 联系方式</span>
            </div>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="8">
                <div class="profile-item">
                  <label>联系电话：</label>
                  <span>{{ profile.phone || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="profile-item">
                  <label>邮箱：</label>
                  <span>{{ profile.email || '暂无' }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <el-col :span="24" class="mt-20">
          <el-card class="profile-card" shadow="hover">
            <div slot="header" class="card-header">
              <span><i class="el-icon-location"></i> 个人信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <div class="profile-item">
                  <label>身份证号：</label>
                  <span>{{ profile.idCard || '暂无' }}</span>
                </div>
              </el-col>
              <el-col :span="24" class="mt-10">
                <div class="profile-item">
                  <label>家庭住址：</label>
                  <span>{{ profile.address || '暂无' }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 员工档案视图 -->
    <div v-else-if="viewMode === 'subordinates'" class="subordinates-container">
      <el-card class="table-card" shadow="hover">
        <div slot="header" class="card-header">
          <h3>
            <i class="el-icon-s-custom"></i>
            <!-- 获取用户角色信息 -->
            <span v-if="$store.getters['userInfo/roles'].includes('ROLE_hr')">关联员工档案</span>
            <!-- 只有当用户没有HR角色但有Leader角色时才显示Leader标题 -->
            <span v-else-if="$store.getters['userInfo/roles'].includes('ROLE_leader')">部门员工档案</span>
            <span v-else>员工档案</span>

            <!-- 操作按钮 -->
            <div class="header-buttons">
              <el-button-group>
                <!-- 获取用户角色信息 -->
                <template v-if="$store.getters['userInfo/roles'].includes('ROLE_hr')">
                  <el-button size="medium" type="success" icon="el-icon-plus" @click="handleAddEmployee"
                  style="box-sizing: border-box;">新增员工</el-button>
                  <el-button size="medium" type="success" icon="el-icon-upload2" @click="handleImportEmployees"
                  style="background: linear-gradient(to right, #00BFA5, #009688); color: white;
                  box-sizing: border-box;" >批量导入</el-button>
                  <el-button size="medium" type="primary" icon="el-icon-download" @click="handleExportEmployees"
                  style="box-sizing: border-box;">导出员工</el-button>
                </template>
              </el-button-group>
            </div>
          </h3>
        </div>

        <div class="filter-section">
          <div class="filter-form">
            <el-form :inline="true" :model="filterForm" class="demo-form-inline">
              <el-form-item label="部门">
                <div class="el-cascader" style="width: 150px;">
                  <div class="el-input el-input--suffix">
                    <input
                      type="text"
                      readonly="readonly"
                      autocomplete="off"
                      placeholder="选择部门"
                      class="el-input__inner"
                      v-model="selectedDepartmentName"
                      @click="departmentPopoverVisible = !departmentPopoverVisible"
                    >
                    <span class="el-input__suffix">
                      <span class="el-input__suffix-inner">
                        <i
                          v-if="selectedDepartmentName"
                          class="el-icon-circle-close el-input__icon el-input__clear"
                          @click.stop="clearSelectedDepartment"
                        ></i>
                        <i
                          v-else
                          class="el-input__icon el-icon-arrow-down"
                          @click.stop="departmentPopoverVisible = !departmentPopoverVisible"
                        ></i>
                      </span>
                    </span>
                  </div>
                  <el-popover
                    placement="bottom-start"
                    width="200"
                    trigger="manual"
                    v-model="departmentPopoverVisible"
                    popper-class="department-popover"
                    :close-on-click-modal="true"
                  >
                    <div class="department-tree-container">
                      <el-tree
                        :data="departmentOptions"
                        :props="{ label: 'name', children: 'children' }"
                        node-key="id"
                        highlight-current
                        :default-expand-all="true"
                        :expand-on-click-node="false"
                        :current-node-key="filterForm.departmentId"
                        @node-click="handleDepartmentTreeSelect"
                      ></el-tree>
                    </div>
                  </el-popover>
                </div>
              </el-form-item>
              <el-form-item label="职位">
                <el-cascader
                  v-model="positionSelection"
                  :options="positionCascaderOptions"
                  :props="{
                    expandTrigger: 'click',
                    checkStrictly: true,
                    emitPath: false,
                    value: 'id',
                    label: 'label',
                    children: 'children'
                  }"
                  placeholder="选择职位"
                  clearable
                  filterable
                  style="width: 170px;"
                  @change="handlePositionCascaderChange"
                ></el-cascader>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="filterForm.status" placeholder="在职状态" clearable style="width: 105px;">
                  <el-option label="在职" :value="true" />
                  <el-option label="离职" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="入职日期">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 250px;"
                />
              </el-form-item>
              <!-- <el-form-item label="姓名">
                <el-input
                  v-model="nameQuery"
                  placeholder="输入姓名"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 130px;"
                  @keyup.enter.native="handleSearch"
                />
              </el-form-item> -->
              <el-form-item label="工号">
                <el-input
                  v-model="workNoQuery"
                  placeholder="输入工号"
                  clearable
                  style="width: 140px;"
                  @keyup.enter.native="handleSearch"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetFilter">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>


        <el-table
          v-loading="loading"
          :data="subordinatesList"
          border
          stripe
          highlight-current-row
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column label="工号" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.workNo">{{ scope.row.workNo }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" min-width="60" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.name">{{ scope.row.name }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="性别" min-width="40" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.gender">{{ scope.row.gender }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="部门" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="职位" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.positionName">
                {{ scope.row.positionName }}
                <el-tag size="mini" type="info" v-if="scope.row.level">L{{ scope.row.level }}</el-tag>
              </span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="联系电话" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.phone">{{ scope.row.phone }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="邮箱" min-width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.email">{{ scope.row.email }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="入职日期" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.entryDate">{{ scope.row.entryDate }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="60" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status ? 'success' : 'danger'">
                {{ scope.row.status ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="150" align="center">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  circle
                  @click="handleViewEmployee(scope.row)"
                  title="查看详情"
                ></el-button>
                <el-button
                  v-if="$store.getters['userInfo/roles'].includes('ROLE_hr')"
                  size="mini"
                  type="warning"
                  icon="el-icon-edit"
                  circle
                  @click="handleEditEmployee(scope.row)"
                  title="编辑员工"
                ></el-button>
                <el-button
                  v-if="$store.getters['userInfo/roles'].includes('ROLE_hr')"
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="handleDeleteEmployee(scope.row)"
                  title="删除员工"
                ></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </el-card>
    </div>

    <!-- 员工信息编辑对话框 - 使用v-if而不是v-show确保完全重新创建组件 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="isAddMode ? '550px' : '800px'"
      :close-on-click-modal="false"
      :before-close="handleDialogBeforeClose"
      :append-to-body="true"
      v-if="dialogVisible"
    >
      <el-form ref="employeeForm" :model="employeeForm" :rules="rules" label-width="80px" :class="{ 'add-employee-form': isAddMode }">
        <!-- 员工档案编辑表单 - 根据EmployeeAddDTO/EmployeeUpdateDTO的字段 -->
        <template v-if="dialogTitle.includes('查看')">
          <el-tabs type="border-card">
            <el-tab-pane label="基本信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="姓名">
                    <div class="info-text">{{ employeeForm.name || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别">
                    <div class="info-text">{{ employeeForm.gender || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="身份证号">
                    <div class="info-text">{{ employeeForm.idCard || '暂无' }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="联系信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系电话">
                    <div class="info-text">{{ employeeForm.phone || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱">
                    <div class="info-text">{{ employeeForm.email || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="家庭住址">
                    <div class="info-text">{{ employeeForm.address || '暂无' }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="工作信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="工号">
                    <div class="info-text">{{ employeeForm.workNo || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="入职日期">
                    <div class="info-text">{{ employeeForm.entryDate || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="部门">
                    <div class="info-text">{{ employeeForm.departmentNames || employeeForm.departmentName || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="职位">
                    <div class="info-text">{{ employeeForm.positionName || '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="职级">
                    <div class="info-text">{{ employeeForm.level ? 'L' + employeeForm.level : '暂无' }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="HRBP">
                    <div class="info-text">
                      {{ employeeForm.hrbpName ? (employeeForm.hrbpName + (employeeForm.hrbpWorkNo ? ` (${employeeForm.hrbpWorkNo})` : '')) : '暂无' }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </template>
        <template v-else-if="isAddMode">
          <!-- 新增员工表单 - 竖向排列，一项一行 -->
          <el-form-item label="姓名" prop="name">
            <el-input v-model="employeeForm.name" placeholder="请输入姓名" maxlength="50" />
          </el-form-item>

          <el-form-item label="入职日期" prop="entryDate">
            <el-date-picker
              v-model="employeeForm.entryDate"
              type="date"
              placeholder="选择入职日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>

          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="employeeForm.gender">
              <el-radio label="男">男</el-radio>
              <el-radio label="女">女</el-radio>
              <el-radio label="其他">其他</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="employeeForm.idCard" placeholder="请输入身份证号" maxlength="18" />
          </el-form-item>

          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="employeeForm.phone" placeholder="请输入联系电话" maxlength="11" />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="employeeForm.email" placeholder="请输入邮箱" maxlength="50" />
          </el-form-item>

          <el-form-item label="家庭住址" prop="address">
            <el-input v-model="employeeForm.address" placeholder="请输入家庭住址" maxlength="200" />
          </el-form-item>

          <el-form-item label="部门" prop="departmentId">
            <el-tree-select
              v-model="employeeForm.departmentId"
              :data="departmentOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              placeholder="请选择部门"
              check-strictly
              clearable
              style="width: 100%;"
            />
          </el-form-item>

          <el-form-item label="职位" prop="positionId" :rules="[{ required: true, message: '请选择职位', trigger: 'change' }]">
            <div class="position-selector">
              <el-select
                v-model="selectedPositionName"
                placeholder="请选择职位名称"
                clearable
                filterable
                style="width: 50%;"
                @change="handlePositionNameSelect"
              >
                <el-option
                  v-for="group in positionGroups"
                  :key="group.value"
                  :label="group.label"
                  :value="group.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="employeeForm.positionId"
                placeholder="请选择职级"
                clearable
                filterable
                style="width: 50%;"
                :disabled="!selectedPositionName"
                @change="handlePositionLevelSelect"
              >
                <el-option
                  v-for="level in currentPositionLevels"
                  :key="level.id"
                  :label="level.levelDisplay"
                  :value="level.id"
                ></el-option>
              </el-select>
            </div>
          </el-form-item>

          <el-form-item label="HRBP" prop="hrbpId">
            <el-popover
              placement="bottom"
              width="700"
              trigger="click"
              v-model="showHRBPSelector"
            >
              <div class="leader-select-container">
                <div class="department-tree">
                  <el-tree
                    :data="departmentOptions"
                    :props="{ label: 'name', children: 'children' }"
                    node-key="id"
                    highlight-current
                    @node-click="handleHRBPDepartmentSelect"
                    default-expand-all
                  ></el-tree>
                </div>
                <div class="employee-list">
                  <div class="employee-list-header">
                    <span>{{ selectedHRBPDepartmentName || '请选择部门' }}</span>
                    <div v-if="loadingHRBPEmployees" class="loading-indicator">
                      <i class="el-icon-loading"></i> 加载中...
                    </div>
                  </div>
                  <el-table
                    v-loading="loadingHRBPEmployees"
                    :data="hrbpEmployees"
                    height="300"
                    @row-click="handleHRBPSelect"
                    highlight-current-row
                    :row-class-name="tableRowClassName"
                    empty-text="该部门暂无HR"
                  >
                    <el-table-column prop="workNo" label="工号" width="120"></el-table-column>
                    <el-table-column prop="name" label="姓名"></el-table-column>
                  </el-table>
                </div>
              </div>
              <el-input
                slot="reference"
                v-model="hrbpDisplayValue"
                placeholder="请选择HRBP"
                readonly
                suffix-icon="el-icon-arrow-down"
              ></el-input>
            </el-popover>
          </el-form-item>

          <el-form-item label="在职状态" prop="status">
            <el-switch
              v-model="employeeForm.status"
              active-text="在职"
              inactive-text="离职"
              :active-value="true"
              :inactive-value="false"
            ></el-switch>
          </el-form-item>
        </template>
        <template v-else-if="dialogTitle === '编辑个人档案'">
          <!-- 编辑个人档案表单 - 不包含工作信息 -->
          <el-tabs type="border-card">
            <el-tab-pane label="基本信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="employeeForm.name" placeholder="请输入姓名" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别" prop="gender">
                    <el-radio-group v-model="employeeForm.gender">
                      <el-radio label="男">男</el-radio>
                      <el-radio label="女">女</el-radio>
                      <el-radio label="其他">其他</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="身份证号" prop="idCard">
                    <el-input v-model="employeeForm.idCard" placeholder="请输入身份证号" maxlength="18" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="联系信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="phone">
                    <el-input v-model="employeeForm.phone" placeholder="请输入联系电话" maxlength="11" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="employeeForm.email" placeholder="请输入邮箱" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="家庭住址" prop="address">
                    <el-input v-model="employeeForm.address" placeholder="请输入家庭住址" maxlength="200" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </template>
        <template v-else>
          <!-- 编辑员工表单 - 保持原来的样式 -->
          <el-tabs type="border-card">
            <el-tab-pane label="基本信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="employeeForm.name" placeholder="请输入姓名" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别" prop="gender">
                    <el-radio-group v-model="employeeForm.gender">
                      <el-radio label="男">男</el-radio>
                      <el-radio label="女">女</el-radio>
                      <el-radio label="其他">其他</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="身份证号" prop="idCard">
                    <el-input v-model="employeeForm.idCard" placeholder="请输入身份证号" maxlength="18" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="联系信息">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="phone">
                    <el-input v-model="employeeForm.phone" placeholder="请输入联系电话" maxlength="11" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="employeeForm.email" placeholder="请输入邮箱" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="家庭住址" prop="address">
                    <el-input v-model="employeeForm.address" placeholder="请输入家庭住址" maxlength="200" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="工作信息">
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="部门" prop="departmentId">
                    <el-tree-select
                      v-model="employeeForm.departmentId"
                      :data="departmentOptions"
                      :props="{ value: 'id', label: 'name', children: 'children' }"
                      value-key="id"
                      placeholder="请选择部门"
                      check-strictly
                      clearable
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="职位" prop="positionId" :rules="[{ required: true, message: '请选择职位', trigger: 'change' }]">
                    <div class="position-selector">
                      <el-select
                        v-model="selectedPositionName"
                        placeholder="请选择职位名称"
                        clearable
                        filterable
                        style="width: 50%;"
                        @change="handlePositionNameSelect"
                      >
                        <el-option
                          v-for="group in positionGroups"
                          :key="group.value"
                          :label="group.label"
                          :value="group.value"
                        ></el-option>
                      </el-select>
                      <el-select
                        v-model="employeeForm.positionId"
                        placeholder="请选择职级"
                        clearable
                        filterable
                        style="width: 50%;"
                        :disabled="!selectedPositionName"
                        @change="handlePositionLevelSelect"
                      >
                        <el-option
                          v-for="level in currentPositionLevels"
                          :key="level.id"
                          :label="level.levelDisplay"
                          :value="level.id"
                        ></el-option>
                      </el-select>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="HRBP" prop="hrbpId">
                    <el-popover
                      placement="bottom"
                      width="700"
                      trigger="click"
                      v-model="showHRBPSelector"
                    >
                      <div class="leader-select-container">
                        <div class="department-tree">
                          <el-tree
                            :data="departmentOptions"
                            :props="{ label: 'name', children: 'children' }"
                            node-key="id"
                            highlight-current
                            @node-click="handleHRBPDepartmentSelect"
                            default-expand-all
                          ></el-tree>
                        </div>
                        <div class="employee-list">
                          <div class="employee-list-header">
                            <span>{{ selectedHRBPDepartmentName || '请选择部门' }}</span>
                            <div v-if="loadingHRBPEmployees" class="loading-indicator">
                              <i class="el-icon-loading"></i> 加载中...
                            </div>
                          </div>
                          <el-table
                            v-loading="loadingHRBPEmployees"
                            :data="hrbpEmployees"
                            height="300"
                            @row-click="handleHRBPSelect"
                            highlight-current-row
                            :row-class-name="tableRowClassName"
                            empty-text="该部门暂无HR"
                          >
                            <el-table-column prop="workNo" label="工号" width="120"></el-table-column>
                            <el-table-column prop="name" label="姓名"></el-table-column>
                          </el-table>
                        </div>
                      </div>
                      <el-input
                        slot="reference"
                        v-model="hrbpDisplayValue"
                        placeholder="请选择HRBP"
                        readonly
                        suffix-icon="el-icon-arrow-down"
                      ></el-input>
                    </el-popover>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </template>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogCancel">{{ dialogTitle.includes('查看') ? '关 闭' : '取 消' }}</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          v-if="!dialogTitle.includes('查看')"
          :loading="submitLoading"
          :disabled="submitLoading"
        >确 定</el-button>
      </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      title="批量导入员工"
      :visible.sync="importDialogVisible"
      width="500px"
      :before-close="handleImportDialogClose"
      :append-to-body="true"
      v-if="importDialogVisible"
      :close-on-click-modal="!importInProgress || importError"
      :close-on-press-escape="!importInProgress || importError"
      :show-close="!importInProgress || importError"
      :destroy-on-close="false"
      custom-class="import-dialog"
    >
      <div class="import-dialog-content">
        <!-- 上传区域 - 仅在未开始导入且未完成导入时显示 -->
        <template v-if="!importInProgress && !importComplete">
          <el-upload
            class="upload-demo"
            drag
            action="/api/employee/import"
            :headers="uploadHeaders"
            :on-success="handleImportSuccess"
            :on-error="handleImportError"
            :before-upload="beforeImportUpload"
            :disabled="importInProgress"
            :with-credentials="true"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且不超过10MB</div>
          </el-upload>
          <div class="import-template">
            <span>没有模板？</span>
            <el-button type="text" @click="downloadTemplate">下载模板</el-button>
          </div>
        </template>

        <!-- 导入进度区域 - 在导入过程中或导入完成后显示 -->
        <div v-if="importInProgress || importComplete" class="import-progress-container">
          <div class="progress-header">
            <i :class="getProgressIcon()" :style="{ color: getProgressIconColor() }"></i>
            <span>{{ getProgressHeaderText() }}</span>
          </div>

          <el-progress
            :percentage="importProgress"
            :status="getProgressStatus()"
            :stroke-width="18"
            class="import-progress-bar"
          ></el-progress>

          <div class="progress-info">
            <div class="progress-message">{{ importProgressMessage }}</div>
            <div class="progress-stats">
              <span>总数: {{ importTotal }}</span>
              <span>成功: <span class="success-count">{{ importSuccess }}</span></span>
            </div>
          </div>

          <!-- 错误报告下载链接 - 兼容旧版本 -->
          <div v-if="errorReportUrl" class="error-report">
            <el-alert
              title="部分数据导入失败"
              type="warning"
              :closable="false"
              show-icon
            >
              <template slot="title">
                部分数据导入失败，
                <el-link
                  type="primary"
                  :href="'/api' + errorReportUrl"
                  target="_blank"
                >下载错误报告</el-link>
              </template>
            </el-alert>
          </div>

          <!-- 错误详情展示 - 新版本 -->
          <div v-if="rowErrorMap && Object.keys(rowErrorMap).length > 0" class="error-report">
            <div class="error-summary">
              <i class="el-icon-warning-outline"></i>
              <span>部分数据导入失败</span>
            </div>
            <div class="error-details">
              <div class="error-header">
                <span>以下行数据导入失败：</span>
                <el-button type="text" size="small" @click="expandAllErrors = !expandAllErrors">
                  {{ expandAllErrors ? '收起全部' : '展开全部' }}
                </el-button>
              </div>
              <el-collapse v-model="activeErrorRows" accordion>
                <el-collapse-item
                  v-for="(errors, rowNum) in rowErrorMap"
                  :key="rowNum"
                  :name="rowNum"
                >
                  <template slot="title">
                    <div class="error-row-title">
                      <span class="error-row-number">第 {{ rowNum }} 行</span>
                      <span class="error-count">{{ Array.isArray(errors) ? errors.length : 1 }}个错误</span>
                    </div>
                  </template>
                  <div class="error-content">
                    <ul class="error-list">
                      <template v-if="Array.isArray(errors)">
                        <li v-for="(error, index) in errors" :key="index">
                          <i class="el-icon-close"></i>
                          <span>{{ error }}</span>
                        </li>
                      </template>
                      <template v-else>
                        <li>
                          <i class="el-icon-close"></i>
                          <span>{{ errors }}</span>
                        </li>
                      </template>
                    </ul>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>

          <!-- 导入错误时显示取消按钮 -->
          <div v-if="importError" class="error-actions">
            <el-button type="primary" @click="closeImportDialog">取消导入</el-button>
          </div>
        </div>
      </div>

      <!-- 对话框底部按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeImportDialog" :disabled="importInProgress && !importError">取 消</el-button>
        <el-button type="primary" @click="closeImportDialog" v-if="importComplete || importError">
          {{ importError ? '关 闭' : '完 成' }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getMyProfile, updateMyProfile, getEmployee, listEmployeesByHr, listEmployeesByLeader, addEmployee, updateEmployee, deleteEmployee, downloadTemplate, exportEmployee } from '@/api/employee/profile'
import { listPosition } from '@/api/system/position'
import { getDepartmentTree, getChildDepartments } from '@/api/system/department'
import { getEmployeesByDepartmentIdAndRole } from '@/api/system/employee'
import { getToken } from '@/utils/auth'
import webSocketClient from '@/utils/websocket'

export default {
  name: 'EmployeeProfile',
  data() {
    return {
      // 视图模式：个人档案/下属档案
      viewMode: 'self',
      // 个人档案信息
      profile: {},
      // 部门名称
      departmentName: '',
      // 职位名称
      positionName: '',
      // 是否有下属
      hasSubordinates: false,
      // 是否为HR
      isHR: false,
      // 是否为领导
      isLeader: false,
      // 是否可以编辑自己的档案
      canEditSelf: false,
      // 下属列表
      subordinatesList: [],
      // 加载状态
      loading: false,
      // 姓名查询
      nameQuery: '',
      // 工号查询
      workNoQuery: '',
      // 过滤表单
      filterForm: {
        positionId: undefined,
        status: undefined,
        departmentId: undefined
      },
      // 部门选项
      departmentOptions: [],
      // 部门选择弹出框是否可见
      departmentPopoverVisible: false,
      // 选中的部门名称
      selectedDepartmentName: '',
      // 入职日期范围
      dateRange: [],
      // 分页参数
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      isAddMode: false,
      submitLoading: false,
      // 员工表单
      employeeForm: {
        id: undefined,
        name: '',
        workNo: '',
        gender: '男',
        departmentId: undefined,
        positionId: undefined,
        entryDate: '',
        phone: '',
        email: '',
        idCard: '',
        address: '',
        hrbpId: undefined,
        status: true
      },
      // 表单验证规则 - 根据EmployeeAddDTO
      rules: {
        name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' },
          { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' }
        ],
        address: [
          { max: 200, message: '地址长度不能超过200个字符', trigger: 'blur' }
        ],
        // 以下规则仅在编辑员工档案时使用，编辑个人档案时不需要验证
        departmentId: [
          { required: true, message: '部门不能为空', trigger: 'change', validator: (_rule, value, callback) => {
            // 如果是编辑个人档案，则跳过验证
            if (this.dialogTitle === '编辑个人档案') {
              callback()
            } else if (!value) {
              callback(new Error('部门不能为空'))
            } else {
              callback()
            }
          }}
        ],
        positionId: [
          { required: true, message: '职位不能为空', trigger: 'change', validator: (_rule, value, callback) => {
            // 如果是编辑个人档案，则跳过验证
            if (this.dialogTitle === '编辑个人档案') {
              callback()
            } else if (!value) {
              callback(new Error('职位不能为空'))
            } else {
              callback()
            }
          }}
        ],
        hrbpId: [
          { required: true, message: 'HRBP不能为空', trigger: 'change', validator: (_rule, value, callback) => {
            // 如果是编辑个人档案，则跳过验证
            if (this.dialogTitle === '编辑个人档案') {
              callback()
            } else if (!value) {
              callback(new Error('HRBP不能为空'))
            } else {
              callback()
            }
          }}
        ],
        entryDate: [
          { required: true, message: '入职日期不能为空', trigger: 'change', validator: (_rule, value, callback) => {
            // 如果是编辑个人档案，则跳过验证
            if (this.dialogTitle === '编辑个人档案') {
              callback()
            } else if (!value) {
              callback(new Error('入职日期不能为空'))
            } else {
              callback()
            }
          }}
        ]
      },
      // 部门选项已移除
      // 职位选项
      positionOptions: [],
      // 职位分组选项
      positionGroups: [],
      // 职位选择相关数据
      positionSelection: null, // 职位级联选择器的值
      positionCascaderOptions: [], // 职位级联选择器的选项
      selectedPositionValue: null, // 保存选择的职位值
      selectedPositionType: null, // 保存选择的职位类型（name或level）
      // HR选项
      hrOptions: [],
      // HRBP选择相关
      selectedHRBP: null,
      selectedHRBPDepartmentName: '',
      selectedHRBPDepartmentId: null,
      hrbpEmployees: [],
      loadingHRBPEmployees: false,
      showHRBPSelector: false,
      // 批量导入对话框
      importDialogVisible: false,
      // 删除确认对话框
      deleteDialogVisible: false,
      deleteEmployeeId: null,
      // 上传头信息
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      // 导入进度相关
      importInProgress: false,
      importComplete: false,
      importProgress: 0,
      importProgressMessage: '',
      importTotal: 0,
      importSuccess: 0,
      errorReportUrl: null,
      importStartTime: null,
      importTimeout: null,
      importError: false,
      rowErrorMap: null, // 行号 -> 错误列表的映射
      expandAllErrors: false, // 是否展开所有错误
      activeErrorRows: [], // 当前展开的错误行
      // WebSocket相关
      wsProgressListener: null,
      wsResultListener: null
    }
  },
  created() {
    this.getUserRole()

    // 检查URL查询参数中是否有viewMode
    const viewModeParam = this.$route.query.viewMode
    if (viewModeParam && (viewModeParam === 'self' || viewModeParam === 'subordinates')) {
      // 如果URL中有viewMode参数，则使用该参数
      this.viewMode = viewModeParam
      console.log('从URL查询参数设置视图模式:', this.viewMode)
    }

    this.initData()
    // 连接WebSocket
    this.connectWebSocket()
  },
  beforeDestroy() {
    // 移除WebSocket监听器
    this.removeWebSocketListeners()
  },
  methods: {
    // 获取用户角色信息 - 只从Vuex中获取，不调用API
    getUserRole() {
      // 从Vuex中获取用户角色信息
      const roles = this.$store.getters['userInfo/roles'] || []

      // 判断用户角色
      this.isHR = roles.includes('ROLE_hr')
      // 如果同时拥有HR和Leader角色，则只显示HR角色的内容
      this.isLeader = roles.includes('ROLE_leader') && !this.isHR
      this.hasSubordinates = this.isHR || this.isLeader
      this.canEditSelf = true // 所有用户都可以编辑自己的基本信息

      console.log('从Vuex获取用户角色信息:', {
        roles: roles,
        isHR: this.isHR,
        isLeader: this.isLeader,
        hasSubordinates: this.hasSubordinates,
        rolesPriority: this.isHR ? 'ROLE_hr优先' : (this.isLeader ? 'ROLE_leader优先' : '普通员工')
      })

      // 如果用户有下属，默认显示下属档案视图
      if (this.hasSubordinates) {
        this.viewMode = 'subordinates'
      }
    },

    // 初始化数据 - 确保在页面加载时调用一次默认接口
    async initData() {
      try {
        this.loading = true
        console.log('开始初始化数据')

        // 1. 加载个人档案信息（无论什么视图都需要）
        await this.getProfileInfo()

        // 2. 如果是下属视图，加载下属数据
        if (this.hasSubordinates && this.viewMode === 'subordinates') {
          console.log('当前是下属视图，加载下属数据')

          // 使用loadSubordinatesData方法统一加载下属数据
          await this.loadSubordinatesData()
        } else {
          console.log('当前是个人视图，不加载下属数据')
        }
      } catch (error) {
        console.error('初始化数据失败', error)
        this.$message.error('加载数据失败，请刷新页面重试')
      } finally {
        this.loading = false
      }
    },
    // 获取个人档案信息 - 确保在页面加载时调用一次默认接口
    async getProfileInfo() {
      try {
        this.loading = true
        console.log('获取个人档案信息')
        const response = await getMyProfile()
        if (response.code === 1 && response.data) {
          this.profile = response.data

          // 直接使用后端返回的部门名称和职位名称
          this.departmentName = this.profile.departmentName || '未知部门'
          this.positionName = this.profile.positionName || '未知职位'

          console.log('成功获取个人档案信息')
        } else {
          console.warn('获取个人档案信息失败', response)
          this.$message.error(response.msg || '获取个人档案信息失败')
        }
      } catch (error) {
        console.error('获取个人档案信息失败', error)
        this.$message.error('获取个人档案信息失败')
      } finally {
        this.loading = false
      }
    },
    // 获取职位名称
    getPositionName(positionId) {
      if (!positionId) {
        this.positionName = '未知职位'
        return
      }

      const position = this.positionOptions.find(item => item.id === positionId)
      this.positionName = position ? position.name : '未知职位'
    },
    // 获取职位选项
    async getPositionOptions() {
      try {
        // 直接使用职位列表接口，确保获取职位ID和名称
        const response = await listPosition()
        if (response.code === 1 && response.data) {
          // 保存原始职位数据
          this.positionOptions = response.data

          // 按职位名称分组
          const groups = {}
          response.data.forEach(position => {
            if (!groups[position.name]) {
              groups[position.name] = []
            }
            groups[position.name].push({
              id: position.id,
              name: position.name,
              level: position.level,
              levelDisplay: `L${position.level}`, // 格式化显示职级
              accountId: position.accountId
            })
          })

          // 转换为下拉选择需要的格式
          this.positionGroups = Object.keys(groups).map(name => ({
            label: name,
            value: name,
            name: name, // 添加name属性
            children: groups[name].map(item => ({
              ...item,
              label: item.levelDisplay // 确保子项有label属性
            }))
          }))

          // 构建级联选择器的选项
          this.positionCascaderOptions = []

          // 构建职位+职级的二级菜单
          Object.keys(groups).forEach(name => {
            const positionGroup = {
              id: name, // 使用职位名称作为id
              label: name,
              type: 'name', // 标记为职位名称类型
              children: []
            }

            // 添加该职位下的所有职级
            groups[name].forEach(position => {
              positionGroup.children.push({
                id: position.id, // 使用职位ID
                label: `L${position.level}`, // 显示职级
                type: 'level', // 标记为职级类型
                positionName: name // 保存职位名称，方便后续使用
              })
            })

            this.positionCascaderOptions.push(positionGroup)
          })

          console.log('获取职位选项成功，级联选择器选项:', JSON.stringify(this.positionCascaderOptions, null, 2))
        } else {
          console.warn('获取职位选项失败', response)
          this.positionOptions = []
          this.positionGroups = []
          this.positionCascaderOptions = []
        }
      } catch (error) {
        console.error('获取职位选项失败', error)
        this.positionOptions = []
        this.positionGroups = []
        this.positionCascaderOptions = []
      }
    },

    // 处理职位名称选择
    handlePositionNameSelect(positionName) {
      this.selectedPositionName = positionName
      const currentPositionId = this.employeeForm.positionId // 保存当前选择的职位ID

      // 根据选择的职位名称筛选对应的职级选项
      if (positionName) {
        const group = this.positionGroups.find(g => g.value === positionName)
        if (group) {
          this.currentPositionLevels = group.children
          console.log('职位名称对应的职级选项:', this.currentPositionLevels)

          // 检查当前选择的职位ID是否在新的职级选项中
          const positionStillValid = this.currentPositionLevels.some(p => p.id === currentPositionId)

          if (!positionStillValid) {
            // 如果当前选择的职位ID不在新的职级选项中，清除选择
            this.employeeForm.positionId = null

            // 如果只有一个职级选项，自动选择
            if (this.currentPositionLevels.length === 1) {
              this.employeeForm.positionId = this.currentPositionLevels[0].id
              console.log('自动选择唯一职级:', this.employeeForm.positionId)
            }
          } else {
            // 如果当前选择的职位ID在新的职级选项中，保持选择
            console.log('保持当前职级选择:', currentPositionId)
          }
        } else {
          this.currentPositionLevels = []
          this.employeeForm.positionId = null
        }
      } else {
        this.currentPositionLevels = []
        this.employeeForm.positionId = null
      }

      // 如果表单已经验证过，重新验证
      if (this.$refs.employeeForm) {
        this.$refs.employeeForm.validateField('positionId')
      }
    },

    // 处理职级选择
    handlePositionLevelSelect(positionId) {
      this.employeeForm.positionId = positionId
      console.log('选择职级:', positionId)
    },

    // 获取HR选项 - 不再使用，避免重复调用API
    async getHROptions() {
      console.log('HR选项功能已禁用，避免重复调用API')
      this.hrOptions = []
    },
    // 获取下属员工列表 - 只调用一次默认接口
    async getSubordinatesList() {
      try {
        this.loading = true

        // 构建查询参数 - 使用默认参数，固定pageSize=10
        const queryParams = {
          pageNum: this.currentPage,
          pageSize: 10, // 固定为10，避免使用100
          name: this.nameQuery || undefined,
          workNo: this.workNoQuery || undefined,
          departmentId: this.filterForm.departmentId, // 部门ID
          status: this.filterForm.status === true ? 1 : (this.filterForm.status === false ? 0 : undefined),
          entryStartDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : undefined,
          entryEndDate: this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : undefined
        }

        // 根据职位选择添加不同的参数
        if (this.positionSelection) {
          // 使用保存的选择类型
          if (this.selectedPositionType === 'name') {
            // 如果选择的是职位名称，添加positionName参数
            queryParams.positionName = this.positionSelection
          } else {
            // 如果选择的是职级，添加positionId参数
            queryParams.positionId = this.positionSelection
          }
        } else if (this.filterForm.positionId) {
          // 兼容旧的选择方式
          queryParams.positionId = this.filterForm.positionId
        }

        // 移除undefined的参数
        Object.keys(queryParams).forEach(key => {
          if (queryParams[key] === undefined) {
            delete queryParams[key]
          }
        })

        console.log('查询参数:', queryParams)

        // 根据角色选择合适的API
        let response

        // 获取用户角色信息
        const roles = this.$store.getters['userInfo/roles'] || []
        const hasHRRole = roles.includes('ROLE_hr')
        const hasLeaderRole = roles.includes('ROLE_leader')

        // 优先使用HR接口 - 如果同时拥有HR和Leader角色，优先使用HR接口
        if (hasHRRole) {
          console.log('使用HR接口查询员工列表 (ROLE_hr优先)')
          response = await listEmployeesByHr(queryParams)
        }
        // 其次使用Leader接口
        else if (hasLeaderRole) {
          console.log('使用Leader接口查询员工列表')
          response = await listEmployeesByLeader(queryParams)
        }
        // 没有权限
        else {
          this.$message.error('您没有权限查看下属员工')
          this.subordinatesList = []
          this.total = 0
          return
        }

        // 处理响应结果
        if (response.code === 1 && response.data) {
          this.subordinatesList = response.data.records || []
          this.total = response.data.total || 0
          console.log(`成功获取${this.total}条员工记录`)
        } else {
          this.$message.error(response.msg || '获取下属员工列表失败')
          this.subordinatesList = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取下属员工列表失败', error)
        this.$message.error('获取下属员工列表失败')
        this.subordinatesList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    // 格式化性别
    formatGender(gender) {
      // 直接返回性别值，不需要转换
      return gender || '未知'
    },
    // 处理职位级联选择器变化
    handlePositionCascaderChange(value) {
      if (!value) {
        // 清空所有职位相关参数
        this.filterForm.positionId = undefined
        return
      }

      // 检查是否是职位ID（职级）
      let isPositionName = true;

      // 检查是否是职位ID（职级）
      for (const group of this.positionCascaderOptions) {
        if (group.children) {
          const levelOption = group.children.find(option => option.id === value)
          if (levelOption) {
            isPositionName = false;
            break;
          }
        }
      }

      // 保存选择的值，用于后续查询
      this.selectedPositionValue = value;
      this.selectedPositionType = isPositionName ? 'name' : 'level';

      if (isPositionName) {
        // 如果选择的是职位名称，清空职位ID
        this.filterForm.positionId = undefined
      } else {
        // 如果选择的是职级，设置职位ID
        this.filterForm.positionId = value
      }
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1
      this.getSubordinatesList()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSubordinatesList()
    },
    // 处理每页条数变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.getSubordinatesList()
    },
    // 处理编辑个人档案
    handleEditProfile() {
      // 先重置表单
      this.resetEmployeeForm()

      // 设置对话框属性
      this.dialogTitle = '编辑个人档案'
      this.isAddMode = false

      // 使用深拷贝设置表单数据
      if (this.profile) {
        // 只复制个人信息相关字段，不包括工作信息（部门、职位、HRBP）
        this.employeeForm = {
          id: this.profile.id,
          name: this.profile.name,
          gender: this.profile.gender,
          idCard: this.profile.idCard,
          phone: this.profile.phone,
          email: this.profile.email,
          address: this.profile.address,
          // 保留工作信息字段但不在表单中显示，提交时需要这些字段
          departmentId: this.profile.departmentId,
          positionId: this.profile.positionId,
          hrbpId: this.profile.hrbpId
        }
      }

      // 最后打开对话框
      this.dialogVisible = true
    },

    // 处理对话框关闭前的操作
    handleDialogBeforeClose(done) {
      // 清空表单数据
      if (this.$refs.employeeForm) {
        this.$refs.employeeForm.resetFields()
      }

      // 重置表单数据为默认值
      this.resetEmployeeForm()

      // 重置提交按钮状态
      this.submitLoading = false

      // 关闭对话框
      done()
    },

    // 处理对话框取消
    handleDialogCancel() {
      // 直接关闭对话框，beforeClose钩子会处理清理工作
      this.dialogVisible = false
    },
    // 处理查看员工
    async handleViewEmployee(row) {
      if (!row || !row.id) {
        this.$message.error('员工ID不能为空')
        return
      }

      try {
        this.loading = true

        // 先重置表单
        this.resetEmployeeForm()

        // 获取员工详细信息
        const response = await getEmployee(row.id)
        if (response.code === 1 && response.data) {
          // 设置对话框属性
          this.dialogTitle = '查看员工档案'
          this.isAddMode = false

          // 使用深拷贝设置表单数据
          this.employeeForm = JSON.parse(JSON.stringify(response.data))

          // 如果有职位ID，设置职位名称和职级
          if (this.employeeForm.positionId) {
            const position = this.positionOptions.find(p => p.id === this.employeeForm.positionId)
            if (position) {
              this.selectedPositionName = position.name
              this.handlePositionNameSelect(position.name)
            }
          }

          // 如果有HRBP ID，设置HRBP信息
          if (this.employeeForm.hrbpId && response.data.hrbpName) {
            this.selectedHRBP = {
              id: this.employeeForm.hrbpId,
              name: response.data.hrbpName,
              workNo: response.data.hrbpWorkNo || ''
            }
          }

          // 如果有部门ID，确保部门ID正确设置
          if (this.employeeForm.departmentId) {
            console.log('设置部门ID:', this.employeeForm.departmentId)
          }

          // 最后打开对话框
          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || '获取员工详情失败')
        }
      } catch (error) {
        console.error('获取员工详情失败', error)
        this.$message.error('获取员工详情失败')
      } finally {
        this.loading = false
      }
    },

    // 处理编辑员工
    async handleEditEmployee(row) {
      if (!row || !row.id) {
        this.$message.error('员工ID不能为空')
        return
      }

      try {
        this.loading = true

        // 先重置表单
        this.resetEmployeeForm()

        // 获取员工详细信息
        const response = await getEmployee(row.id)
        if (response.code === 1 && response.data) {
          // 设置对话框属性
          this.dialogTitle = '编辑员工档案'
          this.isAddMode = false

          // 使用深拷贝设置表单数据
          this.employeeForm = JSON.parse(JSON.stringify(response.data))

          // 如果有职位ID，设置职位名称和职级
          if (this.employeeForm.positionId) {
            const position = this.positionOptions.find(p => p.id === this.employeeForm.positionId)
            if (position) {
              this.selectedPositionName = position.name
              this.handlePositionNameSelect(position.name)
            }
          }

          // 如果有HRBP ID，设置HRBP信息
          if (this.employeeForm.hrbpId && response.data.hrbpName) {
            this.selectedHRBP = {
              id: this.employeeForm.hrbpId,
              name: response.data.hrbpName,
              workNo: response.data.hrbpWorkNo || ''
            }
          }

          // 如果有部门ID，确保部门ID正确设置
          if (this.employeeForm.departmentId) {
            console.log('设置部门ID:', this.employeeForm.departmentId)
          }

          // 最后打开对话框
          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || '获取员工详情失败')
        }
      } catch (error) {
        console.error('获取员工详情失败', error)
        this.$message.error('获取员工详情失败')
      } finally {
        this.loading = false
      }
    },
    // 处理添加员工
    handleAddEmployee() {
      // 先重置表单
      this.resetEmployeeForm()

      // 清除选中的HRBP
      this.selectedHRBP = null

      // 设置对话框属性
      this.dialogTitle = '新增员工'
      this.isAddMode = true

      // 最后打开对话框
      this.dialogVisible = true
    },

    // 重置员工表单
    resetEmployeeForm() {
      this.employeeForm = {
        id: undefined,
        name: '',
        workNo: '',
        gender: '男',
        departmentId: undefined,
        positionId: undefined,
        entryDate: new Date(),
        phone: '',
        email: '',
        idCard: '',
        address: '',
        hrbpId: undefined,
        status: true
      }

      // 重置职位相关数据
      this.selectedPositionName = ''
      this.currentPositionLevels = []

      // 重置HRBP相关数据
      this.selectedHRBP = null
      this.selectedHRBPDepartmentId = null
      this.selectedHRBPDepartmentName = ''
    },
    // 提交表单
    submitForm() {
      // 先禁用提交按钮，防止重复提交
      this.submitLoading = true

      this.$refs.employeeForm.validate(async valid => {
        if (valid) {
          try {
            if (this.isAddMode) {
              // 新增员工 - 适配EmployeeAddDTO
              const formData = {
                name: this.employeeForm.name,
                entryDate: this.employeeForm.entryDate,
                gender: this.employeeForm.gender,
                idCard: this.employeeForm.idCard || null,
                phone: this.employeeForm.phone || null,
                email: this.employeeForm.email || null,
                address: this.employeeForm.address || null,
                departmentId: this.employeeForm.departmentId,
                positionId: this.employeeForm.positionId,
                hrbpId: this.employeeForm.hrbpId,
                status: this.employeeForm.status
              }

              console.log('提交新增员工数据:', formData)

              // 使用/employee接口添加员工
              const response = await addEmployee(formData)
              if (response.code === 1) {
                this.$message.success('新增员工成功')
                // 先关闭对话框，再刷新数据
                this.dialogVisible = false
                // 清除选中的部门和HRBP
                this.selectedDepartmentName = ''
                this.selectedHRBP = null
                // 延迟执行，确保对话框已完全关闭
                setTimeout(() => {
                  this.getSubordinatesList()
                }, 100)
              } else {
                this.$message.error(response.msg || '新增员工失败')
              }
            } else {
              // 更新员工 - 提交EmployeeUpdateDTO中的字段
              let updateData = {}

              // 根据对话框标题判断是个人档案编辑还是员工档案编辑
              if (this.dialogTitle === '编辑个人档案') {
                // 个人档案编辑 - 只更新个人信息，保留工作信息不变
                updateData = {
                  id: this.employeeForm.id,
                  name: this.employeeForm.name,
                  gender: this.employeeForm.gender,
                  idCard: this.employeeForm.idCard,
                  phone: this.employeeForm.phone,
                  email: this.employeeForm.email,
                  address: this.employeeForm.address,
                  // 保留原有的工作信息字段，不允许修改
                  departmentId: this.employeeForm.departmentId,
                  positionId: this.employeeForm.positionId,
                  hrbpId: this.employeeForm.hrbpId
                }
              } else {
                // 员工档案编辑 - 可以更新所有字段
                updateData = {
                  id: this.employeeForm.id,
                  name: this.employeeForm.name,
                  gender: this.employeeForm.gender,
                  idCard: this.employeeForm.idCard,
                  phone: this.employeeForm.phone,
                  email: this.employeeForm.email,
                  address: this.employeeForm.address,
                  departmentId: this.employeeForm.departmentId,
                  positionId: this.employeeForm.positionId,
                  hrbpId: this.employeeForm.hrbpId
                }
              }

              console.log('提交更新数据:', updateData)

              let response
              if (this.viewMode === 'self') {
                // 更新个人档案
                response = await updateMyProfile(updateData)
              } else {
                // 更新下属员工
                response = await updateEmployee(updateData)
              }

              if (response.code === 1) {
                this.$message.success('更新员工成功')
                // 先关闭对话框，再刷新数据
                this.dialogVisible = false
                // 延迟执行，确保对话框已完全关闭
                setTimeout(() => {
                  if (this.viewMode === 'self') {
                    this.getProfileInfo()
                  } else {
                    this.getSubordinatesList()
                  }
                }, 100)
              } else {
                this.$message.error(response.msg || '更新员工失败')
              }
            }
          } catch (error) {
            console.error('提交员工信息失败', error)
            this.$message.error('提交员工信息失败')
          } finally {
            // 无论成功失败，都要恢复提交按钮状态
            this.submitLoading = false
          }
        } else {
          this.submitLoading = false
        }
      })
    },
    // 处理批量导入
    handleImportEmployees() {
      this.importDialogVisible = true
    },
    // 上传前检查
    beforeImportUpload(file) {
      // 检查文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel' ||
                      file.name.endsWith('.xlsx') ||
                      file.name.endsWith('.xls')

      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }

      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }

      // 刷新token信息
      const token = getToken()
      if (token) {
        // 更新上传头信息中的token
        this.uploadHeaders = {
          Authorization: 'Bearer ' + token
        }
        console.log('上传前刷新token信息成功')
      } else {
        console.warn('上传前无法获取token')
      }

      return true
    },
    // 导入成功处理
    handleImportSuccess(response) {
      console.log('文件上传成功:', response)
      if (response.code === 1) {
        this.$message.success(response.msg || '文件上传成功，开始处理导入')
        this.importInProgress = true
        this.importComplete = false // 确保完成标志被重置
        this.importProgress = 0
        this.importError = false
        this.rowErrorMap = null // 确保错误映射被重置
        this.importProgressMessage = '正在准备导入...'

        // 记录开始时间
        this.importStartTime = new Date().getTime()

        // 设置超时检测 - 2分钟后如果还没有收到进度更新，显示超时提示
        this.importTimeout = setTimeout(() => {
          if (this.importInProgress && this.importProgress === 0) {
            this.importError = true
            this.importProgressMessage = '导入处理超时，可能是服务器繁忙或出现错误'
            this.$message.error('导入处理超时，请稍后重试')
          }
        }, 30000) // 30秒超时
      } else {
        this.$message.error(response.msg || '导入失败')
      }
    },
    // 导入失败处理
    handleImportError(err, file, fileList) {
      console.error('文件上传失败:', err)

      // 检查是否是401错误（未授权）
      if (err.status === 401 || (err.message && err.message.includes('401'))) {
        this.$message.error('登录已过期，请重新登录')
        this.importError = true

        // 强制登出并跳转到登录页
        this.$store.dispatch('user/logout').then(() => {
          this.$router.push('/login')
        })
        return
      }

      // 其他错误
      this.$message.error('文件上传失败，请重试')
      this.importError = true

      // 记录更多错误信息
      console.error('文件上传错误详情:', {
        error: err,
        file: file ? file.name : 'unknown',
        fileList: fileList ? fileList.length : 0,
        token: !!localStorage.getItem('token')
      })
    },
    // 连接WebSocket
    connectWebSocket() {
      // 检查用户ID是否存在
      const userId = this.$store.getters.userId
      const token = localStorage.getItem('token')

      console.log('准备连接WebSocket，用户信息:', {
        userId: userId,
        hasToken: !!token,
        userInfo: this.$store.state.user.userInfo,
        userInfoModule: this.$store.state.userInfo
      })

      if (!userId) {
        console.warn('用户ID不存在，尝试从其他来源获取')
      }

      // 连接WebSocket
      webSocketClient.connect()

      // 添加导入进度监听器
      this.wsProgressListener = webSocketClient.addListener('IMPORT_PROGRESS', (data) => {
        console.log('收到导入进度消息:', data)
        this.handleImportProgress(data)
      })

      // 添加导入结果监听器
      this.wsResultListener = webSocketClient.addListener('IMPORT_RESULT', (data) => {
        console.log('收到导入结果消息:', data)
        this.handleImportResult(data)
      })

      // 检查WebSocket连接状态
      setTimeout(() => {
        const isConnected = webSocketClient.isConnected()
        console.log('WebSocket连接状态检查:', isConnected ? '已连接' : '未连接')

        if (!isConnected) {
          console.warn('WebSocket连接失败，将使用轮询方式获取导入状态')
          // 可以在这里添加轮询逻辑作为备选方案
        }
      }, 5000) // 5秒后检查

      console.log('WebSocket连接和监听器已设置，用户ID:', userId)
    },

    // 移除WebSocket监听器
    removeWebSocketListeners() {
      if (this.wsProgressListener) {
        this.wsProgressListener()
        this.wsProgressListener = null
      }

      if (this.wsResultListener) {
        this.wsResultListener()
        this.wsResultListener = null
      }
    },

    // 处理导入进度消息
    handleImportProgress(data) {
      console.log('收到导入进度消息:', data)
      this.importInProgress = true
      this.importProgress = data.progress || 0
      this.importProgressMessage = data.message || ''
      this.importTotal = data.total || 0
      this.importSuccess = data.success || 0

      // 收到进度消息，清除超时定时器
      if (this.importTimeout) {
        clearTimeout(this.importTimeout)
        this.importTimeout = null
      }

      // 如果进度大于0，重置错误状态
      if (this.importProgress > 0) {
        this.importError = false
      }

      // 设置新的超时检测 - 如果3分钟内没有新的进度更新，显示超时提示
      this.importTimeout = setTimeout(() => {
        const now = new Date().getTime()
        const elapsed = now - this.importStartTime

        // 如果已经过去了1分钟，且进度不是100%，显示超时提示
        if (this.importInProgress && this.importProgress < 100 && elapsed > 60000) {
          this.importError = true
          this.importProgressMessage = '导入处理时间过长，可能是服务器繁忙或出现错误'
        }
      }, 60000) // 1分钟超时
    },

    // 处理导入结果消息
    handleImportResult(data) {
      console.log('收到导入结果消息:', data)

      // 调试信息 - 检查rowErrorMap
      if (data.rowErrorMap) {
        console.log('原始rowErrorMap类型:', typeof data.rowErrorMap)
        console.log('原始rowErrorMap是否为数组:', Array.isArray(data.rowErrorMap))
        console.log('原始rowErrorMap的键:', Object.keys(data.rowErrorMap))
        console.log('原始rowErrorMap的值:', Object.values(data.rowErrorMap))

        // 检查是否有__ob__属性（Vue响应式对象）
        if (data.rowErrorMap.__ob__) {
          console.log('检测到Vue响应式对象')
        }

        // 尝试JSON序列化和反序列化
        try {
          const jsonString = JSON.stringify(data.rowErrorMap)
          console.log('JSON序列化结果:', jsonString)
          const parsedObject = JSON.parse(jsonString)
          console.log('JSON反序列化结果:', parsedObject)
        } catch (e) {
          console.error('JSON序列化/反序列化失败:', e)
        }
      }

      // 清除超时定时器
      if (this.importTimeout) {
        clearTimeout(this.importTimeout)
        this.importTimeout = null
      }

      this.importInProgress = false
      this.importComplete = true
      this.importProgress = 100
      this.importProgressMessage = data.message || ''
      this.importTotal = data.total || 0
      this.importSuccess = data.successCount || 0
      this.importError = data.status === 'FAILED'

      // 处理错误映射 - 后端现在直接返回错误映射而不是URL
      if (data.errorReportUrl) {
        // 兼容旧版本 - 如果仍然返回URL
        this.errorReportUrl = data.errorReportUrl
        this.rowErrorMap = null
      } else if (data.rowErrorMap) {
        // 新版本 - 直接返回错误映射
        try {
          // 方法1: 使用JSON序列化和反序列化来去除Vue响应式
          const jsonString = JSON.stringify(data.rowErrorMap)
          const plainObject = JSON.parse(jsonString)
          console.log('JSON序列化/反序列化后的错误映射:', plainObject)
          this.rowErrorMap = plainObject
        } catch (e) {
          console.error('JSON序列化/反序列化失败，尝试备选方法:', e)

          // 方法2: 手动复制对象属性
          const plainObject = {}

          // 检查rowErrorMap是否为对象
          if (typeof data.rowErrorMap === 'object' && data.rowErrorMap !== null) {
            // 如果是__ob__对象（Vue响应式对象），需要特殊处理
            if (data.rowErrorMap.__ob__) {
              console.log('检测到Vue响应式对象，进行特殊处理')
              // 获取所有键（包括不可枚举的）
              const keys = Object.getOwnPropertyNames(data.rowErrorMap).filter(key => key !== '__ob__')

              // 遍历所有键
              for (const key of keys) {
                plainObject[key] = data.rowErrorMap[key]
                console.log(`处理错误行 ${key}:`, plainObject[key])
              }
            } else {
              // 普通对象，直接复制
              Object.keys(data.rowErrorMap).forEach(key => {
                plainObject[key] = data.rowErrorMap[key]
              })
            }
          }

          console.log('手动处理后的错误映射:', plainObject)
          this.rowErrorMap = plainObject
        }

        this.errorReportUrl = null
      } else {
        this.errorReportUrl = null
        this.rowErrorMap = null
      }

      // 如果导入完成，刷新员工列表
      if (data.status === 'SUCCESS' || data.status === 'PARTIAL_SUCCESS') {
        this.getSubordinatesList()
      }

      // 调试日志 - 导入完成后的状态
      console.log('导入完成后的状态:', {
        importComplete: this.importComplete,
        importInProgress: this.importInProgress,
        importError: this.importError,
        rowErrorMap: this.rowErrorMap ? Object.keys(this.rowErrorMap).length : 0,
        hasErrors: this.rowErrorMap && Object.keys(this.rowErrorMap).length > 0
      })
    },

    // 处理导入对话框关闭 - 用于el-dialog的before-close回调
    handleImportDialogClose(done) {
      console.log('处理导入对话框关闭，当前状态:', {
        importInProgress: this.importInProgress,
        importComplete: this.importComplete,
        importError: this.importError,
        hasErrors: this.rowErrorMap && Object.keys(this.rowErrorMap).length > 0
      })

      // 如果导入正在进行中且没有错误，提示确认
      if (this.importInProgress && !this.importError) {
        this.$confirm('导入正在进行中，关闭窗口将无法查看进度，确定要关闭吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.resetImportState()
          if (done) done()
          else this.importDialogVisible = false
        }).catch(() => {
          // 取消关闭
        })
      } else if (this.importComplete && !this.importError && this.rowErrorMap && Object.keys(this.rowErrorMap).length > 0) {
        // 如果导入已完成且有错误数据，提示确认
        this.$confirm('关闭窗口后将无法查看错误详情，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          this.resetImportState()
          if (done) done()
          else this.importDialogVisible = false
        }).catch(() => {
          // 取消关闭
        })
      } else {
        // 如果导入已完成或有错误，直接关闭
        this.resetImportState()
        if (done) done()
        else this.importDialogVisible = false
      }
    },

    // 关闭导入对话框 - 用于按钮点击事件
    closeImportDialog() {
      console.log('关闭导入对话框，当前状态:', {
        importInProgress: this.importInProgress,
        importComplete: this.importComplete,
        importError: this.importError
      })

      // 如果导入正在进行中且没有错误，提示确认
      if (this.importInProgress && !this.importError) {
        this.$confirm('导入正在进行中，关闭窗口将无法查看进度，确定要关闭吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.resetImportState()
          this.importDialogVisible = false
        }).catch(() => {
          // 取消关闭
        })
      } else if (this.importComplete && !this.importError && this.rowErrorMap && Object.keys(this.rowErrorMap).length > 0) {
        // 如果导入已完成且有错误数据，提示确认
        this.$confirm('关闭窗口后将无法查看错误详情，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          this.resetImportState()
          this.importDialogVisible = false
        }).catch(() => {
          // 取消关闭
        })
      } else {
        // 如果导入已完成或有错误，直接关闭
        this.resetImportState()
        this.importDialogVisible = false
      }
    },

    // 获取进度图标
    getProgressIcon() {
      if (this.importError) {
        return 'el-icon-warning'
      } else if (this.importComplete) {
        return 'el-icon-success'
      } else {
        return 'el-icon-loading'
      }
    },

    // 获取进度图标颜色
    getProgressIconColor() {
      if (this.importError) {
        return '#E6A23C' // 警告色
      } else if (this.importComplete) {
        return '#67C23A' // 成功色
      } else {
        return '#409EFF' // 主色
      }
    },

    // 获取进度头部文本
    getProgressHeaderText() {
      if (this.importError) {
        return '导入处理异常'
      } else if (this.importComplete) {
        return '导入处理完成'
      } else {
        return '正在导入员工数据，请勿关闭窗口'
      }
    },

    // 获取进度条状态
    getProgressStatus() {
      if (this.importError) {
        return 'exception'
      } else if (this.importComplete) {
        return 'success'
      } else if (this.importProgress === 100) {
        return 'success'
      } else {
        return ''
      }
    },

    // 重置导入状态
    resetImportState() {
      // 清除超时定时器
      if (this.importTimeout) {
        clearTimeout(this.importTimeout)
        this.importTimeout = null
      }

      this.importInProgress = false
      this.importComplete = false
      this.importProgress = 0
      this.importProgressMessage = ''
      this.importTotal = 0
      this.importSuccess = 0
      this.errorReportUrl = null
      this.importStartTime = null
      this.importError = false
      this.rowErrorMap = null
      this.expandAllErrors = false
      this.activeErrorRows = []
    },


    // 下载模板 - 后端已修改为直接返回文件流
    async downloadTemplate() {
      try {
        this.$message.info('正在下载模板，请稍候...')

        // 调用API获取文件
        const response = await downloadTemplate()

        const fileName = `员工导入模板.xlsx`

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = fileName
        document.body.appendChild(link)
        link.click()

        // 清理
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(link.href)
        }, 100)

        this.$message.success('下载模板成功')
      } catch (error) {
        console.error('下载模板失败', error)
        this.$message.error('下载模板失败，请稍后重试')
      }
    },

    // 重置过滤器
    resetFilter() {
      this.filterForm = {
        positionId: undefined,
        status: undefined,
        departmentId: undefined
      }
      this.dateRange = []
      this.nameQuery = ''
      this.workNoQuery = ''
      this.selectedDepartmentName = ''
      this.positionSelection = null // 重置职位选择
      this.currentPage = 1
      this.getSubordinatesList()
    },

    /** 处理部门树选择（用于筛选） */
    handleDepartmentTreeSelect(data) {
      this.filterForm.departmentId = data.id
      this.selectedDepartmentName = data.name
      // 关闭弹出框
      this.departmentPopoverVisible = false
    },

    /** 清除选中的部门（用于筛选） */
    clearSelectedDepartment() {
      this.filterForm.departmentId = undefined
      this.selectedDepartmentName = ''
    },

    /** 处理部门选择（用于新增员工） - 不再需要，使用el-tree-select组件 */
    handleDepartmentSelect() {
      // 保留此方法以兼容旧代码
      console.log('使用el-tree-select组件，此方法不再需要')
    },



    /** 处理HRBP部门选择 */
    handleHRBPDepartmentSelect(data) {
      this.selectedHRBPDepartmentId = data.id
      this.selectedHRBPDepartmentName = data.name
      this.loadHRBPEmployees(data.id)
    },

    /** 加载HRBP员工列表 */
    loadHRBPEmployees(departmentId) {
      this.loadingHRBPEmployees = true
      this.hrbpEmployees = []

      // 使用导入的API方法获取HR角色的员工
      getEmployeesByDepartmentIdAndRole(departmentId, 'ROLE_hr').then(response => {
        this.loadingHRBPEmployees = false
        if (response && response.code === 1) {
          this.hrbpEmployees = response.data || []
          console.log('加载HR列表成功:', this.hrbpEmployees)

          if (this.hrbpEmployees.length === 0) {
            this.$message.info('该部门下暂无HR')
          }
        } else {
          this.$message.error(response && response.msg || '获取HR列表失败')
        }
      }).catch(error => {
        this.loadingHRBPEmployees = false
        this.$message.error(error.message || '获取HR列表失败')
      })
    },

    /** 处理HRBP选择 */
    handleHRBPSelect(row) {
      this.selectedHRBP = row
      this.employeeForm.hrbpId = row.id
      // 自动关闭选择器
      this.showHRBPSelector = false
    },

    /** 清除选中的HRBP */
    clearSelectedHRBP(e) {
      this.selectedHRBP = null
      this.employeeForm.hrbpId = null
      // 阻止事件冒泡，避免点击清除按钮时触发输入框的点击事件
      if (e) e.stopPropagation()
    },

    /** 表格行样式 */
    tableRowClassName({ row }) {
      if (this.selectedHRBP && row.id === this.selectedHRBP.id) {
        return 'selected-row'
      }
      return ''
    },

    /** 加载部门数据 */
    loadDepartmentData() {
      // 获取用户角色信息
      const roles = this.$store.getters['userInfo/roles'] || []
      const hasHRRole = roles.includes('ROLE_hr')
      const hasLeaderRole = roles.includes('ROLE_leader')

      // 优先使用HR接口 - 如果同时拥有HR和Leader角色，优先使用HR接口
      if (hasHRRole) {
        // HR角色加载完整部门树
        console.log('HR角色加载完整部门树 (ROLE_hr优先)')
        getDepartmentTree().then(response => {
          if (response && response.code === 1) {
            this.departmentOptions = response.data || []
            console.log('HR部门数据加载成功:', this.departmentOptions)
          } else {
            this.$message.error(response && response.msg || '获取部门数据失败')
          }
        }).catch(error => {
          this.$message.error(error.message || '获取部门数据失败')
        })
      } else if (hasLeaderRole) {
        // 获取当前用户的部门ID
        const departmentId = this.profile.departmentId
        console.log('Leader角色加载部门及其子部门, 部门ID:', departmentId)

        if (departmentId) {
          // Leader角色加载部门及其子部门
          getChildDepartments(departmentId).then(response => {
            if (response && response.code === 1) {
              this.departmentOptions = response.data || []
              console.log('Leader部门数据加载成功:', this.departmentOptions)
            } else {
              this.$message.error(response && response.msg || '获取部门数据失败')
            }
          }).catch(error => {
            this.$message.error(error.message || '获取部门数据失败')
          })
        } else {
          console.error('未找到部门ID，无法加载部门数据')
          this.$message.warning('未找到部门信息，无法加载部门数据')
        }
      }
    },

    // 处理删除员工
    handleDeleteEmployee(row) {
      this.$confirm(`确定要删除员工 ${row.name}(${row.workNo}) 吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteEmployee(row.id)
          if (response.code === 1) {
            this.$message.success('删除员工成功')
            this.getSubordinatesList()
          } else {
            this.$message.error(response.msg || '删除员工失败')
          }
        } catch (error) {
          console.error('删除员工失败', error)
          this.$message.error('删除员工失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 加载下属数据 - 只调用一次默认接口
    async loadSubordinatesData() {
      try {
        this.loading = true
        console.log('开始加载下属数据')

        // 1. 先加载职位选项
        console.log('加载职位选项')
        await this.getPositionOptions()

        // 2. 加载部门数据 - 确保个人信息已加载
        console.log('加载部门数据')
        // 确保个人信息已加载，因为Leader需要用到部门ID
        if (!this.profile.id && this.isLeader) {
          console.log('个人信息未加载，先加载个人信息')
          await this.getProfileInfo()
        }
        this.loadDepartmentData()

        // 3. 加载下属列表（这是主要数据，必须加载）
        console.log('加载下属员工列表')
        await this.getSubordinatesList()

        // 不再调用getHROptions，避免重复调用API
      } catch (error) {
        console.error('加载下属数据失败', error)
        this.$message.error('加载数据失败，请刷新页面重试')
      } finally {
        this.loading = false
      }
    },

    // 处理导出员工
    async handleExportEmployees() {
      // 如果已经在加载中，不要重复调用
      if (this.loading) {
        console.log('已经在导出中，跳过重复调用')
        return
      }

      try {
        this.loading = true

        // 构建查询参数，与当前筛选条件一致
        const queryParams = {
          name: this.nameQuery || undefined,
          workNo: this.workNoQuery || undefined,
          departmentId: this.filterForm.departmentId, // 部门ID
          status: this.filterForm.status === true ? 1 : (this.filterForm.status === false ? 0 : undefined),
          entryStartDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : undefined,
          entryEndDate: this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : undefined
        }

        // 根据职位选择添加不同的参数
        if (this.positionSelection) {
          // 使用保存的选择类型
          if (this.selectedPositionType === 'name') {
            // 如果选择的是职位名称，添加positionName参数
            queryParams.positionName = this.positionSelection
          } else {
            // 如果选择的是职级，添加positionId参数
            queryParams.positionId = this.positionSelection
          }
        } else if (this.filterForm.positionId) {
          // 兼容旧的选择方式
          queryParams.positionId = this.filterForm.positionId
        }

        // 移除undefined的参数
        Object.keys(queryParams).forEach(key => {
          if (queryParams[key] === undefined) {
            delete queryParams[key]
          }
        })

        console.log('导出查询参数:', queryParams)

        // 获取用户角色信息
        const roles = this.$store.getters['userInfo/roles'] || []
        const hasHRRole = roles.includes('ROLE_hr')
        const hasLeaderRole = roles.includes('ROLE_leader')

        // 检查权限
        if (!hasHRRole && !hasLeaderRole) {
          this.$message.error('您没有权限导出员工数据')
          return
        }

        // 使用统一的导出接口
        const response = await exportEmployee(queryParams)

        // 检查响应是否为JSON格式（表示可能是错误响应）
        try {
          // 尝试将Blob转换为文本，检查是否为JSON格式
          const text = await this.blobToText(response)
          try {
            const jsonResponse = JSON.parse(text)
            // 如果能解析为JSON，检查code值
            if (jsonResponse.code === 0) {
              this.$message.error(jsonResponse.msg || '导出员工数据失败')
              return
            }
          } catch (e) {
            // 不是JSON格式，继续处理为文件下载
            console.log('响应不是JSON格式，继续处理为文件下载')
          }
        } catch (e) {
          console.log('无法读取响应内容，继续处理为文件下载')
        }

        // 生成带日期时间的文件名
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const hours = String(now.getHours()).padStart(2, '0')
        const minutes = String(now.getMinutes()).padStart(2, '0')
        const seconds = String(now.getSeconds()).padStart(2, '0')
        // 使用更美观的日期时间格式 - 避免使用特殊字符
        const dateTimeStr = `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`
        const fileName = `员工数据_${dateTimeStr}.xlsx`

        // 处理响应结果 - 使用正确的MIME类型
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        // 创建下载链接
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = fileName
        document.body.appendChild(link) // 添加到DOM以确保在所有浏览器中工作
        link.click()

        // 清理
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(link.href)
        }, 100)

        this.$message.success('导出员工数据成功')
      } catch (error) {
        console.error('导出员工数据失败', error)
        this.$message.error('导出员工数据失败')
      } finally {
        this.loading = false
      }
    },

    // 将Blob转换为文本
    blobToText(blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          resolve(reader.result)
        }
        reader.onerror = () => {
          reject(new Error('Failed to read blob as text'))
        }
        reader.readAsText(blob)
      })
    }
  },
  computed: {
    // HRBP显示值
    hrbpDisplayValue() {
      if (this.selectedHRBP) {
        return this.selectedHRBP.name + (this.selectedHRBP.workNo ? ' (' + this.selectedHRBP.workNo + ')' : '')
      }
      return ''
    }
  },
  watch: {
    viewMode(newVal, oldVal) {
      if (newVal === 'subordinates' && newVal !== oldVal) {
        console.log('视图切换到下属视图，加载下属数据')
        // 使用loadSubordinatesData方法统一加载下属数据
        // 这个方法内部已经处理了缓存、顺序加载和重复调用
        this.loadSubordinatesData()
      } else if (newVal === 'self' && newVal !== oldVal) {
        console.log('视图切换到个人视图')
      }
    },
    // 监听展开全部错误的变化
    expandAllErrors(newVal) {
      if (newVal && this.rowErrorMap) {
        // 展开全部错误
        this.activeErrorRows = Object.keys(this.rowErrorMap)
      } else {
        // 收起全部错误
        this.activeErrorRows = []
      }
    },
    // 监听错误映射的变化
    rowErrorMap: {
      handler(newVal) {
        if (newVal && this.expandAllErrors) {
          // 如果设置了展开全部，则展开所有错误行
          this.activeErrorRows = Object.keys(newVal)
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }
}

.profile-container {
  margin-top: 20px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.profile-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.profile-title {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.profile-subtitle {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.profile-content {
  margin-top: 20px;
}

.table-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }
  }
}


.profile-card {
  margin-bottom: 20px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    i {
      margin-right: 5px;
      color: #409EFF;
    }
  }

  .profile-item {
    margin-bottom: 15px;
    padding: 5px 0;
    border-bottom: 1px dashed #ebeef5;
    display: flex;
    align-items: flex-start;

    label {
      font-weight: bold;
      margin-right: 5px;
      color: #606266;
      min-width: 90px;
      display: inline-block;
      text-align: right;
      flex-shrink: 0;
    }

    span {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.subordinates-container {
  margin-top: 20px;

  .table-operations {
    margin-top: 15px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-operations-left {
      display: flex;
      align-items: center;
    }

    .table-operations-right {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.import-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.import-template {
  margin-top: 20px;
  text-align: center;
  width: 100%;
}

.upload-demo {
  width: 100%;
}

/* 修复上传区域样式 */
.upload-demo {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-demo >>> .el-upload {
  display: block;
  width: 100%;
  text-align: center;
}

.upload-demo >>> .el-upload-dragger {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  border: 2px dashed #d9d9d9;
}

.upload-demo >>> .el-upload-dragger:hover {
  border-color: #409EFF;
}

.upload-demo >>> .el-icon-upload {
  font-size: 40px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.upload-demo >>> .el-upload__text {
  text-align: center;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.upload-demo >>> .el-upload__text em {
  color: #409EFF;
  font-style: normal;
}

.upload-demo >>> .el-upload__tip {
  text-align: center;
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
}

/* 确保对话框内容居中 */
.el-dialog__body {
  padding: 20px;
}

/* 确保对话框内容居中 */
.el-dialog {
  display: flex;
  flex-direction: column;
}

.el-dialog__header {
  text-align: center;
  padding: 15px 20px;
}

/* 导入对话框样式 */
.import-dialog {
  .el-dialog__body {
    padding: 20px 30px;
  }

  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #EBEEF5;
  }

  .el-dialog__footer {
    border-top: 1px solid #EBEEF5;
    padding: 15px 20px;
  }
}

/* 导入进度样式 */
.import-progress-container {
  padding: 20px 0;
  width: 100%;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 16px;

  i {
    margin-right: 10px;
    font-size: 20px;
    color: #409EFF;
  }
}

.import-progress-bar {
  margin: 15px 0;
}

.progress-info {
  margin: 15px 0;

  .progress-message {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
    text-align: center;
  }

  .progress-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 14px;

    .success-count {
      color: #67C23A;
      font-weight: bold;
    }
  }
}

.error-report {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #FAECD8;
}

.error-summary {
  background-color: #FDF6EC;
  padding: 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #FAECD8;

  i {
    color: #E6A23C;
    font-size: 20px;
    margin-right: 10px;
  }

  span {
    font-size: 16px;
    font-weight: 600;
    color: #E6A23C;
  }
}

.error-actions {
  margin-top: 20px;
  text-align: center;
}

.error-details {
  background-color: #FFFBF6;
  padding: 15px;
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  span {
    font-weight: 600;
    color: #606266;
  }
}

.error-row-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  display: flex;

  padding: 5px;
  line-height: 1.8;
}

.error-row-number {
  font-weight: 600;
}

.error-count {
    font-size: 12px;
    background-color: #F56C6C;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: inline - block;
    margin-right: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 添加阴影，增加立体感 */
    border: 1px solid #E64A4A; /* 添加边框，让按钮更精致 */
}

.error-count:hover {
    background-color: #E64A4A;
    border-color: #D63A3A; /* 悬停时改变边框颜色 */
}

.error-content {
  background-color: #FFF;
  border-radius: 4px;
  padding: 10px;
  margin-top: 5px;
}

.error-list {
  margin: 0;
  padding: 0;
  list-style: none;

  li {
    margin-bottom: 8px;
    color: #606266;
    display: flex;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }

    i {
      color: #F56C6C;
      margin-right: 8px;
      margin-top: 3px;
      flex-shrink: 0;
    }

    span {
      flex: 1;
    }
  }
}

/* 自定义折叠面板样式 */
.error-details >>> .el-collapse {
  border-top: none;
  border-bottom: none;
}

.error-details >>> .el-collapse-item {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.error-details >>> .el-collapse-item__header {
  background-color: #FFF;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
  margin-bottom: 0;

  &:hover {
    background-color: #F5F7FA;
  }

  &.is-active {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.error-details >>> .el-collapse-item__wrap {
  border: 1px solid #EBEEF5;
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.error-details >>> .el-collapse-item__content {
  padding: 0;
}

.filter-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  height: 70px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.department-path {
  color: #409EFF;
  font-weight: 500;
}

.info-text {
  min-height: 40px;
  line-height: 40px;
  padding: 0 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
}

.department-tree-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;

  .el-tree-node__content {
    height: 32px;
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f0f7ff;
    color: #409EFF;
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.empty-text {
  color: #909399;
  font-style: italic;
}

.parent-department-container {
  position: relative;
  width: 100%;

  .clear-parent-btn {
    position: absolute;
    right: 30px;
    top: 0;
    height: 100%;
    z-index: 1;

    &:hover {
      color: #f56c6c;
    }
  }
}

.leader-select-container {
  display: flex;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  .department-tree {
    width: 40%;
    height: 100%;
    overflow: auto;
    border-right: 1px solid #dcdfe6;
    padding: 10px;
  }

  .employee-list {
    width: 60%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .employee-list-header {
      padding: 10px;
      border-bottom: 1px solid #dcdfe6;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .loading-indicator {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

>>> .selected-row td {
  background-color: #f0f9eb !important;
}

.hrbp-display-input {
  width: 100%;
}

.hrbp-display-input >>> .el-input__inner {
  cursor: pointer;
  background-color: #f5f7fa;
}

.hrbp-display-input >>> .el-input-group__append {
  padding: 0;
}

// 按钮样式优化
.el-button {
  // &.el-button--primary {
  //   background-color: #409eff;
  //   border-color: #409eff;
  // }

  &.el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
  }

  &.el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
  }
}

.hrbp-display-input >>> .el-input-group__append .el-button {
  margin: 0;
  border: none;
  background: transparent;
  padding: 0 10px;
}

.hrbp-display-input >>> .el-input-group__append .el-button:hover {
  color: #409EFF;
}

.hrbp-display-input >>> .el-input-group__append .el-button:first-child {
  border-right: 1px solid #dcdfe6;
}

/* 树选择器样式 */
.el-tree-select >>> .el-input__inner {
  cursor: pointer;
  background-color: #f5f7fa;
}

/* 新增员工表单样式 */
.add-employee-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input, .el-select, .el-date-editor, .el-tree-select {
    width: 100%;
  }

  .el-form-item__label {
    font-weight: normal;

    &:before {
      content: "*";
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .el-form-item__label:before {
    display: none;
  }

  .el-form-item.is-required .el-form-item__label:before {
    display: inline-block;
  }

  /* 职位选择器样式 */
  .position-selector {
    display: flex;
    align-items: center;
    width: 100%;
  }


}


</style>
