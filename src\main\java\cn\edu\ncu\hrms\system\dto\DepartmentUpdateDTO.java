package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新部门数据传输对象 (DTO)
 */
@Data
@ApiModel(value="DepartmentUpdateDTO", description="更新部门数据传输对象")
public class DepartmentUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门名称")
    @Size(max = 50, message = "部门名称长度不能超过50个字符")
    private String name;

     @ApiModelProperty(value = "上级部门ID")
     private Integer parentId;

    @ApiModelProperty(value = "部门领导员工ID")
    private Integer leaderId;

} 