package cn.edu.ncu.hrms.salary.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="PositionPageQueryDTO", description="账套匹配查询DTO")
public class PositionPageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "职位ID")
    private Integer positionId;

    @ApiModelProperty(value = "职位名称")
    private String positionName;

    @ApiModelProperty(value = "账套ID")
    private Integer accountId;

    @ApiModelProperty(value = "账套名称", required = true)
    private String accountSetName;
}
