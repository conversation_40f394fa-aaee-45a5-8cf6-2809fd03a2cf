package cn.edu.ncu.hrms.common.service.impl;

import cn.edu.ncu.hrms.common.mapper.ApprovalProcessMapper;
import cn.edu.ncu.hrms.attendance.mapper.LeaveRequestMapper;
import cn.edu.ncu.hrms.attendance.vo.ApprovalProcessVO;
import cn.edu.ncu.hrms.common.entity.ApprovalProcess;
import cn.edu.ncu.hrms.common.service.ApprovalProcessService;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import cn.edu.ncu.hrms.common.service.EmailService;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.mapper.ResignRequestMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.edu.ncu.hrms.common.constant.AttendanceConstant.*;

/**
 * 审批流程服务实现类
 */
@Service
@Slf4j
public class ApprovalProcessServiceImpl implements ApprovalProcessService {

    @Autowired
    private ApprovalProcessMapper approvalProcessMapper;

    @Autowired
    private ResignRequestMapper resignRequestMapper;

    @Autowired
    private LeaveRequestMapper leaveRequestMapper;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmployeeMapper employeeMapper;

    /**
     * 处理审批操作（批准或驳回）。
     * <p>
     * 1. 校验审批节点是否存在、操作人权限、节点状态是否为 PENDING。
     * 2. 更新当前审批节点的状态 (APPROVED/REJECTED)、审批时间和意见。
     * 3. 如果是批准 (APPROVED):
     *      a. 查找下一个待审批 (PENDING) 节点。
     *      b. 如果找到下一个节点，更新下一个节点状态，并发送通知给下一级审批人。
     *      c. 如果没有下一个节点（流程结束），更新请假申请的总状态为 APPROVED。
     * 4. 如果是驳回 (REJECTED):
     *      a. 更新申请的总状态为 REJECTED。
     *      b. 将该申请所有剩余的 PENDING 状态的审批节点标记为 TERMINATE (表示流程终止)。
     *
     * @param processId   审批流程节点的 ID
     * @param approverId  执行审批操作的员工 ID
     * @param action      审批动作 ("APPROVED" 或 "REJECTED")
     * @param comment     审批意见 (可选)
     * @param requestType 申请类型（LEAVE/RESIGN）
     * @return boolean    审批操作结果
     * @throws RuntimeException 如果数据库更新失败，则抛出运行时异常以触发事务回滚。
     */
    @Override
    @Transactional
    public boolean approve(Integer processId, Integer approverId, String action, String comment, String requestType) {
        log.info("开始处理审批操作，节点ID: {}, 操作人ID: {}, 操作: {}", processId, approverId, action);
        // 步骤 1: 查询并校验审批节点
        ApprovalProcess process = approvalProcessMapper.selectById(processId);
        if (process == null) {
            log.error("审批失败：未找到审批节点ID: {}", processId);
            throw new BusinessException("未找到审批节点");
        }

        // 步骤 1.1: 校验操作人权限
        if (!process.getApproverId().equals(approverId)) {
            log.warn("审批失败：操作人ID: {} 无权审批此节点ID: {} (应由 {} 审批)", approverId, processId, process.getApproverId());
            throw new BusinessException("无权审批此节点");
        }
        // 步骤 1.2: 校验节点状态
        if (!"PENDING".equalsIgnoreCase(process.getStatus())) {
            log.warn("审批失败：节点ID: {} 当前状态为 {}，非PENDING状态，无法审批", processId, process.getStatus());
            throw new BusinessException("审批节点状态异常");
        }
        if (process.getExpireTime().isBefore(LocalDateTime.now())) {
            log.warn("审批失败：节点ID: {} 已超过审批截止时间，无法审批", processId);
            throw new BusinessException("超过审批截止时间，无法审批");
        }

        // 步骤 2: 更新当前审批节点状态
        String newStatus = null;
        if (STATUS_APPROVED.equals(action)) {
            newStatus = STATUS_APPROVED;
        } else if (STATUS_REJECTED.equals(action)) {
            newStatus = STATUS_REJECTED;
        } else {
            log.warn("审批失败：未知操作: {}", action);
            throw new BusinessException("非法审批操作");
        }
        process.setStatus(newStatus);
        process.setComment(comment);
        process.setApproveTime(LocalDateTime.now());
        int updateCount = approvalProcessMapper.update(process);
        if (updateCount > 0) {
            log.info("审批节点ID: {} 状态更新为 {} 成功", processId, process.getStatus());
        } else {
            // 可能由于并发或其他原因导致更新失败
            log.warn("更新审批节点ID: {} 状态时影响行数为0，可能已被其他操作处理。", processId);
            throw new BusinessException("审批失败，请稍后重试");
        }

        // 步骤 3: 处理批准逻辑
        if (STATUS_APPROVED.equals(newStatus)) {
            log.debug("节点ID: {} 被批准，查找下一个审批节点", processId);
            // 步骤 3.1: 查找下一个待审批节点
            ApprovalProcess next = approvalProcessMapper.selectNextPending(process.getRequestId(), process.getStepOrder(), requestType);
            if (next != null) {
                // 步骤 3.2a: 找到下一个节点，更新节点状态，通知下一级审批人
                log.info("申请ID: {} 找到下一审批节点ID: {}, 待审批人ID: {}", process.getRequestId(), next.getId(), next.getApproverId());
                next.setExpireTime(LocalDateTime.now().plus(1, ChronoUnit.DAYS));
                next.setStatus(STATUS_PENDING);
                int count = approvalProcessMapper.update(next);
                if (count > 0) {
                    log.info("下一级节点ID: {} 更新状态为 PENDING 成功", next.getId());
                    sendApprovalNotification(next.getApproverId(), process.getRequestId(), requestType);
                } else {
                    // 可能由于并发或其他原因导致更新失败
                    log.warn("更新节点ID: {} 状态为 PENDING 时影响行数为0，可能已被其他操作处理。", next.getId());
                    throw new BusinessException("审批失败，请稍后重试");
                }
            } else {
                // 步骤 3.2b: 没有下一个节点，流程结束，更新总状态
                log.info("申请ID: {} 已是最后一个审批节点，流程结束", process.getRequestId());
                updateRequestStatus(process.getRequestId(), requestType, STATUS_APPROVED);
            }
        } else {
            // 步骤 4: 处理驳回逻辑
            log.info("申请ID: {} 被审批人ID: {} 驳回 (节点ID: {})，流程终止，更新总状态为 REJECTED", process.getRequestId(), approverId, processId);
            // 步骤 4.1: 更新请假申请总状态
            updateRequestStatus(process.getRequestId(), requestType, STATUS_REJECTED);
            // 步骤 4.2: 将后续 PENDING 节点标记为 TERMINATE
            approvalProcessMapper.updateAllNewStatus(process.getRequestId(), requestType, STATUS_TERMINATE);
        }

        return true;
    }


    /**
     * 更新申请状态（离职/请假）
     * @param requestId    申请ID
     * @param requestType  申请类型（RESIGN/LEAVE）
     * @param status       目标状态（APPROVED/REJECTED）
     * @throws BusinessException 如果更新失败
     */
    private void updateRequestStatus(Integer requestId, String requestType, String status) {
        int count;
        String requestName;

        if (REQUEST_TYPE_RESIGN.equals(requestType)) {
            count = resignRequestMapper.updateStatus(requestId, status);
            requestName = "离职申请";
        } else if (REQUEST_TYPE_LEAVE.equals(requestType)) {
            count = leaveRequestMapper.updateStatus(requestId, status);
            requestName = "请假申请";
        } else {
            throw new IllegalArgumentException("无效的申请类型: " + requestType);
        }

        if (count > 0) {
            log.info("{} ID: {} 更新状态为 {} 成功", requestName, requestId, status);
        } else {
            log.warn("更新{} ID: {} 状态为 {} 时影响行数为0，可能已被其他操作处理。", requestName, requestId, status);
            throw new BusinessException("审批失败，请稍后重试");
        }
    }

    /**
     * 查询指定申请的所有审批流程节点。
     *
     * @param requestId 申请记录的 ID （不同类型的申请ID可能相同，需要type区分）
     * @param type      申请类型（RESIGN/LEAVE）
     * @return {@code List<ApprovalProcess>} 审批流程节点列表，按 stepOrder 排序。如果查询失败或无记录，返回空列表。
     */
    @Override
    public List<ApprovalProcessVO> getApprovalProcessListByRequestId(Integer requestId, String type) {
        log.info("开始查询审批流程节点列表，申请ID: {}", requestId);
        try {
            List<ApprovalProcess> processList = approvalProcessMapper.selectByRequestIdAndType(requestId, type);
            List<ApprovalProcessVO> processVOList = processList.stream()
                    .map(this::convertToApprovalProcessVO)
                    .collect(Collectors.toList());
            log.info("查询到申请ID: {} 的 {} 条审批流程记录", requestId, processList.size());
            return processVOList;
        } catch (Exception e) {
            log.error("查询审批流程节点列表失败，申请ID: {}，异常: {}", requestId, e.getMessage());
            return new ArrayList<>();
        }
    }

    // 辅助方法：将 ApprovalProcess 转换为 ApprovalProcessVO
    private ApprovalProcessVO convertToApprovalProcessVO(ApprovalProcess process) {
        ApprovalProcessVO vo = new ApprovalProcessVO();
        BeanUtils.copyProperties(process, vo);
        Integer approverId = process.getApproverId();
        Employee approver = employeeMapper.selectById(approverId);
        if (approver != null) {
            vo.setApproverName(approver.getName());
            vo.setApproverWorkNo(approver.getWorkNo());
        }
        vo.setStatusName(getStatusName(process.getStatus()));
        return vo;
    }

    private String getStatusName(String status) {
        if (status == null) return null;
        switch (status) {
            case STATUS_PENDING:
                return "待审批";
            case STATUS_APPROVED:
                return "已批准";
            case STATUS_REJECTED:
                return "已驳回";
            case STATUS_NEW:
            case STATUS_TERMINATE:
            case STATUS_CANCELED:
            default:
                return "";
        }
    }


    /**
     * 撤回指定申请ID的审批流程。
     *
     * @param requestId 申请ID
     */
    @Override
    public Boolean cancelApprovalProcessByRequestId(Integer requestId, String type) {
        log.debug("准备将申请ID：{} 申请类型：{} 的所有所有审批节点撤回", requestId, type);
        try {
            List<ApprovalProcess> list = approvalProcessMapper.selectByRequestIdAndType(requestId, type);
            int cancelCount = approvalProcessMapper.cancel(requestId, type);
            if (cancelCount == list.size()) {
                log.info("撤回审批节点成功");
                return true;
            } else {
                log.error("撤回所有审批节点失败，撤回数量不符");
                return false;
            }
        } catch (Exception e) {
            log.error("更新申请ID: {} 申请类型：{}  的所有节点为 CANCELED 时数据库操作失败，异常: {}", requestId, type, e.getMessage());
            return false;
        }
    }


    /**
     * 发送审批通知邮件给指定的审批人。
     * <p>
     * 1. 根据审批人ID查询员工信息以获取邮箱。
     * 2. 如果找到邮箱，调用 {@link EmailService} 发送邮件。
     * 3. 处理可能的异常，邮件发送失败不应阻塞主流程，仅记录错误日志。
     *
     * @param approverId 待通知的审批人员工 ID
     * @param requestId  相关的请假申请 ID
     */
    public void sendApprovalNotification(Integer approverId, Integer requestId, String requestType) {
        log.debug("准备发送审批通知，审批人ID: {}, 申请ID: {}", approverId, requestId);
        String typeName = getTypeName(requestType);
        Employee approver = null;
        try {
            // 步骤 1: 查询审批人信息
            approver = employeeMapper.selectById(approverId);
        } catch (Exception e) {
            log.error("查询审批人信息失败，审批人ID: {}, 异常: {}", approverId, e.getMessage());
            return; // 查询失败，无法发送
        }

        if (approver != null && approver.getEmail() != null && !approver.getEmail().isEmpty()) {
            // 步骤 2: 发送邮件
            String email = approver.getEmail();
            String subject = "【HRMS】待处理审批：" + typeName + "申请通知";
            // 修复：修正多行字符串格式，确保在双引号内
            String text = String.format("尊敬的 %s，\n\n您有一个新的" + typeName + "申请（ID: %d）需要审批。\n\n请及时登录HRMS系统处理。\n\n此邮件为系统自动发送，请勿回复。",
                    approver.getName() != null ? approver.getName() : "审批人", // 尝试获取姓名
                    requestId);
            try {
                emailService.sendSimpleMail(email, subject, text);
                log.info("已成功发送审批通知邮件给审批人ID: {}, 邮箱: {}", approverId, email);
            } catch (Exception e) {
                log.error("发送审批通知邮件失败，审批人ID: {}, 邮箱: {}, 异常: {}", approverId, email, e.getMessage());
                // 邮件发送失败不影响主流程
            }
        } else {
            log.warn("无法发送审批通知：未找到审批人ID: {} 的信息或邮箱为空。", approverId);
        }
    }


}
