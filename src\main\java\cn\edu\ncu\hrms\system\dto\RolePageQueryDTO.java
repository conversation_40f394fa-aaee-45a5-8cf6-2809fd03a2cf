package cn.edu.ncu.hrms.system.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="RolePageQueryDTO", description="角色分页查询数据传输对象")
public class RolePageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "角色ID")
    private Integer id;

    @ApiModelProperty(value = "角色名称")
    private String name;

}
