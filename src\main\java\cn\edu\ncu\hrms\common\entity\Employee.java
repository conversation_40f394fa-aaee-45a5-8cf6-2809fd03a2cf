package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("员工档案信息")
public class Employee {
    @ApiModelProperty(value = "员工ID", required = true, example = "1001")
    private Integer id;

    @ApiModelProperty(value = "工号", required = true, example = "zhangsan10")
    private String workNo;

    @ApiModelProperty(value = "姓名", required = true, example = "张三")
    private String name;

    @ApiModelProperty(value = "入职日期", example = "2023-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryDate;

    @ApiModelProperty(value = "性别", allowableValues = "MALE,FEMALE,OTHER", example = "MALE")
    private String gender;

    @ApiModelProperty(value = "身份证号", example = "110101199003072516")
    private String idCard;

    @ApiModelProperty(value = "联系电话", example = "13800138000")
    private String phone;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "家庭住址", example = "北京市朝阳区")
    private String address;

    @ApiModelProperty(value = "部门ID", required = true, example = "201")
    private Integer departmentId;

    @ApiModelProperty(value = "职位ID", required = true, example = "301")
    private Integer positionId;

    @ApiModelProperty(value = "HRBP员工ID", example = "1002")
    private Integer hrbpId;

    @ApiModelProperty(value = "在职状态", allowableValues = "0,1", example = "1")
    private Boolean status;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-01-02 13:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
