package cn.edu.ncu.hrms.system.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO; // 继承通用的分页查询DTO
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户分页查询数据传输对象 (DTO)
 */
@Data
@ApiModel(value="UserPageQueryDTO", description="用户分页查询数据传输对象")
public class UserPageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "用户ID")
    private Integer id;

    @ApiModelProperty(value = "用户名 (模糊查询)")
    private String username;

    @ApiModelProperty(value = "是否只查询管理员 (true/false)", notes = "不传则查询所有类型")
    private Boolean isAdmin;

} 