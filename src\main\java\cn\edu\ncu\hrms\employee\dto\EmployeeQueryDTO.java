package cn.edu.ncu.hrms.employee.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 员工查询条件DTO
 */
@Data
@ApiModel(description = "员工查询条件")
public class EmployeeQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "部门及其子部门Id（Leader查询专用）", example = "1")
    private List<Integer> departmentIds;

    @ApiModelProperty(value = "HR的员工ID（HR查询专用）", example = "2")
    private Integer hrbpId;

    @ApiModelProperty(value = "部门ID（精确查询）", example = "2")
    private Integer departmentId;

    @ApiModelProperty(value = "员工姓名（模糊查询）", example = "张三")
    private String name;

    @ApiModelProperty(value = "员工工号（精确查询）", example = "zhangshan10")
    private String workNo;

    @ApiModelProperty(value = "职位名称（精确查询）", example = "后端开发")
    private String positionName;

    @ApiModelProperty(value = "职位（名称+职级）", example = "1")
    private Integer positionId;

    @ApiModelProperty(value = "员工状态(1:在职, 0:离职)", example = "1")
    private Integer status;

    @ApiModelProperty(value = "入职开始日期", example = "2024-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryStartDate;

    @ApiModelProperty(value = "入职结束日期", example = "2024-12-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryEndDate;
} 