package cn.edu.ncu.hrms.salary.controller;

import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.EmployeeQueryDTO;
import cn.edu.ncu.hrms.salary.dto.SalaryPageQueryDTO;
import cn.edu.ncu.hrms.salary.service.SalaryReportService;
import cn.edu.ncu.hrms.salary.vo.SalaryReportVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 薪资报表控制器
 */
@RestController
@RequestMapping("/salary/report")
@Api(tags = "薪资报表接口")
public class SalaryController {

    @Autowired
    private SalaryReportService salaryService;

    @Autowired
    private SecurityUtil securityUtil;


    /**
     * 获取我的历史薪资 (分页)
     */
    @GetMapping("/self")
    @ApiOperation("获取我的历史薪资 (分页)")
    @PreAuthorize("@hrms.hasAuthority('salary:myreport:query')")
    public Result<PageInfo<SalaryReportVO>> getMyHistorySalary(@Valid SalaryPageQueryDTO queryDTO) {
        Integer employeeId = securityUtil.getCurrentEmployeeId();
        if (employeeId == null) {
            return Result.error("无法获取当前用户信息");
        }
        queryDTO.setEmployeeId(employeeId);

        return salaryService.pageSalaryReports(queryDTO);
    }

    /**
     * 分页查询薪资报表 (Leader/HR)
     */
    @GetMapping("/page")
    @ApiOperation("分页查询薪资报表 (Leader/HR)")
    @PreAuthorize("@hrms.hasAuthority('salary:report:list')")
    public Result<PageInfo<SalaryReportVO>> pageSalaryReports(@Valid SalaryPageQueryDTO queryDTO) {
        return salaryService.pageSalaryReports(queryDTO);
    }

    @GetMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiOperation("导出薪资报表")
    @PreAuthorize("@hrms.hasAuthority('salary:report:list')")
    public byte[] exportEmployees(SalaryPageQueryDTO queryDTO) {
        return salaryService.exportSalaryReport(queryDTO);
    }

    /**
     * 根据ID获取薪资报表详情 (Leader/HR)
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取薪资报表详情 (Leader/HR)")
    @PreAuthorize("@hrms.hasAuthority('salary:report:query')")
    public Result<SalaryReportVO> getSalaryReportById(
            @PathVariable @ApiParam("薪资报表ID") Integer id) {
        return salaryService.getSalaryReportVOById(id);
    }

}