package cn.edu.ncu.hrms.attendance.service.impl;

import cn.edu.ncu.hrms.attendance.dto.AttendanceConfigDTO;
import cn.edu.ncu.hrms.attendance.dto.AttendancePageQueryDTO;
import cn.edu.ncu.hrms.attendance.dto.AttendanceUpdateDTO;
import cn.edu.ncu.hrms.attendance.mapper.AttendanceMapper;
import cn.edu.ncu.hrms.attendance.service.AttendanceService;
import cn.edu.ncu.hrms.attendance.vo.AttendanceStatisticsVO;
import cn.edu.ncu.hrms.attendance.vo.AttendanceVO;
import cn.edu.ncu.hrms.attendance.vo.DashboardAttendanceStatsVO;
import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.constant.RoleConstant;
import cn.edu.ncu.hrms.common.entity.AccountSet;
import cn.edu.ncu.hrms.common.entity.Attendance;
import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.kafka.KafkaProducerService;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.DateUtil;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.salary.mapper.AccountSetMapper;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.edu.ncu.hrms.common.constant.AttendanceConstant.*;

/**
 * 考勤服务实现类
 */
@Service
public class AttendanceServiceImpl implements AttendanceService {

    private static final Logger log = LoggerFactory.getLogger(AttendanceServiceImpl.class);

    @Autowired
    private AttendanceMapper attendanceMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private AccountSetMapper accountSetMapper;

    @Autowired
    private SecurityUtil securityUtil;

    @Autowired(required = false) // 设置为 false，即使没有 KafkaProducer 也能运行
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 处理员工打卡请求 (签到或签退)。
     * 
     * 1. 校验输入参数 (员工ID, 打卡类型, 位置)。
     * 2. 查询员工信息和部门信息 (用于获取打卡规则)。
     * 3. 校验打卡地理位置是否在部门设定的范围内。
     * 4. 查询当天是否已存在该员工的考勤记录。
     * 5. 根据打卡类型 (签到/签退) 和考勤记录存在情况，进行校验 (如：是否重复签到、是否未签到就签退)。
     * 6. 创建新的考勤实体或获取现有实体。
     * 7. 调用 clockIn 或 clockOut 辅助方法处理具体的打卡逻辑 (判断状态、计算扣款等)。
     * 8. 保存 (插入或更新) 考勤记录到数据库。
     * 9. 返回打卡结果 (成功信息或错误信息)。
     *
     * @param employeeId 员工ID
     * @param type       打卡类型 (使用 AttendanceConstant.CLOCK_IN / CLOCK_OUT)
     * @param location   打卡位置 [经度, 纬度]
     * @return Result<AttendanceVO> 如果打卡成功，Data 中可能包含更新后的 AttendanceVO (当前实现返回简单成功消息)
     */
    @Override
    @Transactional
    public Result clock(Integer employeeId, String type, double[] location) {
        // 1. 参数校验
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (employeeId == null) employeeId = currentEmployeeId;
        else if (currentEmployeeId != null && !currentEmployeeId.equals(employeeId)) {
            log.warn("打卡失败：员工ID与当前用户不一致 (employeeId={}, currentEmployeeId={})", employeeId, currentEmployeeId);
            return Result.error("员工ID与当前用户不一致，打卡失败");
        }
        if (location == null || location.length != 2 || type == null ||
                (!type.equals(CLOCK_IN) && !type.equals(CLOCK_OUT))) {
            log.warn("打卡失败：参数无效 (employeeId={}, type={}, location={})", employeeId, type, Arrays.toString(location));
            return Result.error("无效的打卡参数");
        }
        // 判断是否工作日
        DayOfWeek dayOfWeek = LocalDate.now().getDayOfWeek();
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            return Result.error("非工作日，无需打卡");
        }

        // 2. 校验部门打卡配置信息
        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) {
            log.warn("打卡失败：员工不存在，ID={}", employeeId);
            return Result.error("账号异常，请联系管理员");
        }
        Department department = departmentMapper.selectById(employee.getDepartmentId());
        if (department == null) {
            return Result.error("未查询到关联的部门信息");
        }
        double[] departmentLocation = null;
        String locationJsonString = null;
        try {
            if (department.getClockLocation() == null) {
                return Result.error("部门未配置打卡规则，请联系部门领导");
            }
            departmentLocation = objectMapper.readValue(department.getClockLocation(), double[].class);
            locationJsonString = objectMapper.writeValueAsString(location);
        } catch (JsonProcessingException e) {
            log.error("部门打卡位置配置有误，请联系管理员", e);
            return Result.error("打卡位置异常，请联系管理员");
        }

        LocalTime clockInStartTime = DateUtil.parseToLocalTime(department.getClockInStartTime());
        LocalTime clockInEndTime = DateUtil.parseToLocalTime((department.getClockInEndTime()));
        if  (CLOCK_IN.equals(type)) {
            if (clockInStartTime == null) {
                // 没有配置上班打卡时间开始时间则默认为当日0点
                department.setClockInStartTime(LocalTime.MIN.toString());
            } else {
                department.setClockInStartTime(clockInStartTime.toString());
            }
            if (clockInEndTime == null) {
                return Result.error("未配置上班打卡时间，请联系部门领导");
            } else {
                department.setClockInEndTime(clockInEndTime.toString());
            }
        }
        LocalTime clockOutStartTime = DateUtil.parseToLocalTime(department.getClockOutStartTime());
        LocalTime clockOutEndTime = DateUtil.parseToLocalTime(department.getClockOutEndTime());
        if  (CLOCK_OUT.equals(type) && clockOutStartTime == null && clockOutEndTime == null ) {
            if (clockOutStartTime == null) {
                return Result.error("未配置下班打卡时间，请联系部门领导");
            } else {
                department.setClockOutStartTime(clockOutStartTime.toString());
            }
            if (clockOutEndTime == null) {
                // 没有配置下班打卡时间结束时间则默认为当日23:59:59
                department.setClockOutEndTime(LocalTime.MAX.toString());
            } else {
                department.setClockOutEndTime(clockOutEndTime.toString());
            }
        }


        // 3. 校验打卡范围
        if (!isInChina(departmentLocation)) {
            return Result.error("打卡位置配置有误，请联系部门领导");
        }
        Integer clockRadius = department.getClockRadius();
        if (clockRadius != null && clockRadius > 0) {
            GlobalCoordinates clockPoint = new GlobalCoordinates(departmentLocation[0], departmentLocation[1]);
            GlobalCoordinates userPoint = new GlobalCoordinates(location[0], location[1]);
            double distance = getDistanceMeter(clockPoint, userPoint, Ellipsoid.Sphere);
            if (distance > clockRadius) {
                log.warn("打卡失败：超出打卡范围，员工ID={}, 距离={}, 允许半径={}", employeeId, distance, clockRadius);
                return Result.error(String.format("不在打卡范围内 (距离打卡点 %.1f 米)", distance));
            }
        }

        // 4. 查询当天记录
        LocalDate today = LocalDate.now();
        Attendance existingAttendance = attendanceMapper.selectByEmployeeAndDate(employeeId, today);

        // 5. 校验打卡状态
        if (type.equals(CLOCK_IN)) {
            if (existingAttendance != null && existingAttendance.getClockInTime() != null) {
                 log.warn("签到失败：重复签到，员工ID={}, 日期={}", employeeId, today);
                return Result.error("今天已经签到，不能重复签到");
            }
        } else { // CLOCK_OUT
            if (existingAttendance == null || existingAttendance.getClockInTime() == null) {
                log.warn("签退失败：未签到或记录不存在，员工ID={}, 日期={}", employeeId, today);
                return Result.error("您尚未签到，请签到后再签退");
            }
            if (AttendanceConstant.CLOCK_ABSENT.equals(existingAttendance.getClockInStatus())) {
                log.warn("签退失败：今日考勤状态为缺勤，员工ID={}, 日期={}", employeeId, today);
                return Result.error("今日考勤状态为缺勤，无法签退");
            }
            /*if (existingAttendance.getClockOutTime() != null) {
                 log.warn("签退失败：重复签退，员工ID={}, 日期={}", employeeId, today);
                 return Result.error("今天已经签退，不能重复签退");
            }*/
        }

        // 6. 准备考勤实体
        Attendance attendance;
        if (existingAttendance == null) {
            attendance = new Attendance();
            attendance.setEmployeeId(employeeId);
            attendance.setDate(today);
        } else {
            attendance = existingAttendance;
        }


        // 7. 处理具体打卡逻辑 (clockIn / clockOut)
        Result specificResult = null;
        Integer positionId = employee.getPositionId(); // 获取职位ID，可能用于计算扣款
        if (type.equals(CLOCK_IN)) {
            attendance.setClockInLocation(locationJsonString);
            specificResult = clockIn(employeeId, positionId, attendance, department, today);
        } else { // CLOCK_OUT
            attendance.setClockOutLocation(locationJsonString);
            specificResult = clockOut(employeeId, attendance, department);
        }

        // 如果 clockIn/clockOut 返回错误，则直接返回该错误
        if (specificResult.getCode() != 1) {
            return specificResult;
        }

        // 8. 保存记录
        int rows;
        try {
            if (existingAttendance == null) {
                rows = attendanceMapper.insert(attendance);
            } else {
                rows = attendanceMapper.update(attendance);
            }
        } catch (Exception e) {
            log.error("保存考勤记录失败，员工ID: {}, 操作: {}, 异常: ",
                    employeeId, (existingAttendance == null ? "插入" : "更新"), e);
            return Result.error("系统异常，请稍后重试");
        }

        // 9. 返回结果
        if (rows > 0) {
            log.info("员工 {} {} 打卡成功", employeeId, type.equals(CLOCK_IN) ? "签到" : "签退");
            return Result.success(specificResult.getMsg());
        }
        log.error("员工 {} 打卡失败：数据库操作未影响任何行", employeeId);
        return Result.error("打卡失败，请刷新页面重试");
    }

    /**
     * 计算两个地理坐标点之间的距离（单位：米）。
     * 使用 Geodesy 库进行计算。
     *
     * @param gpsFrom   起始点坐标
     * @param gpsTo     目标点坐标
     * @param ellipsoid 使用的椭球模型 (例如 Ellipsoid.Sphere 或 Ellipsoid.WGS84)
     * @return 两点间的椭球距离 (米)
     */
    public static double getDistanceMeter(GlobalCoordinates gpsFrom, GlobalCoordinates gpsTo, Ellipsoid ellipsoid) {
        GeodeticCurve geoCurve = new GeodeticCalculator().calculateGeodeticCurve(ellipsoid, gpsFrom, gpsTo);
        return geoCurve.getEllipsoidalDistance();
    }

    /**
     * 校验地理坐标有效性
     * @param coordinate
     * @return
     */
    public static boolean isInChina(double[] coordinate) {

        double longitude = coordinate[0];
        double latitude = coordinate[1];

        // 中国大致地理范围
        return (longitude >= 73.66 && longitude <= 135.05) &&
                (latitude >= 3.86 && latitude <= 53.55);
    }

    /**
     * 处理签到逻辑 (私有辅助方法)。
     * 
     * 1. 设置签到时间和位置。
     * 2. 获取部门设定的签到时间范围。
     * 3. 判断签到时间：
     *    - 早于签到开始时间：错误。
     *    - 在签到结束时间之前：正常。
     *    - 晚于签到结束时间：
     *      - 超过迟到阈值：缺勤，计算缺勤扣款 (示例逻辑)。
     *      - 未超过迟到阈值：迟到，设置迟到扣款 (示例逻辑)。
     * 4. 更新考勤实体的签到状态和扣款金额。
     * 5. 返回包含成功消息的 Result。
     *
     * @param employeeId 员工ID
     * @param positionId 职位ID (用于获取薪资计算扣款)
     * @param attendance 考勤实体 (会被修改)
     * @param department 部门实体 (包含打卡规则)
     * @param today      当前日期
     * @return Result 包含操作结果消息
     */
    public Result clockIn(Integer employeeId, Integer positionId, Attendance attendance, Department department, LocalDate today) {
        // 1. 设置签到信息
        LocalTime time = LocalTime.now();
        attendance.setClockInTime(time);

        // 2. 获取签到时间规则
        LocalTime clockInStartTime = LocalTime.parse(department.getClockInStartTime(), DateTimeFormatter.ISO_LOCAL_TIME);
        LocalTime clockInEndTime = LocalTime.parse(department.getClockInEndTime(), DateTimeFormatter.ISO_LOCAL_TIME);
        int lateThreshold = department.getLateThreshold() != null ? department.getLateThreshold() : 0; // 迟到阈值(分钟)
        BigDecimal lateDeduction = department.getLateDeduction() != null ? department.getLateDeduction() : BigDecimal.ZERO; // 迟到扣款

        // 3. 判断签到时间
        String message;
        String status;
        BigDecimal deduction = BigDecimal.ZERO;

        if (time.isBefore(clockInStartTime)) {
            log.info("员工 {} 尝试在 {} 签到，早于允许开始时间 {}", employeeId, time, clockInStartTime);
            return Result.error("未到上班签到时间");
        } else if (time.isAfter(clockInEndTime.plusMinutes(lateThreshold))) {
            // 超过迟到时间 + 阈值 = 缺勤
            status = AttendanceConstant.CLOCK_ABSENT;
            message = "超过签到时间，记为缺勤";
            log.warn("员工 {} 在 {} 签到，已超过迟到阈值，记为缺勤", employeeId, time);
            AccountSet accountSet = accountSetMapper.selectByPositionId(positionId);
            if (accountSet != null) {
                // 按当月工作日折算缺勤扣款（四舍五入保留两位小数）
                int workingDays = DateUtil.getWorkingDaysInMonth(LocalDate.now());
                deduction = accountSet.getBaseSalary().divide(BigDecimal.valueOf(workingDays), 2, RoundingMode.HALF_UP);
            }
        } else if (time.isAfter(clockInEndTime)) {
            // 在签到结束时间之后，但在阈值内 = 迟到
            status = AttendanceConstant.CLOCK_LATE;
            message = "签到成功，状态为迟到";
            deduction = lateDeduction; // 使用部门配置的迟到扣款
            log.info("员工 {} 在 {} 签到，迟到，扣款 {}", employeeId, time, deduction);
        } else {
            // 正常签到
            status = AttendanceConstant.CLOCK_NORMAL;
            message = "签到成功";
            log.info("员工 {} 在 {} 正常签到", employeeId, time);
        }

        // 4. 更新考勤实体
        attendance.setClockInStatus(status);
        attendance.setDeduction(deduction);

        // 5. 返回成功结果
        return Result.success(message);
    }

    /**
     * 处理签退逻辑 (私有辅助方法)。
     * 
     * 1. 设置签退时间和位置。
     * 2. 获取部门设定的签退时间范围。
     * 3. 判断签退时间：
     *    - 晚于签退结束时间：错误。
     *    - 早于签退开始时间：
     *      - 早于早退阈值：错误。
     *      - 在早退阈值内：早退，设置早退扣款 (示例逻辑)。
     *    - 在签退时间范围内：正常。
     * 4. 更新考勤实体的签退状态和扣款金额 (累加)。
     * 5. 返回包含成功消息的 Result。
     *
     * @param employeeId 员工ID
     * @param attendance 考勤实体 (会被修改)
     * @param department 部门实体 (包含打卡规则)
     * @return Result 包含操作结果消息
     */
    public Result clockOut(Integer employeeId, Attendance attendance, Department department) {
        // 1. 设置签退信息
        LocalTime time = LocalTime.now();
        attendance.setClockOutTime(time);

        // 2. 获取签退时间规则
        LocalTime clockOutStartTime = LocalTime.parse(department.getClockOutStartTime(), DateTimeFormatter.ISO_LOCAL_TIME);
        LocalTime clockOutEndTime = LocalTime.parse(department.getClockOutEndTime(), DateTimeFormatter.ISO_LOCAL_TIME);
        int earlyThreshold = department.getEarlyThreshold() != null ? department.getEarlyThreshold() : 0; // 早退阈值(分钟)
        BigDecimal earlyDeduction = department.getEarlyDeduction() != null ? department.getEarlyDeduction() : BigDecimal.ZERO; // 早退扣款

        // 3. 判断签退时间
        String message;
        String status = AttendanceConstant.CLOCK_NORMAL; // 默认为正常
        BigDecimal deduction = BigDecimal.ZERO;

        if (time.isAfter(clockOutEndTime)) {
             log.info("员工 {} 尝试在 {} 签退，晚于允许结束时间 {}", employeeId, time, clockOutEndTime);
            return Result.error("已超过下班签退时间");
        } else if (time.isBefore(clockOutStartTime.minusMinutes(earlyThreshold))) {
            // 早于 (开始时间 - 阈值) = 不允许签退
             log.info("员工 {} 尝试在 {} 签退，早于允许的最早签退时间 {}", employeeId, time, clockOutStartTime.minusMinutes(earlyThreshold));
            return Result.error("未到下班签退时间");
        } else if (time.isBefore(clockOutStartTime)) {
            // 在 (开始时间 - 阈值) 和 开始时间 之间 = 早退
            status = AttendanceConstant.CLOCK_EARLY;
            message = "签退成功，状态为早退";
            deduction = earlyDeduction; // 使用部门配置的早退扣款
             log.info("员工 {} 在 {} 早退，扣款 {}", employeeId, time, deduction);
        } else {
            // 正常签退
            message = "签退成功";
             log.info("员工 {} 在 {} 正常签退", employeeId, time);
        }

        // 4. 更新考勤实体
        attendance.setClockOutStatus(status);
        // 如果已有签到扣款，则累加；否则设置为当前扣款
        attendance.setDeduction(attendance.getDeduction() != null ? attendance.getDeduction().add(deduction) : deduction);

        // 5. 返回成功结果
        return Result.success(message);
    }


    /**
     * 更新指定的考勤记录。
     *
     * 1. 校验考勤记录是否存在。
     * 2. 使用 BeanUtils 将 DTO 中的非空属性复制到查询出的实体对象。
     *    （注意：简单 BeanUtils.copyProperties 会覆盖所有字段，即使DTO中为null）。
     *    （如果需要部分更新，应手动设置或使用其他工具）。
     * 3. 设置更新时间。
     * 4. 执行数据库更新操作。
     * 5. (可选) 发送 Kafka 消息通知。
     *
     * @param id        要更新的考勤记录 ID
     * @param updateDTO 包含更新信息的 DTO (不应包含 employeeId 和 date)
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> update(Integer id, AttendanceUpdateDTO updateDTO) {
        if (id == null) {
            return Result.error("考勤ID不能为空");
        }

        // 1. 检查记录是否存在
        Attendance existingAttendance = attendanceMapper.selectById(id);
        if (existingAttendance == null) {
            log.warn("更新考勤记录失败：记录不存在，ID: {}", id);
            return Result.error("考勤记录不存在");
        }

        // 2. 复制属性 (注意：简单复制会覆盖所有字段)
        BeanUtils.copyProperties(updateDTO, existingAttendance);

        // 3. 设置更新时间和确保 ID 不变
        existingAttendance.setUpdateTime(LocalDateTime.now());
        existingAttendance.setId(id); // 确保 ID 正确

        // 4. 更新数据库
        int rows = attendanceMapper.update(existingAttendance);
        if (rows > 0) {
            log.info("更新考勤记录成功，ID: {}", id);
            return Result.success();
        } else {
            // 影响 0 行可能是因为数据未变化，检查记录是否依然存在
            log.warn("更新考勤记录操作数据库未影响任何行(或记录内容无变化)，ID: {}", id);
            Attendance current = attendanceMapper.selectById(id);
            if (current == null) {
                log.error("更新考勤记录失败：记录已被并发删除，ID: {}", id);
                return Result.error("更新失败：记录已被删除");
            }
            return Result.success("数据无变化");
        }
    }

    /**
     * 根据 ID 获取考勤记录视图对象 (AttendanceVO)。
     *
     * @param id 考勤记录 ID
     * @return Result<AttendanceVO> 包含视图对象的结果
     */
    @Override
    public Result<AttendanceVO> getVOById(Integer id) {
        if (id == null) {
             return Result.error("考勤ID不能为空");
        }
        Attendance attendance = attendanceMapper.selectById(id);
        if (attendance == null) {
             log.warn("获取考勤记录 VO 失败：记录不存在，ID: {}", id);
            return Result.error("考勤记录不存在");
        }
        AttendanceVO vo = convertToAttendanceVO(attendance);
        return Result.success(vo);
    }

    /**
     * 获取指定员工在指定日期的考勤记录实体。
     *
     * @param employeeId 员工ID
     * @param date       日期
     * @return Result<Attendance> 包含考勤实体对象的结果
     */
    @Override
    public Result<Attendance> getByEmployeeAndDate(Integer employeeId, LocalDate date) {
        if (employeeId == null || date == null) {
            log.warn("获取员工考勤记录失败：员工ID或日期为空 (employeeId={}, date={})", employeeId, date);
            return Result.error("员工ID和日期不能为空");
        }
        Attendance attendance = attendanceMapper.selectByEmployeeAndDate(employeeId, date);
        if (attendance == null) {
             log.info("员工 {} 在 {} 无考勤记录", employeeId, date);
            // 返回成功但数据为空，还是错误？根据接口约定决定
            return Result.success(null); // 或者 Result.error("记录不存在")
        }
        return Result.success(attendance);
    }
    
    /**
     * 获取指定员工的所有考勤记录列表。
     * (注意：数据量可能很大，谨慎使用，考虑分页或日期范围查询)
     *
     * @param employeeId 员工ID
     * @return Result<List<Attendance>> 包含考勤实体列表的结果
     */
    @Override
    public Result<List<Attendance>> listByEmployee(Integer employeeId) {
        if (employeeId == null) return Result.error("员工ID不能为空");
        List<Attendance> list = attendanceMapper.selectByEmployee(employeeId);
        log.info("查询到员工 {} 的 {} 条考勤记录", employeeId, list.size());
        return Result.success(list);
    }

    /**
     * 获取指定员工在指定日期范围内的考勤记录列表。
     *
     * @param employeeId 员工ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return Result<List<Attendance>> 包含考勤实体列表的结果
     */
    @Override
    public Result<List<Attendance>> listByEmployeeAndDateRange(Integer employeeId, LocalDate startDate, LocalDate endDate) {
        if (employeeId == null || startDate == null || endDate == null || startDate.isAfter(endDate)) {
            return Result.error("无效的查询参数");
        }
        List<Attendance> list = attendanceMapper.selectByEmployeeAndDateRange(employeeId, startDate, endDate);
        log.info("查询到员工 {} 在 {} 到 {} 期间的 {} 条考勤记录", employeeId, startDate, endDate, list.size());
        return Result.success(list);
    }

    /**
     * 获取指定部门在指定日期的所有员工考勤记录列表。
     *
     * @param departmentId 部门ID
     * @param date         日期
     * @return Result<List<Attendance>> 包含考勤实体列表的结果
     */
    @Override
    public Result<List<Attendance>> listByDepartment(Integer departmentId, LocalDate date) {
        if (departmentId == null || date == null) {
            return Result.error("部门ID和日期不能为空");
        }
        List<Attendance> list = attendanceMapper.selectByDepartment(departmentId, date);
        log.info("查询到部门 {} 在 {} 的 {} 条考勤记录", departmentId, date, list.size());
        return Result.success(list);
    }

    @Override
    public Result<List<Attendance>> getEmployeeMonthlyRecords(Integer employeeId, String yearMonth) {
        log.info("获取员工月度考勤记录，员工ID: {}, 月份: {}", employeeId, yearMonth);
        if (employeeId == null) {
            return Result.error("员工ID不能为空");
        }
        if (yearMonth == null || !yearMonth.matches("^\\d{4}-\\d{2}$")) { // 简单格式校验
            return Result.error("月份格式不正确，应为 YYYY-MM");
        }

        try {
            List<Attendance> records = attendanceMapper.selectEmployeeRecordsByMonth(employeeId, yearMonth);
            return Result.success(records);
        } catch (Exception e) {
            log.error("查询员工月度考勤记录失败，员工ID: {}, 月份: {}, 错误: ", employeeId, yearMonth, e);
            return Result.error("查询考勤记录失败");
        }
    }


    /**
     * 获取指定员工在指定日期范围内的考勤统计数据。
     *
     * 1. 校验输入参数（员工ID、日期范围）。
     * 2. 查询员工信息。
     * 3. 调用 Mapper 查询原始统计数据 (Map<String, Object>)。
     * 4. 调用辅助方法 buildStatisticsVO 将 Map 转换为 AttendanceStatisticsVO。
     *
     * @param employeeId 员工ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return Result<AttendanceStatisticsVO> 包含统计视图对象的结果
     */
    @Override
    public Result<AttendanceStatisticsVO> getStatistics(Integer employeeId, LocalDate startDate, LocalDate endDate) {
        // 1. 校验参数
        if (employeeId == null || startDate == null || endDate == null || startDate.isAfter(endDate)) {
            log.warn("获取员工考勤统计失败：参数无效 (employeeId={}, startDate={}, endDate={})", employeeId, startDate, endDate);
            return Result.error("无效的查询参数");
        }
        LocalDate currentDate = LocalDate.now();
        endDate = endDate.isAfter(currentDate) ? currentDate : endDate;

        // 2. 查询员工信息
        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) {
            log.warn("获取员工考勤统计失败：员工不存在，ID={}", employeeId);
            return Result.error("员工不存在");
        }
        LocalDate entryDate = employee.getEntryDate();

        // 3. 查询原始统计数据
        Map<String, Object> statData = null;
        try {
            if (entryDate != null && entryDate.isBefore(endDate)) {
                statData = attendanceMapper.selectStatisticsByEmployeeAndDateRange(employeeId, startDate, endDate);
            }
        } catch (Exception e) {
            log.error("查询员工考勤统计数据失败，员工ID: {}, 日期范围: {} - {}, 异常: ", employeeId, startDate, endDate, e);
            return Result.error("查询统计数据失败");
        }
        if (statData == null) {
            statData = new HashMap<>(); // 确保 statData 非空，防止后续空指针
            log.info("员工 {} 在 {} 到 {} 期间无考勤统计数据", employeeId, startDate, endDate);
        }

        // 4. 构建 VO
        AttendanceStatisticsVO statsVO = buildStatisticsVO(employee.getDepartmentId(), statData, startDate, endDate, entryDate);
        log.info("成功获取员工 {} 在 {} 到 {} 期间的考勤统计", employeeId, startDate, endDate);
        return Result.success(statsVO);
    }



    /**
     * 构建考勤统计视图对象 (AttendanceStatisticsVO) (私有辅助方法)。
     *
     * 1. 初始化 VO 对象并设置基本信息（员工ID、姓名、日期范围）。
     * 2. 填充部门名称。
     * 3. 从传入的原始统计数据 Map (statData) 中提取各项统计值并设置到 VO。
     * 4. 计算并设置应出勤天数（排除周末）。
     * 5. 计算并设置实际出勤天数（正常+迟到+早退）。
     * 6. 计算并设置出勤率。
     *
     * @param statData   从 Mapper 查询到的原始统计数据 Map
     * @param startDate  统计开始日期
     * @param endDate    统计结束日期
     * @return AttendanceStatisticsVO 组装好的统计视图对象
     */
    private AttendanceStatisticsVO buildStatisticsVO(Integer departmentId, Map<String, Object> statData, LocalDate startDate, LocalDate endDate, LocalDate entryDate) {
        // 1. 初始化并设置基本信息
        AttendanceStatisticsVO vo = new AttendanceStatisticsVO();
        vo.setStartDate(startDate);
        vo.setEndDate(endDate);

        // 2. 填充部门名称
        if (departmentId != null) {
            Department department = departmentMapper.selectById(departmentId);
            if (department != null) {
                vo.setDepartmentName(department.getName());
            }
        }

        // 3. 提取原始统计数据
        vo.setLateTimes(((Number) statData.getOrDefault("lateTimes", 0)).intValue());
        vo.setEarlyTimes(((Number) statData.getOrDefault("earlyTimes", 0)).intValue());
        vo.setAbsentDays(((Number) statData.getOrDefault("absentDays", 0)).intValue());
        double leaveDays = ((Number) statData.getOrDefault("leaveDays", 0.0)).doubleValue();
        double actualWorkDays = ((Number) statData.getOrDefault("actualWorkDays", 0.0)).doubleValue();
        vo.setLeaveDays(leaveDays);
        vo.setActualWorkDays(actualWorkDays);

        // 4. 获取本月工作日天数
        int workDays = DateUtil.getWorkingDaysInMonth(startDate);
        vo.setWorkDays(workDays);

        // 5. 计算应出勤天数
        double shouldWorkDays = 0;
        if (entryDate != null && entryDate.isBefore(endDate)) {
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
                if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
                    shouldWorkDays++;
                }
                currentDate = currentDate.plusDays(1);
            }
        }
        shouldWorkDays -= leaveDays;
        vo.setShouldWorkDays(shouldWorkDays);

        // 6. 计算出勤率
        if (shouldWorkDays > 0) {
            vo.setAttendanceRate(BigDecimal.valueOf(actualWorkDays * 100.0 / shouldWorkDays)
                    .setScale(0, RoundingMode.HALF_UP).doubleValue()); // 使用 RoundingMode
        } else {
            vo.setAttendanceRate(100.0); // 应出勤为0时，算100%
        }

        return vo;
    }




    /**
     * 分页查询考勤记录，并转换为 VO。
     * 
     * 1. 校验分页参数。
     * 2. 使用 PageHelper 开启分页。
     * 3. 调用 Mapper 根据查询 DTO (queryDTO) 查询考勤实体列表。
     * 4. 使用 PageInfo 包装查询结果（实体列表）。
     * 5. 将实体列表 (pageInfoEntity.getList()) 转换为视图对象列表 (List<AttendanceVO>)。
     * 6. 创建新的 PageInfo<AttendanceVO>，复制分页信息，并设置转换后的视图对象列表。
     * 7. 返回包含分页视图对象的结果。
     *
     * @param queryDTO 包含分页和查询条件的 DTO
     * @return Result<PageInfo<AttendanceVO>>
     */
    @Override
    public Result<PageInfo<AttendanceVO>> pageVOs(AttendancePageQueryDTO queryDTO) {
        // 1. 校验分页参数
        if (queryDTO == null || queryDTO.getPageNum() <= 0 || queryDTO.getPageSize() <= 0) {
            log.warn("分页查询考勤VO失败：无效的分页参数");
            return Result.error("无效的分页参数");
        }
        List<String> currentUserRoles = securityUtil.getCurrentUserRoles();
        Employee currentEmployee = securityUtil.getCurrentEmployee();
        if (currentEmployee == null) {
            log.warn("分页查询考勤VO失败：当前用户信息为空");
            return Result.error("无法获取当前登录员工信息，请重新登录");
        }
        if (currentUserRoles.contains(RoleConstant.ROLE_HR)) {
            queryDTO.setHrbpId(currentEmployee.getId());
        } else if (currentUserRoles.contains(RoleConstant.ROLE_LEADER)) {
            queryDTO.setDepartmentId(currentEmployee.getDepartmentId());
        }

        // 2. 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 3. 执行查询
        List<Attendance> attendanceList = attendanceMapper.selectByCondition(queryDTO);

        // 4. 包装实体分页结果
        PageInfo<Attendance> pageInfoEntity = new PageInfo<>(attendanceList);

        // 5. 转换列表为 VO 列表
        List<AttendanceVO> attendanceVOList = pageInfoEntity.getList().stream()
                .map(this::convertToAttendanceVO) // 调用辅助转换方法
                .collect(Collectors.toList());

        // 6. 创建并填充 VO 分页结果
        PageInfo<AttendanceVO> pageInfoVO = new PageInfo<>();
        BeanUtils.copyProperties(pageInfoEntity, pageInfoVO, "list"); // 复制分页信息
        pageInfoVO.setList(attendanceVOList); // 设置 VO 列表

        log.info("分页查询考勤VO成功，条件: {}, 返回 {} 条记录, 总计 {} 条", 
                 queryDTO, attendanceVOList.size(), pageInfoVO.getTotal());
        return Result.success(pageInfoVO);
    }


    private AttendanceVO convertToAttendanceVO(Attendance attendance) {
        if (attendance == null) {
            return null;
        }
        AttendanceVO vo = new AttendanceVO();
        BeanUtils.copyProperties(attendance, vo);

        if (attendance.getEmployeeId() != null) {
            Employee employee = employeeMapper.selectById(attendance.getEmployeeId());
            if (employee != null) {
                vo.setEmployeeName(employee.getName());
                vo.setWorkNo(employee.getWorkNo());
                 Department department = departmentMapper.selectById(employee.getDepartmentId());
                 if (department != null) {
                     vo.setDepartmentName(department.getName());
                 }
            }
        }
        return vo;
    }


    @Override
    public Result<DashboardAttendanceStatsVO> getDashboardAttendanceStats() {
        LocalDate today = LocalDate.now();
        log.info("获取管理员仪表盘今日考勤统计，日期: {}", today);

        try {
            int count = employeeMapper.count();
            Map<String, Object> statsMap = attendanceMapper.countTodayAttendanceStatus(today);

            if (statsMap == null) {
                log.warn("今日考勤统计数据为空，日期: {}", today);
                // 返回一个空的VO或包含0的VO
                return Result.success(new DashboardAttendanceStatsVO());
            }

            DashboardAttendanceStatsVO vo = new DashboardAttendanceStatsVO();
            // 从 Map 中提取数据，注意类型转换和空值处理
            vo.setTotalEmployees(count);
            vo.setNormalCount(((Number) statsMap.getOrDefault("normalCount", 0)).intValue());
            vo.setLateCount(((Number) statsMap.getOrDefault("lateCount", 0)).intValue());
            vo.setEarlyLeaveCount(((Number) statsMap.getOrDefault("earlyLeaveCount", 0)).intValue());
            vo.setAbsentCount(((Number) statsMap.getOrDefault("absentCount", 0)).intValue());
            vo.setLeaveCount(((Number) statsMap.getOrDefault("leaveCount", 0)).intValue());


            return Result.success(vo);

        } catch (Exception e) {
            log.error("获取今日考勤统计失败，日期: {}, 错误: ", today, e);
            return Result.error("获取考勤统计失败");
        }
    }


    @Override
    public Result<AttendanceConfigDTO> getDepartmentAttendanceConfig() {
        Integer currentDepartmentId = securityUtil.getCurrentDepartmentId();
        if (currentDepartmentId == null) {
            log.warn("更新部门考勤配置失败：当前用户部门ID为空");
            return Result.error("无法获取用户部门ID，请重新登录");
        }

        log.debug("获取部门考勤配置，部门ID: {}", currentDepartmentId);
        Department department = departmentMapper.selectById(currentDepartmentId);
        if (department == null) {
            log.warn("获取部门考勤配置失败：部门不存在，部门ID: {}", currentDepartmentId);
            return Result.error("部门不存在");
        }
        AttendanceConfigDTO configDTO = new AttendanceConfigDTO();
        BeanUtils.copyProperties(department, configDTO);
        configDTO.setDepartmentId(currentDepartmentId);

        log.info("获取部门考勤配置成功，部门ID: {}", currentDepartmentId);
        return Result.success(configDTO);
    }


    @Override
    public Result<Void> updateDepartmentAttendanceConfig(AttendanceConfigDTO configDTO) {
        Integer currentDepartmentId = securityUtil.getCurrentDepartmentId();
        if (currentDepartmentId == null) {
            log.warn("更新部门考勤配置失败：当前用户部门ID为空");
            return Result.error("无法获取部门ID，请重新登录");
        }

        log.debug("更新部门考勤配置，部门ID: {}, 数据: {}", currentDepartmentId, configDTO);
        configDTO.setDepartmentId(currentDepartmentId);
        int rows = departmentMapper.updateAttendanceConfig(configDTO);
        if (rows > 0) {
            log.info("更新部门考勤配置成功，部门ID: {}", currentDepartmentId);
            return Result.success();
        } else {
            log.warn("更新部门考勤配置失败，数据库操作未影响行数，部门ID: {}", currentDepartmentId);
            return Result.error("更新失败，请稍后再试");
        }
    }


    @Override
    public Result<AttendanceVO> getTodayAttendanceRecord() {
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("获取今日考勤记录失败：当前用户ID为空");
            return Result.error("无法获取当前用户ID，请检查登陆状态");
        }

        log.debug("获取今日考勤记录，员工ID: {}", currentEmployeeId);
        LocalDate today = LocalDate.now();
        Attendance todayAttendanceRecord = attendanceMapper.selectByEmployeeAndDate(currentEmployeeId, today);
        if (todayAttendanceRecord == null) {
            log.debug("今日无考勤记录，员工ID: {}", currentEmployeeId);
            return Result.success(new AttendanceVO());
        }

        AttendanceVO attendanceVO = new AttendanceVO();
        attendanceVO.setId(todayAttendanceRecord.getId());
        attendanceVO.setEmployeeId(todayAttendanceRecord.getEmployeeId());
        attendanceVO.setDate(today);
        attendanceVO.setClockInTime(todayAttendanceRecord.getClockInTime());
        attendanceVO.setClockInStatus(todayAttendanceRecord.getClockInStatus());
        attendanceVO.setClockOutTime(todayAttendanceRecord.getClockOutTime());
        attendanceVO.setClockOutStatus(todayAttendanceRecord.getClockOutStatus());

        return Result.success(attendanceVO);
    }
}