package cn.edu.ncu.hrms.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户角色参数DTO
 */
@Data
@ApiModel(description = "用户角色参数")
public class UserRoleDTO {

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    private Integer userId;

    @NotEmpty(message = "角色ID列表不能为空")
    @ApiModelProperty(value = "角色ID列表", required = true)
    private List<Integer> roleIds;
} 