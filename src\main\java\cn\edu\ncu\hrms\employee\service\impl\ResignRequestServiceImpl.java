package cn.edu.ncu.hrms.employee.service.impl;

import cn.edu.ncu.hrms.auth.service.UserService;
import cn.edu.ncu.hrms.common.entity.*;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import cn.edu.ncu.hrms.common.mapper.ApprovalProcessMapper;
import cn.edu.ncu.hrms.common.service.ApprovalProcessService;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.ResignApplyDTO;
import cn.edu.ncu.hrms.employee.dto.ResignApproveDTO;
import cn.edu.ncu.hrms.employee.dto.ResignPageQueryDTO;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.mapper.ResignRequestMapper;
import cn.edu.ncu.hrms.employee.service.ResignRequestService;
import cn.edu.ncu.hrms.employee.vo.ResignRequestVO;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import cn.edu.ncu.hrms.system.service.SystemUserService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.edu.ncu.hrms.common.constant.AttendanceConstant.*;
import static java.time.temporal.ChronoUnit.DAYS;

/**
 * 离职申请服务实现类
 */
@Service
@Slf4j
public class ResignRequestServiceImpl implements ResignRequestService {

    @Autowired
    private ResignRequestMapper resignRequestMapper;

    @Autowired
    private ApprovalProcessService approvalProcessService;

    @Autowired
    private ApprovalProcessMapper approvalProcessMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private SystemUserService userService;

    @Autowired
    private SecurityUtil securityUtil;

    /**
     * 员工提交离职申请。
     *
     * @param applyDTO 包含离职日期、原因等信息的 DTO。
     * @return 包含操作结果的 Result 对象。
     */
    @Override
    @Transactional
    public Result<Void> apply(ResignApplyDTO applyDTO) {
        log.debug("开始处理离职申请: {}", applyDTO);

        // 步骤 1: 获取当前申请人的员工ID，并进行合法性校验
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("离职申请失败：无法获取当前用户的员工信息");
            return Result.error("无法获取当前用户信息");
        }
        log.debug("申请人 Employee ID: {}", currentEmployeeId);
        // 校验该员工是否已有待审批或已完成的离职申请
        List<ResignRequest> requests = resignRequestMapper.selectByEmployeeId(currentEmployeeId);
        for (ResignRequest request : requests) {
            if (request.getStatus().equals(STATUS_PENDING) || request.getStatus().equals(STATUS_APPROVED)) {
                log.warn("离职申请失败：该员工已有待审批或已完成的离职申请");
                return Result.error("已有待审批或已完成的离职申请");
            }
        }


        // 步骤 2: 构建 ResignRequest 实体
        ResignRequest resignRequest = new ResignRequest();
        BeanUtils.copyProperties(applyDTO, resignRequest);
        resignRequest.setEmployeeId(currentEmployeeId);
        resignRequest.setStatus(STATUS_PENDING); // 设置初始状态为待审批

        // 步骤 3: 保存离职申请到数据库
        int rows = resignRequestMapper.insert(resignRequest);
        if (rows > 0) {
            log.debug("离职申请提交成功, Employee ID: {}, Request ID: {}", currentEmployeeId, resignRequest.getId());
            // 步骤 4: 初始化审批流程节点
            List<ApprovalProcess> approvalProcesses = initResignApprovalProcess(resignRequest.getId(), currentEmployeeId);
            if (approvalProcesses == null || approvalProcesses.isEmpty()) {
                log.error("离职审批流程初始化失败：无对应审批人");
                return Result.error("未配置审批人，请联系管理员");
            }
            int count = approvalProcessMapper.batchInsert(approvalProcesses);
            if (count != approvalProcesses.size()) {
                log.error("离职审批流程初始化失败：批量插入审批流程节点失败");
                return Result.error("审批流程初始化失败，请稍后重试");
            }
            // 发送审批通知消息给第一个审批人
            approvalProcessService.sendApprovalNotification(approvalProcesses.get(0).getApproverId(), resignRequest.getId(), REQUEST_TYPE_RESIGN);
            return Result.success();
        } else {
            log.error("离职申请提交失败：数据库插入操作未成功, Employee ID: {}", currentEmployeeId);
            return Result.error("申请失败，请稍后重试");
        }
    }

    /**
     * 初始化离职审批流程
     *
     * @param requestId
     */
    private List<ApprovalProcess> initResignApprovalProcess(Integer requestId, Integer employeeId) {
        // 离职需要三级审批：+1，+2，HR，如果没有上级领导，则只需要HR审批
        List<ApprovalProcess> processList = new LinkedList<>();
        Employee employee = employeeMapper.selectById(employeeId);
        Integer departmentId = employee.getDepartmentId();
        if (departmentId != null) {
            Department department = departmentMapper.selectById(departmentId);
            Integer leaderId = department.getLeaderId();
            // 获取一级审批人（如果员工是部门领导，则一级审批人为+1部门领导，否则是本部门领导）
            Integer firstApproverId = getFirstApproverId(employeeId, leaderId, department);
            if (firstApproverId != null) {
                addApprovalProcess(processList, requestId, firstApproverId, (byte) 1, STATUS_PENDING);
                // 获取二级审批人（如果员工是部门领导，则二级审批人为+2部门领导，否则是+1部门领导）
                Integer secondApproverId = getSecondApproverId(employeeId.equals(leaderId), department.getParentId());
                if (secondApproverId != null) {
                    addApprovalProcess(processList, requestId, secondApproverId, (byte) 2, STATUS_NEW);
                }
            }
        }
        addHRBPApprovalProcess(processList, requestId, employeeId);
        return processList;
    }


    private Integer getFirstApproverId(Integer employeeId, Integer leaderId, Department department) {
        if (employeeId.equals(leaderId)) {
            // 若员工是本部门领导，则一级审批人是上级部门领导
            Department parentDepartment = getParentDepartment(department.getParentId());
            return parentDepartment != null ? parentDepartment.getLeaderId() : null;
        }
        // 否则一级审批人是本部门领导
        return leaderId;
    }

    private Integer getSecondApproverId(Boolean isLeader, Integer parentDeptId) {
        // 获取+1部门
        Department department1 = departmentMapper.selectById(parentDeptId);
        if (department1 == null) return null;
        if (isLeader) {
            // 若员工是本部门领导，则二级审批人是+2部门领导
            Integer parentId = department1.getParentId();
            if (parentId != null) {
                Department department2 = departmentMapper.selectById(parentId);
                return department2 != null ? department2.getLeaderId() : null;
            }
        } else {
            // 否则二级审批人为+1部门领导
            return department1.getLeaderId();
        }

        return null;
    }

    private Department getParentDepartment(Integer parentDeptId) {
        return parentDeptId != null ? departmentMapper.selectById(parentDeptId) : null;
    }

    private void addApprovalProcess(List<ApprovalProcess> processList, Integer requestId, Integer approverId, byte stepOrder, String status) {
        ApprovalProcess process = new ApprovalProcess();
        process.setRequestId(requestId);
        process.setApproverId(approverId);
        process.setStepOrder(stepOrder);
        process.setType(REQUEST_TYPE_RESIGN);
        process.setStatus(status);
        if (status.equals(STATUS_PENDING)) {
            process.setExpireTime(LocalDateTime.now().plus(1, DAYS));
        }
        processList.add(process);
    }

    private void addHRBPApprovalProcess(List<ApprovalProcess> processList, Integer requestId, Integer employeeId) {
        Integer hrbpId = employeeMapper.getHRBPId(employeeId);
        if (hrbpId == null) {
            log.error("员工 {} 未配置 HRBP", employeeId);
        } else {
            byte stepOrder = (byte) (processList.size() + 1);
            String status = processList.isEmpty() ? STATUS_PENDING : STATUS_NEW;
            addApprovalProcess(processList, requestId, hrbpId, stepOrder, status);
        }
    }


    /**
     * 审批离职申请。
     *
     * @param requestId  离职申请的 ID。
     * @param approveDTO 包含审批结果（同意/拒绝）和审批备注的 DTO。
     * @return 包含操作结果的 Result 对象。
     */
    @Override
    @Transactional
    public Result<Void> approve(Integer requestId, ResignApproveDTO approveDTO) {
        log.debug("开始审批离职申请, Request ID: {}, 审批信息: {}", requestId, approveDTO);
        if (requestId == null) {
            log.warn("审批离职申请失败：申请 ID 为空");
            return Result.error("申请ID不能为空");
        }

        // 步骤 1: 获取当前审批人Employee ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("审批离职申请失败：无法获取当前审批人信息");
            return Result.error("系统异常，请联系管理员");
        }
        log.debug("审批人 Employee ID: {}", currentEmployeeId);

        // 步骤 2: 校验审批状态值是否合法
        String status = approveDTO.getStatus();
        if (!STATUS_APPROVED.equals(status) && !STATUS_REJECTED.equals(status)) {
            log.warn("审批离职申请失败：无效的审批状态 '{}'", status);
            return Result.error("无效的审批操作，必须 'APPROVED' 或 'REJECTED'");
        }

        // 步骤 3: 查询有效的离职申请记录
        ResignRequest resignRequest = resignRequestMapper.selectById(requestId);
        if (resignRequest == null || resignRequest.getDelFlag()) {
            log.warn("审批离职申请失败：申请不存在或已被删除, ID: {}", requestId);
            return Result.error("离职申请不存在或已被删除");
        }

        // 步骤 4: 检查申请当前状态是否为待审批 (防止重复处理)
        if (!STATUS_PENDING.equals(resignRequest.getStatus())) {
            log.warn("审批离职申请失败：申请状态非待审批, 当前状态: '{}'", resignRequest.getStatus());
            return Result.error("该申请已处理，请勿重复操作");
        }

        // 步骤 5: 获取离职申请的当前审批节点ID
        Integer processId = approvalProcessMapper.selectCurrentPending(requestId, REQUEST_TYPE_RESIGN);
        log.debug("离职申请ID: {}  当前审批节点 ID: {}", requestId, processId);

        // 步骤 6: 进行节点审批操作，并处理后续流程
        try {
            approvalProcessService.approve(processId, currentEmployeeId, status, approveDTO.getComment(), REQUEST_TYPE_RESIGN);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }


        // TODO 离职审批通过后，由定时任务（在期望离职日期）执行离职流程：设置员工状态为离职并删除用户账号。

        return Result.success();
    }

    /**
     * 员工撤回自己提交的、且处于 "待审批" 状态的离职申请。
     *
     * @param requestId 离职申请的 ID。
     * @return 包含操作结果的 Result 对象。
     */
    @Override
    @Transactional
    public Result<Void> cancel(Integer requestId) {
        log.debug("开始撤回离职申请, Request ID: {}", requestId);
        if (requestId == null) {
            log.warn("撤回离职申请失败：申请 ID 为空");
            return Result.error("申请ID不能为空");
        }

        // 步骤 1: 获取当前操作用户的员工 ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("撤回离职申请失败：无法获取当前用户信息");
            return Result.error("无法获取当前用户信息");
        }
        log.debug("Operator Employee ID: {}", currentEmployeeId);

        // 步骤 2: 查询有效的离职申请记录
        ResignRequest resignRequest = resignRequestMapper.selectById(requestId);
        if (resignRequest == null || resignRequest.getDelFlag()) {
            log.warn("撤回离职申请失败：申请不存在或已被删除, ID: {}", requestId);
            return Result.error("离职申请不存在或已被删除");
        }

        // 步骤 3: 校验是否为申请人本人操作
        if (!currentEmployeeId.equals(resignRequest.getEmployeeId())) {
            log.warn("撤回离职申请失败：非申请人操作, Request ID: {}, Applicant Employee ID: {}, Operator Employee ID: {}",
                    requestId, resignRequest.getEmployeeId(), currentEmployeeId);
            return Result.error("只能撤回自己的离职申请");
        }

        // 步骤 4: 检查申请状态是否允许撤回 (仅 PENDING 状态)
        if (!STATUS_PENDING.equals(resignRequest.getStatus())) {
            log.warn("撤回离职申请失败：当前状态 '{}' 不允许撤回, Request ID: {}", resignRequest.getStatus(), requestId);
            return Result.error("只有待审批状态的申请才能撤回");
        }

        // 步骤 5: 更新状态为 CANCELLED
        resignRequest.setStatus(STATUS_CANCELED);
        resignRequest.setUpdateTime(LocalDateTime.now());

        // 步骤 6: 更新数据库
        int rows = resignRequestMapper.update(resignRequest);
        if (rows > 0) {
            Boolean success = approvalProcessService.cancelApprovalProcessByRequestId(requestId, REQUEST_TYPE_RESIGN);
            if (!success) {
                log.error("离职申请 {} 已撤回，但审批流程实例撤回逻辑尚未实现！", requestId);
                return Result.error("撤回失败，请稍后重试");
            }
            log.debug("离职申请撤回成功, Request ID: {}", requestId);
            return Result.success();
        } else {
            log.error("撤回离职申请失败：数据库更新操作未成功, Request ID: {}", requestId);
            return Result.error("撤回失败，请稍后重试");
        }
    }

    /**
     * 根据 ID 查询离职申请的详细信息，并转换为 VO 对象。
     *
     * @param requestId 离职申请的 ID。
     * @return 包含详细信息的 VO 对象或错误结果。
     */
    @Override
    public Result<ResignRequestVO> getVOById(Integer requestId) {
        log.debug("查询离职申请详情 (VO), ID: {}", requestId);
        if (requestId == null) {
            return Result.error("ID 不能为空");
        }

        ResignRequest resignRequest = resignRequestMapper.selectById(requestId);
        if (resignRequest == null) {
            log.debug("查询离职申请详情失败：申请不存在或已被删除, ID: {}", requestId);
            return Result.error("离职申请不存在或已被删除");
        }

        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("查询离职申请详情失败：无法获取当前用户信息");
            return Result.error("无法获取当前用户信息，请检查登录状态");
        }

        ResignRequestVO vo = convertToResignRequestVO(resignRequest, currentEmployeeId);
        log.debug("查询离职申请详情成功, ID: {}", requestId);
        return Result.success(vo);
    }

    /**
     * 分页查询离职申请列表，并转换为 VO 对象。
     *
     * @param queryDTO 查询 DTO
     * @return 分页后的离职申请 VO 列表 (PageInfo<ResignRequestVO>)。
     */
    @Override
    public PageInfo<ResignRequestVO> pageVOs(ResignPageQueryDTO queryDTO) {
        log.debug("分页查询离职申请, 条件: {}", queryDTO);
        // 获取当前登录用户的员工ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            log.warn("分页查询离职申请失败：无法获取当前用户信息");
            return null;
        }
        queryDTO.setCurrentEmployeeId(currentEmployeeId);

        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<ResignRequest> list = resignRequestMapper.selectByCondition(queryDTO);
        PageInfo<ResignRequest> pageInfoSrc = new PageInfo<>(list);

        List<ResignRequestVO> voList = list.stream()
                .map(request -> convertToResignRequestVO(request, currentEmployeeId))
                .collect(Collectors.toList());

        PageInfo<ResignRequestVO> pageInfoVO = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfoSrc, pageInfoVO, "list");

        return pageInfoVO;
    }

    /**
     * 根据离职申请 ID 获取申请人的员工 ID。
     *
     * @param requestId 离职申请 ID。
     * @return 对应的员工 ID；如果申请不存在，则返回 null。
     */
    @Override
    public Integer getEmployeeIdByRequestId(Integer requestId) {
        if (requestId == null) return null;
        ResignRequest req = resignRequestMapper.selectById(requestId);
        Integer employeeId = (req != null) ? req.getEmployeeId() : null;
        log.trace("根据 Request ID {} 查询到 Employee ID: {}", requestId, employeeId);
        return employeeId;
    }


    @Override
    public Result<List<ResignRequest>> getSelfResignList() {
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        log.debug("查询员工 {} 的离职申请列表", currentEmployeeId);
        List<ResignRequest> list = resignRequestMapper.selectByEmployeeId(currentEmployeeId);
        return Result.success(list);
    }


    // --- 私有辅助方法 ---

    /**
     * 将 ResignRequest 实体转换为 ResignRequestVO 视图对象。
     *
     * @param resignRequest 离职申请实体对象。
     * @return 转换后的 VO 对象；如果输入为 null 则返回 null。
     */
    private ResignRequestVO convertToResignRequestVO(ResignRequest resignRequest, Integer currentEmployeeId) {
        if (resignRequest == null) return null;
        ResignRequestVO vo = new ResignRequestVO();
        BeanUtils.copyProperties(resignRequest, vo);

        // 填充关联信息
        if (resignRequest.getEmployeeId() != null) {
            Employee employee = employeeMapper.selectById(resignRequest.getEmployeeId());
            if (employee != null) {
                vo.setEmployeeId(employee.getId());
                vo.setEmployeeName(employee.getName());
                vo.setEmployeeWorkNo(employee.getWorkNo());
                vo.setDepartmentId(employee.getDepartmentId());
                if (employee.getDepartmentId() != null) {
                    Department department = departmentMapper.selectById(employee.getDepartmentId());
                    if (department != null) {
                        vo.setDepartmentName(department.getName());
                    }
                }
                if(employee.getPositionId() != null) {
                    Position position = positionMapper.selectById(employee.getPositionId());
                    if (position != null) {
                        vo.setPositionName(position.getName());
                    }
                }
            }
        }
        // 填充状态名称
        vo.setStatusName(getStatusName(resignRequest.getStatus()));

        // 查询申请当前是否待我审批
        vo.setIsPendingByMe(approvalProcessMapper.selectIsPendingByMe(resignRequest.getId(), REQUEST_TYPE_RESIGN, currentEmployeeId));

        return vo;
    }


    /**
     * 每天 18:00 执行当日离职员工流程
     * Cron: 秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 18 * * ?")
    public void processDailyResign() {
        try {
            // 获取今日需要处理的离职申请
            List<ResignRequest> requests = resignRequestMapper.selectResignationsForToday();
            log.info("开始处理今日离职申请, 共 {} 个", requests.size());
            for (ResignRequest request : requests) {
                handleSingleResignation(request);
            }
        } catch (Exception e) {
            log.error("处理离职申请时发生异常", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleSingleResignation(ResignRequest request) {
        Integer employeeId = request.getEmployeeId();
        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) return;

        // 更新员工状态为离职
        employee.setStatus(false);
        int rows = employeeMapper.update(employee);
        if (rows <= 0) {
            log.error("更新员工 {} 离职状态失败", employeeId);
            return;
        }

        // 删除用户账号
        User user = userService.findUserByEmployeeId(employeeId);
        if (user != null) {
            Result result = userService.deleteUser(user.getId());
            if (Result.ERROR_CODE == result.getCode()) {
                log.error("员工 {} 关联的用户 {} 删除失败: {}", employeeId, user.getId(), result.getMsg());
            } else {
                log.info("员工 {} 关联的用户 {} 已成功删除", employeeId, user.getId());
            }
        } else {
            log.warn("未找到员工 {} 的关联用户", employeeId);
        }
    }

}
