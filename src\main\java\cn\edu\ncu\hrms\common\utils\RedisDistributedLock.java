package cn.edu.ncu.hrms.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RedisDistributedLock {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String LOCK_PREFIX = "lock:";

    /**
     * 尝试获取锁
     * @param key 锁键
     * @param expireTime 过期时间（秒）
     * @return 是否成功
     */
    public boolean tryLock(String key, long expireTime) {
        String lockKey = LOCK_PREFIX + key;
        return Boolean.TRUE.equals(redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "locked", expireTime, TimeUnit.SECONDS));
    }

    /**
     * 释放锁
     */
    public void unlock(String key) {
        redisTemplate.delete(LOCK_PREFIX + key);
    }
}
