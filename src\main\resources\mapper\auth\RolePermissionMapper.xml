<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.edu.ncu.hrms.auth.mapper.RolePermissionMapper">

    <!-- 插入单条角色权限关联关系 -->
    <insert id="insert" parameterType="RolePermission">
        INSERT INTO role_permission (
            role_id, permission_id, create_time, update_time, del_flag
        ) VALUES (
            #{roleId}, #{permissionId}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 根据角色ID逻辑删除其所有权限关联 -->
    <update id="deleteByRoleId" parameterType="java.lang.Integer">
        UPDATE role_permission
        SET del_flag = null, update_time = NOW()
        WHERE role_id = #{roleId}
    </update>

    <!-- 根据权限ID逻辑删除其所有角色关联 -->
    <update id="deleteByPermissionId" parameterType="java.lang.Integer">
        UPDATE role_permission
        SET del_flag = null, update_time = NOW()
        WHERE permission_id = #{permissionId}
    </update>

    <!-- 批量插入角色权限关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO role_permission (
            role_id, permission_id, create_time, update_time, del_flag
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.permissionId}, NOW(), NOW(), 0)
        </foreach>
    </insert>
    
    <!-- 查询指定角色和权限的关联关系（用于判重或业务校验） -->
    <select id="selectByRoleIdAndPermissionId" resultType="RolePermission">
        SELECT 
            role_id, permission_id, create_time, update_time, del_flag
        FROM 
            role_permission
        WHERE 
            role_id = #{roleId}
            AND permission_id = #{permissionId}
            AND del_flag = 0
    </select>

    <!-- 根据权限ID统计关联的未删除角色数量 -->
    <select id="countByPermissionId" resultType="int" parameterType="java.lang.Integer">
        SELECT COUNT(*)
        FROM role_permission
        WHERE permission_id = #{permissionId}
          AND del_flag = 0
    </select>

</mapper> 