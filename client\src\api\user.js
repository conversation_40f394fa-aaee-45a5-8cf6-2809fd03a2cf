import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @returns {Promise} 返回token
 */
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @returns {Promise} 返回结果
 */
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise} 返回用户信息
 */
export function getUserInfo() {
  return request({
    url: '/auth/info',
    method: 'get'
  })
}

/**
 * 修改密码
 * @param {Object} data - 密码信息
 * @returns {Promise} 返回结果
 */
export function changePassword(data) {
  return request({
    url: '/auth/password',
    method: 'put',
    data
  })
}

/**
 * 用户登出
 * @returns {Promise} 返回结果
 */
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

/**
 * 修改当前用户的用户名
 * @param {Object} data - 用户名信息
 * @returns {Promise} 返回结果
 */
export function changeUsername(userId, data) {
  return request({
    url: `/system/user/${userId}`,
    method: 'put',
    data
  })
}
