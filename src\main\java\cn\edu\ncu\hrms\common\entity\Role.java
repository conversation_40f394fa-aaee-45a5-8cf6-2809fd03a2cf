package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("角色信息")
public class Role {
    @ApiModelProperty(value = "角色ID", required = true, example = "1")
    private Integer id;

    @ApiModelProperty(value = "角色名称", required = true, example = "管理员")
    private String name;

    @ApiModelProperty(value = "角色标识", required = true, example = "ROLE_admin")
    private String roleKey;

    @ApiModelProperty(value = "角色描述", example = "系统超级管理员")
    private String description;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
