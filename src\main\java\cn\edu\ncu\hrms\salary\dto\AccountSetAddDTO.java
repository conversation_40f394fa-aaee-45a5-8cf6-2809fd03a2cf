package cn.edu.ncu.hrms.salary.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 新增薪资账套 DTO
 */
@Data
@ApiModel("新增薪资账套数据")
public class AccountSetAddDTO {

    @ApiModelProperty(value = "账套名称", required = true)
    @NotBlank(message = "账套名称不能为空")
    private String name;

    @ApiModelProperty(value = "月基本工资", required = true)
    @NotNull(message = "月基本工资不能为空")
    @DecimalMin(value = "0.0", message = "月基本工资不能为负数")
    @DecimalMax(value = "100000.0", message = "月基本工资不能超过10万")
    private BigDecimal baseSalary;

    @ApiModelProperty(value = "绩效比例", required = true)
    @NotNull(message = "绩效比例不能为空")
    @DecimalMin(value = "0.0", message = "绩效比例不能为负数")
    @DecimalMax(value = "100.0", message = "绩效比例不能超过100%")
    private BigDecimal performanceRate;

    @ApiModelProperty(value = "社保比例", required = true)
    @NotNull(message = "社保比例不能为空")
    @DecimalMin(value = "0.0", message = "社保比例不能为负数")
    @DecimalMax(value = "30.0", message = "社保比例不能超过30%")
    private BigDecimal insuranceRate;

} 