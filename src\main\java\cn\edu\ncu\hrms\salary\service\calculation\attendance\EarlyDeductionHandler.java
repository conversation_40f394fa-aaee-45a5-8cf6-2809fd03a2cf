package cn.edu.ncu.hrms.salary.service.calculation.attendance;

import java.math.BigDecimal;

/**
 * 考勤扣款处理链 - 早退扣款处理器
 */
public class EarlyDeductionHandler implements DeductionHandler {
    private DeductionHandler next; // 链中的下一个处理器

    @Override
    public void setNext(DeductionHandler next) {
        this.next = next;
    }

    /**
     * 处理早退扣款。
     * 计算当前早退次数的扣款，并将其累加到之前的扣款总额上。
     * 然后将请求传递给链中的下一个处理器（如果存在）。
     *
     * @param context 包含考勤统计信息的上下文对象
     * @param currentDeduction 到此处理器为止已累积的扣款金额
     * @return 经过此处理器及后续处理器计算后的总扣款金额
     */
    @Override
    public BigDecimal handle(DeductionContext context, BigDecimal currentDeduction) {
        // 1. 计算早退扣款：早退次数 * 单次早退扣款
        BigDecimal total = currentDeduction.add(context.getEarlyDeduction().multiply(BigDecimal.valueOf(context.getEarlyTimes())));
        // 2. 调用下一个处理器
        if (next != null) {
            return next.handle(context, total);
        }
        // 3. 如果是最后一个处理器，返回最终结果
        return total;
    }
}
