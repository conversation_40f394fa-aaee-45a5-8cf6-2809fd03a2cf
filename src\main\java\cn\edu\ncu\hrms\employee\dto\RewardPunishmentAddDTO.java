package cn.edu.ncu.hrms.employee.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel(description = "新增奖惩信息数据传输对象")
public class RewardPunishmentAddDTO {

    @ApiModelProperty(value = "操作人员工ID", example = "1")
    private Integer operatorId;

    @ApiModelProperty(value = "员工ID", example = "1001")
    private Integer employeeId;

    @ApiModelProperty(value = "奖惩类型（0奖励 1惩罚）", allowableValues = "0,1", example = "0")
    private Integer type;

    @ApiModelProperty(value = "奖惩标题", example = "优秀员工奖励")
    private String title;

    @ApiModelProperty(value = "奖惩内容", example = "因表现优异，给予奖金奖励")
    private String content;

    @ApiModelProperty(value = "奖惩金额", example = "500.00")
    @DecimalMax(value = "10000.00", message = "金额不能超过10000元")
    private BigDecimal amount;

    @ApiModelProperty(value = "奖惩日期", example = "2023-10-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate rewardDate;

    @ApiModelProperty(value = "备注", example = "无")
    private String remark;
}