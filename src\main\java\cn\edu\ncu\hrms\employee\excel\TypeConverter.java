package cn.edu.ncu.hrms.employee.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * 奖惩类型转换器
 */
public class TypeConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) {
        String value = context.getReadCellData().getStringValue();
        if ("奖励".equals(value)) {
            return 0;
        } else if ("惩罚".equals(value)) {
            return 1;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        Integer value = context.getValue();
        String textValue = null;
        if (value != null) {
            if (value == 0) {
                textValue = "奖励";
            } else if (value == 1) {
                textValue = "惩罚";
            }
        }
        return new WriteCellData<>(textValue);
    }
} 