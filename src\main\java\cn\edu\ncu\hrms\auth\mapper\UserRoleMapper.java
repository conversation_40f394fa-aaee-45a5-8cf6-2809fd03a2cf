package cn.edu.ncu.hrms.auth.mapper;

import cn.edu.ncu.hrms.common.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关系Mapper接口
 */
@Mapper
public interface UserRoleMapper {

    /**
     * 添加用户角色关系
     * 
     * @param userRole 用户角色关系
     * @return 影响行数
     */
    int insert(UserRole userRole);

    /**
     * 根据用户ID删除用户角色关系
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Integer userId);

    /**
     * 根据角色ID删除用户角色关系
     * 
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(Integer roleId);

    /**
     * 批量添加用户角色关系
     * 
     * @param userRoles 用户角色关系列表
     * @return 影响行数
     */
    int batchInsert(List<UserRole> userRoles);
    
    /**
     * 根据用户ID和角色ID查询用户角色关系
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 用户角色关系
     */
    UserRole selectByUserIdAndRoleId(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    /**
     * 根据角色ID统计关联的用户数量
     * 
     * @param roleId 角色ID
     * @return 关联的用户数量
     */
    int countByRoleId(Integer roleId);

} 