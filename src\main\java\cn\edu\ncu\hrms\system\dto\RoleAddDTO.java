package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 创建角色数据传输对象 (DTO)
 */
@Data
@ApiModel(value="RoleAddDTO", description="创建角色数据传输对象")
public class RoleAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "角色标识", required = true, notes = "例如: admin, employee")
    @NotBlank(message = "角色标识不能为空")
    @Size(max = 50, message = "角色标识长度不能超过50个字符")
    private String roleKey;

    @ApiModelProperty(value = "角色描述")
    @Size(max = 255, message = "角色描述长度不能超过255个字符")
    private String description;

} 