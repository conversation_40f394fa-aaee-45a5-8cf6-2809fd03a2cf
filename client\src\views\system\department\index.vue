<template>
  <div class="app-container">
    <div class="table-container">
      <div class="table-header">
        <div class="left">
          <h3>部门管理</h3>
        </div>
        <div class="right">
          <el-button type="success" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="departmentList"
        row-key="id"
        border
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="name" label="部门名称" min-width="140" />
        <el-table-column prop="id" label="部门ID" min-width="60"  />
        <el-table-column prop="leaderName" label="部门领导" min-width="140"  />
        <el-table-column prop="createTime" label="创建时间" min-width="180" align="center" />
        <el-table-column prop="updateTime" label="更新时间" min-width="180" align="center" />
        <el-table-column label="操作" align="center" min-width="300">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
            >详情</el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
            >新增</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级部门" prop="parentId">
          <div class="parent-department-container">
            <el-tree-select
              v-model="form.parentId"
              :data="processedDepartmentOptions"
              :props="{ value: 'id', label: 'name', children: 'children', disabled: 'disabled' }"
              value-key="id"
              placeholder="请选择上级部门"
              check-strictly
              clearable
            />
            <el-button
              v-if="form.parentId"
              type="text"
              icon="el-icon-close"
              class="clear-parent-btn"
              @click="clearParentDepartment"
            ></el-button>
          </div>
          <div class="form-tip">顶级部门可不选，不能选择当前部门及其子部门作为上级部门</div>
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门领导" prop="leaderId">
          <div class="leader-select-container">
            <div class="department-tree">
              <el-tree
                :data="departmentOptions"
                :props="{ label: 'name', children: 'children' }"
                node-key="id"
                highlight-current
                @node-click="handleDepartmentSelect"
              ></el-tree>
            </div>
            <div class="employee-list">
              <div class="employee-list-header">
                <span>{{ selectedDepartmentName || '请选择部门' }}</span>
                <div v-if="loadingEmployees" class="loading-indicator">
                  <i class="el-icon-loading"></i> 加载中...
                </div>
              </div>
              <el-table
                v-loading="loadingEmployees"
                :data="departmentEmployees"
                height="300"
                @row-click="handleEmployeeSelect"
                highlight-current-row
                :row-class-name="tableRowClassName"
                empty-text="该部门暂无员工"
              >
                <el-table-column prop="workNo" label="工号" width="120"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
              </el-table>
            </div>
          </div>
          <div class="selected-employee" v-if="selectedEmployee">
            <span class="label">已选择:</span>
            <el-tag size="medium" type="success">
              <i class="el-icon-user"></i>
              {{ selectedEmployee.name }}
              <span class="work-no" v-if="selectedEmployee.workNo">({{ selectedEmployee.workNo }})</span>
            </el-tag>
            <el-button type="text" icon="el-icon-close" class="clear-btn" @click="clearSelectedEmployee"></el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 部门详情对话框 -->
    <el-dialog title="部门详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="部门ID">{{ detail.id }}</el-descriptions-item>
        <el-descriptions-item label="部门名称">{{ detail.name }}</el-descriptions-item>
        <el-descriptions-item label="上级部门ID">{{ detail.parentId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="部门领导">{{ detail.leaderName || '无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detail.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="上班打卡时间">
          {{ detail.clockInStartTime || '未设置' }} - {{ detail.clockInEndTime || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="下班打卡时间">
          {{ detail.clockOutStartTime || '未设置' }} - {{ detail.clockOutEndTime || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="迟到阈值">{{ detail.lateThreshold || '未设置' }} 分钟</el-descriptions-item>
        <el-descriptions-item label="迟到扣款">{{ detail.lateDeduction || '未设置' }} 元</el-descriptions-item>
        <el-descriptions-item label="早退阈值">{{ detail.earlyThreshold || '未设置' }} 分钟</el-descriptions-item>
        <el-descriptions-item label="早退扣款">{{ detail.earlyDeduction || '未设置' }} 元</el-descriptions-item>
        <el-descriptions-item label="打卡范围">{{ detail.clockRadius || '未设置' }} 米</el-descriptions-item>
        <el-descriptions-item label="打卡地点" :span="2">
          <span v-if="detail.clockLocation">{{ detail.clockLocation }}</span>
          <span v-else>未设置</span>
          <el-button
            v-if="detail.clockLocation"
            type="text"
            icon="el-icon-location"
            @click="showMap = true"
          >查看地图</el-button>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 打卡地点地图 -->
      <div v-if="showMap && detail.clockLocation" class="map-container">
        <div class="map-header">
          <h4>打卡地点地图</h4>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-close"
            @click="showMap = false"
          >关闭地图</el-button>
        </div>
        <div id="mapContainer" class="map"></div>
        <div class="map-address" v-if="locationAddress">
          <span>{{ locationAddress }}</span>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepartmentTree, getDepartment, addDepartment, updateDepartment, deleteDepartment, getEmployeesByDepartmentId } from '@/api/system/department'
import { listEmployee, getEmployee } from '@/api/employee'
// 高德地图API将通过CDN动态加载

// 高德地图API类型在window对象上
/* global AMap */

export default {
  name: 'Department',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 部门表格数据
      departmentList: [],
      // 部门树选项
      departmentOptions: [],
      // 部门领导选项
      leaderOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 部门详情数据
      detail: {},
      // 地图实例
      map: null,
      // 是否显示地图
      showMap: false,
      // 地点地址
      locationAddress: '',
      // 选中的部门名称
      selectedDepartmentName: '',
      // 选中的部门ID
      selectedDepartmentId: null,
      // 部门员工列表
      departmentEmployees: [],
      // 员工列表加载状态
      loadingEmployees: false,
      // 选中的员工
      selectedEmployee: null,
      // 表单参数
      form: {
        id: undefined,
        parentId: undefined,
        name: undefined,
        leaderId: undefined
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '部门名称不能为空', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '部门名称不能包含空格', trigger: 'blur' }
        ]
      },
      // 禁用的部门ID列表（当前部门及其子部门）
      disabledDepartmentIds: []
    }
  },
  computed: {
    // 处理部门选项，禁用当前部门及其子部门
    processedDepartmentOptions() {
      if (!this.departmentOptions || this.departmentOptions.length === 0) {
        return []
      }

      // 深拷贝部门选项，避免修改原始数据
      const clonedOptions = JSON.parse(JSON.stringify(this.departmentOptions))

      // 如果没有禁用的部门ID，直接返回原始选项
      if (!this.disabledDepartmentIds || this.disabledDepartmentIds.length === 0) {
        return clonedOptions
      }

      // 递归处理部门选项，设置disabled属性
      const processOptions = (options) => {
        return options.map(option => {
          // 如果当前部门ID在禁用列表中，设置disabled为true
          if (this.disabledDepartmentIds.includes(option.id)) {
            option.disabled = true
          }

          // 如果有子部门，递归处理
          if (option.children && option.children.length > 0) {
            option.children = processOptions(option.children)
          }

          return option
        })
      }

      return processOptions(clonedOptions)
    }
  },

  created() {
    this.getList()
    this.getLeaderOptions()
  },

  beforeDestroy() {
    // 销毁地图实例
    this.destroyMap()
  },

  watch: {
    // 监听详情弹窗关闭，销毁地图
    detailOpen(val) {
      if (!val) {
        this.destroyMap()
        this.showMap = false
        this.locationAddress = ''
      }
    },
    // 监听地图显示状态
    showMap(val) {
      if (val && this.detail.clockLocation) {
        this.$nextTick(() => {
          this.initMap()
        })
      } else if (!val) {
        this.destroyMap()
      }
    }
  },
  methods: {
    /** 查询部门列表 */
    getList() {
      this.loading = true
      getDepartmentTree().then(response => {
        if (response && response.code === 1) {
          this.departmentList = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取部门列表失败')
          this.departmentList = []
        }
        this.loading = false
      }).catch(error => {
        this.$message.error(error.message || '获取部门列表失败')
        this.departmentList = []
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 */
    getTreeSelect() {
      getDepartmentTree().then(response => {
        if (response && response.code === 1) {
          this.departmentOptions = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取部门树失败')
          this.departmentOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取部门树失败')
        this.departmentOptions = []
      })
    },
    /** 获取部门领导选项 */
    getLeaderOptions() {
      listEmployee().then(response => {
        if (response && response.code === 1) {
          this.leaderOptions = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取员工列表失败')
          this.leaderOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取员工列表失败')
        this.leaderOptions = []
      })
    },

    /** 处理部门选择 */
    handleDepartmentSelect(data) {
      this.selectedDepartmentId = data.id
      this.selectedDepartmentName = data.name
      this.loadDepartmentEmployees(data.id)
    },

    /** 加载部门员工 */
    loadDepartmentEmployees(departmentId) {
      this.loadingEmployees = true
      this.departmentEmployees = []

      getEmployeesByDepartmentId(departmentId).then(response => {
        this.loadingEmployees = false
        if (response && response.code === 1) {
          this.departmentEmployees = response.data || []

          // 如果有部门领导ID，尝试在新加载的员工列表中查找并选中
          if (this.form.leaderId) {
            const leader = this.departmentEmployees.find(emp => emp.id === this.form.leaderId)
            if (leader) {
              this.selectedEmployee = leader
            }
          }

          if (this.departmentEmployees.length === 0) {
            this.$message.info('该部门下暂无员工')
          }
        } else {
          this.$message.error(response && response.msg || '获取部门员工失败')
        }
      }).catch(error => {
        this.loadingEmployees = false
        this.$message.error(error.message || '获取部门员工失败')
      })
    },

    /** 处理员工选择 */
    handleEmployeeSelect(row) {
      this.selectedEmployee = row
      this.form.leaderId = row.id
    },

    /** 清除选中的员工 */
    clearSelectedEmployee() {
      this.selectedEmployee = null
      this.form.leaderId = undefined
    },

    /** 清除上级部门 */
    clearParentDepartment() {
      this.form.parentId = undefined
    },

    /** 设置表格行的样式 */
    tableRowClassName({ row }) {
      // 如果当前行是选中的员工，则添加高亮样式
      if (this.selectedEmployee && row.id === this.selectedEmployee.id) {
        return 'selected-row'
      }
      return ''
    },
    /** 获取禁用的部门ID列表（当前部门及其所有子部门） */
    getDisabledDepartmentIds(id, departmentList) {
      if (!id || !departmentList || departmentList.length === 0) {
        return []
      }

      const disabledIds = [id] // 当前部门ID

      // 递归查找所有子部门
      const findChildDepartments = (parentId, departments) => {
        departments.forEach(dept => {
          if (dept.parentId === parentId) {
            disabledIds.push(dept.id)
            findChildDepartments(dept.id, departments)
          }
        })
      }

      // 将树形结构扁平化
      const flattenDepartments = (departments) => {
        let result = []
        departments.forEach(dept => {
          // 创建一个新对象，避免修改原始数据
          const newDept = { ...dept }
          delete newDept.children
          result.push(newDept)

          if (dept.children && dept.children.length > 0) {
            result = result.concat(flattenDepartments(dept.children))
          }
        })
        return result
      }

      // 扁平化部门列表
      const flatDepartments = flattenDepartments(departmentList)
      console.log('扁平化后的部门列表:', flatDepartments)

      // 查找所有子部门ID
      findChildDepartments(id, flatDepartments)

      return disabledIds
    },

    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.getTreeSelect()
      // 清空禁用部门列表
      this.disabledDepartmentIds = []
      if (row != null && row.id) {
        this.form.parentId = row.id
      }
      this.open = true
      this.title = '添加部门'
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.detail = {}
      this.showMap = false
      this.locationAddress = ''
      this.detailOpen = true
      getDepartment(row.id).then(response => {
        if (response && response.code === 1) {
          this.detail = response.data || {}
        } else {
          this.$message.error(response && response.msg || '获取部门详情失败')
        }
      }).catch(error => {
        this.$message.error(error.message || '获取部门详情失败')
      })
    },

    /** 初始化地图 */
    initMap() {
      // 确保地图容器已经渲染
      this.$nextTick(() => {
        // 动态加载高德地图脚本
        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.async = true
        script.src = 'https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662&plugin=AMap.Scale,AMap.ToolBar,AMap.Geocoder&security=1f88be581712848c141824105b6ab32a'
        script.onerror = () => {
          console.error('高德地图脚本加载失败')
          this.$message.error('高德地图加载失败')
        }

        script.onload = () => {
          // 地图脚本加载完成后初始化地图
          if (window.AMap) {
            try {
              // 将AMap赋值给局部变量，避免TypeScript警告
              const AMap = window.AMap

              // 创建地图实例
              this.map = new AMap.Map('mapContainer', {
                zoom: 15,
                viewMode: '3D'
              })

              // 解析打卡地点坐标
              const location = JSON.parse(this.detail.clockLocation)
              // 设置地图中心点
              this.map.setCenter(location)

              // 添加标记点
              const marker = new AMap.Marker({
                position: location,
                title: this.detail.name + '打卡点'
              })
              this.map.add(marker)

              // 添加打卡范围圆圈
              const radius = this.detail.clockRadius ? parseInt(this.detail.clockRadius) : 100 // 如果没有设置，默认100米
              const circle = new AMap.Circle({
                center: location,
                radius: radius, // 打卡范围
                strokeColor: '#3366FF',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillColor: '#99CCFF',
                fillOpacity: 0.3
              })
              this.map.add(circle)

              // 添加范围说明文本
              const radiusText = new AMap.Text({
                text: `范围: ${radius}米`,
                position: [location[0], location[1] + 0.001], // 稍微偏上一点
                style: {
                  'background-color': '#fff',
                  'border-radius': '3px',
                  'border-width': '1px',
                  'border-color': '#ccc',
                  'padding': '5px',
                  'font-size': '12px',
                  'color': '#333'
                }
              })
              this.map.add(radiusText)

              // 添加控件
              this.map.addControl(new AMap.Scale())
              this.map.addControl(new AMap.ToolBar())

              // 添加地址解析
              const geocoder = new AMap.Geocoder({
                radius: 1000, // 搜索半径，单位：米
                extensions: 'all' // 返回完整地址信息
              })

              // 设置初始地址为坐标
              this.locationAddress = `经纬度: ${location[0]}, ${location[1]}`

              // 注意：高德地图API中经纬度的顺序是[经度,纬度]
              geocoder.getAddress(location, (status, result) => {
                if (status === 'complete' && result.info === 'OK') {
                  this.locationAddress = result.regeocode.formattedAddress
                } else {
                  // 尝试使用另一种方式获取地址
                  this.getAddressByLngLat(location[0], location[1])
                }
              })

            } catch (error) {
              console.error('地图初始化失败:', error)
              this.$message.error('地图初始化失败: ' + error.message)
            }
          } else {
            console.error('高德地图API未成功加载')
            this.$message.error('高德地图API未成功加载')
          }
        }

        document.head.appendChild(script)
      })
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeSelect()

      // 设置禁用的部门ID（当前部门及其所有子部门）
      this.disabledDepartmentIds = this.getDisabledDepartmentIds(row.id, this.departmentList)
      console.log('禁用的部门ID列表:', this.disabledDepartmentIds)

      // 先获取员工列表，确保leaderOptions已初始化
      const loadLeaderOptions = new Promise((resolve) => {
        if (!Array.isArray(this.leaderOptions) || this.leaderOptions.length === 0) {
          listEmployee().then(response => {
            if (response && response.code === 1) {
              this.leaderOptions = response.data || []
            } else {
              this.$message.error(response && response.msg || '获取员工列表失败')
              this.leaderOptions = []
            }
            resolve()
          }).catch(error => {
            this.$message.error(error.message || '获取员工列表失败')
            this.leaderOptions = []
            resolve()
          })
        } else {
          resolve()
        }
      })

      // 等待员工列表加载完成后再处理部门信息
      loadLeaderOptions.then(() => {
        getDepartment(row.id).then(response => {
          if (response && response.code === 1) {
            this.form = response.data || {}

            // 先加载部门员工列表
            if (this.form.id) {
              this.selectedDepartmentId = this.form.id
              this.selectedDepartmentName = this.form.name

              // 加载部门员工列表
              getEmployeesByDepartmentId(this.form.id).then(response => {
                if (response && response.code === 1) {
                  this.departmentEmployees = response.data || []

                  // 如果有部门领导，在员工列表中查找并选中
                  if (this.form.leaderId) {
                    const leaderInDepartment = this.departmentEmployees.find(emp => emp.id === this.form.leaderId)
                    if (leaderInDepartment) {
                      this.selectedEmployee = leaderInDepartment
                    } else {
                      // 如果在部门员工列表中找不到，尝试在全局员工列表中查找
                      const leaderInAll = this.leaderOptions.find(emp => emp.id === this.form.leaderId)
                      if (leaderInAll) {
                        this.selectedEmployee = leaderInAll
                      } else {
                        // 如果在全局员工列表中也找不到，单独查询
                        this.$message.info('正在加载部门领导信息...')
                        getEmployee(this.form.leaderId).then(empResponse => {
                          if (empResponse && empResponse.code === 1 && empResponse.data) {
                            this.selectedEmployee = empResponse.data
                          }
                        }).catch(() => {
                          // 忽略错误
                        })
                      }
                    }
                  }
                }
              }).catch(error => {
                console.error('加载部门员工失败:', error)
              })
            } else if (this.form.leaderId) {
              // 如果没有部门ID但有负责人ID，直接查询负责人信息
              const leader = this.leaderOptions.find(item => item.id === this.form.leaderId)
              if (leader) {
                this.selectedEmployee = leader
              } else {
                this.$message.info('正在加载部门领导信息...')
                getEmployee(this.form.leaderId).then(empResponse => {
                  if (empResponse && empResponse.code === 1 && empResponse.data) {
                    this.selectedEmployee = empResponse.data
                  }
                }).catch(() => {
                  // 忽略错误
                })
              }
            }
          } else {
            this.$message.error(response && response.msg || '获取部门详情失败')
            this.form = { ...row }
          }
          this.open = true
          this.title = '修改部门'
        }).catch(error => {
          this.$message.error(error.message || '获取部门详情失败')
          this.form = { ...row }
          this.open = true
          this.title = '修改部门'
        })
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDepartment(this.form.id, this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '修改失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '修改失败')
            })
          } else {
            addDepartment(this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '新增失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '新增失败')
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的部门? 删除后将无法恢复!', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return deleteDepartment(row.id)
      }).then(response => {
        if (response && response.code === 1) {
          this.getList()
          this.$message.success('删除成功')
        } else {
          this.$message.error(response && response.msg || '删除失败')
        }
      }).catch(error => {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除失败')
        }
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        name: undefined,
        leaderId: undefined
      }
      // 重置部门员工选择相关数据
      this.selectedDepartmentId = null
      this.selectedDepartmentName = ''
      this.departmentEmployees = []
      this.selectedEmployee = null
      // 清空禁用部门列表
      this.disabledDepartmentIds = []
      this.resetForm('form')
    },
    /** 重置表单 */
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return ''
      const hours = time.getHours().toString().padStart(2, '0')
      const minutes = time.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },

    /** 销毁地图实例 */
    destroyMap() {
      if (this.map) {
        try {
          this.map.destroy()
        } catch (error) {
          console.error('销毁地图失败:', error)
        }
        this.map = null
      }
    },

    /** 通过经纬度获取地址（备用方法） */
    getAddressByLngLat(lng, lat) {
      // 使用高德地图Web服务API进行逆地理编码
      const url = `https://restapi.amap.com/v3/geocode/regeo?key=a3299b78709563deb62714fce9c1b662&location=${lng},${lat}&radius=1000&extensions=all`

      // 使用fetch API请求
      fetch(url)
        .then(response => response.json())
        .then(data => {
          if (data.status === '1' && data.regeocode) {
            this.locationAddress = data.regeocode.formatted_address
          } else {
            // 如果无法解析地址，则显示坐标
            this.locationAddress = `经纬度: ${lng}, ${lat}`
          }
        })
        .catch(error => {
          console.error('地址解析失败:', error)
          // 如果解析失败，则显示坐标
          this.locationAddress = `经纬度: ${lng}, ${lat}`
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.table-container {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .left {
    h3 {
      font-size: 18px;
      margin: 0;
    }
  }

  .right {
    display: flex;
    align-items: center;

    .search-input {
      width: 200px;
      margin-right: 10px;
    }
  }
}

.time-separator {
  display: inline-block;
  width: 4%;
  text-align: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
}

.map-container {
  margin-top: 20px;

  .map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .map {
    width: 100%;
    height: 400px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .map-address {
    margin-top: 10px;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
  }
}

.parent-department-container {
  position: relative;
  display: flex;
  align-items: center;

  .el-tree-select {
    flex: 1;
  }

  .clear-parent-btn {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    padding: 2px;
    color: #909399;
    z-index: 10;

    &:hover {
      color: #f56c6c;
    }
  }
}

.leader-select-container {
  display: flex;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  height: 350px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.leader-select-container .department-tree {
  width: 40%;
  border-right: 1px solid #dcdfe6;
  overflow: auto;
  padding: 10px;
  background-color: #f9fafc;
}

.leader-select-container .department-tree >>> .el-tree-node__content {
  height: 32px;
}

.leader-select-container .department-tree >>> .el-tree-node__content:hover {
  background-color: #ecf5ff;
}

.leader-select-container .department-tree >>> .el-tree-node.is-current > .el-tree-node__content {
  background-color: #ecf5ff;
  color: #409EFF;
  font-weight: bold;
}

.leader-select-container .employee-list {
  width: 60%;
  display: flex;
  flex-direction: column;
}

.leader-select-container .employee-list .employee-list-header {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #303133;
}

.leader-select-container .employee-list .employee-list-header .loading-indicator {
  font-size: 12px;
  color: #909399;
}

.leader-select-container .employee-list >>> .el-table .el-table__header-wrapper th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  padding: 8px 0;
}

.leader-select-container .employee-list >>> .el-table .el-table__row {
  cursor: pointer;
}

.leader-select-container .employee-list >>> .el-table .el-table__row:hover > td {
  background-color: #f5f7fa;
}

.leader-select-container .employee-list >>> .el-table .el-table__row td {
  padding: 8px 0;
}

.selected-employee {
  margin-top: 10px;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
}

.selected-employee .label {
  font-weight: bold;
  color: #67c23a;
  margin-right: 8px;
}

.selected-employee .el-tag {
  margin-right: 5px;
  padding: 0 10px;
  height: 28px;
  line-height: 26px;
}

.selected-employee .el-tag i {
  margin-right: 5px;
}

.selected-employee .el-tag .work-no {
  color: #85ce61;
  font-size: 12px;
  margin-left: 3px;
}

.selected-employee .clear-btn {
  padding: 2px;
  color: #909399;
}

.selected-employee .clear-btn:hover {
  color: #f56c6c;
}

>>> .selected-row {
  background-color: #f0f9eb;
  font-weight: bold;
}

>>> .selected-row:hover > td {
  background-color: #e6f7d9 !important;
}

>>> .selected-row td {
  background-color: #f0f9eb !important;
}
</style>