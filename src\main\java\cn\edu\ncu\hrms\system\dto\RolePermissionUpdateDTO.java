package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 更新角色权限数据传输对象 (DTO)
 */
@Data
@ApiModel(value="RolePermissionUpdateDTO", description="更新角色权限数据传输对象")
public class RolePermissionUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 注意：这里允许列表为空，表示清空角色的所有权限
    @ApiModelProperty(value = "权限ID列表", required = true)
    @NotNull(message = "权限ID列表不能为空，若需清空权限请传递空列表")
    private List<Integer> permissionIds;
} 