package cn.edu.ncu.hrms.common.kafka;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.utils.ExcelUtil;
import cn.edu.ncu.hrms.employee.service.EmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static cn.edu.ncu.hrms.common.constant.KafkaConstant.*;

@Component
@Slf4j
public class KafkaConsumer {

    @KafkaListener(topics = ATTENDANCE_TOPIC)
    public void clock(String message) {
        log.debug("接收到打卡消息：{}", message);
    }

}
