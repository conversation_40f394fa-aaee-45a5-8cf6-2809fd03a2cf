import request from '@/utils/request'

// 查询账套匹配列表
export function listAccountSetting(query) {
  return request({
    url: '/account/setting/list',
    method: 'get',
    params: query
  })
}

// 查询账套匹配详细
export function getAccountSetting(id) {
  return request({
    url: '/account/setting/' + id,
    method: 'get'
  })
}

// 新增账套匹配
export function addAccountSetting(data) {
  return request({
    url: '/account/setting',
    method: 'post',
    data: data
  })
}

// 修改账套匹配
export function updateAccountSetting(data) {
  return request({
    url: '/account/setting',
    method: 'put',
    data: data
  })
}

// 删除账套匹配
export function deleteAccountSetting(id) {
  return request({
    url: '/account/setting/' + id,
    method: 'delete'
  })
}
