package cn.edu.ncu.hrms.attendance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "提交请假申请数据传输对象")
public class LeaveApplyDTO {

    @NotBlank(message = "请假类型不能为空")
    @ApiModelProperty(value = "请假类型", allowableValues = "ANNUAL,SICK,PERSONAL,MARRIAGE,MATERNITY,OTHER", example = "ANNUAL（年假）")
    private String leaveType;


    @ApiModelProperty(value = "开始日期", required = true, example = "2025-01-20")
    @NotNull(message = "请假开始时间不能为空")
    @FutureOrPresent(message = "请假开始时间不能早于当前时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "开始时段", allowableValues = "AM,PM", example = "AM", notes = "AM表示上午请假，PM表示下午请假")
    private String startPeriod;

    @ApiModelProperty(value = "结束日期", required = true, example = "2025-01-23")
    @NotNull(message = "请假结束时间不能为空")
    @FutureOrPresent(message = "请假结束时间不能早于当前时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "结束时段", allowableValues = "AM,PM", example = "PM", notes = "AM表示上午请假，PM表示下午请假")
    private String endPeriod;

    @ApiModelProperty(value = "请假原因", example = "家庭事务")
    private String reason;

} 