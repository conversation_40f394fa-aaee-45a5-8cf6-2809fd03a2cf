<script>
export default {
  name: 'MenuItem',
  functional: true, // 函数式组件
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    // 图标 和标题
    const { icon, title } = context.props
    const vnodes = []
    // 渲染图标
    if (icon) {
      if (icon.includes('el-icon')) {
        vnodes.push(<i class={[icon, 'sub-el-icon']} />)
      } else {
        vnodes.push(<svg-icon icon-class={icon}/>)
      }
    }
    // 渲染标题
    if (title) {
      vnodes.push(<span slot='title' style="font-size: 14px; font-weight: 500;">{(title)}</span>)
    }
    return vnodes
  }
}
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1.2em;
  height: 1.2em;
  margin-right: 5px;
  font-size: 18px;
  text-align: center;
  transition: all 0.3s;
}
</style>
