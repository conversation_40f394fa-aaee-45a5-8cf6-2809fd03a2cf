import request from '@/utils/request'

/**
 * 分页获取职位与账套匹配列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回职位列表
 */
export function getPositionPage(params) {
  return request({
    url: '/salary/position/page',
    method: 'get',
    params
  })
}

/**
 * 获取所有职位列表
 * @returns {Promise} 返回职位列表
 */
export function getAllPositions() {
  return request({
    url: '/salary/position/all',
    method: 'get'
  })
}

/**
 * 根据账套ID查询职位列表
 * @param {Number} accountId - 账套ID
 * @returns {Promise} 返回职位列表
 */
export function getPositionsByAccountId(accountId) {
  return request({
    url: `/system/position/account/${accountId}`,
    method: 'get'
  })
}

/**
 * 为职位匹配账套
 * @param {Number} positionId - 职位ID
 * @param {Number} accountId - 账套ID
 * @returns {Promise} 返回结果
 */
export function matchingAccountSet(positionId, accountId) {
  return request({
    url: `/salary/position/${positionId}/${accountId}`,
    method: 'put'
  })
}
