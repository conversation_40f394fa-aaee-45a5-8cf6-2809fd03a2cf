package cn.edu.ncu.hrms.salary.service;

import cn.edu.ncu.hrms.common.entity.AccountSet;
import cn.edu.ncu.hrms.salary.vo.AccountSetVO;
import cn.edu.ncu.hrms.salary.dto.AccountSetPageQueryDTO;
import cn.edu.ncu.hrms.salary.dto.AccountSetAddDTO;
import cn.edu.ncu.hrms.salary.dto.AccountSetUpdateDTO;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 薪资账套服务接口
 */
public interface AccountSetService {

    /**
     * 创建薪资账套
     *
     * @param addDTO 账套添加数据
     * @return 操作结果
     */
    Result<Void> addAccountSet(AccountSetAddDTO addDTO);

    /**
     * 修改薪资账套
     *
     * @param id        账套ID
     * @param updateDTO 账套更新数据
     * @return 操作结果
     */
    Result<Void> updateAccountSet(Integer id, AccountSetUpdateDTO updateDTO);

    /**
     * 删除薪资账套
     *
     * @param id 账套ID
     * @return 操作结果
     */
    Result<Void> deleteAccountSet(Integer id);

    /**
     * 根据ID获取账套详情视图对象
     *
     * @param id 账套ID
     * @return 账套详情视图对象
     */
    Result<AccountSetVO> getAccountSetVOById(Integer id);

    /**
     * 分页查询账套列表
     *
     * @param queryDTO 查询条件
     * @return 分页账套列表视图对象
     */
    Result<PageInfo<AccountSetVO>> pageAccountSets(AccountSetPageQueryDTO queryDTO);

    /**
     * 获取所有账套列表 (用于下拉选择)
     *
     * @return 所有账套列表视图对象
     */
    Result<List<AccountSetVO>> listAllAccountSets();
}
