<template>
  <div class="app-container">
    <div class="table-container">
      <div class="table-header">
        <div class="left">
          <h3>权限管理</h3>
        </div>
        <div class="right">
          <el-input
            v-model="queryParams.permissionName"
            placeholder="请输入权限名称"
            clearable
            size="small"
            class="search-input"
            @keyup.enter.native="handleQuery"
          />
          <el-input
            v-model="queryParams.id"
            placeholder="请输入权限ID"
            clearable
            size="small"
            class="search-input"
            @keyup.enter.native="handleQuery"
          />
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button type="info" icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        row-key="id"
        border
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="name" label="权限名称" min-width="170" />
        <el-table-column prop="id" label="权限ID" min-width="80" />
        <el-table-column prop="key" label="权限标识" min-width="180" />
        <el-table-column prop="description" label="权限描述"  min-width="230" />
        <el-table-column prop="createTime" label="创建时间" align="center" min-width="150" />
        <el-table-column label="操作" align="center" min-width="300">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
            >新增</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加或修改权限对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="权限名称" prop="permissionName">
          <el-input v-model="form.permissionName" placeholder="请输入权限名称" />
          <div/>
        </el-form-item>
        <el-form-item label="权限标识" prop="permissionKey">
          <el-input v-model="form.permissionKey" placeholder="请输入权限标识" />
          <div class="permission-key-tip">格式：一级(system)、二级(system:permission)、三级(system:permission:list)</div>
          <div/>
        </el-form-item>
        <el-form-item label="权限描述" prop="description" style="margin-top: 25px;">
          <el-input v-model="form.description" type="textarea" placeholder="请输入权限描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPermissionTree, getPermission, addPermission, updatePermission, deletePermission } from '@/api/system/permission'

export default {
  name: 'Permission',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 权限表格数据
      permissionList: [],
      // 权限树选项
      permissionOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        permissionName: undefined,
        id: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        permissionName: undefined,
        permissionKey: undefined,
        description: undefined
      },
      // 表单校验
      rules: {
        permissionName: [
          { required: true, message: '权限名称不能为空', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '权限名称不能包含空格', trigger: 'blur' }
        ],
        permissionKey: [
          { required: true, message: '权限标识不能为空', trigger: 'blur' },
          {
            pattern: /^[a-z]+(\:[a-z]+){0,2}$/,
            message: '权限标识格式：一级(system)、二级(system:permission)、三级(system:permission:list)',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询权限列表 */
    getList() {
      this.loading = true
      getPermissionTree(this.queryParams).then(response => {
        if (response && response.code === 1) {
          this.permissionList = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取权限列表失败')
          this.permissionList = []
        }
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.permissionList = []
        this.$message.error(error.message || '获取权限列表失败')
      })
    },
    /** 转换权限数据结构 */
    handleTree(data, id, parentId) {
      const config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: 'children'
      }

      const childrenListMap = {}
      const nodeMap = {}
      const result = []

      data.forEach(item => {
        const newItem = { ...item }
        const parentIdVal = newItem[config.parentId]
        if (childrenListMap[parentIdVal] == null) {
          childrenListMap[parentIdVal] = []
        }
        nodeMap[newItem[config.id]] = newItem
        childrenListMap[parentIdVal].push(newItem)
      })

      data.forEach(item => {
        const newItem = { ...item }
        const parentIdVal = newItem[config.parentId]
        const id = newItem[config.id]
        if (childrenListMap[id]) {
          newItem[config.childrenList] = childrenListMap[id]
        }
        if (parentIdVal == null || !nodeMap[parentIdVal]) {
          result.push(newItem)
        }
      })

      return result
    },
    /** 查询权限下拉树结构 */
    getTreeSelect() {
      getPermissionTree().then(response => {
        if (response && response.code === 1) {
          this.permissionOptions = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取权限树结构失败')
          this.permissionOptions = []
        }
      }).catch(error => {
        this.permissionOptions = []
        this.$message.error(error.message || '获取权限树结构失败')
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        permissionName: undefined,
        id: undefined
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      // 如果传入了行数据，预填充权限标识前缀
      if (row && row.key) {
        // 检查当前权限层级
        const keyParts = row.key.split(':')
        // 如果已经是三级权限，不能再添加子权限
        if (keyParts.length >= 3) {
          this.$message.warning('三级权限不能再添加子权限')
          return
        }
        // 预填充父权限标识作为前缀
        this.form.permissionKey = row.key + ':'
      }
      this.open = true
      this.title = row && row.key ? '添加子权限' : '添加权限'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getPermission(row.id).then(response => {
        if (response && response.code === 1) {
          this.form = response.data || {}
        } else {
          this.$message.error(response && response.msg || '获取权限详情失败')
          this.form = {
            id: row.id,
            permissionName: row.name,
            permissionKey: row.key,
            description: row.description
          }
        }
        this.open = true
        this.title = '修改权限'
      }).catch(error => {
        this.$message.error(error.message || '获取权限详情失败')
        this.form = {
          id: row.id,
          permissionName: row.name,
          permissionKey: row.key,
          description: row.description
        }
        this.open = true
        this.title = '修改权限'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePermission(this.form.id, this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '修改失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '修改失败')
            })
          } else {
            addPermission(this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '新增失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '新增失败')
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的权限?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return deletePermission(row.id)
      }).then(response => {
        if (response && response.code === 1) {
          this.getList()
          this.$message.success('删除成功')
        } else {
          this.$message.error(response && response.msg || '删除失败')
        }
      }).catch(error => {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除失败')
        }
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        permissionName: undefined,
        permissionKey: undefined,
        description: undefined
      }
      this.resetForm('form')
    },
    /** 重置表单 */
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    }
  }
}
</script>

<style scoped>
.permission-key-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
}
</style>
