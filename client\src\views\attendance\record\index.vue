<template>
  <div class="app-container">
    <div class="page-header">
      <h2>考勤管理</h2>
      <div class="header-actions">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="self">个人考勤</el-radio-button>
          <el-radio-button label="subordinates" v-if="hasSubordinates">员工考勤</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 个人考勤视图 -->
    <div v-if="viewMode === 'self'" class="attendance-container">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="attendance-card">
            <div slot="header" class="card-header">
              <h3>
                <span><i class="el-icon-alarm-clock"></i> 我的考勤记录</span>
              </h3>
              <div class="month-selector">
                <el-button icon="el-icon-arrow-left" size="mini" circle @click="prevMonth"></el-button>
                <span class="month-text">{{ currentYear }}年{{ currentMonth }}月</span>
                <el-button icon="el-icon-arrow-right" size="mini" circle @click="nextMonth"></el-button>
              </div>
            </div>
            <div class="attendance-summary-cards">
              <el-row :gutter="20" style="margin-bottom: 20px; margin-top: 10px;">
                <el-col :span="6">
                  <div class="summary-card">
                    <div class="summary-icon">
                      <i class="el-icon-date"></i>
                    </div>
                    <div class="summary-title">工作日天数</div>
                    <div class="summary-value">{{ summary.workDays }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="summary-card">
                    <div class="summary-icon">
                      <i class="el-icon-time"></i>
                    </div>
                    <div class="summary-title">应出勤天数</div>
                    <div class="summary-value">{{ summary.shouldWorkDays }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="summary-card success-card">
                    <div class="summary-icon">
                      <i class="el-icon-check"></i>
                    </div>
                    <div class="summary-title">实际出勤天数</div>
                    <div class="summary-value">{{ summary.actualWorkDays }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="summary-card">
                    <div class="summary-icon">
                      <i class="el-icon-data-analysis"></i>
                    </div>
                    <div class="summary-title">出勤率</div>
                    <div class="summary-value">{{ summary.attendanceRate }}%</div>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-bottom: 20px;">
                <el-col :span="6">
                  <div class="summary-card warning-card">
                    <div class="summary-icon">
                      <i class="el-icon-warning"></i>
                    </div>
                    <div class="summary-title">迟到次数</div>
                    <div class="summary-value">{{ summary.lateTimes }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="summary-card warning-card">
                    <div class="summary-icon">
                      <i class="el-icon-warning-outline"></i>
                    </div>
                    <div class="summary-title">早退次数</div>
                    <div class="summary-value">{{ summary.earlyTimes }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="summary-card danger-card">
                    <div class="summary-icon">
                      <i class="el-icon-close"></i>
                    </div>
                    <div class="summary-title">缺勤天数</div>
                    <div class="summary-value">{{ summary.absentDays }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="summary-card info-card">
                    <div class="summary-icon">
                      <i class="el-icon-message"></i>
                    </div>
                    <div class="summary-title">请假天数</div>
                    <div class="summary-value">{{ summary.leaveDays }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div style="margin-top: 20px;"></div>

            <el-table
              v-loading="loading"
              :data="attendanceRecords"
              border
              stripe
              highlight-current-row
              style="width: 100%"
            >
              <el-table-column prop="date" label="日期" min-width="120" align="center" />
              <el-table-column prop="weekday" label="星期" min-width="80" align="center" />
              <el-table-column prop="clockInTime" label="签到时间" min-width="120" align="center">
                <template slot-scope="scope">
                  <span :class="getClockInClass(scope.row)">{{ scope.row.clockInTime || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="签到状态" min-width="100" align="center">
                <template slot-scope="scope">
                  <!-- <span v-if="!scope.row.clockInStatus || scope.row.clockInStatus === ''">-</span> -->
                  <el-tag
                    :type="getClockInStatusType(scope.row.clockInStatus)"
                    :class="{
                      'weekend-tag': scope.row.clockInStatus === 'WEEKEND',
                      'future-tag': scope.row.clockInStatus === 'FUTURE'
                    }"
                  >
                    {{ getClockInStatusText(scope.row.clockInStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="clockOutTime" label="签退时间" min-width="120" align="center">
                <template slot-scope="scope">
                  <span :class="getClockOutClass(scope.row)">{{ scope.row.clockOutTime || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="签退状态" min-width="100" align="center">
                <template slot-scope="scope">
                  <!-- <span v-if="!scope.row.clockOutStatus || scope.row.clockOutStatus === ''">-</span> -->
                  <el-tag
                    :type="getClockOutStatusType(scope.row.clockOutStatus)"
                    :class="{
                      'weekend-tag': scope.row.clockOutStatus === 'WEEKEND',
                      'future-tag': scope.row.clockOutStatus === 'FUTURE'
                    }"
                  >
                    {{ getClockOutStatusText(scope.row.clockOutStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="scope">
                  <div class="operation-buttons">
                    <el-button
                      size="mini"
                      type="primary"
                      icon="el-icon-view"
                      circle
                      @click="handleViewAttendanceDetail(scope.row)"
                      title="查看详情"
                    ></el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="clock-card">
            <div slot="header" class="card-header">
              <span>今日打卡</span>
            </div>
            <div class="clock-container">
              <div class="current-time">{{ currentTime }}</div>
              <div class="current-date">{{ currentDate }}</div>
              <div class="clock-status">
                <div class="status-item">
                  <span class="label">签到时间：</span>
                  <span class="value" :class="{ 'clocked': todayRecord.clockInTime }">
                    {{ todayRecord.clockInTime || '未打卡' }}
                  </span>
                </div>
                <div class="status-item">
                  <span class="label">签退时间：</span>
                  <span class="value" :class="{ 'clocked': todayRecord.clockOutTime }">
                    {{ todayRecord.clockOutTime || '未打卡' }}
                  </span>
                </div>
              </div>
              <div class="clock-buttons">
                <el-button
                  type="primary"
                  :disabled="todayRecord.clockInTime || !isWorkday"
                  @click="handleClockIn"
                  :loading="clockInLoading"
                >上班签到</el-button>
                <el-button
                  type="success"
                  :disabled="!todayRecord.clockInTime || todayRecord.clockOutTime || !isWorkday"
                  @click="handleClockOut"
                  :loading="clockOutLoading"
                >下班签退</el-button>
              </div>
              <div class="clock-notice" v-if="!isWorkday">
                <i class="el-icon-info"></i>
                <span>今日为非工作日，无需打卡</span>
              </div>
              <div class="clock-rules">
                <div class="rule-title">打卡规则</div>
                <div class="rule-item">
                  <span class="label">签到时间：</span>
                  <span class="value">{{ clockRules.clockInEndTime  ? (clockRules.clockInStartTime ? clockRules.clockInStartTime : '00:00') + ' - ' + clockRules.clockInEndTime : '未配置' }}</span>
                </div>
                <div class="rule-item">
                  <span class="label">签退时间：</span>
                  <span class="value">{{ clockRules.clockOutStartTime  ? clockRules.clockOutStartTime + ' - ' + (clockRules.clockOutEndTime ? clockRules.clockOutEndTime : '23:59')  : '未配置' }}</span>
                </div>
                <div class="rule-item location-item">
                  <span class="label">打卡地点：</span>
                  <div class="value location-value">{{ clockRules.locationName || (clockRules.clockLocation ? '加载中...' : '未配置') }}</div>
                </div>
                <div class="rule-item">
                  <span class="label">打卡范围：</span>
                  <span class="value">{{ clockRules.clockRadius ? clockRules.clockRadius + '米' : '未配置' }}</span>
                </div>
              </div>
              <div id="personalMapContainer" style="width: 100%; height: 200px; margin-top: 10px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 下属考勤视图 -->
    <div v-else-if="viewMode === 'subordinates'" class="subordinates-container">
      <el-card class="table-card" shadow="hover">
        <div slot="header" class="card-header">
          <h3>
            <i class="el-icon-alarm-clock"></i>
            <!-- 根据角色显示不同的标题 -->
            <span v-if="isHR">关联员工考勤</span>
            <span v-else-if="isLeader">部门员工考勤</span>
            <span v-else>员工考勤</span>

            <!-- 操作按钮 -->
            <div class="header-buttons" v-if="isLeader">
              <el-button-group>
                <el-button type="primary" size="medium" icon="el-icon-setting" @click="handleConfigClock">考勤设置</el-button>
              </el-button-group>
            </div>
          </h3>
        </div>

        <div class="filter-section">
          <div class="filter-form">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="员工工号">
                <el-input
                  v-model="searchQuery"
                  placeholder="请输入员工工号"
                  clearable
                  prefix-icon="el-icon-search"
                  style="width: 180px;"
                  @keyup.enter.native="handleSearch"
                />
              </el-form-item>
              <el-form-item label="签到状态">
                <el-select v-model="clockInStatusFilter" placeholder="请选择签到状态" clearable style="width: 170px;">
                  <el-option label="正常" value="NORMAL" />
                  <el-option label="迟到" value="LATE" />
                  <el-option label="缺勤" value="ABSENT" />
                  <el-option label="请假" value="LEAVE" />
                </el-select>
              </el-form-item>
              <el-form-item label="签退状态">
                <el-select v-model="clockOutStatusFilter" placeholder="请选择签退状态" clearable style="width: 170px;">
                  <el-option label="正常" value="NORMAL" />
                  <el-option label="早退" value="EARLY" />
                  <el-option label="缺勤" value="ABSENT" />
                  <el-option label="请假" value="LEAVE" />
                </el-select>
              </el-form-item>
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 250px;"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetFilter">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="subordinatesRecords"
          border
          stripe
          highlight-current-row
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column prop="workNo" label="工号" min-width="100" align="center" />
          <el-table-column prop="employeeName" label="姓名" min-width="80" align="center" />
          <el-table-column prop="departmentName" label="部门" min-width="120" align="center" />
          <el-table-column label="日期" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.date || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="签到时间" min-width="100" align="center">
            <template slot-scope="scope">
              <span :class="{ 'text-warning': scope.row.clockInStatus === 'LATE', 'text-danger': scope.row.clockInStatus === 'ABSENT' }">
                {{ scope.row.clockInTime || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="签到状态" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="!scope.row.clockInStatus || scope.row.clockInStatus === ''">-</span>
              <el-tag v-else :type="getClockInStatusType(scope.row.clockInStatus)">
                {{ getClockInStatusText(scope.row.clockInStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="签退时间" min-width="100" align="center">
            <template slot-scope="scope">
              <span :class="{ 'text-warning': scope.row.clockOutStatus === 'EARLY', 'text-danger': scope.row.clockOutStatus === 'ABSENT' }">
                {{ scope.row.clockOutTime || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="签退状态" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="!scope.row.clockOutStatus || scope.row.clockOutStatus === ''">-</span>
              <el-tag v-else :type="getClockOutStatusType(scope.row.clockOutStatus)">
                {{ getClockOutStatusText(scope.row.clockOutStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="deduction" label="扣款" min-width="100" align="center">
            <template slot-scope="scope">
              <span :class="{ 'deduction-value': scope.row.deduction > 0 }">
                {{ scope.row.deduction ? '¥' + scope.row.deduction.toFixed(2) : '-' }}
              </span>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" min-width="150" align="center" fixed="right">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  circle
                  @click="handleViewAttendanceDetail(scope.row)"
                  title="查看详情"
                ></el-button>
                <el-button
                  v-if="isLeader || isHR"
                  size="mini"
                  type="warning"
                  icon="el-icon-edit"
                  circle
                  @click="handleEditRecord(scope.row)"
                  title="修改"
                ></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </el-card>
    </div>

    <!-- 考勤设置对话框 -->
    <el-dialog title="考勤设置" :visible.sync="clockConfigVisible" width="800px" :close-on-click-modal="false">
      <el-form ref="clockConfigForm" :model="clockConfigForm" :rules="clockConfigRules" label-width="120px">
        <el-form-item label="签到时间" required>
          <el-col :span="11">
            <el-form-item prop="clockInStartTime">
              <el-time-picker
                v-model="clockConfigForm.clockInStartTime"
                placeholder="开始时间"
                style="width: 100%;"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="time-separator text-center">-</el-col>
          <el-col :span="11">
            <el-form-item prop="clockInEndTime">
              <el-time-picker
                v-model="clockConfigForm.clockInEndTime"
                placeholder="结束时间"
                style="width: 100%;"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="迟到设置">
          <el-col :span="13">
            <el-form-item label="迟到阈值(分钟)" label-width="115px" prop="lateThreshold">
              <el-input-number v-model="clockConfigForm.lateThreshold" :min="0" :max="120" :step="1" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="time-separator text-center"></el-col>
          <el-col :span="11">
            <el-form-item label="迟到扣款(元)" label-width="115px" prop="lateDeduction">
              <el-input-number v-model="clockConfigForm.lateDeduction" :min="0" :max="1000" :step="10" :precision="2" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="签退时间" required>
          <el-col :span="11">
            <el-form-item prop="clockOutStartTime">
              <el-time-picker
                v-model="clockConfigForm.clockOutStartTime"
                placeholder="开始时间"
                style="width: 100%;"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="time-separator text-center">-</el-col>
          <el-col :span="11">
            <el-form-item prop="clockOutEndTime">
              <el-time-picker
                v-model="clockConfigForm.clockOutEndTime"
                placeholder="结束时间"
                style="width: 100%;"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="早退设置">
          <el-col :span="13">
            <el-form-item label="早退阈值(分钟)" label-width="115px" prop="earlyThreshold">
              <el-input-number v-model="clockConfigForm.earlyThreshold" :min="0" :max="120" :step="1" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="time-separator text-center"></el-col>
          <el-col :span="11">
            <el-form-item label="早退扣款(元)" label-width="115px" prop="earlyDeduction">
              <el-input-number v-model="clockConfigForm.earlyDeduction" :min="0" :max="1000" :step="10" :precision="2" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="搜索地点">
          <el-input
            v-model="searchAddress"
            placeholder="请输入地址关键词"
            @keyup.enter.native="searchLocation"
            style="margin-bottom: 10px;">
            <el-button slot="append" icon="el-icon-search" @click="searchLocation"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="打卡地图">
          <div id="mapContainer" style="width: 100%; height: 300px;"></div>
        </el-form-item>
        <el-form-item label="打卡范围(米)" prop="clockRadius">
          <el-input-number v-model="clockConfigForm.clockRadius" :min="50" :max="1000" :step="50" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="clockConfigVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitClockConfig">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 考勤记录修改对话框 -->
    <el-dialog title="修改考勤记录" :visible.sync="recordEditVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="recordEditForm" :model="recordEditForm" :rules="recordEditRules" label-width="100px">
        <el-form-item label="日期" prop="date">
          <el-input v-model="recordEditForm.date" disabled />
        </el-form-item>
        <el-form-item label="上班打卡" prop="clockInTime">
          <el-time-picker
            v-model="recordEditForm.clockInTime"
            placeholder="上班打卡时间"
            style="width: 100%;"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            clearable
          />
        </el-form-item>
        <el-form-item label="签到状态" prop="clockInStatus">
          <el-select v-model="recordEditForm.clockInStatus" placeholder="请选择签到状态" style="width: 100%;">
            <el-option label="正常" value="NORMAL" />
            <el-option label="迟到" value="LATE" />
            <el-option label="缺勤" value="ABSENT" />
            <el-option label="请假" value="LEAVE" />
          </el-select>
        </el-form-item>
        <el-form-item label="下班打卡" prop="clockOutTime">
          <el-time-picker
            v-model="recordEditForm.clockOutTime"
            placeholder="下班打卡时间"
            style="width: 100%;"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            clearable
          />
        </el-form-item>
        <el-form-item label="签退状态" prop="clockOutStatus">
          <el-select v-model="recordEditForm.clockOutStatus" placeholder="请选择签退状态" style="width: 100%;">
            <el-option label="正常" value="NORMAL" />
            <el-option label="早退" value="EARLY" />
            <el-option label="缺勤" value="ABSENT" />
            <el-option label="请假" value="LEAVE" />
          </el-select>
        </el-form-item>
        <el-form-item label="扣款金额" prop="deduction">
          <el-input-number
            v-model="recordEditForm.deduction"
            :precision="2"
            :step="10"
            :min="0"
            :max="1000"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="recordEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRecordEdit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 考勤详情对话框 -->
    <el-dialog
      :title="attendanceDetailInfo && attendanceDetailInfo.isSubordinate ? '员工考勤详情' : '个人考勤详情'"
      :visible.sync="attendanceDetailVisible"
      width="600px"
      custom-class="attendance-detail-dialog"
      :close-on-click-modal="true"
    >
      <div v-if="attendanceDetailInfo" class="attendance-detail-container">
        <!-- 日期和员工信息 -->
        <div class="detail-header">
          <div class="detail-date">
            <span class="date">{{ attendanceDetailInfo.date }}</span>
            <span class="weekday">{{ attendanceDetailInfo.weekday }}</span>
          </div>
          <!-- 只有在员工考勤详情时才显示员工信息 -->
          <div v-if="attendanceDetailInfo.isSubordinate" class="employee-info">
            <span class="employee-name">{{ attendanceDetailInfo.employeeName }}</span>
            <span class="employee-workno">({{ attendanceDetailInfo.workNo }})</span>
            <span class="employee-department">{{ attendanceDetailInfo.departmentName }}</span>
          </div>
        </div>

        <!-- 考勤信息卡片 -->
        <el-row :gutter="20">
          <!-- 签到信息 -->
          <el-col :span="12">
            <el-card shadow="hover" class="detail-card">
              <div slot="header" class="card-header">
                <i class="el-icon-sunrise"></i>
                <span>签到信息</span>
              </div>
              <div class="card-content">
                <div class="info-row">
                  <span class="info-label">时间：</span>
                  <span class="info-value">{{ attendanceDetailInfo.clockInTime }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">状态：</span>
                  <el-tag
                    :type="getClockInStatusType(attendanceDetailInfo.clockInStatus)"
                    :class="{
                      'weekend-tag': attendanceDetailInfo.clockInStatus === 'WEEKEND',
                      'future-tag': attendanceDetailInfo.clockInStatus === 'FUTURE'
                    }"
                    size="small"
                  >
                    {{ attendanceDetailInfo.clockInStatusText }}
                  </el-tag>
                </div>
                <div class="info-row">
                  <span class="info-label">位置：</span>
                  <span class="info-value location-text">{{ attendanceDetailInfo.clockInLocation }}</span>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 签退信息 -->
          <el-col :span="12">
            <el-card shadow="hover" class="detail-card">
              <div slot="header" class="card-header">
                <i class="el-icon-sunset"></i>
                <span>签退信息</span>
              </div>
              <div class="card-content">
                <div class="info-row">
                  <span class="info-label">时间：</span>
                  <span class="info-value">{{ attendanceDetailInfo.clockOutTime }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">状态：</span>
                  <el-tag
                    :type="getClockOutStatusType(attendanceDetailInfo.clockOutStatus)"
                    :class="{
                      'weekend-tag': attendanceDetailInfo.clockOutStatus === 'WEEKEND',
                      'future-tag': attendanceDetailInfo.clockOutStatus === 'FUTURE'
                    }"
                    size="small"
                  >
                    {{ attendanceDetailInfo.clockOutStatusText }}
                  </el-tag>
                </div>
                <div class="info-row">
                  <span class="info-label">位置：</span>
                  <span class="info-value location-text">{{ attendanceDetailInfo.clockOutLocation }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 扣款信息 -->
        <el-card shadow="hover" class="detail-card" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <i class="el-icon-money"></i>
            <span>扣款信息</span>
          </div>
          <div class="card-content">
            <div class="info-row">
              <span class="info-label">金额：</span>
              <span class="info-value deduction-value">{{ attendanceDetailInfo.deductionText }}</span>
            </div>
          </div>
        </el-card>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="attendanceDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import { listPersonalAttendance, listSubordinateAttendance, clock, clockIn, clockOut, updateAttendance, getAttendanceStatistics, getDepartmentAttendanceConfig, updateDepartmentAttendanceConfig, getAttendance, getTodayAttendance } from '@/api/attendance/record'
import { initMap, createMarker, createCircle, getCurrentPosition, calculateDistance, createGeocoder, searchLocation, getAddress, getAddressFromAPI } from '@/utils/amap'

export default {
  name: 'AttendanceRecord',
  data() {
    return {
      // 视图模式：个人考勤/下属考勤
      viewMode: 'self',
      // 是否有下属
      hasSubordinates: false,
      // 是否为HR
      isHR: false,
      // 是否为领导
      isLeader: false,
      // 加载状态
      loading: false,
      // 当前时间
      currentTime: '',
      // 当前日期
      currentDate: '',
      // 当前年份
      currentYear: new Date().getFullYear(),
      // 当前月份
      currentMonth: new Date().getMonth() + 1,
      // 是否为工作日
      isWorkday: true,
      // 上班打卡加载状态
      clockInLoading: false,
      // 下班打卡加载状态
      clockOutLoading: false,
      // 今日考勤记录
      todayRecord: {
        clockInTime: '',
        clockOutTime: '',
        clockInStatus: '',
        clockOutStatus: ''
      },
      // 考勤规则
      clockRules: {
        clockInStartTime: null,
        clockInEndTime: null,
        lateThreshold: null,
        lateDeduction: null,
        clockOutStartTime: null,
        clockOutEndTime: null,
        earlyThreshold: null,
        earlyDeduction: null,
        clockLocation: null,
        locationName: null,
        clockRadius: null
      },
      // 个人考勤记录
      attendanceRecords: [],
      // 考勤汇总
      summary: {
        workDays: 0,
        actualWorkDays: 0,
        normalDays: 0,
        lateTimes: 0,
        earlyTimes: 0,
        absentDays: 0,
        leaveDays: 0,
        attendanceRate: 0
      },
      // 下属考勤记录
      subordinatesRecords: [],
      // 搜索参数
      searchQuery: '',
      clockInStatusFilter: '',
      clockOutStatusFilter: '',
      dateRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      // 分页参数
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 考勤设置对话框
      clockConfigVisible: false,
      clockConfigForm: {
        departmentId: null,
        clockInStartTime: null,
        clockInEndTime: null,
        lateThreshold: null,
        lateDeduction: null,
        clockOutStartTime: null,
        clockOutEndTime: null,
        earlyThreshold: null,
        earlyDeduction: null,
        clockLocation: null,
        clockRadius: null
      },
      clockConfigRules: {
        clockInStartTime: [
          { message: '请选择上班打卡开始时间', trigger: 'change' },
          { validator: this.validateClockInStartTime, trigger: 'change' }
        ],
        clockInEndTime: [
          { required: true, message: '请选择上班打卡结束时间', trigger: 'change' },
          { validator: this.validateClockInEndTime, trigger: 'change' }
        ],
        clockOutStartTime: [
          { required: true, message: '请选择下班打卡开始时间', trigger: 'change' },
          { validator: this.validateClockOutStartTime, trigger: 'change' }
        ],
        clockOutEndTime: [
          { message: '请选择下班打卡结束时间', trigger: 'change' },
          { validator: this.validateClockOutEndTime, trigger: 'change' }
        ],
        clockRadius: [
          { required: true, message: '请输入打卡范围', trigger: 'blur' }
        ]
      },
      // 高德地图相关
      map: null,
      marker: null,
      circle: null,
      searchAddress: '',
      geocoder: null,
      // 考勤记录修改对话框
      recordEditVisible: false,
      recordEditForm: {
        id: null,
        employeeId: null,
        date: '',
        clockInTime: '',
        clockInStatus: '',
        clockOutTime: '',
        clockOutStatus: '',
        deduction: 0
      },
      recordEditRules: {
        clockInStatus: [
          { required: true, message: '请选择签到状态', trigger: 'change' }
        ],
        clockOutStatus: [
          { required: true, message: '请选择签退状态', trigger: 'change' }
        ]
      },
      // 考勤详情对话框
      attendanceDetailVisible: false,
      attendanceDetailInfo: null,
      // 定时器
      timer: null
    }
  },
  created() {
    this.getUserRole()
    this.initCurrentTime()

    // 如果有下属，默认显示员工考勤界面
    if (this.hasSubordinates) {
      this.viewMode = 'subordinates'
      this.$nextTick(() => {
        this.getSubordinatesRecords()
      })
    } else {
      // 如果没有下属，显示个人考勤界面
      this.viewMode = 'self'
      this.getTodayRecord()
      this.getAttendanceRecords()
      this.getClockRules()
    }
  },
  // 添加自定义校验方法
  beforeCreate() {
    // 上班打卡开始时间校验
    this.validateClockInStartTime = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }

      const endTime = this.clockConfigForm.clockInEndTime
      if (endTime && value >= endTime) {
        callback(new Error('签到开始时间必须早于结束时间'))
      } else {
        // 如果修改了开始时间，重新校验结束时间
        if (this.$refs.clockConfigForm) {
          this.$refs.clockConfigForm.validateField('clockInEndTime')
        }
        callback()
      }
    }

    // 上班打卡结束时间校验
    this.validateClockInEndTime = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }

      const startTime = this.clockConfigForm.clockInStartTime
      if (startTime && value <= startTime) {
        callback(new Error('签到结束时间必须晚于开始时间'))
        return
      }

      const outStartTime = this.clockConfigForm.clockOutStartTime
      if (outStartTime && value >= outStartTime) {
        callback(new Error('签到结束时间必须早于签退开始时间'))
      } else {
        // 如果修改了上班结束时间，重新校验下班开始时间
        if (this.$refs.clockConfigForm) {
          this.$refs.clockConfigForm.validateField('clockOutStartTime')
        }
        callback()
      }
    }

    // 下班打卡开始时间校验
    this.validateClockOutStartTime = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }

      const inEndTime = this.clockConfigForm.clockInEndTime
      if (inEndTime && value <= inEndTime) {
        callback(new Error('签退开始时间必须晚于签到结束时间'))
        return
      }

      const outEndTime = this.clockConfigForm.clockOutEndTime
      if (outEndTime && value >= outEndTime) {
        callback(new Error('签退开始时间必须早于结束时间'))
      } else {
        // 如果修改了下班开始时间，重新校验下班结束时间
        if (this.$refs.clockConfigForm) {
          this.$refs.clockConfigForm.validateField('clockOutEndTime')
        }
        callback()
      }
    }

    // 下班打卡结束时间校验
    this.validateClockOutEndTime = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }

      const outStartTime = this.clockConfigForm.clockOutStartTime
      if (outStartTime && value <= outStartTime) {
        callback(new Error('签退结束时间必须晚于开始时间'))
      } else {
        callback()
      }
    }
  },
  mounted() {
    // 启动定时器，每秒更新当前时间
    this.timer = setInterval(this.updateCurrentTime, 1000)
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 获取用户角色信息
    getUserRole() {
      // 从Vuex中获取用户角色信息
      const roles = this.$store.getters.userRoles || []
      this.isHR = roles.includes('ROLE_hr')
      this.isLeader = roles.includes('ROLE_leader')
      this.hasSubordinates = this.isHR || this.isLeader
    },
    // 初始化当前时间
    initCurrentTime() {
      const now = new Date()
      this.currentTime = moment(now).format('HH:mm:ss')
      this.currentDate = moment(now).format('YYYY年MM月DD日') + ' ' + this.getWeekday(now.getDay())
      // 判断是否为工作日（简单判断，周一到周五为工作日）
      this.isWorkday = now.getDay() >= 1 && now.getDay() <= 5
    },
    // 更新当前时间
    updateCurrentTime() {
      const now = new Date()
      this.currentTime = moment(now).format('HH:mm:ss')
    },
    // 获取星期几
    getWeekday(day) {
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      return weekdays[day]
    },
    // 获取今日考勤记录
    async getTodayRecord() {
      try {
        // 使用新的API接口获取今日考勤记录
        const response = await getTodayAttendance()

        if (response.code === 1 && response.data) {
          const todayRecord = response.data
          console.log('获取到的今日考勤记录:', todayRecord)

          // 更新今日考勤数据
          if (todayRecord.clockInTime) {
            // 直接使用LocalTime格式，只需要截取到分钟
            const clockInTimeStr = todayRecord.clockInTime.substring(0, 5) // 截取HH:mm部分
            this.todayRecord.clockInTime = clockInTimeStr
            this.todayRecord.clockInStatus = todayRecord.clockInStatus || ''
          } else {
            this.todayRecord.clockInTime = ''
            this.todayRecord.clockInStatus = ''
          }

          if (todayRecord.clockOutTime) {
            // 直接使用LocalTime格式，只需要截取到分钟
            const clockOutTimeStr = todayRecord.clockOutTime.substring(0, 5) // 截取HH:mm部分
            this.todayRecord.clockOutTime = clockOutTimeStr
            this.todayRecord.clockOutStatus = todayRecord.clockOutStatus || ''
          } else {
            this.todayRecord.clockOutTime = ''
            this.todayRecord.clockOutStatus = ''
          }

          // 更新打卡时间的样式
          this.$nextTick(() => {
            this.updateClockTimeStyles()
          })
        } else {
          console.warn('获取今日考勤记录失败:', response.msg)
          this.todayRecord.clockInTime = ''
          this.todayRecord.clockOutTime = ''
          this.todayRecord.clockInStatus = ''
          this.todayRecord.clockOutStatus = ''
        }
      } catch (error) {
        console.error('获取今日考勤记录失败', error)
        this.$message.error('获取今日考勤记录失败：' + (error.message || '未知错误'))
        this.todayRecord.clockInTime = ''
        this.todayRecord.clockOutTime = ''
        this.todayRecord.clockInStatus = ''
        this.todayRecord.clockOutStatus = ''
      }
    },

    // 更新打卡时间的样式
    updateClockTimeStyles() {
      // 获取打卡时间元素
      const clockInTimeEl = document.querySelector('.clock-status .status-item:first-child .value')
      const clockOutTimeEl = document.querySelector('.clock-status .status-item:last-child .value')

      if (clockInTimeEl) {
        // 移除所有可能的状态类
        clockInTimeEl.classList.remove('time-normal', 'time-warning', 'time-danger', 'time-info')

        // 根据状态添加对应的类
        if (this.todayRecord.clockInStatus) {
          if (this.todayRecord.clockInStatus === 'LATE') {
            clockInTimeEl.classList.add('time-warning')
          } else if (this.todayRecord.clockInStatus === 'ABSENT') {
            clockInTimeEl.classList.add('time-danger')
          } else if (this.todayRecord.clockInStatus === 'LEAVE') {
            clockInTimeEl.classList.add('time-info')
          } else if (this.todayRecord.clockInStatus === 'NORMAL') {
            clockInTimeEl.classList.add('time-normal')
          }
        }
      }

      if (clockOutTimeEl) {
        // 移除所有可能的状态类
        clockOutTimeEl.classList.remove('time-normal', 'time-warning', 'time-danger', 'time-info')

        // 根据状态添加对应的类
        if (this.todayRecord.clockOutStatus) {
          if (this.todayRecord.clockOutStatus === 'EARLY') {
            clockOutTimeEl.classList.add('time-warning')
          } else if (this.todayRecord.clockOutStatus === 'ABSENT') {
            clockOutTimeEl.classList.add('time-danger')
          } else if (this.todayRecord.clockOutStatus === 'LEAVE') {
            clockOutTimeEl.classList.add('time-info')
          } else if (this.todayRecord.clockOutStatus === 'NORMAL') {
            clockOutTimeEl.classList.add('time-normal')
          }
        }
      }
    },
    // 获取考勤规则
    async getClockRules() {
      try {
        // 调用获取部门考勤配置的API
        const response = await getDepartmentAttendanceConfig()

        if (response.code === 1 && response.data) {
          // 更新考勤规则
          this.clockRules = response.data

          // 如果有打卡位置，通过高德地图API获取地点名称
          if (this.clockRules.clockLocation &&
              typeof this.clockRules.clockLocation === 'string' &&
              this.clockRules.clockLocation.trim() !== '') {
            try {
              // 解析打卡位置
              const location = JSON.parse(this.clockRules.clockLocation)

              // 确保解析后是一个有效的坐标数组
              if (Array.isArray(location) &&
                  location.length === 2 &&
                  !isNaN(location[0]) &&
                  !isNaN(location[1])) {

                console.log('获取打卡地点名称，坐标:', location)

                // 使用高德地图API获取地址
                const address = await getAddressFromAPI(location)
                console.log('获取到的打卡地点名称:', address)

                // 更新打卡地点名称
                this.clockRules.locationName = address
              } else {
                console.error('打卡位置格式无效')
                this.clockRules.locationName = '未知地点'
              }
            } catch (error) {
              console.error('获取地点名称失败:', error)
              this.clockRules.locationName = '未知地点'
            }
          } else if (!this.clockRules.locationName) {
            // 如果没有打卡位置且没有地点名称，设置为未配置
            this.clockRules.locationName = '未配置'
          }

          // 初始化个人考勤地图
          this.$nextTick(() => {
            this.initPersonalMap()
          })
        } else {
          console.warn('获取考勤规则失败:', response.msg)
          // 使用默认规则
          this.clockRules = {
            clockInStartTime: '',
            clockInEndTime: '',
            lateThreshold: 30,
            lateDeduction: 50,
            clockOutStartTime: '',
            clockOutEndTime: '',
            earlyThreshold: 30,
            earlyDeduction: 50,
            clockLocation: null,
            locationName: '未配置',
            clockRadius: null
          }

          // 初始化个人考勤地图（使用默认值）
          this.$nextTick(() => {
            this.initPersonalMap()
          })
        }
      } catch (error) {
        console.error('获取考勤规则失败', error)
        // 使用默认规则
        this.clockRules = {
          clockInStartTime: '',
          clockInEndTime: '',
          lateThreshold: null,
          lateDeduction: null,
          clockOutStartTime: '',
          clockOutEndTime: '',
          earlyThreshold: null,
          earlyDeduction: null,
          clockLocation: null,
          locationName: '暂未配置',
          clockRadius: null
        }

        // 初始化个人考勤地图（使用默认值）
        this.$nextTick(() => {
          this.initPersonalMap()
        })
      }
    },

    // 初始化个人考勤地图 - 显示用户定位和打卡地点（如果有）
    initPersonalMap() {
      try {
        // 创建地图实例
        const personalMap = initMap('personalMapContainer')

        // 输出调试信息
        console.log('初始化地图时的clockRules:', JSON.stringify(this.clockRules))

        // 检查是否有有效的打卡位置配置
        let hasValidClockLocation = false
        let clockLocationCoords = null

        if (this.clockRules.clockLocation &&
            typeof this.clockRules.clockLocation === 'string' &&
            this.clockRules.clockLocation.trim() !== '') {
          try {
            clockLocationCoords = JSON.parse(this.clockRules.clockLocation)
            console.log('解析后的打卡位置坐标:', clockLocationCoords)
            // 确保解析后是一个有效的坐标数组
            if (Array.isArray(clockLocationCoords) &&
                clockLocationCoords.length === 2 &&
                !isNaN(clockLocationCoords[0]) &&
                !isNaN(clockLocationCoords[1])) {
              hasValidClockLocation = true
              console.log('有效的打卡位置:', hasValidClockLocation)
            }
          } catch (e) {
            console.error('打卡位置格式无效:', e)
            hasValidClockLocation = false
          }
        }

        // 添加定位控件
        personalMap.plugin(['AMap.Geolocation'], () => {
          /* eslint-disable no-undef */
          // 添加定位控件
          const geolocation = new AMap.Geolocation({
            enableHighAccuracy: true,
            timeout: 10000,
            buttonPosition: 'RB',
            buttonOffset: new AMap.Pixel(10, 10),
            zoomToAccuracy: false, // 不自动缩放到定位点
            showButton: true,
            // 关键设置：禁用蓝色圆圈
            showCircle: false
          })
          personalMap.addControl(geolocation)

          // 监听定位成功事件
          geolocation.on('complete', (data) => {
            const userLocation = data.position

            // 如果有有效的打卡位置，则添加打卡范围
            if (hasValidClockLocation) {
              // 创建中心点坐标对象
              const centerPosition = new AMap.LngLat(clockLocationCoords[0], clockLocationCoords[1]);
              console.log('创建中心点坐标:', centerPosition);

              // 不再创建标记点，只保留圆圈范围

              // 如果有有效的打卡范围，则添加圆形范围
              if (this.clockRules.clockRadius && this.clockRules.clockRadius > 0) {
                try {
                  // 添加圆形范围 - 确保使用数字类型的半径
                  const radius = parseInt(this.clockRules.clockRadius, 10)
                  console.log('创建打卡范围圆圈:', centerPosition, radius)

                  // 直接创建圆形，使用相同的中心点坐标
                  const circle = new AMap.Circle({
                    center: centerPosition,  // 使用共用坐标对象
                    radius: radius,
                    strokeColor: '#1874DD',  // 更深的蓝色
                    strokeWeight: 2,
                    strokeOpacity: 1,        // 完全不透明的边框
                    fillColor: '#1874DD',    // 更深的蓝色
                    fillOpacity: 0.4,        // 更高的填充透明度
                    zIndex: 100              // 确保在其他元素之上，但在标记之下
                  });

                  // 将圆形添加到地图
                  personalMap.add(circle);

                  // 保存圆形引用
                  this.circle = circle;

                  console.log('圆形创建成功并添加到地图');
                } catch (error) {
                  console.error('创建圆形范围失败:', error)
                }
              }

              // 调整地图视野以同时显示用户位置和打卡位置
              try {
                // 创建两个点的数组
                const points = [
                  new AMap.LngLat(userLocation.lng, userLocation.lat),
                  new AMap.LngLat(clockLocationCoords[0], clockLocationCoords[1])
                ]

                // 计算两点之间的距离（米）
                const distance = points[0].distance(points[1])
                console.log('用户位置与打卡地点距离:', distance, '米')

                // 根据距离动态计算合适的缩放级别
                let zoom = 15 // 默认缩放级别
                if (distance < 100) {
                  zoom = 16
                } else if (distance < 500) {
                  zoom = 15
                } else if (distance < 1000) {
                  zoom = 14
                } else if (distance < 5000) {
                  zoom = 13
                } else if (distance < 10000) {
                  zoom = 12
                } else {
                  zoom = 11
                }

                // 创建包含所有点的视野范围
                const bounds = new AMap.Bounds()
                points.forEach(point => {
                  bounds.extend(point)
                })

                // 考虑打卡范围，扩大视野
                if (this.clockRules.clockRadius && this.clockRules.clockRadius > 0) {
                  const radius = parseInt(this.clockRules.clockRadius, 10)
                  // 如果范围较大，适当调整缩放级别
                  if (radius > 1000) {
                    zoom -= 1
                  } else if (radius > 500) {
                    zoom -= 0.5
                  }
                }

                // 先设置缩放级别
                personalMap.setZoom(zoom)

                // 然后调整地图视野，确保两个点都在视野内，并留有一定边距
                // 使用较大的边距，确保圆形完全可见
                personalMap.setBounds(bounds, true, [100, 100, 100, 100])

                console.log('地图视野已调整，缩放级别:', zoom)
              } catch (error) {
                console.error('调整地图视野失败:', error)
                // 如果调整视野失败，则以用户位置为中心
                personalMap.setCenter(userLocation)
                personalMap.setZoom(15)
              }
            } else {
              // 如果没有打卡位置，则以用户位置为中心
              personalMap.setCenter(userLocation)
              personalMap.setZoom(15)
            }
          })

          // 自动触发一次定位
          geolocation.getCurrentPosition()
        })
      } catch (error) {
        console.error('初始化地图失败:', error)
      }
    },

    // 根据半径计算合适的缩放级别
    getZoomLevelByRadius(radius) {
      // 根据半径大小返回合适的缩放级别
      if (radius <= 50) return 18
      if (radius <= 100) return 17
      if (radius <= 200) return 16
      if (radius <= 500) return 15
      if (radius <= 1000) return 14
      return 13
    },


    // 获取个人考勤记录
    async getAttendanceRecords() {
      try {
        this.loading = true
        // 格式化年月为YYYY-MM格式
        const month = `${this.currentYear}-${this.currentMonth < 10 ? '0' + this.currentMonth : this.currentMonth}`

        // 调用获取个人考勤记录的API
        const response = await listPersonalAttendance({ month })

        if (response.code === 1 && response.data) {
          // 处理返回的数据
          const records = []
          // 获取当月所有天数
          const daysInMonth = new Date(this.currentYear, this.currentMonth, 0).getDate()

          // 创建一个日期到考勤记录的映射
          const recordMap = {}
          response.data.forEach(record => {
            // 使用record.date字段，这是API返回的实际字段名
            const date = record.date
            recordMap[date] = record
          })

          // 生成当月所有日期的考勤记录
          for (let i = 1; i <= daysInMonth; i++) {
            const date = new Date(this.currentYear, this.currentMonth - 1, i)
            const dateStr = moment(date).format('YYYY-MM-DD')
            const weekday = date.getDay()
            const isWeekend = weekday === 0 || weekday === 6

            // 如果是周末，添加周末记录
            if (isWeekend) {
              records.push({
                date: dateStr,
                weekday: this.getWeekday(weekday),
                clockInTime: '',
                clockInStatus: 'WEEKEND',
                clockInLocation: null,
                clockOutTime: '',
                clockOutStatus: 'WEEKEND',
                clockOutLocation: null,
                status: 'WEEKEND',
                deduction: 0,
                createTime: null,
                updateTime: null
              })
              continue
            }

            // 如果是当天之后的日期，添加未来记录
            const today = new Date()
            if (date > today) {
              records.push({
                date: dateStr,
                weekday: this.getWeekday(weekday),
                clockInTime: '',
                clockInStatus: 'FUTURE',
                clockInLocation: null,
                clockOutTime: '',
                clockOutStatus: 'FUTURE',
                clockOutLocation: null,
                status: 'FUTURE',
                deduction: 0,
                createTime: null,
                updateTime: null
              })
              continue
            }

            // 如果有该日期的考勤记录，使用后端返回的数据
            if (recordMap[dateStr]) {
              const record = recordMap[dateStr]

              // 注意：我们不再需要计算工作时长，因为已经从表格中移除了工时列

              // 确定状态
              let status = ''
              if (record.clockInStatus === 'LATE') {
                status = 'LATE'
              } else if (record.clockOutStatus === 'EARLY') {
                status = 'EARLY'
              } else if (record.clockInStatus === 'ABSENT' || record.clockOutStatus === 'ABSENT') {
                status = 'ABSENT'
              } else if (record.clockInStatus === 'LEAVE' || record.clockOutStatus === 'LEAVE') {
                status = 'LEAVE'
              }

              // 格式化时间 - 直接使用API返回的时间格式
              const clockInTime = record.clockInTime ? record.clockInTime.substring(0, 5) : '' // 截取HH:mm部分
              const clockOutTime = record.clockOutTime ? record.clockOutTime.substring(0, 5) : '' // 截取HH:mm部分

              // 生成备注
              let remark = record.remark || ''
              if (!remark) {
                if (status === 'LATE') {
                  remark = '迟到'
                } else if (status === 'EARLY') {
                  remark = '早退'
                } else if (status === 'ABSENT') {
                  remark = '无打卡记录'
                } else if (status === 'LEAVE') {
                  remark = '请假'
                }
              }

              records.push({
                id: record.id,
                date: dateStr,
                weekday: this.getWeekday(weekday),
                clockInTime,
                clockInStatus: record.clockInStatus || status,
                clockInLocation: record.clockInLocation,
                clockOutTime,
                clockOutStatus: record.clockOutStatus || status,
                clockOutLocation: record.clockOutLocation,
                status,
                deduction: record.deduction,
                createTime: record.createTime,
                updateTime: record.updateTime
              })
            } else {
              // 如果没有该日期的考勤记录，添加缺勤记录
              records.push({
                date: dateStr,
                weekday: this.getWeekday(weekday),
                clockInTime: '',
                clockInStatus: '',
                clockInLocation: null,
                clockOutTime: '',
                clockOutStatus: '',
                clockOutLocation: null,
                status: '',
                deduction: 0,
                createTime: null,
                updateTime: null
              })
            }
          }

          this.attendanceRecords = records

          // 获取考勤统计数据
          this.getAttendanceStatistics()
        } else {
          this.$message.error(response.msg || '获取考勤记录失败')
          this.attendanceRecords = []
        }
      } catch (error) {
        console.error('获取个人考勤记录失败', error)
        this.$message.error('获取考勤记录失败：' + (error.message || '未知错误'))
        this.attendanceRecords = []
      } finally {
        this.loading = false
      }
    },
    // 获取考勤统计数据
    async getAttendanceStatistics() {
      try {
        // 获取当前员工ID
        const employeeId = this.getCurrentEmployeeId()
        if (!employeeId) {
          console.error('无法获取当前员工ID')
          this.calculateSummary() // 使用本地计算作为备选
          return
        }

        // 获取当月第一天和最后一天
        const firstDay = moment(new Date(this.currentYear, this.currentMonth - 1, 1)).format('YYYY-MM-DD')
        const lastDay = moment(new Date(this.currentYear, this.currentMonth, 0)).format('YYYY-MM-DD')

        // 调用获取考勤统计数据的API
        const response = await getAttendanceStatistics(employeeId, firstDay, lastDay)

        if (response.code === 1 && response.data) {
          const stats = response.data

          this.summary = {
            workDays: stats.workDays || 0,
            shouldWorkDays: stats.shouldWorkDays || 0,
            actualWorkDays: stats.actualWorkDays || 0,
            lateTimes: stats.lateTimes || 0,
            earlyTimes: stats.earlyTimes || 0,
            absentDays: stats.absentDays || 0,
            leaveDays: stats.leaveDays || 0,
            attendanceRate: stats.attendanceRate || 0
          }
        } else {
          console.warn('获取考勤统计数据失败:', response.msg)
          this.calculateSummary() // 使用本地计算作为备选
        }
      } catch (error) {
        console.error('获取考勤统计数据失败', error)
        this.calculateSummary() // 使用本地计算作为备选
      }
    },

    // 本地计算考勤汇总（备选方法）
    calculateSummary() {
      const workDays = this.attendanceRecords.filter(record => record.status !== 'WEEKEND').length
      const shouldWorkDays = this.attendanceRecords.filter(record => record.status !== 'WEEKEND' && record.status !== 'FUTURE').length
      const lateTimes = this.attendanceRecords.filter(record => record.clockInStatus === 'LATE').length
      const earlyTimes = this.attendanceRecords.filter(record => record.clockOutStatus === 'EARLY').length
      const absentDays = this.attendanceRecords.filter(record => record.status === 'ABSENT').length
      const leaveDays = this.attendanceRecords.filter(record => record.status === 'LEAVE').length
      const nullDays = this.attendanceRecords.filter(record => record.status === null || record.status === '').length

      // 计算实际出勤天数
      const actualWorkDays = shouldWorkDays - leaveDays - absentDays - nullDays

      // 计算出勤率
      const attendanceRate = shouldWorkDays > 0 ? Math.round((actualWorkDays / shouldWorkDays) * 100) : 0

      this.summary = {
        workDays,
        shouldWorkDays,
        actualWorkDays,
        lateTimes,
        earlyTimes,
        absentDays,
        leaveDays,
        attendanceRate
      }
    },
    // 获取下属考勤记录
    async getSubordinatesRecords() {
      try {
        this.loading = true
        // 构建查询参数
        const queryParams = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          workNo: this.searchQuery || undefined,
          clockInStatus: this.clockInStatusFilter || undefined,
          clockOutStatus: this.clockOutStatusFilter || undefined,
          startDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : moment().format('YYYY-MM-DD'),
          endDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[1] : moment().format('YYYY-MM-DD')
        }

        console.log('查询参数:', queryParams)

        // 调用获取下属考勤记录的API
        const response = await listSubordinateAttendance(queryParams)
        console.log('API返回数据:', JSON.stringify(response))

        if (response.code === 1) {
          // 确保返回的数据是正确的格式
          let records = []

          // 检查返回的数据结构
          if (response.data) {
            console.log('返回的数据结构:', Object.keys(response.data))

            if (response.data.records && Array.isArray(response.data.records)) {
              // 如果返回的是 {total: xxx, records: [...]} 格式
              records = response.data.records
              this.total = response.data.total || 0
              console.log('使用records字段:', records.length)
            } else if (response.data.list && Array.isArray(response.data.list)) {
              // 如果返回的是 {total: xxx, list: [...]} 格式
              records = response.data.list
              this.total = response.data.total || 0
              console.log('使用list字段:', records.length)
            } else if (Array.isArray(response.data)) {
              // 如果直接返回数组
              records = response.data
              this.total = records.length
              console.log('使用直接数组:', records.length)
            } else {
              console.error('未知的返回数据格式:', JSON.stringify(response.data))

              // 尝试直接使用response.data
              this.subordinatesRecords = [response.data]
              this.total = 1
              console.log('尝试直接使用response.data')

              // 强制更新视图
              this.$nextTick(() => {
                this.$forceUpdate()
              })
              return
            }
          } else {
            console.warn('返回的数据为空')
            this.subordinatesRecords = []
            this.total = 0
            return
          }

          console.log('处理前的记录数:', records.length, '记录:', JSON.stringify(records))

          if (records.length === 0) {
            this.subordinatesRecords = []
            this.total = 0
            return
          }

          // 直接使用原始记录，不做任何处理
          this.subordinatesRecords = records
          console.log('最终表格数据:', this.subordinatesRecords.length, '条记录:', JSON.stringify(this.subordinatesRecords))

          // 强制更新视图
          this.$nextTick(() => {
            this.$forceUpdate()
          })
        } else {
          this.$message.error(response.msg || '获取下属考勤记录失败')
          this.subordinatesRecords = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取下属考勤记录失败', error)
        this.$message.error('获取下属考勤记录失败：' + (error.message || '未知错误'))
        this.subordinatesRecords = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    // 处理搜索
    handleSearch() {
      this.currentPage = 1
      this.getSubordinatesRecords()
    },

    // 重置筛选条件
    resetFilter() {
      this.searchQuery = ''
      this.clockInStatusFilter = ''
      this.clockOutStatusFilter = ''
      this.dateRange = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      this.currentPage = 1
      this.getSubordinatesRecords()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSubordinatesRecords()
    },
    // 处理每页条数变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.getSubordinatesRecords()
    },
    // 获取当前员工ID
    getCurrentEmployeeId() {
      // 从Vuex中获取当前用户信息
      const employeeId = this.$store.getters.userInfo?.employeeId
      if (!employeeId) {
        console.error('无法获取当前员工ID')
        return null
      }
      return employeeId
    },

    // 获取地理位置
    async getLocation() {
      // 获取当前位置
      const result = await getCurrentPosition()
      const position = result.position
      const location = [position.lng, position.lat]

      // 如果有打卡位置，检查是否在范围内
      if (this.clockRules.clockLocation &&
          typeof this.clockRules.clockLocation === 'string' &&
          this.clockRules.clockLocation.trim() !== '') {
        try {
          const clockLocation = JSON.parse(this.clockRules.clockLocation)

          // 确保解析后是一个有效的坐标数组
          if (Array.isArray(clockLocation) &&
              clockLocation.length === 2 &&
              !isNaN(clockLocation[0]) &&
              !isNaN(clockLocation[1])) {

            // 计算距离
            const distance = calculateDistance(location, clockLocation)

            // 检查是否有有效的打卡范围
            if (this.clockRules.clockRadius && this.clockRules.clockRadius > 0) {
              if (distance > this.clockRules.clockRadius) {
                throw new Error(`您当前位置距离打卡地点${distance.toFixed(0)}米，超出了${this.clockRules.clockRadius}米的打卡范围`)
              }
            }
          }
        } catch (error) {
          console.error('解析打卡位置失败:', error)
          throw error
        }
      }

      return location
    },

    // 处理上班打卡
    async handleClockIn() {
      try {
        this.clockInLoading = true

        // 获取当前员工ID
        const employeeId = this.getCurrentEmployeeId()
        if (!employeeId) {
          this.$message.error('无法获取当前员工信息')
          return
        }

        // 获取地理位置
        let location
        try {
          location = await this.getLocation()
        } catch (error) {
          this.$message.error(error.message)
          this.clockInLoading = false
          return
        }

        // 调用上班打卡API
        const response = await clockIn({
          employeeId,
          location,
          type: 'IN'
        })

        if (response.code === 1) {
          // 使用后端返回的消息
          this.$message.success(response.msg || '上班打卡成功')
          // 重新获取今日考勤记录
          this.getTodayRecord()
          // 刷新考勤记录
          this.getAttendanceRecords()
        } else {
          this.$message.error(response.msg || '上班打卡失败')
        }
      } catch (error) {
        console.error('上班打卡失败', error)
        this.$message.error('上班打卡失败：' + (error.message || '未知错误'))
      } finally {
        this.clockInLoading = false
      }
    },
    // 处理下班打卡
    async handleClockOut() {
      try {
        this.clockOutLoading = true

        // 获取当前员工ID
        const employeeId = this.getCurrentEmployeeId()
        if (!employeeId) {
          this.$message.error('无法获取当前员工信息')
          return
        }

        // 获取地理位置
        let location
        try {
          location = await this.getLocation()
        } catch (error) {
          this.$message.error(error.message)
          this.clockOutLoading = false
          return
        }

        // 调用下班打卡API
        const response = await clockOut({
          employeeId,
          location,
          type: 'OUT'
        })

        if (response.code === 1) {
          // 使用后端返回的消息
          this.$message.success(response.msg || '下班打卡成功')
          // 重新获取今日考勤记录
          this.getTodayRecord()
          // 刷新考勤记录
          this.getAttendanceRecords()
        } else {
          this.$message.error(response.msg || '下班打卡失败')
        }
      } catch (error) {
        console.error('下班打卡失败', error)
        this.$message.error('下班打卡失败：' + (error.message || '未知错误'))
      } finally {
        this.clockOutLoading = false
      }
    },
    // 处理上一月
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--
        this.currentMonth = 12
      } else {
        this.currentMonth--
      }
      this.getAttendanceRecords()
    },
    // 处理下一月
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++
        this.currentMonth = 1
      } else {
        this.currentMonth++
      }
      this.getAttendanceRecords()
    },
    // 获取上班打卡类名
    getClockInClass(record) {
      if (!record.clockInTime) return ''
      if (record.clockInStatus === 'LATE') return 'text-warning'
      if (record.clockInStatus === 'ABSENT') return 'text-danger'
      return ''
    },
    // 获取下班打卡类名
    getClockOutClass(record) {
      if (!record.clockOutTime) return ''
      if (record.clockOutStatus === 'EARLY') return 'text-warning'
      if (record.clockOutStatus === 'ABSENT') return 'text-danger'
      return ''
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'NORMAL': 'success',
        'LATE': 'warning',
        'EARLY': 'warning',
        'ABSENT': 'danger',
        'LEAVE': 'info',
        'WEEKEND': '',
        'FUTURE': ''
      }
      return statusMap[status] || ''
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'NORMAL': '正常',
        'LATE': '迟到',
        'EARLY': '早退',
        'ABSENT': '无打卡',
        'LEAVE': '请假',
        'WEEKEND': '周末',
        'FUTURE': '未来'
      }
      return statusMap[status] || '未知'
    },
    // 获取签到状态类型
    getClockInStatusType(status) {
      const statusMap = {
        'NORMAL': 'success',
        'LATE': 'warning',
        'ABSENT': 'danger',
        'LEAVE': 'info',
        'WEEKEND': 'info',
        'FUTURE': 'info',
        null: 'danger',
        '': 'danger'
      }
      return statusMap[status] || ''
    },
    // 获取签到状态文本
    getClockInStatusText(status) {
      const statusMap = {
        'NORMAL': '正常',
        'LATE': '迟到',
        'ABSENT': '缺勤',
        'LEAVE': '请假',
        'WEEKEND': '周末',
        'FUTURE': '未来',
        null : '无记录',
        '': '无记录'
      }
      return statusMap[status] || '未知'
    },
    // 获取签退状态类型
    getClockOutStatusType(status) {
      const statusMap = {
        'NORMAL': 'success',
        'EARLY': 'warning',
        'ABSENT': 'danger',
        'LEAVE': 'info',
        'WEEKEND': 'info',
        'FUTURE': 'info',
        null: 'danger',
        '' : 'danger'
      }
      return statusMap[status] || ''
    },
    // 获取签退状态文本
    getClockOutStatusText(status) {
      const statusMap = {
        'NORMAL': '正常',
        'EARLY': '早退',
        'ABSENT': '缺勤',
        'LEAVE': '请假',
        'WEEKEND': '周末',
        'FUTURE': '未来',
        null: '无记录',
        '': '无记录'

      }
      return statusMap[status] || '未知'
    },
    // 获取状态对应的CSS类名
    getStatusClass(status) {
      if (status === '正常') return 'status-normal'
      if (status === '迟到' || status === '早退') return 'status-warning'
      if (status === '缺勤') return 'status-danger'
      if (status === '请假') return 'status-info'
      if (status === '周末' || status === '未来') return 'status-blue'
      return ''
    },
    // 格式化时间显示
    formatTime(time) {
      if (!time) return '-'

      // 如果是日期时间格式
      if (typeof time === 'string') {
        // 检查是否包含日期部分
        if (time.includes('T') || time.includes('-')) {
          return moment(time).format('HH:mm')
        }

        // 检查是否只有时间部分
        if (time.includes(':')) {
          return time.substring(0, 5) // 只取HH:mm部分
        }

        // 如果是没有分隔符的时间格式
        if (time.length === 6) { // HHmmss
          return moment(time, 'HHmmss').format('HH:mm')
        }
      }

      // 其他情况
      try {
        return moment(time).format('HH:mm')
      } catch (e) {
        console.error('时间格式化失败:', time, e)
        return time || '-'
      }
    },
    // 查看考勤详情
    async handleViewAttendanceDetail(row) {
      console.log('查看考勤详情:', row)

      try {
        this.loading = true

        // 根据当前视图模式决定是否调用接口
        if (this.viewMode === 'subordinates') {
          // 员工考勤 - 调用获取考勤详情的API
          const response = await getAttendance(row.id)
          console.log('考勤详情API返回:', response)

          if (response.code === 1 && response.data) {
            const detailData = response.data

            // 获取日期和星期
            let date = detailData.date || '-'
            // 使用现有的getWeekday方法，但需要将"星期"替换为"周"以保持一致性
            let weekday = (date !== '-' ? this.getWeekday(moment(date).day()).replace('星期', '周') : '-')

            // 创建一个详情对象，用于显示
            this.attendanceDetailInfo = {
              id: detailData.id,
              date: date,
              weekday: weekday,
              employeeName: detailData.employeeName,
              workNo: detailData.workNo,
              departmentName: detailData.departmentName,
              clockInTime: detailData.clockInTime || '未打卡',
              clockInStatus: detailData.clockInStatus,
              clockInStatusText: this.getClockInStatusText(detailData.clockInStatus),
              clockInLocation: detailData.clockInLocation || '无位置信息',
              clockOutTime: detailData.clockOutTime || '未打卡',
              clockOutStatus: detailData.clockOutStatus,
              clockOutStatusText: this.getClockOutStatusText(detailData.clockOutStatus),
              clockOutLocation: detailData.clockOutLocation || '无位置信息',
              deduction: detailData.deduction,
              deductionText: detailData.deduction ? `¥${parseFloat(detailData.deduction).toFixed(2)}` : '无扣款',
              createTime: detailData.createTime ? moment(detailData.createTime).format('YYYY-MM-DD HH:mm:ss') : '-',
              updateTime: detailData.updateTime ? moment(detailData.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-',
              // 标记是员工考勤详情
              isSubordinate: true
            }
          } else {
            this.$message.error(response.msg || '获取考勤详情失败')
            this.loading = false
            return
          }
        } else {
          // 个人考勤 - 直接使用行数据
          // 获取日期和星期
          let date = row.date || '-'
          // 使用现有的getWeekday方法，但需要将"星期"替换为"周"以保持一致性
          let weekday = row.weekday || (date !== '-' ? this.getWeekday(moment(date).day()).replace('星期', '周') : '-')

          // 创建一个详情对象，用于显示
          this.attendanceDetailInfo = {
            id: row.id,
            date: date,
            weekday: weekday,
            clockInTime: row.clockInTime || '未打卡',
            clockInStatus: row.clockInStatus,
            clockInStatusText: this.getClockInStatusText(row.clockInStatus),
            clockInLocation: row.clockInLocation || '无位置信息',
            clockOutTime: row.clockOutTime || '未打卡',
            clockOutStatus: row.clockOutStatus,
            clockOutStatusText: this.getClockOutStatusText(row.clockOutStatus),
            clockOutLocation: row.clockOutLocation || '无位置信息',
            deduction: row.deduction,
            deductionText: row.deduction ? `¥${parseFloat(row.deduction).toFixed(2)}` : '无扣款',
            createTime: row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-',
            updateTime: row.updateTime ? moment(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-',
            // 标记是个人考勤详情
            isSubordinate: false
          }
        }

        console.log('考勤详情对象:', this.attendanceDetailInfo)

        // 显示考勤详情对话框
        this.attendanceDetailVisible = true
      } catch (error) {
        console.error('获取考勤详情失败:', error)
        this.$message.error('获取考勤详情失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    // 处理考勤设置
    async handleConfigClock() {
      try {
        // 调用获取部门考勤配置的API
        const response = await getDepartmentAttendanceConfig()

        if (response.code === 1 && response.data) {
          // 设置表单数据
          this.clockConfigForm = { ...response.data }
        } else {
          console.warn('获取考勤配置失败:', response.msg)
          // 使用默认设置
          this.clockConfigForm = {
            departmentId: null,
            clockInStartTime: '',
            clockInEndTime: '',
            lateThreshold: null,
            lateDeduction: null,
            clockOutStartTime: '',
            clockOutEndTime: '',
            earlyThreshold: null,
            earlyDeduction: null,
            clockLocation: '',
            clockRadius: null
          }
        }

        this.clockConfigVisible = true

        // 初始化地图
        this.$nextTick(() => {
          this.initConfigMap()
        })
      } catch (error) {
        console.error('获取考勤设置失败', error)
        this.$message.error('获取考勤设置失败：' + (error.message || '未知错误'))
      }
    },

    // 初始化配置地图
    initConfigMap() {
      try {
        // 创建地图实例
        this.map = initMap('mapContainer', {
          zoom: 15,
          resizeEnable: true
        })

        // 创建地理编码实例
        this.geocoder = createGeocoder()

        // 添加定位控件
        const geolocation = new AMap.Geolocation({
          enableHighAccuracy: true, // 是否使用高精度定位，默认:true
          timeout: 10000, // 超过10秒后停止定位，默认：无穷大
          zoomToAccuracy: true, // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
          buttonPosition: 'RB', // 定位按钮的停靠位置
          buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
          showMarker: false, // 定位成功后在定位到的位置显示点标记，默认：true
          showCircle: false, // 定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, // 定位成功后将定位到的位置作为地图中心点，默认：true
          position: 'RB' // 定位按钮的位置
        })

        this.map.addControl(geolocation)

        // 如果有打卡位置，则显示打卡位置和范围
        if (this.clockConfigForm.clockLocation) {
          try {
            // 解析打卡位置
            const location = JSON.parse(this.clockConfigForm.clockLocation)

            // 设置地图中心点
            this.map.setCenter(location)

            // 添加标记
            this.marker = createMarker(location, this.map, {
              draggable: true,
              title: '打卡地点'
            })

            // 添加圆形范围
            this.circle = createCircle(location, this.clockConfigForm.clockRadius || 100, this.map)

            // 监听标记拖拽结束事件
            this.marker.on('dragend', () => {
              const position = this.marker.getPosition()
              this.clockConfigForm.clockLocation = JSON.stringify([position.lng, position.lat])
              this.circle.setCenter(position)

              // 逆地理编码获取地点名称
              this.updateAddressFromPosition(position)
            })
          } catch (error) {
            console.error('解析打卡位置失败:', error)
            this.initDefaultMap()
          }
        } else {
          this.initDefaultMap()
        }

        // 监听地图点击事件
        this.map.on('click', (e) => {
          try {
            const position = e.lnglat

            // 验证坐标有效性
            if (!position || typeof position.lng !== 'number' || typeof position.lat !== 'number' ||
                isNaN(position.lng) || isNaN(position.lat)) {
              console.error('地图点击获取的位置坐标无效', position)
              return
            }

            this.clockConfigForm.clockLocation = JSON.stringify([position.lng, position.lat])

            // 如果已经有标记，则移动标记
            if (this.marker) {
              this.marker.setPosition(position)
            } else {
              // 否则创建新标记
              const marker = createMarker(position, this.map, {
                draggable: true,
                title: '打卡地点'
              })

              // 确保标记创建成功
              if (marker) {
                this.marker = marker

                // 监听标记拖拽结束事件
                this.marker.on('dragend', () => {
                  const newPosition = this.marker.getPosition()
                  this.clockConfigForm.clockLocation = JSON.stringify([newPosition.lng, newPosition.lat])

                  if (this.circle) {
                    this.circle.setCenter(newPosition)
                  }

                  // 逆地理编码获取地点名称
                  this.updateAddressFromPosition(newPosition)
                })
              }
            }

            // 如果已经有圆形，则移动圆形
            if (this.circle) {
              this.circle.setCenter(position)
            } else if (this.marker) { // 只有当标记创建成功时才创建圆形
              // 否则创建新圆形
              this.circle = createCircle(position, this.clockConfigForm.clockRadius || 100, this.map)
            }

            // 逆地理编码获取地点名称
            this.updateAddressFromPosition(position)
          } catch (error) {
            console.error('处理地图点击事件失败:', error)
          }
        })

        // 监听打卡范围变化
        this.$watch('clockConfigForm.clockRadius', (newValue) => {
          if (this.circle) {
            this.circle.setRadius(newValue)
          }
        })
      } catch (error) {
        console.error('初始化配置地图失败:', error)
      }
    },

    // 从位置更新地址
    async updateAddressFromPosition(position) {
      try {
        // 使用高德地图API获取地址
        const location = [position.lng, position.lat]
        const address = await getAddressFromAPI(location)
        console.log('获取到的地址:', address)
        this.searchAddress = address
      } catch (error) {
        console.error('获取地址失败:', error)
        // 如果API调用失败，尝试使用AMap.Geocoder
        try {
          const address = await getAddress(position, this.geocoder)
          this.searchAddress = address
        } catch (secondError) {
          console.error('备用方法获取地址也失败:', secondError)
          this.searchAddress = '未知地点'
        }
      }
    },

    // 初始化默认地图
    async initDefaultMap() {
      try {
        // 获取当前位置
        const result = await getCurrentPosition()
        const position = result.position

        // 验证坐标有效性
        if (!position || typeof position.lng !== 'number' || typeof position.lat !== 'number' ||
            isNaN(position.lng) || isNaN(position.lat)) {
          console.error('获取到的用户位置坐标无效', position)
          // 使用默认位置（北京）
          this.map.setCenter([116.397428, 39.90923])
          return
        }

        this.map.setCenter(position)

        // 创建用户位置标记
        createUserLocationMarker(position, this.map)
      } catch (error) {
        console.warn('获取用户位置失败:', error.message)
        // 使用默认位置（北京）
        this.map.setCenter([116.397428, 39.90923])
      }
    },

    // 搜索地点
    async searchLocation() {
      if (!this.searchAddress) return

      try {
        // 搜索地点 - 使用新的API
        const result = await searchLocation(this.searchAddress)

        // 验证坐标有效性
        if (!result || typeof result.lng !== 'number' || typeof result.lat !== 'number' ||
            isNaN(result.lng) || isNaN(result.lat)) {
          throw new Error('获取到的位置坐标无效')
        }

        // 创建AMap.LngLat对象
        const location = new AMap.LngLat(result.lng, result.lat)

        // 设置地图中心
        this.map.setCenter(location)

        // 更新打卡位置
        this.clockConfigForm.clockLocation = JSON.stringify([result.lng, result.lat])

        // 更新搜索框显示的地址
        this.searchAddress = result.name + ' (' + (result.address || '未知地址') + ')'

        // 如果已经有标记，则移动标记
        if (this.marker) {
          this.marker.setPosition(location)
        } else {
          // 否则创建新标记
          const marker = createMarker(location, this.map, {
            draggable: true,
            title: '打卡地点'
          })

          // 确保标记创建成功
          if (marker) {
            this.marker = marker

            // 监听标记拖拽结束事件
            this.marker.on('dragend', () => {
              const position = this.marker.getPosition()
              this.clockConfigForm.clockLocation = JSON.stringify([position.lng, position.lat])

              if (this.circle) {
                this.circle.setCenter(position)
              }

              // 逆地理编码获取地点名称
              this.updateAddressFromPosition(position)
            })
          }
        }

        // 如果已经有圆形，则移动圆形
        if (this.circle) {
          this.circle.setCenter(location)
        } else if (this.marker) { // 只有当标记创建成功时才创建圆形
          // 创建新圆形
          this.circle = createCircle(location, this.clockConfigForm.clockRadius || 100, this.map)
        }
      } catch (error) {
        this.$message.error(error.message || '地点搜索失败，请尝试其他关键词')
        console.error('搜索地点失败:', error)
      }
    },

    // 提交考勤设置
    submitClockConfig() {
      this.$refs.clockConfigForm.validate(async valid => {
        if (valid) {
          try {
            // 调用更新考勤配置的API
            const response = await updateDepartmentAttendanceConfig(this.clockConfigForm)

            if (response.code === 1) {
              this.$message.success(response.msg || '考勤设置更新成功')
              this.clockConfigVisible = false

              // 更新本地规则
              this.getClockRules()
            } else {
              this.$message.error(response.msg || '考勤设置更新失败')
            }
          } catch (error) {
            console.error('更新考勤设置失败', error)
            this.$message.error('更新考勤设置失败：' + (error.message || '未知错误'))
          }
        }
      })
    },
    // 处理编辑考勤记录
    handleEditRecord(row) {
      console.log('编辑考勤记录:', row)

      // 获取日期
      let date = row.date || moment().format('YYYY-MM-DD')

      // 检查是否为当月记录
      const currentMonth = moment().format('YYYY-MM')
      const recordMonth = moment(date).format('YYYY-MM')

      if (recordMonth !== currentMonth) {
        this.$message.warning('只能修改当月的考勤记录')
        return
      }

      // 创建编辑表单数据
      this.recordEditForm = {
        id: row.id,
        employeeId: row.employeeId,
        date: date,
        clockInTime: row.clockInTime || '',
        clockInStatus: row.clockInStatus || '',
        clockOutTime: row.clockOutTime || '',
        clockOutStatus: row.clockOutStatus || '',
        deduction: row.deduction ? parseFloat(row.deduction) : 0
      }

      console.log('编辑表单数据:', this.recordEditForm)
      this.recordEditVisible = true
    },
    // 提交考勤记录编辑
    submitRecordEdit() {
      this.$refs.recordEditForm.validate(async valid => {
        if (valid) {
          try {
            // 构建提交数据
            const formData = {
              id: this.recordEditForm.id,
              clockInTime: this.recordEditForm.clockInTime,
              clockInStatus: this.recordEditForm.clockInStatus,
              clockOutTime: this.recordEditForm.clockOutTime,
              clockOutStatus: this.recordEditForm.clockOutStatus,
              deduction: this.recordEditForm.deduction
            }

            // 调用更新考勤记录的API
            const response = await updateAttendance(formData)

            if (response.code === 1) {
              this.$message.success(response.msg || '考勤记录更新成功')
              this.recordEditVisible = false
              this.getSubordinatesRecords()
            } else {
              this.$message.error(response.msg || '考勤记录更新失败')
            }
          } catch (error) {
            console.error('更新考勤记录失败', error)
            this.$message.error('更新考勤记录失败：' + (error.message || '未知错误'))
          }
        }
      })
    }
  },
  watch: {
    viewMode(newVal) {
      if (newVal === 'subordinates') {
        this.getSubordinatesRecords()
      } else if (newVal === 'self') {
        this.getTodayRecord()
        this.getAttendanceRecords()
        this.getClockRules()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }
}

.attendance-container {
  margin-top: 20px;
}

.attendance-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 25px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 5px;
        // color: #409EFF;
      }
    }
  }

  .month-selector {
    display: flex;
    align-items: center;

    .month-text {
      margin: 0 10px;
      font-size: 14px;
      font-weight: bold;
    }
  }
}

.clock-card {
  margin-bottom: 20px;

  .clock-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
  }

  .current-time {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #409EFF;
  }

  .current-date {
    font-size: 16px;
    color: #606266;
    margin-bottom: 20px;
  }

  .clock-status {
    width: 100%;
    margin-bottom: 20px;

    .status-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 0 20px;

      .label {
        color: #606266;
      }

      .value {
        color: #909399;

        &.clocked {
          color: #67C23A;
          font-weight: bold;
        }
      }
    }
  }

  .clock-buttons {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-bottom: 20px;

    .el-button {
      width: 120px;
    }
  }

  .clock-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f0f9eb;
    color: #67C23A;
    border-radius: 4px;

    i {
      margin-right: 5px;
    }
  }

  .clock-rules {
    width: 100%;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;

    .rule-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }

    .rule-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 0 20px;

      .label {
        color: #606266;
        flex-shrink: 0;
      }

      .value {
        color: #409EFF;
        text-align: right;
      }

      &.location-item {
        align-items: flex-start;

        .location-value {
          max-width: 80%;
          // word-break: break-all;
          // line-height: 1.5;
          text-align: right;
        }
      }
    }
  }
}

.attendance-summary {
  margin-bottom: 20px;
}

.summary-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .summary-icon {
    font-size: 24px;
    color: #409EFF;
    margin-bottom: 8px;

    i {
      background-color: rgba(64, 158, 255, 0.1);
      border-radius: 50%;
      padding: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
    }
  }

  .summary-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 8px;
  }

  .summary-value {
    font-size: 26px;
    font-weight: bold;
    color: #409EFF;
  }

  &.success-card {
    .summary-icon {
      color: #67C23A;

      i {
        background-color: rgba(103, 194, 58, 0.1);
      }
    }

    .summary-value {
      color: #67C23A;
    }
  }

  &.warning-card {
    .summary-icon {
      color: #E6A23C;

      i {
        background-color: rgba(230, 162, 60, 0.1);
      }
    }

    .summary-value {
      color: #E6A23C;
    }
  }

  &.danger-card {
    .summary-icon {
      color: #F56C6C;

      i {
        background-color: rgba(245, 108, 108, 0.1);
      }
    }

    .summary-value {
      color: #F56C6C;
    }
  }

  &.info-card {
    .summary-icon {
      color: #909399;

      i {
        background-color: rgba(144, 147, 153, 0.1);
      }
    }

    .summary-value {
      color: #909399;
    }
  }
}

.profile-container {
  margin-top: 20px;
}

.table-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }
  }
}

.subordinates-container {
  margin-top: 20px;

  .table-operations {
    margin-top: 15px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-operations-left {
      display: flex;
      align-items: center;
    }

    .table-operations-right {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.filter-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  height: 70px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.attendance-detail-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .attendance-detail-container {
    .detail-header {
      text-align: center;
      margin-bottom: 20px;

      .detail-date {
        display: inline-block;
        padding: 10px 20px;
        background-color: #ecf5ff;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        margin-bottom: 10px;

        .date {
          font-size: 20px;
          font-weight: bold;
          color: #409EFF;
          margin-right: 10px;
        }

        .weekday {
          font-size: 16px;
          color: #606266;
        }
      }

      .employee-info {
        margin-top: 10px;

        .employee-name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 5px;
        }

        .employee-workno {
          font-size: 14px;
          color: #909399;
          margin-right: 10px;
        }

        .employee-department {
          font-size: 14px;
          color: #606266;
          background-color: #f0f2f5;
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }

    .detail-card {
      margin-bottom: 15px;
      border-radius: 4px;
      overflow: hidden;

      .card-header {
        display: flex;
        align-items: center;
        font-weight: 600;

        i {
          margin-right: 5px;
          font-size: 16px;
          color: #409EFF;
        }
      }

      .card-content {
        padding: 10px 0;

        .info-row {
          display: flex;
          margin-bottom: 10px;
          align-items: center;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            width: 50px;
            color: #606266;
            font-weight: 500;
          }

          .info-value {
            flex: 1;
            color: #303133;

            &.location-text {
              word-break: break-all;
            }
          }

          .deduction-value {
            color: #F56C6C;
            font-weight: 600;
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
  }
}

.text-warning {
  color: #E6A23C;
}

.text-danger {
  color: #EC6B6A;
}

.deduction-value {
  color: #F56C6C;
  font-weight: bold;
}

/* 打卡时间颜色样式 */
.time-normal {
  color: #67C23A !important;
}

.time-warning {
  color: #E6A23C !important;
}

.time-danger {
  color: #F56C6C !important;
}

.time-info {
  color: #909399 !important;
}

/* 自定义周末和未来状态的标签样式 */
.weekend-tag, .future-tag {
  color: #409EFF !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.2) !important;
}

.time-separator {
  text-align: center;
  line-height: 40px;
}

.text-center {
  text-align: center;
}
</style>