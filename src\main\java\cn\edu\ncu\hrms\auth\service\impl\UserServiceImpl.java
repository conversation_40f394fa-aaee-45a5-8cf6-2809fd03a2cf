package cn.edu.ncu.hrms.auth.service.impl;

import cn.edu.ncu.hrms.auth.dto.LoginDTO;
import cn.edu.ncu.hrms.auth.dto.PasswordChangeDTO;
import cn.edu.ncu.hrms.auth.dto.RegisterDTO;
import cn.edu.ncu.hrms.auth.mapper.PermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.RoleMapper;
import cn.edu.ncu.hrms.auth.mapper.UserMapper;
import cn.edu.ncu.hrms.auth.mapper.UserRoleMapper;
import cn.edu.ncu.hrms.auth.service.UserService;
import cn.edu.ncu.hrms.auth.vo.UserInfoVO;
import cn.edu.ncu.hrms.common.entity.Role;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.entity.UserRole;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.JwtUtil;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.edu.ncu.hrms.common.constant.RoleConstant.ROLE_EMPLOYEE;

/**
 * 用户认证服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    @Lazy
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SecurityUtil securityUtil;

    /**
     * 根据用户名获取用户实体信息（主要供内部认证流程使用）
     *
     * @param username 用户名
     * @return User 用户实体，如果不存在则返回 null
     */
    @Override
    public User getUserByUsername(String username) {
        // 此方法主要由 Spring Security 内部调用，用于加载用户信息
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            log.warn("尝试加载不存在或已停用的用户: {}", username); // 保留警告日志
        }
        return user;
    }

    /**
     * 处理用户登录请求
     * 
     * 1. 校验用户名是否存在且用户状态正常。
     * 2. 校验密码是否匹配。
     * 3. 生成 JWT 令牌。
     * 4. 查询用户权限信息。
     * 5. 将用户信息（可选）和权限信息存入 Redis，设置过期时间。
     *
     * @param loginDTO 包含用户名和密码的登录数据传输对象
     * @return Result<String> 包含JWT令牌的登录结果，若登录失败则返回错误信息。
     */
    @Override
    public Result<String> login(LoginDTO loginDTO) {
        // 1. 校验用户名是否存在且用户状态正常
        User user = userMapper.selectByUsername(loginDTO.getUsername());
        if (user == null || user.getDelFlag()) { // 同时检查用户是否存在和是否被删除
            log.warn("登录失败：用户不存在或已被停用，用户名: {}", loginDTO.getUsername());
            return Result.error("用户不存在或密码错误"); // 统一错误提示
        }

        // 2. 校验密码是否匹配
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            log.warn("登录失败：密码错误，用户名: {}", loginDTO.getUsername());
            return Result.error("用户不存在或密码错误");
        }

        if (loginDTO.getIsAdmin() && !user.getIsAdmin()) {
            log.warn("登录失败：非管理员无法登录管理员系统，用户名: {}", loginDTO.getUsername());
            return Result.error("非管理员无法登录管理员系统");
        }

        if (!loginDTO.getIsAdmin() && user.getIsAdmin() && user.getEmployeeId() == null) {
            log.warn("登录失败：管理员登录员工系统但关联员工，用户名: {}", loginDTO.getUsername());
            return Result.error("未关联员工，无法登录员工系统");
        }

        // 3. 生成 JWT 令牌
        String token = jwtUtil.generateToken(loginDTO.getUsername());

        // 4. 查询用户权限信息
        List<String> permissions = permissionMapper.selectPermKeysByUserId(user.getId());
        List<String> roleKeys = roleMapper.selectRolesByUserId(user.getId()).stream().map(Role::getRoleKey).collect(Collectors.toList());
        permissions.addAll(roleKeys);


        // 5. 将用户信息（可选）和权限信息存入 Redis
        redisTemplate.opsForValue().set(String.format("login:%s", loginDTO.getUsername()), user, 24, TimeUnit.HOURS);
        redisTemplate.opsForValue().set(String.format("permissions:%s", loginDTO.getUsername()), permissions, 24, TimeUnit.HOURS);
        log.info("用户登录成功，用户名: {}", loginDTO.getUsername());
        return Result.success(token, "登录成功");
    }

    /**
     * 处理用户登出请求
     * 
     * 1. 从 Redis 中删除用户的登录信息（如果存在）。
     * 2. 从 Redis 中删除用户的权限信息。
     *
     * @param username 需要登出的用户名
     * @return Result<Void> 操作结果
     */
    @Override
    public Result<Void> logout(String username) {
        log.info("开始处理登出请求，用户名: {}", username);
        // 1. & 2. 从Redis中删除用户信息和权限信息
        redisTemplate.delete(String.format("login:%s", username));
        redisTemplate.delete(String.format("permissions:%s", username));
        log.info("用户登出成功，用户名: {}", username);
        return Result.success();
    }

    /**
     * 处理员工注册请求
     * 
     * 1. 校验用户名是否已被注册。
     * 2. 校验员工工号是否存在于员工档案中。
     * 3. 校验该员工工号是否已被其他用户绑定。
     * 4. 创建新的用户记录，密码进行加密处理。
     * 5. 查询默认的员工角色。
     * 6. 为新用户绑定员工角色。
     * 
     * 使用 @Transactional 保证操作的原子性。
     *
     * @param registerDTO 包含注册信息的员工注册数据传输对象
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> register(RegisterDTO registerDTO) {
        log.info("开始处理员工注册请求，用户名: {}, 工号: {}", registerDTO.getUsername(), registerDTO.getEmployeeId());

        // 1. 校验用户名是否已被注册
        if (userMapper.existsByUsername(registerDTO.getUsername()) > 0) {
            log.warn("注册失败：用户名已存在，用户名: {}", registerDTO.getUsername());
            return Result.error("用户名已存在");
        }

        // 2. 校验员工工号是否存在于员工档案中
        Employee employee = employeeMapper.selectByWorkNo(registerDTO.getEmployeeId());
        if (employee == null) {
            log.warn("注册失败：员工工号不存在，工号: {}", registerDTO.getEmployeeId());
            return Result.error("员工工号不存在，请联系HR确认");
        }

        // 3. 校验该员工工号是否已被其他用户绑定
        User existingUserWithEmployeeId = userMapper.selectByEmployeeId(employee.getId());
        if (existingUserWithEmployeeId != null) {
            log.warn("注册失败：该工号已被注册，工号: {}, 关联用户: {}", registerDTO.getEmployeeId(), existingUserWithEmployeeId.getUsername());
            return Result.error("该工号已被注册，如有疑问请联系管理员");
        }

        // 4. 创建新的用户记录，密码进行加密处理
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword())); // 加密密码
        user.setIsAdmin(false); // 员工注册默认为非管理员
        user.setEmployeeId(employee.getId()); // 关联员工ID
        // user.setDelFlag(false); // 默认为 false，无需显式设置
        // user.setCreateTime(LocalDateTime.now()); // 数据库或MyBatis Plus可自动填充
        // user.setUpdateTime(LocalDateTime.now());
        int insertUserResult = userMapper.insert(user);
        if (insertUserResult <= 0 || user.getId() == null) {
             log.error("注册失败：插入用户数据失败，用户名: {}", registerDTO.getUsername());
             throw new BusinessException("用户注册失败，请稍后重试"); // 触发事务回滚
        }

        // 5. 查询默认的员工角色
        Role employeeRole = roleMapper.selectByRoleKey(ROLE_EMPLOYEE);
        if (employeeRole == null) {
            log.error("注册失败：未找到基础员工角色 '{}'", ROLE_EMPLOYEE);
            throw new BusinessException("系统错误：员工角色配置不存在"); // 触发事务回滚
        }

        // 6. 为新用户绑定员工角色
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getId());
        userRole.setRoleId(employeeRole.getId());
        int insertUserRoleResult = userRoleMapper.insert(userRole);
        if (insertUserRoleResult <= 0) {
             log.error("注册失败：分配角色失败，用户ID: {}, 角色ID: {}", user.getId(), employeeRole.getId());
             throw new BusinessException("用户注册失败，请稍后重试"); // 触发事务回滚
        }

        log.info("员工用户注册成功，用户名: {}, 工号: {}", registerDTO.getUsername(), registerDTO.getEmployeeId());
        return Result.success();
    }

    /**
     * 修改当前登录用户的密码
     * 
     * 1. 根据用户名查询用户信息。
     * 2. 校验旧密码是否正确。
     * 3. 对新密码进行加密。
     * 4. 更新数据库中的用户密码。
     * 5. 从 Redis 中删除该用户的登录和权限缓存，强制用户下次请求时重新认证。
     * 
     * 使用 @Transactional 保证操作的原子性。
     *
     * @param username           当前登录的用户名
     * @param passwordChangeDTO 包含旧密码和新密码的数据传输对象
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> changePassword(String username, PasswordChangeDTO passwordChangeDTO) {
        log.info("开始处理修改密码请求，用户名: {}", username);

        // 1. 根据用户名查询用户信息
        User user = userMapper.selectByUsername(username);
        if (user == null || user.getDelFlag()) { // 检查用户是否存在且未被删除
            log.error("修改密码失败：用户不存在或已被停用，用户名: {}", username);
            // 理论上，能调用此接口的用户应该是存在的，但增加健壮性检查
            return Result.error("用户信息异常，请重新登录");
        }

        // 2. 校验旧密码是否正确
        if (!passwordEncoder.matches(passwordChangeDTO.getOldPassword(), user.getPassword())) {
            log.warn("修改密码失败：旧密码错误，用户名: {}", username);
            return Result.error("旧密码错误");
        }

        // 3. 对新密码进行加密
        String newEncodedPassword = passwordEncoder.encode(passwordChangeDTO.getNewPassword());

        // 4. 更新数据库中的用户密码
        // 注意：只更新密码字段，避免覆盖其他信息。如果 update 方法支持动态更新则没问题。
        // 如果不支持，最好使用专门的 updatePassword 方法。
        User passwordUpdateUser = new User();
        passwordUpdateUser.setId(user.getId());
        passwordUpdateUser.setPassword(newEncodedPassword);
        // 假设 userMapper.update 是动态更新
        int updateResult = userMapper.update(passwordUpdateUser);
        if (updateResult <= 0) {
            log.error("修改密码失败：数据库更新失败，用户ID: {}", user.getId());
            throw new BusinessException("密码修改失败，请稍后重试"); // 触发事务回滚
        }

        // 5. 从 Redis 中删除缓存，强制重新认证
        redisTemplate.delete(String.format("login:%s", username));
        redisTemplate.delete(String.format("permissions:%s", username));
        log.info("用户密码修改成功，并已清除缓存，用户名: {}", username);
        return Result.success();
    }

    /**
     * 获取当前登录用户的详细信息
     * 
     * 1. 从 Spring Security 上下文获取当前登录用户名。
     * 2. 根据用户名从数据库查询用户基本信息。
     * 3. 尝试从 Redis 缓存获取用户权限列表。
     * 4. 如果缓存未命中，则从数据库查询权限列表，并回存 Redis。
     * 5. 组装 UserInfoVO 对象返回。
     *
     * @return Result<UserInfoVO> 包含用户详细信息的操作结果
     */
    @Override
    public Result<UserInfoVO> getCurrentUserInfo() {
        // 1. 从 Spring Security 上下文获取当前登录用户名
        String username = securityUtil.getCurrentUsername();
        if (username == null) {
            log.error("获取当前用户信息失败：无法从 SecurityContext 获取用户名");
            return Result.error("无法获取用户信息，请确认登录状态");
        }
        log.info("开始获取当前登录用户信息，用户名: {}", username);

        // 2. 根据用户名从数据库查询用户基本信息
        User user = userMapper.selectByUsername(username);
        if (user == null || user.getDelFlag()) {
            log.error("获取当前用户信息失败：用户不存在或已被停用，用户名: {}", username);
            return Result.error("用户不存在或已被停用");
        }

        // 3. 尝试从 Redis 缓存获取用户权限列表
        List<String> permissions = null;
        try {
            permissions = (List<String>) redisTemplate.opsForValue().get(String.format("permissions:%s", username));
        } catch (Exception e) {
             log.error("从 Redis 获取权限信息时发生异常，用户名: {}", username, e);
        }
       
        // 4. 如果缓存未命中或获取异常，则从数据库查询权限列表，并回存 Redis
        if (permissions == null) {
            log.warn("Redis 权限缓存未命中或获取失败，将从数据库查询，用户名: {}", username);
            permissions = permissionMapper.selectPermKeysByUserId(user.getId());
            // 回存 Redis
            try {
                 redisTemplate.opsForValue().set(String.format("permissions:%s", username), permissions, 2, TimeUnit.HOURS);
            } catch (Exception e) {
                 log.error("权限信息回存 Redis 失败，用户名: {}", username, e);
            }
        }

        // 查询用户的角色列表
        List<Role> roles = roleMapper.selectRolesByUserId(user.getId());
        List<String> roleKeys = roles.stream().map(Role::getRoleKey).collect(Collectors.toList());

        // 5. 组装 UserInfoVO 对象返回
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        userInfoVO.setPermissions(permissions);
        userInfoVO.setRoleKeys(roleKeys);


        log.info("获取当前登录用户信息成功，用户名: {}", username);
        return Result.success(userInfoVO);
    }
} 