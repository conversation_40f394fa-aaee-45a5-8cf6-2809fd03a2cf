<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img src="https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif" class="user-avatar" />
          <span class="user-name">{{ name }}</span>
          <i class="el-icon-arrow-down" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="showUsernameDialog">
            <span style="display:block;">修改用户名</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="showPasswordDialog">
            <span style="display:block;">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="passwordDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :destroy-on-close="true"
      custom-class="password-dialog"
    >
      <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules" label-width="100px">
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password placeholder="请输入原密码" />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password placeholder="请输入新密码" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password placeholder="请再次输入新密码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="changePassword">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 修改用户名对话框 -->
    <el-dialog
      title="修改用户名"
      :visible.sync="usernameDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :destroy-on-close="true"
      custom-class="username-dialog"
    >
      <el-form ref="usernameForm" :model="usernameForm" :rules="usernameRules" label-width="100px">
        <el-form-item label="当前用户名">
          <el-input v-model="name" disabled />
        </el-form-item>
        <el-form-item label="新用户名" prop="newUsername">
          <el-input v-model="usernameForm.newUsername" placeholder="请输入新用户名" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="usernameDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="changeUsername">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import { changePassword, changeUsername } from '@/api/user'

export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  data() {
    // 验证确认密码是否与新密码一致
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    return {
      passwordDialogVisible: false,
      usernameDialogVisible: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      usernameForm: {
        newUsername: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      usernameRules: {
        newUsername: [
          { required: true, message: '请输入新用户名', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'name'
    ])
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.$store.dispatch('user/logout')
        this.$message.success('退出登录成功')
        this.$router.push('/login')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败', error)
        }
      }
    },
    showPasswordDialog() {
      this.passwordDialogVisible = true
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    },

    showUsernameDialog() {
      this.usernameDialogVisible = true
      this.usernameForm = {
        newUsername: ''
      }
    },
    async changePassword() {
      try {
        await this.$refs.passwordForm.validate()
        const response = await changePassword({
          oldPassword: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        })

        if (response && response.code === 0) {
          // 密码修改失败
          this.$message.error(response.msg || '密码修改失败')
        } else {
          // 密码修改成功
          this.$message.success('密码修改成功，请重新登录')
          this.passwordDialogVisible = false
          // 直接退出登录，不需要用户确认
          this.$alert('您修改了自己的密码，需要重新登录', '提示', {
            confirmButtonText: '确定',
            callback: async () => {
              await this.$store.dispatch('user/logout')
              this.$router.push('/login')
            }
          })
        }
      } catch (error) {
        console.error('修改密码失败', error)
        this.$message.error(error.response?.data?.msg || '修改密码失败')
      }
    },

    async changeUsername() {
      try {
        await this.$refs.usernameForm.validate()
        const userId = this.$store.getters.userId
        const response = await changeUsername(userId, {
          username: this.usernameForm.newUsername
        })

        if (response && response.code === 0) {
          this.$message.error(response.msg || '用户名修改失败')
        } else {
          this.$message.success('用户名修改成功，请重新登录')
          this.usernameDialogVisible = false

          // 直接退出登录，不需要用户确认
          this.$alert('您修改了自己的用户名，需要重新登录', '提示', {
            confirmButtonText: '确定',
            callback: async () => {
              await this.$store.dispatch('user/logout')
              this.$router.push('/login')
            }
          })
        }
      } catch (error) {
        console.error('修改用户名失败', error)
        this.$message.error(error.response?.data?.msg || '修改用户名失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 0px;
        position: relative;
        display: flex;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }

        .user-name {
          margin-left: 5px;
          margin-right: 5px;
          cursor: pointer;
          color: #333;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
/* 对话框公共样式 */
.password-dialog,
.username-dialog {
  .el-dialog {
    z-index: 3000 !important; /* 提高z-index确保在最上层 */
  }

  .el-dialog__header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 30px 20px;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
    border-top: 1px solid #f0f0f0;
  }

  .dialog-footer {
    .el-button {
      padding: 9px 20px;
    }
  }
}
