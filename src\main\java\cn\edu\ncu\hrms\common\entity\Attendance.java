package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.beans.Transient;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("考勤记录")
public class Attendance {
    @ApiModelProperty(value = "考勤记录ID", required = true, example = "701")
    private Integer id;

    @ApiModelProperty(value = "员工ID", required = true, example = "1001")
    private Integer employeeId;

    @ApiModelProperty(value = "考勤日期", required = true, example = "2025-04-15")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    @ApiModelProperty(value = "上班签到时间", example = "08:58:00")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime clockInTime;

    @ApiModelProperty(value = "上班签到位置（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockInLocation;

    @ApiModelProperty(value = "上班签到状态", allowableValues = "NORMAL,LATE,ABSENT,LEAVE", example = "NORMAL")
    private String clockInStatus;

    @ApiModelProperty(value = "下班签退时间", example = "18:02:00")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime clockOutTime;

    @ApiModelProperty(value = "下班签退位置（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockOutLocation;

    @ApiModelProperty(value = "下班签退状态", allowableValues = "NORMAL,EARLY,ABSENT,LEAVE", example = "NORMAL")
    private String clockOutStatus;

    @ApiModelProperty(value = "扣除金额", example = "0.00")
    private BigDecimal deduction;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;

    private transient boolean needsUpdate = false;

    public boolean isNeedsUpdate() {
        return needsUpdate;
    }

    public void setNeedsUpdate(boolean needsUpdate) {
        this.needsUpdate = needsUpdate;
    }
}
