/**
 * Firefox浏览器兼容性处理工具
 * 专门解决Firefox在加载高德地图API时的兼容性问题
 */

// 检测是否为Firefox浏览器
export function isFirefox() {
  return navigator.userAgent.toLowerCase().indexOf('firefox') > -1
}

// Firefox专用的高德地图API加载器
export function loadAmapForFirefox() {
  return new Promise((resolve, reject) => {
    // 如果不是Firefox，直接解析
    if (!isFirefox()) {
      resolve()
      return
    }

    // 如果AMap已经存在，直接解析
    if (window.AMap) {
      resolve()
      return
    }

    console.log('检测到Firefox浏览器，使用兼容性加载方案')

    // 创建一个临时的错误处理器
    const originalErrorHandler = window.onerror
    const originalUnhandledRejection = window.onunhandledrejection

    // 临时屏蔽高德地图相关错误
    window.onerror = function(message, source, lineno, colno, error) {
      if (source && (source.includes('webapi.amap.com') || source.includes('count?type=UIInit'))) {
        console.warn('Firefox: 已忽略高德地图统计错误')
        return true
      }
      if (originalErrorHandler) {
        return originalErrorHandler.call(this, message, source, lineno, colno, error)
      }
    }

    window.onunhandledrejection = function(event) {
      if (event.reason && event.reason.toString().includes('amap')) {
        console.warn('Firefox: 已忽略高德地图Promise错误')
        event.preventDefault()
        return
      }
      if (originalUnhandledRejection) {
        return originalUnhandledRejection.call(this, event)
      }
    }

    // 延迟加载高德地图API
    setTimeout(() => {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662&plugin=AMap.Scale,AMap.ToolBar,AMap.Geocoder,AMap.Geolocation'
      script.async = true
      
      script.onload = () => {
        console.log('Firefox: 高德地图API加载成功')
        // 恢复原始错误处理器
        setTimeout(() => {
          window.onerror = originalErrorHandler
          window.onunhandledrejection = originalUnhandledRejection
        }, 2000)
        resolve()
      }

      script.onerror = () => {
        console.error('Firefox: 高德地图API加载失败')
        // 恢复原始错误处理器
        window.onerror = originalErrorHandler
        window.onunhandledrejection = originalUnhandledRejection
        reject(new Error('高德地图API加载失败'))
      }

      document.head.appendChild(script)
    }, 500)
  })
}

// Firefox专用的错误抑制器
export function suppressAmapErrorsForFirefox() {
  if (!isFirefox()) {
    return
  }

  // 监听全局错误事件
  window.addEventListener('error', function(event) {
    const target = event.target || event.srcElement
    
    // 检查是否是高德地图相关的脚本错误
    if (target && target.src && target.src.includes('webapi.amap.com')) {
      console.warn('Firefox: 已抑制高德地图脚本错误')
      event.preventDefault()
      event.stopPropagation()
      return false
    }

    // 检查错误消息是否包含高德地图相关内容
    if (event.message && (
      event.message.includes('webapi.amap.com') ||
      event.message.includes('count?type=UIInit') ||
      event.message.includes('AMap') ||
      event.message.includes('amap')
    )) {
      console.warn('Firefox: 已抑制高德地图相关错误:', event.message)
      event.preventDefault()
      event.stopPropagation()
      return false
    }
  }, true)

  // 监听未处理的Promise拒绝
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.toString().toLowerCase().includes('amap')) {
      console.warn('Firefox: 已抑制高德地图Promise错误:', event.reason)
      event.preventDefault()
    }
  })

  console.log('Firefox: 高德地图错误抑制器已启动')
}

// 初始化Firefox兼容性处理
export function initFirefoxCompatibility() {
  if (isFirefox()) {
    console.log('检测到Firefox浏览器，启动兼容性处理')
    suppressAmapErrorsForFirefox()
  }
}

// 检查高德地图API是否可用
export function checkAmapAvailability() {
  return new Promise((resolve) => {
    const maxAttempts = 10
    let attempts = 0

    const checkInterval = setInterval(() => {
      attempts++
      
      if (window.AMap) {
        clearInterval(checkInterval)
        resolve(true)
        return
      }

      if (attempts >= maxAttempts) {
        clearInterval(checkInterval)
        console.warn('高德地图API检查超时')
        resolve(false)
      }
    }, 500)
  })
}
