<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-left">
        <div class="welcome-text">
          <h2>欢迎使用</h2>
          <h1>HRMS 企业人事管理系统</h1>
          <p>高效、智能的一站式人力资源管理平台</p>
        </div>
      </div>
      <div class="login-right">
        <div class="login-form-container">
          <div class="title-container">
            <h3 class="title">用户登录</h3>
          </div>

          <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">
            <div class="user-type-selector">
              <el-radio-group v-model="isAdmin" size="medium">
                <el-radio :label="false">员工登录</el-radio>
                <el-radio :label="true">管理员登录</el-radio>
              </el-radio-group>
            </div>

            <el-form-item prop="username">
              <span class="svg-container">
                <i class="el-icon-user" />
              </span>
              <el-input
                ref="username"
                v-model="loginForm.username"
                placeholder="请输入用户名"
                name="username"
                type="text"
                tabindex="1"
                autocomplete="on"
              />
            </el-form-item>

            <el-form-item prop="password">
              <span class="svg-container">
                <i class="el-icon-lock" />
              </span>
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                autocomplete="on"
                @keyup.enter.native="handleLogin"
              />
              <span class="show-pwd" @click="showPwd">
                <i :class="passwordType === 'password' ? 'el-icon-view' : 'el-icon-hide'" />
              </span>
            </el-form-item>

            <el-button :loading="loading" type="primary" class="login-button" @click.native.prevent="handleLogin">登录</el-button>

            <div class="tips">
              <span>还没有账号？</span>
              <router-link to="/register">
                <span class="register-link">立即注册</span>
              </router-link>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import { resetTokenExpiredMessageFlag } from '@/utils/request'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请输入正确的用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于6位'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      isAdmin: false
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    // 在登录页面创建时重置Token过期状态
    resetTokenExpiredMessageFlag()
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // 添加isAdmin字段，区分管理员和员工登录
            const loginData = {
              ...this.loginForm,
              isAdmin: this.isAdmin
            }

            // 使用store进行登录
            try {
              await this.$store.dispatch('user/login', loginData)
              this.$message.success('登录成功')

              // 使用setTimeout确保路由已经加载完成
              setTimeout(() => {
                this.$router.push({ path: this.redirect || '/' })
              }, 100)
            } catch (error) {
              console.error('登录失败', error)
              // 显示错误消息
              if (error.response && error.response.data) {
                this.$message.error(error.response.data.msg || '登录失败')
              } else {
                this.$message.error(error.message || '登录失败，请检查网络连接')
              }
              // 重置加载状态，允许用户重新点击登录按钮
              this.loading = false
            }
          } catch (error) {
            console.error('登录失败', error)
            this.$message.error(error.message || '登录失败')
            this.loading = false
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调问题 */
$bg:#f5f7fa;
$light_gray:#333;
$cursor: #333;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid #dcdfe6;
    background: #fff;
    border-radius: 4px;
    color: #454545;
    margin-bottom: 20px;
  }

  .el-radio-group {
    margin-bottom: 20px;
  }
}
</style>

<style lang="scss" scoped>
$primary-color: #409EFF;
$dark_gray: #606266;
$light_gray: #333;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: #f5f7fa;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  .login-box {
    width: 80%;
    max-width: 1200px;
    height: 600px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    overflow: hidden;
  }

  .login-left {
    flex: 1;
    background: linear-gradient(135deg, #409EFF, #36D1DC);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    padding: 40px;

    .welcome-text {
      text-align: center;

      h1 {
        font-size: 32px;
        margin-bottom: 20px;
        font-weight: bold;
      }

      h2 {
        font-size: 24px;
        margin-bottom: 10px;
      }

      p {
        font-size: 16px;
        opacity: 0.8;
      }
    }
  }

  .login-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .login-form-container {
    width: 80%;
    max-width: 400px;
  }

  .user-type-selector {
    margin-bottom: 20px;
    text-align: center;
  }

  .tips {
    font-size: 14px;
    color: $dark_gray;
    margin-top: 20px;
    text-align: center;

    .register-link {
      margin-left: 5px;
      color: $primary-color;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 30px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .login-button {
    width: 100%;
    margin-bottom: 10px;
    height: 44px;
    font-size: 16px;
    border-radius: 4px;
  }
}
</style>
