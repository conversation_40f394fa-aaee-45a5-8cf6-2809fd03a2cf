package cn.edu.ncu.hrms.salary.service;

import cn.edu.ncu.hrms.employee.dto.EmployeeQueryDTO;
import cn.edu.ncu.hrms.salary.vo.SalaryReportVO;
import cn.edu.ncu.hrms.salary.dto.SalaryPageQueryDTO;
import cn.edu.ncu.hrms.common.entity.SalaryReport;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;

import javax.servlet.http.HttpServletResponse;
import javax.sound.midi.SoundbankResource;

import java.util.Map;

/**
 * 薪资报表服务接口
 */
public interface SalaryReportService {
    
    /**
     * 分页查询薪资报表VO
     * @param queryDTO 查询条件 DTO
     * @return Result<PageInfo<SalaryReportVO>>
     */
    Result<PageInfo<SalaryReportVO>> pageSalaryReports(SalaryPageQueryDTO queryDTO);
    
    /**
     * 根据ID获取薪资报表详情VO
     * @param id 薪资报表ID
     * @return Result<SalaryReportVO>
     */
    Result<SalaryReportVO> getSalaryReportVOById(Integer id);


    /**
     * 导出薪资报表为Excel
     * @param queryDTO
     * @return
     */
    byte[] exportSalaryReport(SalaryPageQueryDTO queryDTO);
}