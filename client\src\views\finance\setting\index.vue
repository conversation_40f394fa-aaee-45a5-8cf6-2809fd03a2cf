<template>
  <div class="app-container">
    <!-- 页面标题卡片 -->
    <el-card class="page-header-card">
      <div class="page-header">
        <h2>账套匹配</h2>
        <div class="header-actions">
          <el-button
            v-if="isHR"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddSetting"
          >新增匹配</el-button>
        </div>
      </div>
    </el-card>

    <!-- 内容卡片 -->
    <el-card class="content-card">
      <!-- 搜索区域 -->
      <div class="filter-form">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="职位名称">
            <el-input v-model="queryParams.positionName" placeholder="请输入职位名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="账套名称">
            <el-input v-model="queryParams.accountSetName" placeholder="请输入账套名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="positionList"
        border
        style="width: 100%; margin-top: 15px;"
      >
        <el-table-column prop="id" label="职位ID" width="80" align="center" />
        <el-table-column prop="name" label="职位名称" min-width="150" align="center" />
        <el-table-column prop="level" label="职级" width="100" align="center">
          <template slot-scope="scope">
            L{{ scope.row.level }}
          </template>
        </el-table-column>
        <el-table-column prop="accountId" label="账套ID" width="80" align="center" />
        <el-table-column label="账套名称" min-width="150" align="center">
          <template slot-scope="scope">
            {{ getAccountName(scope.row.accountId) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="isHR"
              size="mini"
              type="primary"
              icon="el-icon-connection"
              circle
              title="匹配账套"
              @click="handleMatchAccount(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        style="margin-top: 15px; text-align: right;"
      />
    </el-card>

    <!-- 账套匹配对话框 -->
    <el-dialog title="账套匹配" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="positionForm" :model="positionForm" :rules="rules" label-width="100px">
        <el-form-item label="职位名称">
          <el-input v-model="positionForm.name" disabled />
        </el-form-item>
        <el-form-item label="职级">
          <el-input v-model="positionForm.levelDisplay" disabled />
        </el-form-item>
        <el-form-item label="账套" prop="accountId">
          <el-select v-model="positionForm.accountId" placeholder="请选择账套" style="width: 100%;">
            <el-option
              v-for="item in accountOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPositionPage, matchingAccountSet } from '@/api/finance/position'
import { getAllAccountSets } from '@/api/finance/account'
import { parseTime } from '@/utils/index'

export default {
  name: 'FinanceSetting',
  data() {
    return {
      // 是否为HR
      isHR: false,
      // 加载状态
      loading: false,
      // 职位列表
      positionList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        positionName: undefined,
        accountSetName: undefined,
        positionId: undefined,
        accountId: undefined
      },
      // 总记录数
      total: 0,
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      isAddMode: false,
      // 职位表单
      positionForm: {
        id: undefined,
        name: '',
        level: 5,
        levelDisplay: 'L5',
        accountId: undefined
      },
      // 表单验证规则
      rules: {
        accountId: [
          { required: true, message: '请选择账套', trigger: 'change' }
        ]
      },
      // 职级选项
      levelOptions: [5, 6, 7, 8, 9, 10],
      // 账套选项
      accountOptions: []
    }
  },
  created() {
    this.getUserRole()
    this.getPositionList()
    this.getAccountOptions()
  },
  methods: {
    // 获取用户角色信息 - 从Vuex中获取，不再调用API
    getUserRole() {
      // 从Vuex中获取用户角色信息
      this.isHR = this.$store.getters['userInfo/hasRole']('ROLE_hr')

      // 如果不是HR角色，跳转到首页
      if (!this.isHR) {
        this.$router.push('/dashboard')
        this.$message.warning('您没有权限访问此页面')
      }
    },

    // 获取职位列表
    async getPositionList() {
      try {
        this.loading = true
        const response = await getPositionPage(this.queryParams)

        if (response.code === 1) {
          this.positionList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取职位列表失败')
          this.positionList = []
          this.total = 0
        }
        this.loading = false
      } catch (error) {
        console.error('获取职位列表失败', error)
        this.$message.error('获取职位列表失败')
        this.loading = false
      }
    },

    // 获取账套选项
    async getAccountOptions() {
      try {
        const response = await getAllAccountSets()

        if (response.code === 1) {
          this.accountOptions = response.data || []
        } else {
          this.$message.error(response.msg || '获取账套选项失败')
          this.accountOptions = []
        }
      } catch (error) {
        console.error('获取账套选项失败', error)
        this.$message.error('获取账套选项失败')
      }
    },

    // 根据账套ID获取账套名称
    getAccountName(accountId) {
      if (!accountId) return '未分配'
      const account = this.accountOptions.find(item => item.id === accountId)
      return account ? account.name : `账套ID: ${accountId}`
    },

    // 格式化日期时间
    formatDateTime(time) {
      if (!time) return ''

      // 检查时间格式，如果是字符串格式的日期时间，直接返回
      if (typeof time === 'string' && time.includes('-') && time.includes(':')) {
        // 将 "2025-05-04 15:14:44" 格式转换为 "2025-05-04 15:14:44"
        return time.replace('T', ' ').split('.')[0]
      }

      return parseTime(time)
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getPositionList()
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        positionName: undefined,
        accountSetName: undefined,
        positionId: undefined,
        accountId: undefined
      }
      this.getPositionList()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getPositionList()
    },

    // 处理每页条数变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.getPositionList()
    },

    // 处理添加职位账套匹配 - 保留按钮但不实现功能
    handleAddSetting() {
      this.$message.info('请在管理员系统中添加职位与账套匹配')
    },

    // 处理匹配账套
    handleMatchAccount(row) {
      this.positionForm = {
        id: row.id,
        name: row.name,
        level: row.level,
        levelDisplay: `L${row.level}`,
        accountId: row.accountId
      }
      this.dialogVisible = true
    },

    // 提交表单
    submitForm() {
      this.$refs.positionForm.validate(async valid => {
        if (valid) {
          // 弹出警告提示
          this.$confirm('修改职位匹配的账套会影响所有属于该职位的员工薪资，并且本月立即生效，是否继续？', '警告', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            try {
              // 匹配账套
              const response = await matchingAccountSet(this.positionForm.id, this.positionForm.accountId)

              if (response.code === 1) {
                this.$message.success('账套匹配成功')
                this.dialogVisible = false
                this.getPositionList()
              } else {
                this.$message.error(response.msg || '账套匹配失败')
              }
            } catch (error) {
              console.error('账套匹配失败', error)
              this.$message.error('账套匹配失败')
            }
          }).catch(() => {
            // 取消操作
            this.$message.info('已取消匹配')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header-card {
  margin-bottom: 20px;
}

.content-card {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
  }
}

.filter-form {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  height: 70px;
}
</style>
