const getters = {
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  token: state => state.user.token,
  userId: state => state.user.userInfo.id,
  avatar: state => state.user.userInfo.avatar,
  name: state => state.user.userInfo.username,
  routes: state => state.user.routes,
  permissions: state => state.user.userInfo.permissions || [],
  isAdmin: state => state.user.userInfo.isAdmin,

  // 用户信息相关
  userInfo: state => state.userInfo.userInfo,
  userRoles: state => state.userInfo.roles,
  userPermissions: state => state.userInfo.permissions,
  hasRole: state => role => state.userInfo.roles.includes(role),
  hasPermission: state => permission => state.userInfo.permissions.includes(permission),
  isHR: state => state.userInfo.roles.includes('ROLE_hr'),
  isLeader: state => state.userInfo.roles.includes('ROLE_leader'),
  isEmployee: state => state.userInfo.roles.includes('ROLE_employee')
}

export default getters
