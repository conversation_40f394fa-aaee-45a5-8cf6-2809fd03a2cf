package cn.edu.ncu.hrms.auth.controller;

import cn.edu.ncu.hrms.auth.service.PermissionService;
import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 权限控制器
 */
@RestController
@RequestMapping("/permission")
@Api(tags = "权限管理接口")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;


    /**
     * 根据ID获取权限
     */
    @GetMapping("/{id}")
    @ApiOperation("根据权限ID获取权限")
    @PreAuthorize("@hrms.hasAuthority('system:permission:query')")
    public Result<Permission> getPermissionById(@PathVariable Integer id) {
        Permission permission = permissionService.getPermissionById(id);
        return Result.success(permission);
    }


    /**
     * 获取所有权限列表
     */
    @GetMapping("/all")
    @ApiOperation("获取所有权限列表")
    @PreAuthorize("@hrms.hasAuthority('system:permission:list')")
    public Result<List<Permission>> getPermissionList() {
        List<Permission> permissions = permissionService.getPermissionList();
        return Result.success(permissions);
    }


    /**
     * 根据角色ID获取权限列表
     */
    @GetMapping("/role/{roleId}")
    @ApiOperation("根据角色ID获取权限列表")
    @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:permission:query')")
    public Result<List<Permission>> getPermissionsByRoleId(@PathVariable Integer roleId) {
        List<Permission> permissions = permissionService.getPermissionsByRoleId(roleId);
        return Result.success(permissions);
    }

    /**
     * 根据用户ID获取权限列表
     */
    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID获取权限列表")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:permission:query')")
    public Result<List<Permission>> getPermissionsByUserId(@PathVariable Integer userId) {
        List<Permission> permissions = permissionService.getPermissionsByUserId(userId);
        return Result.success(permissions);
    }
} 