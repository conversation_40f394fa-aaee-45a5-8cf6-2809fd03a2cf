package cn.edu.ncu.hrms.attendance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 打卡数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("打卡请求")
public class ClockDTO {
    
    @ApiModelProperty(value = "员工ID", example = "1001")
    private Integer employeeId;
    
    @ApiModelProperty(value = "打卡位置（经纬度）", required = true, example = "[116.34351300000004, 40.060244]")
    private double[] location;

    @ApiModelProperty(value = "打卡类型", allowableValues = "IN,OUT", example = "IN")
    private String type;
} 