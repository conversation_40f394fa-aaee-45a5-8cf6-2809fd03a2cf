package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("职位信息")
public class Position {
    @ApiModelProperty(value = "职位ID", required = true, example = "301")
    private Integer id;

    @ApiModelProperty(value = "职位名称", required = true, example = "后端开发")
    private String name;

    @ApiModelProperty(value = "职级", required = true, example = "5")
    private Byte level;

    @ApiModelProperty(value = "关联账套ID", required = true, example = "501")
    private Integer accountId;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-01-02 13:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
