package cn.edu.ncu.hrms.system.service;

// import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.PermissionTreeQueryDTO;
import cn.edu.ncu.hrms.system.vo.PermissionVO;
// 引入 DTO
import cn.edu.ncu.hrms.system.dto.PermissionAddDTO;
import cn.edu.ncu.hrms.system.dto.PermissionUpdateDTO;

import java.util.List;
import java.util.Map;

/**
 * 系统权限管理服务接口
 */
public interface SystemPermissionService {

    /**
     * 获取所有权限列表
     * @return Result<List<PermissionVO>> 权限VO列表
     */
    Result<List<PermissionVO>> getPermissionList(PermissionTreeQueryDTO queryDTO);

    /**
     * 获取所有权限列表（树形结构）
     * @return 树形权限列表
     */
    Result<List<Map<String, Object>>> getPermissionTree(PermissionTreeQueryDTO queryDTO);

    /**
     * 根据ID获取权限信息
     * @param id 权限ID
     * @return Result<PermissionVO> 权限VO
     */
    Result<PermissionVO> getPermissionVOById(Integer id);

    /**
     * 创建新权限
     * @param addDTO 权限创建信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> createPermission(PermissionAddDTO addDTO);

    /**
     * 更新权限信息
     * @param id 权限ID
     * @param updateDTO 权限更新信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> updatePermission(Integer id, PermissionUpdateDTO updateDTO);

    /**
     * 删除权限
     * @param id 权限ID
     * @return Result<Void> 操作结果
     */
    Result<Void> deletePermission(Integer id);

    /**
     * 获取角色拥有的权限列表
     * @param roleId 角色ID
     * @return Result<List<PermissionVO>> 权限VO列表
     */
    Result<List<PermissionVO>> getPermissionsByRoleId(Integer roleId);

    /**
     * 获取用户拥有的权限列表
     * @param userId 用户ID
     * @return Result<List<PermissionVO>> 权限VO列表
     */
    Result<List<PermissionVO>> getPermissionsByUserId(Integer userId);
}

