package cn.edu.ncu.hrms.employee.controller;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentAddDTO;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentQueryDTO;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentUpdateDTO;
import cn.edu.ncu.hrms.employee.service.RewardPunishmentService;
import cn.edu.ncu.hrms.employee.vo.RewardPunishmentVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 奖惩信息管理Controller
 */
@RestController
@RequestMapping("/employee/reward-punishment")
@Api(tags = "奖惩管理接口")
public class RewardPunishmentController {

    @Autowired
    private RewardPunishmentService rewardPunishmentService;

    /**
     * 获取奖惩信息详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取奖惩信息详情")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:query')")
    public Result<RewardPunishmentVO> getById(@PathVariable @ApiParam("奖惩记录ID") Long id) {
        return rewardPunishmentService.getVOById(id);
    }

    /**
     * 获取当前登录员工奖惩信息
     */
    @GetMapping("/page/self")
    @ApiOperation("获取个人奖惩信息列表")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:query')")
    public Result<PageResult<RewardPunishmentVO>> listByEmployeeId(PageQueryDTO queryDTO) {
        PageInfo<RewardPunishmentVO> pageInfo = rewardPunishmentService.listVOsByEmployeeId(queryDTO);
        if (pageInfo == null) {
            return Result.error("为获取到登录员工信息，请检查登录状态");
        }
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }

    /**
     * 分页查询奖惩信息
     */
    @GetMapping("/page")
    @ApiOperation("分页查询奖惩信息")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:list')")
    public Result<PageResult<RewardPunishmentVO>> page(@Valid RewardPunishmentQueryDTO queryDTO) {
        PageInfo<RewardPunishmentVO> rewardPunishmentVOPageInfo = rewardPunishmentService.pageVOs(queryDTO);
        return PageResult.success(rewardPunishmentVOPageInfo.getTotal(), rewardPunishmentVOPageInfo.getList());
    }


    /**
     * 添加奖惩信息
     */
    @PostMapping
    @ApiOperation("添加奖惩信息")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:add')")
    public Result<Void> add(@Valid @RequestBody RewardPunishmentAddDTO addDTO) {
        return rewardPunishmentService.add(addDTO);
    }


    /**
     * 更新奖惩信息
     */
    @PutMapping
    @ApiOperation("更新奖惩信息")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:edit')")
    public Result<Void> update(@Valid @RequestBody RewardPunishmentUpdateDTO updateDTO) {
        return rewardPunishmentService.update(updateDTO);
    }

    /**
     * 删除奖惩信息
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除奖惩信息")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:delete')")
    public Result<Void> delete(@PathVariable @ApiParam("奖惩记录ID") Long id) {
        return rewardPunishmentService.delete(id);
    }

    /**
     * 批量删除奖惩信息
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除奖惩信息")
    @PreAuthorize("@hrms.hasAuthority('employee:rewardpunishment:delete')")
    public Result<Void> batchDelete(@RequestBody @ApiParam("奖惩记录ID列表") List<Long> ids) {
        return rewardPunishmentService.batchDelete(ids);
    }

//    /**
//     * 获取奖惩类型统计数据
//     */
//    @GetMapping("/stats/type")
//    @ApiOperation("获取奖惩类型统计数据")
//    @PreAuthorize("@hrms.hasAuthority('rewardpunishment:stats')")
//    public Result<?> getTypeStatistics() {
//        return rewardPunishmentService.getTypeStatistics();
//    }
//
//    /**
//     * 获取指定日期范围内的奖惩金额统计
//     */
//    @GetMapping("/stats/amount")
//    @ApiOperation("获取指定日期范围内的奖惩金额统计")
//    @PreAuthorize("@hrms.hasAuthority('rewardpunishment:stats')")
//    public Result<?> getAmountStatistics(
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") @ApiParam("开始日期") LocalDate startDate,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") @ApiParam("结束日期") LocalDate endDate) {
//        return rewardPunishmentService.getAmountStatistics(startDate, endDate);
//    }
//
//    /**
//     * 获取员工奖惩排行榜
//     */
//    @GetMapping("/stats/ranking")
//    @ApiOperation("获取员工奖惩排行榜")
//    @PreAuthorize("@hrms.hasAuthority('rewardpunishment:stats')")
//    public Result<?> getEmployeeRanking(
//            @RequestParam(required = false) @ApiParam("奖惩类型，1奖励，2惩罚，不传则统计总分") Integer type,
//            @RequestParam(required = false, defaultValue = "10") @ApiParam("榜单数量限制") Integer limit) {
//        return rewardPunishmentService.getEmployeeRanking(type, limit);
//    }

//    /**
//     * 导出奖惩信息Excel
//     */
//    @GetMapping("/export")
//    @ApiOperation("导出奖惩信息Excel")
//    @PreAuthorize("@hrms.hasAuthority('rewardpunishment:export')")
//    public void exportExcel(HttpServletResponse response, RewardPunishmentQueryDTO queryDTO) {
//        rewardPunishmentService.exportExcel(response, queryDTO);
//    }
//
//    /**
//     * 下载导入模板
//     */
//    @GetMapping("/template")
//    @ApiOperation("下载奖惩信息导入模板Excel")
//    @PreAuthorize("@hrms.hasAuthority('rewardpunishment:import')")
//    public void downloadTemplate(HttpServletResponse response) {
//        rewardPunishmentService.downloadTemplate(response);
//    }
//
//    /**
//     * 导入奖惩信息
//     */
//    @PostMapping("/import")
//    @ApiOperation("导入奖惩信息Excel")
//    @PreAuthorize("@hrms.hasAuthority('rewardpunishment:import')")
//    public Result<Void> importExcel(@RequestParam("file") @ApiParam("Excel文件") MultipartFile file) {
//        return rewardPunishmentService.importExcel(file);
//    }

} 