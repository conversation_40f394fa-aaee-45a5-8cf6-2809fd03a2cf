package cn.edu.ncu.hrms.auth.mapper;

import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.system.dto.PermissionTreeQueryDTO;
import cn.edu.ncu.hrms.system.dto.PermissionUpdateDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限表 Mapper 接口
 */
@Mapper
public interface PermissionMapper {

    /**
     * 根据角色 ID 查询该角色直接拥有的权限列表
     * 
     * 通过 role_permission 中间表关联查询。
     *
     * @param roleId 角色ID
     * @return List<Permission> 权限列表
     */
    List<Permission> selectPermissionsByRoleId(Integer roleId);

    /**
     * 根据权限 ID 查询权限
     *
     * @param id 权限ID
     * @return Permission 权限实体，未找到返回 null
     */
    Permission selectById(Integer id);

    /**
     * 新增权限
     * 
     * 用于管理员添加权限。
     * XML 中需配置 useGeneratedKeys="true" keyProperty="id" 以返回自增主键。
     *
     * @param permission 待插入的权限信息
     * @return int 影响行数
     */
    int insert(Permission permission);

    /**
     * 根据主键 ID 更新权限信息
     * 
     * 用于管理员修改权限。
     * XML 中应使用 <set> 和 <if> 实现动态更新。
     *
     * @param updateDTO 包含主键 ID 和待更新字段的权限信息
     * @return int 影响行数
     */
    int update(PermissionUpdateDTO updateDTO);

    /**
     * 根据主键 ID 删除权限
     * 
     * 注意：删除权限前应处理关联的角色。可能是物理删除或逻辑删除。
     *
     * @param id 权限ID
     * @return int 影响行数
     */
    int deleteById(Integer id);

    /**
     * 查询所有权限列表
     * 
     * 用于管理员权限管理列表展示，可在 Service 层配合 PageHelper 分页。
     *
     * @return List<Permission> 所有权限的列表
     */
    List<Permission> selectList();

    /**
     * 根据条件查询权限列表
     *
     * 用于管理员权限管理列表展示，可在 Service 层配合 PageHelper 分页。
     *
     * @return List<Permission> 所有权限的列表
     */
    List<Permission> selectListByCondition(PermissionTreeQueryDTO queryDTO);

    /**
     * 根据用户 ID 查询该用户拥有的所有权限列表（合并角色权限）
     * 
     * 通过 user_role 和 role_permission 关联查询，并去重。
     * 返回的是权限标识符 (perm_key) 列表，用于 Spring Security 授权。
     *
     * @param userId 用户ID
     * @return List<String> 该用户拥有的权限标识符列表 (例如 ["system:user:list", "system:user:add"])
     */
    List<String> selectPermKeysByUserId(Integer userId);

    /**
     * 根据用户 ID 查询该用户拥有的所有权限实体列表（合并角色权限）
     * 
     * 通过 user_role 和 role_permission 关联查询，并去重。
     *
     * @param userId 用户ID
     * @return List<Permission> 该用户拥有的权限实体列表
     */
    List<Permission> selectPermissionsByUserId(Integer userId);

    /**
     * 根据权限标识查询权限
     *
     * @param permissionKey 权限标识
     * @return Permission 权限实体，未找到返回 null
     */
    Permission selectByPermissionKey(String permissionKey);
} 