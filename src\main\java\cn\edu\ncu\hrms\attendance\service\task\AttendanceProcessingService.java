package cn.edu.ncu.hrms.attendance.service.task;

import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.entity.AccountSet;
import cn.edu.ncu.hrms.common.entity.Attendance;
import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.common.utils.DateUtil;
import cn.edu.ncu.hrms.salary.mapper.AccountSetMapper;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
@Service
public class AttendanceProcessingService {

    @Autowired
    private PositionMapper positionMapper;
    @Autowired
    private AccountSetMapper accountSetMapper;

    /**
     * 处理单个考勤记录，确定最终状态和扣款（如果状态为null）
     *
     * @param attendance 待处理的考勤记录
     * @param department 员工所属部门信息
     * @param employeeId 员工ID (用于日志)
     * @param positionId 员工职位ID (用于查找账套计算缺勤)
     * @return 返回处理后的 Attendance 对象 (可能是传入的对象，也可能是新创建的)
     */
    public Attendance finalizeAttendanceRecord(Attendance attendance, Department department, Integer employeeId, Integer positionId) {
        LocalDate attendanceDate = attendance.getDate();
        boolean needsUpdate = false;
        BigDecimal dailyDeduction = BigDecimal.ZERO; // 当天的总扣款

        // --- 0. 安全检查 ---
        if (department == null) {
            log.warn("员工 {} 日期 {} 的考勤记录无法处理：缺少部门信息。", employeeId, attendanceDate);
            // 如果没有部门规则，无法判断迟到早退，可以默认正常或标记为待处理
            // 这里我们选择不更新状态，让其保持null，但后续缺勤判断仍会生效
        }

        // --- 1. 处理上班签到状态 ---
        if (attendance.getClockInStatus() == null) {
            needsUpdate = true;
            if (attendance.getClockInTime() == null) {
                // 没有签到时间，记为缺勤
                attendance.setClockInStatus(AttendanceConstant.CLOCK_ABSENT);
                log.info("员工 {} 日期 {} 上班未签到，记为缺勤。", employeeId, attendanceDate);
            } else if (department != null && StringUtils.hasText(department.getClockInStartTime()) && StringUtils.hasText(department.getClockInEndTime())) {
                // 有签到时间，根据部门规则判断
                try {
                    LocalTime clockInTime = attendance.getClockInTime();
                    int lateThreshold = department.getLateThreshold() != null ? department.getLateThreshold() : 0;
                    BigDecimal lateDeduction = department.getLateDeduction() != null ? department.getLateDeduction() : BigDecimal.ZERO;
                    LocalTime ruleEndTime = LocalTime.parse(department.getClockInEndTime(), DateTimeFormatter.ISO_LOCAL_TIME);

                    if (clockInTime.isAfter(ruleEndTime.plusMinutes(lateThreshold))) {
                        attendance.setClockInStatus(AttendanceConstant.CLOCK_ABSENT);
                        log.warn("员工 {} 日期 {} 在 {} 签到，超过迟到阈值，上班记为缺勤。", employeeId, attendanceDate, clockInTime);
                        // 缺勤扣款在最后统一计算
                    } else if (clockInTime.isAfter(ruleEndTime)) {
                        attendance.setClockInStatus(AttendanceConstant.CLOCK_LATE);
                        dailyDeduction = dailyDeduction.add(lateDeduction);
                        log.info("员工 {} 日期 {} 在 {} 签到，迟到，扣款 {}", employeeId, attendanceDate, clockInTime, lateDeduction);
                    } else {
                        // 正常签到 (包括早于 ruleStartTime 但在允许范围内的情况，这里简化为只要不晚于 ruleEndTime 就 NORMAL)
                        attendance.setClockInStatus(AttendanceConstant.CLOCK_NORMAL);
                        log.info("员工 {} 日期 {} 在 {} 正常签到。", employeeId, attendanceDate, clockInTime);
                    }
                } catch (DateTimeParseException e) {
                    log.error("员工 {} 部门 {} 的上班时间规则格式错误: {} 或 {}, 无法判断签到状态。",
                            employeeId, department.getId(), department.getClockInStartTime(), department.getClockInEndTime(), e);
                    // 格式错误，无法判断，保持null
                }
            } else {
                log.warn("员工 {} 日期 {} 有签到时间但部门 {} 规则不完整，无法判断签到状态。", employeeId, attendanceDate, department != null ? department.getId() : "N/A");
                // 规则不完整，保持 null
            }
        }

        // --- 2. 处理下班签退状态 ---
        if (attendance.getClockOutStatus() == null) {
            needsUpdate = true;
            if (attendance.getClockOutTime() == null) {
                // 没有签退时间，记为缺勤
                attendance.setClockOutStatus(AttendanceConstant.CLOCK_ABSENT);
                log.info("员工 {} 日期 {} 下班未签退，记为缺勤。", employeeId, attendanceDate);
            } else if (department != null && StringUtils.hasText(department.getClockOutStartTime()) && StringUtils.hasText(department.getClockOutEndTime())) {
                // 有签退时间，根据部门规则判断
                try {
                    LocalTime clockOutTime = attendance.getClockOutTime();
                    LocalTime ruleStartTime = LocalTime.parse(department.getClockOutStartTime(), DateTimeFormatter.ISO_LOCAL_TIME);
                    int earlyThreshold = department.getEarlyThreshold() != null ? department.getEarlyThreshold() : 0;
                    BigDecimal earlyDeduction = department.getEarlyDeduction() != null ? department.getEarlyDeduction() : BigDecimal.ZERO;


                    if (clockOutTime.isBefore(ruleStartTime.minusMinutes(earlyThreshold))) {
                        attendance.setClockOutStatus(AttendanceConstant.CLOCK_ABSENT);
                        log.warn("员工 {} 日期 {} 在 {} 签退，早于 {}，下班记为缺勤。", employeeId, attendanceDate, clockOutTime, ruleStartTime.minusMinutes(earlyThreshold));
                        // 缺勤扣款在最后统一计算
                    } else if (clockOutTime.isBefore(ruleStartTime)) {
                        // 早于规定下班时间 = 早退
                        attendance.setClockOutStatus(AttendanceConstant.CLOCK_EARLY);
                        dailyDeduction = dailyDeduction.add(earlyDeduction);
                        log.info("员工 {} 日期 {} 在 {} 签退，早退，扣款 {}", employeeId, attendanceDate, clockOutTime, earlyDeduction);
                    } else {
                        // 正常签退 (等于或晚于 ruleStartTime)
                        attendance.setClockOutStatus(AttendanceConstant.CLOCK_NORMAL);
                        log.info("员工 {} 日期 {} 在 {} 正常签退。", employeeId, attendanceDate, clockOutTime);
                    }
                } catch (DateTimeParseException e) {
                    log.error("员工 {} 部门 {} 的下班时间规则格式错误: {}, 无法判断签退状态。",
                            employeeId, department.getId(), department.getClockOutStartTime(), e);
                    // 格式错误，无法判断，保持null
                }
            } else {
                log.warn("员工 {} 日期 {} 有签退时间但部门 {} 规则不完整，无法判断签退状态。", employeeId, attendanceDate, department != null ? department.getId() : "N/A");
                // 规则不完整，保持 null
            }
        }

        // --- 3. 计算最终扣款 (处理缺勤覆盖) ---
        boolean isAbsent = AttendanceConstant.CLOCK_ABSENT.equals(attendance.getClockInStatus()) || AttendanceConstant.CLOCK_ABSENT.equals(attendance.getClockOutStatus());
        if (isAbsent) {
            // 如果当天被记为缺勤，则按缺勤计算扣款，覆盖之前的迟到/早退扣款
            BigDecimal absentDeduction = calculateAbsenteeismDeduction(positionId, attendanceDate);
            if (absentDeduction.compareTo(BigDecimal.ZERO) > 0) {
                log.info("员工 {} 日期 {} 状态为缺勤，计算缺勤扣款: {}", employeeId, attendanceDate, absentDeduction);
                dailyDeduction = absentDeduction; // 使用缺勤扣款
                needsUpdate = true; // 即使状态之前已设置，扣款变化也需更新
            } else {
                log.warn("员工 {} 日期 {} 状态为缺勤，但无法计算缺勤扣款 (可能缺少职位或账套信息)。", employeeId, attendanceDate);
                // 保留原来的迟到早退扣款
                if (attendance.getDeduction() == null || dailyDeduction.compareTo(attendance.getDeduction()) != 0) {
                    needsUpdate = true; // 如果计算出的迟到早退扣款和原来不同，也标记更新
                }
            }
        } else {
            // 非缺勤，检查计算出的迟到/早退扣款是否与记录中的不同
            if (attendance.getDeduction() == null || dailyDeduction.compareTo(attendance.getDeduction()) != 0) {
                needsUpdate = true;
            }
        }

        // --- 4. 设置最终扣款并返回 ---
        if (needsUpdate) {
            attendance.setDeduction(dailyDeduction.setScale(2, RoundingMode.HALF_UP));
            attendance.setUpdateTime(java.time.LocalDateTime.now());
            log.debug("员工 {} 日期 {} 最终状态: 上班-{}, 下班-{}, 扣款: {}", employeeId, attendanceDate, attendance.getClockInStatus(), attendance.getClockOutStatus(), attendance.getDeduction());
        } else {
            log.debug("员工 {} 日期 {} 考勤记录无需更新。", employeeId, attendanceDate);
        }

        // 返回修改后的对象，标记是否需要更新由调用者判断
        attendance.setNeedsUpdate(needsUpdate); // 添加一个临时标记，方便调用处判断
        return attendance;
    }


    /**
     * 计算缺勤扣款
     *
     * @param positionId 职位ID
     * @param date       缺勤日期
     * @return 缺勤扣款金额，如果无法计算则返回 0
     */
    public BigDecimal calculateAbsenteeismDeduction(Integer positionId, LocalDate date) {
        if (positionId == null) return BigDecimal.ZERO;

        Position position = positionMapper.selectById(positionId);
        if (position == null || position.getAccountId() == null) return BigDecimal.ZERO;

        AccountSet accountSet = accountSetMapper.selectById(position.getAccountId());
        if (accountSet == null || accountSet.getBaseSalary() == null) return BigDecimal.ZERO;

        // 获取当月工作日天数
        int workingDays = DateUtil.getWorkingDaysInMonth(date);
        if (workingDays <= 0) {
            log.warn("无法计算月份 {} 的工作日数，缺勤扣款计算失败。", YearMonth.from(date));
            return BigDecimal.ZERO; // 防止除零
        }

        // 缺勤扣款 = 基本工资 / 当月工作日
        return accountSet.getBaseSalary()
                .divide(BigDecimal.valueOf(workingDays), 2, RoundingMode.HALF_UP);
    }

}
