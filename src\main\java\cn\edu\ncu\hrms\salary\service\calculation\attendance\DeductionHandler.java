package cn.edu.ncu.hrms.salary.service.calculation.attendance;

import java.math.BigDecimal;

/**
 * 责任链模式：考勤扣款处理接口
 */
public interface DeductionHandler {
    /**
     * 设置责任链中的下一个处理器。
     * @param next 下一个处理器
     */
    void setNext(DeductionHandler next);

    /**
     * 处理考勤扣款请求。
     * 实现类应根据 context 中的信息计算当前环节的扣款，
     * 累加到 currentDeduction 上，然后传递给下一个处理器或返回最终结果。
     *
     * @param context 包含考勤统计信息的上下文对象
     * @param currentDeduction 到当前处理器为止已累积的扣款金额
     * @return 经过此处理器及后续处理器计算后的总扣款金额
     */
    BigDecimal handle(DeductionContext context, BigDecimal currentDeduction);
}
