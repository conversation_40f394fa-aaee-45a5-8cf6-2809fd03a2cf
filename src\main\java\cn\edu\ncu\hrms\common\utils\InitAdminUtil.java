package cn.edu.ncu.hrms.common.utils;

import cn.edu.ncu.hrms.auth.mapper.PermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.RoleMapper;
import cn.edu.ncu.hrms.auth.mapper.RolePermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.UserMapper;
import cn.edu.ncu.hrms.auth.mapper.UserRoleMapper;
import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.entity.Role;
import cn.edu.ncu.hrms.common.entity.RolePermission;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.entity.UserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static cn.edu.ncu.hrms.common.constant.RoleConstant.ADMIN_PERMISSION_KEY;
import static cn.edu.ncu.hrms.common.constant.RoleConstant.ROLE_ADMIN;

/**
 * 初始化管理员账户和权限工具
 * 用于系统首次启动时，确保超级管理员账户存在并具有所有权限
 */
@Slf4j
@Component
public class InitAdminUtil implements CommandLineRunner {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private static final String ADMIN_USERNAME = "admin";
    private static final String ADMIN_PASSWORD = "123123";


    @Override
    @Transactional
    public void run(String... args) {
        log.info("开始检查初始化管理员账户...");
        initAdminAccount();
        log.info("管理员账户检查完成。");
    }

    /**
     * 初始化管理员账户
     */
    private void initAdminAccount() {
        // 1. 检查admin用户是否存在
        User adminUser = userMapper.selectByUsername(ADMIN_USERNAME);
        
        if (adminUser == null) {
            log.info("管理员账户不存在，创建新的管理员账户...");
            // 创建新的管理员用户
            adminUser = new User();
            adminUser.setUsername(ADMIN_USERNAME);
            adminUser.setPassword(passwordEncoder.encode(ADMIN_PASSWORD));
            adminUser.setIsAdmin(true);
            adminUser.setEmployeeId(null); // 管理员可以不关联员工
            
            // 插入用户
            int result = userMapper.insert(adminUser);
            if (result <= 0 || adminUser.getId() == null) {
                log.error("创建管理员账户失败！");
                return;
            }
            log.info("管理员账户创建成功，ID: {}", adminUser.getId());
        } else {
            log.info("管理员账户已存在，ID: {}", adminUser.getId());
        }
        
        // 2. 检查admin角色是否存在
        Role adminRole = roleMapper.selectByRoleKey(ROLE_ADMIN);
        if (adminRole == null) {
            log.info("管理员角色不存在，创建新的管理员角色...");
            // 创建新的管理员角色
            adminRole = new Role();
            adminRole.setName("超级管理员");
            adminRole.setRoleKey(ROLE_ADMIN);
            adminRole.setDescription("系统超级管理员，拥有所有权限");
            int result = roleMapper.insert(adminRole);
            if (result <= 0 || adminRole.getId() == null) {
                log.error("创建管理员角色失败！");
                return;
            }
            log.info("管理员角色创建成功，ID: {}", adminRole.getId());
        } else {
            log.info("管理员角色已存在，ID: {}", adminRole.getId());
        }
        
        // 3. 检查用户是否已关联角色
        UserRole userRole = userRoleMapper.selectByUserIdAndRoleId(adminUser.getId(), adminRole.getId());
        if (userRole == null) {
            log.info("管理员用户未关联管理员角色，创建关联...");
            // 创建用户-角色关联
            userRole = new UserRole();
            userRole.setUserId(adminUser.getId());
            userRole.setRoleId(adminRole.getId());
            int result = userRoleMapper.insert(userRole);
            if (result <= 0) {
                log.error("关联管理员用户和角色失败！");
                return;
            }
            log.info("用户-角色关联创建成功");
        } else {
            log.info("管理员用户已关联管理员角色");
        }
        
        // 4. 确保所有权限被分配给管理员角色
        Permission permission = permissionMapper.selectByPermissionKey(ADMIN_PERMISSION_KEY);
        if (permission == null) {
            log.info("创建权限: {}", ADMIN_PERMISSION_KEY);
            permission = new Permission();
            permission.setPermissionKey(ADMIN_PERMISSION_KEY);
            permission.setPermissionName("所有权限");
            permission.setDescription("拥有所有接口权限（管理员）");
            int result = permissionMapper.insert(permission);
            if (result <= 0 || permission.getId() == null) {
                log.error("创建权限失败: {}", ADMIN_PERMISSION_KEY);
            }
        }

        // 分配权限给管理员角色
        try {
            assignPermissionToRole(adminRole.getId(), permission.getId());
        } catch (Exception e) {
            // 可能是由于唯一键约束导致的异常，即权限已经分配给该角色
            log.warn("权限 {} 分配给管理员角色 发生异常: {}", ADMIN_PERMISSION_KEY, e.getMessage());
        }

        
        // 临时添加系统权限标识符
        // ensureSystemPermissions();
    }
    
    /**
     * 确保系统基础权限存在并分配给管理员
     * 实际系统中应该有一个更完整的权限初始化过程
     */
    private void ensureSystemPermissions() {
        // 系统基础权限
        String[] basicPermissions = {
            "system:user:list", "system:user:query", "system:user:add", "system:user:edit", "system:user:delete",
            "system:role:list", "system:role:query", "system:role:add", "system:role:edit", "system:role:delete",
            "system:menu:list", "system:menu:query", "system:menu:add", "system:menu:edit", "system:menu:delete",
            "system:dept:list", "system:dept:query", "system:dept:add", "system:dept:edit", "system:dept:delete",
            "system:permission:list", "system:permission:query", "system:permission:add", "system:permission:edit", "system:permission:delete"
        };
        
        Role adminRole = roleMapper.selectByRoleKey(ROLE_ADMIN);
        if (adminRole == null) {
            log.error("找不到管理员角色，无法分配权限");
            return;
        }
        
        for (String permKey : basicPermissions) {
            // 检查权限是否存在
            Permission permission = permissionMapper.selectByPermissionKey(permKey);
            if (permission == null) {
                log.info("创建权限: {}", permKey);
                permission = new Permission();
                permission.setPermissionKey(permKey);
                int result = permissionMapper.insert(permission);
                if (result <= 0 || permission.getId() == null) {
                    log.error("创建权限失败: {}", permKey);
                    continue;
                }
            }
            
            // 分配权限给管理员角色
            try {
                assignPermissionToRole(adminRole.getId(), permission.getId());
                log.info("分配权限 {} 给管理员角色", permKey);
            } catch (Exception e) {
                // 可能是由于唯一键约束导致的异常，即权限已经分配给该角色
                log.debug("权限 {} 可能已经分配给管理员角色: {}", permKey, e.getMessage());
            }
        }
    }

    /**
     * 分配权限给角色
     * 通过RolePermissionMapper添加关联关系
     */
    private void assignPermissionToRole(Integer roleId, Integer permissionId) {
        // 先检查关联关系是否已存在
        RolePermission existingRolePermission = rolePermissionMapper.selectByRoleIdAndPermissionId(roleId, permissionId);
        if (existingRolePermission != null) {
            log.debug("权限 {} 已经分配给角色 {}", permissionId, roleId);
            return;
        }
        
        // 创建新的角色-权限关联
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionId(permissionId);
        rolePermissionMapper.insert(rolePermission);
        log.info("权限 {} 成功分配给角色 {}", permissionId, roleId);
    }
} 