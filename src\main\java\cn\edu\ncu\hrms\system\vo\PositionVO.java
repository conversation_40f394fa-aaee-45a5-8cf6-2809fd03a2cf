package cn.edu.ncu.hrms.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 职位信息视图对象 (VO)
 */
@Data
@ApiModel(value="PositionVO", description="职位信息视图对象")
public class PositionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "职位ID")
    private Integer id;

    @ApiModelProperty(value = "职位名称")
    private String name;

    @ApiModelProperty(value = "职级")
    private Byte level;

    @ApiModelProperty(value = "关联账套ID")
    private Integer accountId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

} 