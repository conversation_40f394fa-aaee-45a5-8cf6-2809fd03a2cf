{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { isFirefox, checkAmapAvailability } from '@/utils/firefox-compatibility';\nimport { initMap } from '@/utils/amap';\nexport default {\n  name: 'FirefoxTest',\n  data() {\n    return {\n      browserInfo: navigator.userAgent,\n      isFirefoxBrowser: false,\n      amapStatus: '检查中...',\n      testResults: [],\n      showMap: false\n    };\n  },\n  mounted() {\n    this.initTest();\n  },\n  methods: {\n    async initTest() {\n      // 检测浏览器\n      this.isFirefoxBrowser = isFirefox();\n      this.addTestResult('info', `浏览器检测完成，Firefox: ${this.isFirefoxBrowser}`);\n\n      // 检查高德地图API状态\n      try {\n        const available = await checkAmapAvailability();\n        this.amapStatus = available ? '可用' : '不可用';\n        this.addTestResult(available ? 'success' : 'error', `高德地图API状态: ${this.amapStatus}`);\n      } catch (error) {\n        this.amapStatus = '检查失败';\n        this.addTestResult('error', `高德地图API检查失败: ${error.message}`);\n      }\n    },\n    testAmapAPI() {\n      this.addTestResult('info', '开始测试高德地图API...');\n      try {\n        if (typeof window.AMap === 'undefined') {\n          this.addTestResult('error', 'AMap对象不存在');\n          return;\n        }\n\n        // 测试基本API\n        const version = window.AMap.version || '未知版本';\n        this.addTestResult('success', `AMap版本: ${version}`);\n\n        // 测试创建LngLat对象\n        const lngLat = new window.AMap.LngLat(116.397428, 39.90923);\n        this.addTestResult('success', `创建LngLat对象成功: ${lngLat.toString()}`);\n\n        // 测试Geolocation\n        if (window.AMap.Geolocation) {\n          this.addTestResult('success', 'Geolocation插件可用');\n        } else {\n          this.addTestResult('warning', 'Geolocation插件不可用');\n        }\n      } catch (error) {\n        this.addTestResult('error', `API测试失败: ${error.message}`);\n      }\n    },\n    testMapInit() {\n      this.addTestResult('info', '开始测试地图初始化...');\n      try {\n        // 显示地图容器\n        this.showMap = true;\n\n        // 等待DOM更新\n        this.$nextTick(() => {\n          try {\n            const map = initMap('testMapContainer', {\n              zoom: 10,\n              center: [116.397428, 39.90923]\n            });\n            if (map) {\n              this.addTestResult('success', '地图初始化成功');\n\n              // 测试地图方法\n              setTimeout(() => {\n                try {\n                  const center = map.getCenter();\n                  this.addTestResult('success', `地图中心点: ${center.toString()}`);\n                  const zoom = map.getZoom();\n                  this.addTestResult('success', `地图缩放级别: ${zoom}`);\n                } catch (error) {\n                  this.addTestResult('error', `地图方法测试失败: ${error.message}`);\n                }\n              }, 1000);\n            } else {\n              this.addTestResult('error', '地图初始化返回null');\n            }\n          } catch (error) {\n            this.addTestResult('error', `地图初始化失败: ${error.message}`);\n          }\n        });\n      } catch (error) {\n        this.addTestResult('error', `地图测试准备失败: ${error.message}`);\n      }\n    },\n    addTestResult(type, message) {\n      const time = new Date().toLocaleTimeString();\n      this.testResults.push({\n        type,\n        time,\n        message\n      });\n\n      // 同时输出到控制台\n      const logMethod = type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'log';\n      console[logMethod](`[Firefox测试] ${time}: ${message}`);\n    },\n    clearConsole() {\n      console.clear();\n      this.testResults = [];\n      this.addTestResult('info', '控制台已清空');\n    }\n  }\n};", "map": {"version": 3, "names": ["isFirefox", "checkAmapAvailability", "initMap", "name", "data", "browserInfo", "navigator", "userAgent", "isFirefoxBrowser", "amapStatus", "testResults", "showMap", "mounted", "initTest", "methods", "addTestResult", "available", "error", "message", "testAmapAPI", "window", "AMap", "version", "lngLat", "LngLat", "toString", "Geolocation", "testMapInit", "$nextTick", "map", "zoom", "center", "setTimeout", "getCenter", "getZoom", "type", "time", "Date", "toLocaleTimeString", "push", "logMethod", "console", "clearConsole", "clear"], "sources": ["src/views/test/firefox-test.vue"], "sourcesContent": ["<template>\n  <div class=\"firefox-test\">\n    <el-card>\n      <div slot=\"header\">\n        <span>Firefox兼容性测试页面</span>\n      </div>\n      \n      <div class=\"test-info\">\n        <p><strong>浏览器信息：</strong>{{ browserInfo }}</p>\n        <p><strong>是否为Firefox：</strong>{{ isFirefoxBrowser ? '是' : '否' }}</p>\n        <p><strong>高德地图API状态：</strong>{{ amapStatus }}</p>\n      </div>\n\n      <div class=\"test-actions\">\n        <el-button type=\"primary\" @click=\"testAmapAPI\">测试高德地图API</el-button>\n        <el-button type=\"success\" @click=\"testMapInit\">测试地图初始化</el-button>\n        <el-button type=\"warning\" @click=\"clearConsole\">清空控制台</el-button>\n      </div>\n\n      <div class=\"test-results\" v-if=\"testResults.length > 0\">\n        <h4>测试结果：</h4>\n        <ul>\n          <li v-for=\"(result, index) in testResults\" :key=\"index\" :class=\"result.type\">\n            <strong>{{ result.time }}:</strong> {{ result.message }}\n          </li>\n        </ul>\n      </div>\n\n      <!-- 地图容器 -->\n      <div class=\"map-container\" v-if=\"showMap\">\n        <h4>地图测试区域：</h4>\n        <div id=\"testMapContainer\" style=\"width: 100%; height: 300px; border: 1px solid #ddd;\"></div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { isFirefox, checkAmapAvailability } from '@/utils/firefox-compatibility'\nimport { initMap } from '@/utils/amap'\n\nexport default {\n  name: 'FirefoxTest',\n  data() {\n    return {\n      browserInfo: navigator.userAgent,\n      isFirefoxBrowser: false,\n      amapStatus: '检查中...',\n      testResults: [],\n      showMap: false\n    }\n  },\n  mounted() {\n    this.initTest()\n  },\n  methods: {\n    async initTest() {\n      // 检测浏览器\n      this.isFirefoxBrowser = isFirefox()\n      this.addTestResult('info', `浏览器检测完成，Firefox: ${this.isFirefoxBrowser}`)\n\n      // 检查高德地图API状态\n      try {\n        const available = await checkAmapAvailability()\n        this.amapStatus = available ? '可用' : '不可用'\n        this.addTestResult(available ? 'success' : 'error', `高德地图API状态: ${this.amapStatus}`)\n      } catch (error) {\n        this.amapStatus = '检查失败'\n        this.addTestResult('error', `高德地图API检查失败: ${error.message}`)\n      }\n    },\n\n    testAmapAPI() {\n      this.addTestResult('info', '开始测试高德地图API...')\n      \n      try {\n        if (typeof window.AMap === 'undefined') {\n          this.addTestResult('error', 'AMap对象不存在')\n          return\n        }\n\n        // 测试基本API\n        const version = window.AMap.version || '未知版本'\n        this.addTestResult('success', `AMap版本: ${version}`)\n\n        // 测试创建LngLat对象\n        const lngLat = new window.AMap.LngLat(116.397428, 39.90923)\n        this.addTestResult('success', `创建LngLat对象成功: ${lngLat.toString()}`)\n\n        // 测试Geolocation\n        if (window.AMap.Geolocation) {\n          this.addTestResult('success', 'Geolocation插件可用')\n        } else {\n          this.addTestResult('warning', 'Geolocation插件不可用')\n        }\n\n      } catch (error) {\n        this.addTestResult('error', `API测试失败: ${error.message}`)\n      }\n    },\n\n    testMapInit() {\n      this.addTestResult('info', '开始测试地图初始化...')\n      \n      try {\n        // 显示地图容器\n        this.showMap = true\n        \n        // 等待DOM更新\n        this.$nextTick(() => {\n          try {\n            const map = initMap('testMapContainer', {\n              zoom: 10,\n              center: [116.397428, 39.90923]\n            })\n            \n            if (map) {\n              this.addTestResult('success', '地图初始化成功')\n              \n              // 测试地图方法\n              setTimeout(() => {\n                try {\n                  const center = map.getCenter()\n                  this.addTestResult('success', `地图中心点: ${center.toString()}`)\n                  \n                  const zoom = map.getZoom()\n                  this.addTestResult('success', `地图缩放级别: ${zoom}`)\n                } catch (error) {\n                  this.addTestResult('error', `地图方法测试失败: ${error.message}`)\n                }\n              }, 1000)\n            } else {\n              this.addTestResult('error', '地图初始化返回null')\n            }\n          } catch (error) {\n            this.addTestResult('error', `地图初始化失败: ${error.message}`)\n          }\n        })\n      } catch (error) {\n        this.addTestResult('error', `地图测试准备失败: ${error.message}`)\n      }\n    },\n\n    addTestResult(type, message) {\n      const time = new Date().toLocaleTimeString()\n      this.testResults.push({\n        type,\n        time,\n        message\n      })\n      \n      // 同时输出到控制台\n      const logMethod = type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'log'\n      console[logMethod](`[Firefox测试] ${time}: ${message}`)\n    },\n\n    clearConsole() {\n      console.clear()\n      this.testResults = []\n      this.addTestResult('info', '控制台已清空')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.firefox-test {\n  padding: 20px;\n}\n\n.test-info {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n}\n\n.test-info p {\n  margin: 5px 0;\n}\n\n.test-actions {\n  margin-bottom: 20px;\n}\n\n.test-actions .el-button {\n  margin-right: 10px;\n}\n\n.test-results {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #fafafa;\n  border-radius: 4px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.test-results ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.test-results li {\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.test-results li:last-child {\n  border-bottom: none;\n}\n\n.test-results li.success {\n  color: #67c23a;\n}\n\n.test-results li.error {\n  color: #f56c6c;\n}\n\n.test-results li.warning {\n  color: #e6a23c;\n}\n\n.test-results li.info {\n  color: #409eff;\n}\n\n.map-container {\n  margin-top: 20px;\n  padding: 15px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n</style>\n"], "mappings": ";AAsCA,SAAAA,SAAA,EAAAC,qBAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA,EAAAC,SAAA,CAAAC,SAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA,MAAAD,SAAA;MACA;MACA,KAAAL,gBAAA,GAAAR,SAAA;MACA,KAAAe,aAAA,kCAAAP,gBAAA;;MAEA;MACA;QACA,MAAAQ,SAAA,SAAAf,qBAAA;QACA,KAAAQ,UAAA,GAAAO,SAAA;QACA,KAAAD,aAAA,CAAAC,SAAA,2CAAAP,UAAA;MACA,SAAAQ,KAAA;QACA,KAAAR,UAAA;QACA,KAAAM,aAAA,0BAAAE,KAAA,CAAAC,OAAA;MACA;IACA;IAEAC,YAAA;MACA,KAAAJ,aAAA;MAEA;QACA,WAAAK,MAAA,CAAAC,IAAA;UACA,KAAAN,aAAA;UACA;QACA;;QAEA;QACA,MAAAO,OAAA,GAAAF,MAAA,CAAAC,IAAA,CAAAC,OAAA;QACA,KAAAP,aAAA,uBAAAO,OAAA;;QAEA;QACA,MAAAC,MAAA,OAAAH,MAAA,CAAAC,IAAA,CAAAG,MAAA;QACA,KAAAT,aAAA,6BAAAQ,MAAA,CAAAE,QAAA;;QAEA;QACA,IAAAL,MAAA,CAAAC,IAAA,CAAAK,WAAA;UACA,KAAAX,aAAA;QACA;UACA,KAAAA,aAAA;QACA;MAEA,SAAAE,KAAA;QACA,KAAAF,aAAA,sBAAAE,KAAA,CAAAC,OAAA;MACA;IACA;IAEAS,YAAA;MACA,KAAAZ,aAAA;MAEA;QACA;QACA,KAAAJ,OAAA;;QAEA;QACA,KAAAiB,SAAA;UACA;YACA,MAAAC,GAAA,GAAA3B,OAAA;cACA4B,IAAA;cACAC,MAAA;YACA;YAEA,IAAAF,GAAA;cACA,KAAAd,aAAA;;cAEA;cACAiB,UAAA;gBACA;kBACA,MAAAD,MAAA,GAAAF,GAAA,CAAAI,SAAA;kBACA,KAAAlB,aAAA,sBAAAgB,MAAA,CAAAN,QAAA;kBAEA,MAAAK,IAAA,GAAAD,GAAA,CAAAK,OAAA;kBACA,KAAAnB,aAAA,uBAAAe,IAAA;gBACA,SAAAb,KAAA;kBACA,KAAAF,aAAA,uBAAAE,KAAA,CAAAC,OAAA;gBACA;cACA;YACA;cACA,KAAAH,aAAA;YACA;UACA,SAAAE,KAAA;YACA,KAAAF,aAAA,sBAAAE,KAAA,CAAAC,OAAA;UACA;QACA;MACA,SAAAD,KAAA;QACA,KAAAF,aAAA,uBAAAE,KAAA,CAAAC,OAAA;MACA;IACA;IAEAH,cAAAoB,IAAA,EAAAjB,OAAA;MACA,MAAAkB,IAAA,OAAAC,IAAA,GAAAC,kBAAA;MACA,KAAA5B,WAAA,CAAA6B,IAAA;QACAJ,IAAA;QACAC,IAAA;QACAlB;MACA;;MAEA;MACA,MAAAsB,SAAA,GAAAL,IAAA,yBAAAA,IAAA;MACAM,OAAA,CAAAD,SAAA,iBAAAJ,IAAA,KAAAlB,OAAA;IACA;IAEAwB,aAAA;MACAD,OAAA,CAAAE,KAAA;MACA,KAAAjC,WAAA;MACA,KAAAK,aAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}