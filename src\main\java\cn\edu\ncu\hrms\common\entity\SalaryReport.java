package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("薪资报表")
public class SalaryReport {
    @ApiModelProperty(value = "报表ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "员工ID", required = true)
    private Integer employeeId;

    @ApiModelProperty(value = "账套ID", required = true)
    private Integer accountId;

    @ApiModelProperty(value = "所属月份", required = true)
    private String month;

    @ApiModelProperty(value = "基本工资", required = true)
    private BigDecimal baseSalary;

    @ApiModelProperty(value = "考核系数", example = "1.0")
    private BigDecimal adjustmentCoefficient;

    @ApiModelProperty("绩效奖金")
    private BigDecimal performanceBonus;

    @ApiModelProperty("工作奖金")
    private BigDecimal workBonus;

    @ApiModelProperty("工作罚款")
    private BigDecimal workFine;

    @ApiModelProperty("考勤罚款")
    private BigDecimal attendanceFine;

    @ApiModelProperty("社保扣除")
    private BigDecimal insurance;

    @ApiModelProperty(value = "应发总额", required = true)
    private BigDecimal total;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
