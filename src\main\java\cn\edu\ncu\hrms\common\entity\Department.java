package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("部门信息")
public class Department {
    @ApiModelProperty(value = "部门ID", required = true, example = "201")
    private Integer id;

    @ApiModelProperty(value = "部门名称", required = true, example = "技术部")
    private String name;

    @ApiModelProperty(value = "上级部门ID", example = "200")
    private Integer parentId;

    @ApiModelProperty(value = "部门领导ID", example = "1003")
    private Integer leaderId;

    @ApiModelProperty(value = "上班打卡开始时间", example = "08:00")
    private String clockInStartTime;

    @ApiModelProperty(value = "上班打卡结束时间", example = "09:00")
    private String clockInEndTime;

    @ApiModelProperty(value = "迟到阈值（分钟）", example = "30")
    private Integer lateThreshold;

    @ApiModelProperty(value = "迟到扣款（元）", example = "50.00")
    private BigDecimal lateDeduction;

    @ApiModelProperty(value = "下班打卡开始时间", example = "17:00")
    private String clockOutStartTime;

    @ApiModelProperty(value = "下班打卡结束时间", example = "18:00")
    private String clockOutEndTime;

    @ApiModelProperty(value = "早退阈值（分钟）", example = "30")
    private Integer earlyThreshold;

    @ApiModelProperty(value = "早退扣款（元）", example = "50.00")
    private BigDecimal earlyDeduction;

    @ApiModelProperty(value = "打卡地点（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockLocation;

    @ApiModelProperty(value = "打卡范围（米）", example = "100")
    private Integer clockRadius;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-01-02 13:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
