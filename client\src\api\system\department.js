import request from '@/utils/request'

// 查询部门列表
export function listDepartment(query) {
  return request({
    url: '/system/department/list',
    method: 'get',
    params: query
  })
}

// 查询部门树结构
export function getDepartmentTree() {
  return request({
    url: '/system/department/tree',
    method: 'get'
  })
}

// 获取部门及其子部门列表
export function getChildDepartments(id) {
  return request({
    url: `/system/department/${id}/child`,
    method: 'get'
  })
}

// 查询部门详细
export function getDepartment(id) {
  return request({
    url: '/system/department/' + id,
    method: 'get'
  })
}

// 根据部门ID查询员工
export function getEmployeesByDepartmentId(id) {
  return request({
    url: '/system/employee/department/' + id,
    method: 'get'
  })
}

// 新增部门
export function addDepartment(data) {
  return request({
    url: '/system/department',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDepartment(id, data) {
  return request({
    url: '/system/department/' + id,
    method: 'put',
    data: data
  })
}

// 删除部门
export function deleteDepartment(id) {
  return request({
    url: '/system/department/' + id,
    method: 'delete'
  })
}
