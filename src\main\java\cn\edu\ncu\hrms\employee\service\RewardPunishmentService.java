package cn.edu.ncu.hrms.employee.service;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentAddDTO;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentQueryDTO;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentUpdateDTO;
import cn.edu.ncu.hrms.employee.vo.RewardPunishmentVO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 奖惩信息服务接口
 */
public interface RewardPunishmentService {

    /**
     * 根据ID获取奖惩信息 VO
     */
    Result<RewardPunishmentVO> getVOById(Long id);

    /**
     * 根据员工ID获取奖惩信息 VO 列表
     */
    PageInfo<RewardPunishmentVO> listVOsByEmployeeId(PageQueryDTO queryDTO);

    /**
     * 分页查询奖惩信息 VO
     */
    PageInfo<RewardPunishmentVO> pageVOs(RewardPunishmentQueryDTO queryDTO);

    /**
     * 添加奖惩信息
     */
    Result<Void> add(RewardPunishmentAddDTO addDTO);


    /**
     * 更新奖惩信息
     */
    Result<Void> update(RewardPunishmentUpdateDTO updateDTO);

    /**
     * 删除奖惩信息
     */
    Result<Void> delete(Long id);

    /**
     * 批量删除奖惩信息
     */
    Result<Void> batchDelete(List<Long> ids);

    /**
     * 获取奖惩类型统计数据
     */
    Result<?> getTypeStatistics();

    /**
     * 获取指定日期范围内的奖惩金额统计
     */
    Result<?> getAmountStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 获取员工奖惩排行榜
     */
    Result<?> getEmployeeRanking(Integer type, Integer limit);

    /**
     * 导出奖惩信息Excel
     */
    void exportExcel(HttpServletResponse response, RewardPunishmentQueryDTO queryDTO);

    /**
     * 下载导入模板
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入奖惩信息
     */
    Result<Void> importExcel(MultipartFile file);
} 