package cn.edu.ncu.hrms.system.controller;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.employee.service.EmployeeService;
import cn.edu.ncu.hrms.system.vo.DashboardDistributionStatsVO;
import cn.edu.ncu.hrms.system.vo.DashboardOnboardingStatsVO;
import cn.edu.ncu.hrms.system.dto.EmployeeAddDTO;
import cn.edu.ncu.hrms.system.dto.EmployeePageQueryDTO;
import cn.edu.ncu.hrms.system.dto.EmployeeUpdateDTO;
import cn.edu.ncu.hrms.system.service.SystemEmployeeService;
import cn.edu.ncu.hrms.system.vo.EmployeeVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统员工管理控制器
 */
@RestController
@RequestMapping("/system/employee")
@Api(tags = "系统员工管理接口")
public class SystemEmployeeController {

    @Autowired
    private SystemEmployeeService systemEmployeeService;

    @Autowired
    private EmployeeService employeeService;

    /**
     * 获取员工列表 (返回VO)
     */
    @GetMapping("/list")
    @ApiOperation("获取员工列表(包含部门职位名称)")
    @PreAuthorize("@hrms.hasAuthority('system:employee:query')")
    public Result<List<EmployeeVO>> getEmployeeList() {
        return systemEmployeeService.listAllEmployees();
    }

    /**
     * 分页查询员工 (使用DTO)
     */
    @GetMapping("/page")
    @ApiOperation("分页查询员工(包含部门职位名称)")
    @PreAuthorize("@hrms.hasAuthority('system:employee:query')")
    public Result<PageResult<EmployeeVO>> pageEmployees(EmployeePageQueryDTO queryDTO) {
        PageInfo<EmployeeVO> employeeVOPageInfo = systemEmployeeService.pageEmployees(queryDTO);
        return PageResult.success(employeeVOPageInfo.getTotal(), employeeVOPageInfo.getList());
    }

    /**
     * 获取未注册用户的员工列表
     */
    @GetMapping("/unregistered")
    @ApiOperation("获取未注册用户的员工列表")
    @PreAuthorize("@hrms.hasAuthority('system:employee:query')")
    public Result<List<EmployeeVO>> getUnregisteredEmployeeList() {
        return systemEmployeeService.getUnregisteredEmployeeList();
    }

    /**
     * 根据部门ID查询员工（返回VO），可指定角色（返回Employee）
     */
    @GetMapping("/department/{id}")
    @ApiOperation("根据部门ID查询员工，可指定角色")
    @PreAuthorize("@hrms.hasAuthority('system:employee:query')")
    public Result<?> getEmployeesByDepartmentId(@PathVariable Integer id, @RequestParam(required = false) String roleKey) {
        if (roleKey == null) {
            return systemEmployeeService.getEmployeesByDepartmentId(id);
        } else {
            return systemEmployeeService.getEmployeesByDepartmentIdAndRole(id, roleKey);
        }
    }


    /**
     * 根据ID获取员工信息 (返回VO)
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取员工信息(包含部门职位名称)")
    @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:employee:query')")
    public Result<EmployeeVO> getEmployeeVOById(@PathVariable Integer id) {
        return systemEmployeeService.getEmployeeVOById(id);
    }

    /**
     * 创建员工
     */
    @PostMapping
    @ApiOperation("创建员工")
    @PreAuthorize("@hrms.hasAuthority('system:employee:add')")
    public Result<Void> createEmployee(@Valid @RequestBody EmployeeAddDTO addDTO) {
        return employeeService.createEmployee(addDTO);
    }

    /**
     * 更新员工信息
     */
    @PutMapping
    @ApiOperation("更新员工信息")
    @PreAuthorize("@hrms.hasAuthority('system:employee:edit')")
    public Result<Void> updateEmployee(@Valid @RequestBody EmployeeUpdateDTO updateDTO) {
        return systemEmployeeService.updateEmployee(updateDTO);
    }

    /**
     * 删除员工
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除员工")
    @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:employee:delete')")
    public Result<Void> deleteEmployee(@PathVariable Integer id) {
        return employeeService.deleteEmployee(id);
    }

    /**
     * 更新员工状态（离职/在职）
     */
    @PutMapping("/status/{id}")
    @ApiOperation("更新员工状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "int", paramType = "path"),
            @ApiImplicitParam(name = "status", value = "状态(true-在职 false-离职)", required = true, dataType = "boolean", paramType = "query")
    })
    @PreAuthorize("@hrms.hasAuthority('system:employee:edit')")
    public Result<Void> updateEmployeeStatus(@PathVariable Integer id, @RequestParam Boolean status) {
        return systemEmployeeService.updateEmployeeStatus(id, status);
    }

    @GetMapping("/dashboard/onboarding")
    @ApiOperation("获员工入职情况统计仪表盘")
    @PreAuthorize("@hrms.hasAuthority('system:employee:dashboard')")
    public Result<DashboardOnboardingStatsVO> getDashboardOnboardingStats() {
        return systemEmployeeService.getDashboardOnboardingStats();
    }

    @GetMapping("/dashboard/distribution")
    @ApiOperation("获取员工部门分布统计仪表盘")
    @PreAuthorize("@hrms.hasAuthority('system:employee:dashboard')")
    public Result<List<DashboardDistributionStatsVO>> getDashboardDistributionStats() {
        return systemEmployeeService.getDashboardDistributionStats();
    }
} 