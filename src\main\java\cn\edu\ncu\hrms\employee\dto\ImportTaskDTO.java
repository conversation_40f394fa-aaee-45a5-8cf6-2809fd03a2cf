package cn.edu.ncu.hrms.employee.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "员工批量导入消息内容")
public class ImportTaskDTO {

    @ApiModelProperty("文件路径")
    private String filePath;

    @ApiModelProperty("用户ID")
    private Integer uid;
}
