import { getToken, setToken, removeToken } from '@/utils/auth'
import { login, getUserInfo, logout as logoutAPI } from '@/api/user'
import { resetRouter } from '@/router'
import { resetTokenExpiredMessageFlag } from '@/utils/request'
import store from '@/store'

const state = {
  token: getToken(), // 从缓存中读取初始值
  userInfo: {}, // 存储用户基本资料状态
  routes: [] // 路由数组
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    // 同步到缓存
    setToken(token)
  },
  REMOVE_TOKEN: (state) => {
    // 删除Vuex的token
    state.token = null
    removeToken()
  },
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_ROUTES: (state, routes) => {
    state.routes = routes // 设置路由
  }
}

const actions = {
  // 登录
  async login({ commit }, loginData) {
    try {
      // 重置Token过期消息标记
      resetTokenExpiredMessageFlag()

      const response = await login(loginData)
      if (response.code === 1 && response.data) {
        commit('SET_TOKEN', response.data)
        return response.data
      } else {
        // 明确处理code=0的情况，返回服务器提供的错误消息
        return Promise.reject(new Error(response.msg || '登录失败'))
      }
    } catch (error) {
      // 处理网络错误或其他异常
      return Promise.reject(error)
    }
  },

  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const response = await getUserInfo()
      if (response.code === 1 && response.data) {
        // 确保有权限数组
        if (!response.data.permissions) {
          response.data.permissions = []
        }
        commit('SET_USER_INFO', response.data)
        return response.data
      } else {
        return Promise.reject(new Error(response.msg || '获取用户信息失败'))
      }
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // 退出登录
  async logout({ commit, state }) {
    try {
      // 只有在token存在且有效时才调用后端登出接口
      if (state.token) {
        try {
          await logoutAPI()
        } catch (error) {
          // 如果是401或403错误，说明token已过期，不需要处理
          if (!(error.response && (error.response.status === 401 || error.response.status === 403))) {
            console.error('登出API调用失败:', error)
          }
        }
      }

      // 无论后端响应如何，都清除前端状态
      commit('REMOVE_TOKEN')
      commit('SET_USER_INFO', {})
      // 重置用户角色信息
      await store.dispatch('userInfo/resetUserInfo')
      // 重置路由
      resetRouter()
      // 重置Token过期消息标记
      resetTokenExpiredMessageFlag()

      return Promise.resolve()
    } catch (error) {
      console.error('登出过程中发生错误:', error)

      // 即使过程中出错，也要清除前端状态
      commit('REMOVE_TOKEN')
      commit('SET_USER_INFO', {})
      // 重置用户角色信息
      await store.dispatch('userInfo/resetUserInfo')
      resetRouter()
      resetTokenExpiredMessageFlag()

      return Promise.resolve()
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
