package cn.edu.ncu.hrms.employee.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.Data;

import java.time.LocalDate;

/**
 * 奖惩信息Excel数据模型
 */
@Data
@HeadRowHeight(25)
@HeadStyle(fillBackgroundColor = 22)
@HeadFontStyle(fontHeightInPoints = 12)
public class RewardPunishmentExcel {

    @ExcelProperty("员工ID")
    @ColumnWidth(15)
    private Long employeeId;

    @ExcelProperty("员工姓名")
    @ColumnWidth(15)
    private String employeeName;

    @ExcelProperty(value = "奖惩类型", converter = TypeConverter.class)
    @ColumnWidth(15)
    private Integer type;

    @ExcelProperty("奖惩标题")
    @ColumnWidth(25)
    private String title;

    @ExcelProperty("奖惩内容")
    @ColumnWidth(35)
    private String content;

    @ExcelProperty("奖惩金额")
    @ColumnWidth(15)
    private Double amount;

    @ExcelProperty("奖惩日期")
    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd")
    private LocalDate rewardDate;

    @ExcelProperty("申请人")
    @ColumnWidth(15)
    private String applyUserName;

    @ExcelProperty("审批人")
    @ColumnWidth(15)
    private String approveUserName;

    @ExcelProperty(value = "审批状态", converter = ApproveStatusConverter.class)
    @ColumnWidth(15)
    private Integer approveStatus;

    @ExcelProperty("备注")
    @ColumnWidth(35)
    private String remark;
} 