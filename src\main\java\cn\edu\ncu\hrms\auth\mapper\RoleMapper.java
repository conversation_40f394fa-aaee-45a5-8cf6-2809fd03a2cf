package cn.edu.ncu.hrms.auth.mapper;

import cn.edu.ncu.hrms.common.entity.Role;
import cn.edu.ncu.hrms.system.dto.RolePageQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色表 Mapper 接口
 */
@Mapper
public interface RoleMapper {

    /**
     * 根据用户 ID 查询该用户拥有的所有角色列表
     * 
     * 通过 user_role 中间表关联查询。
     *
     * @param userId 用户ID
     * @return List<Role> 角色列表
     */
    List<Role> selectRolesByUserId(Integer userId);

    /**
     * 根据角色 ID 查询角色
     *
     * @param id 角色ID
     * @return Role 角色实体，未找到返回 null
     */
    Role selectById(Integer id);

    /**
     * 新增角色
     * 
     * 用于管理员添加角色。
     * XML 中需配置 useGeneratedKeys="true" keyProperty="id" 以返回自增主键。
     *
     * @param role 待插入的角色信息
     * @return int 影响行数
     */
    int insert(Role role);

    /**
     * 根据主键 ID 更新角色信息
     * 
     * 用于管理员修改角色。
     * XML 中应使用 <set> 和 <if> 实现动态更新。
     *
     * @param role 包含主键 ID 和待更新字段的角色信息
     * @return int 影响行数
     */
    int update(Role role);

    /**
     * 根据主键 ID 删除角色
     * 
     * 注意：删除角色前应处理关联的用户和权限。可能是物理删除或逻辑删除，取决于业务设计。
     *
     * @param id 角色ID
     * @return int 影响行数
     */
    int deleteById(Integer id);

    /**
     * 查询所有角色列表
     * 
     * 用于管理员角色管理列表展示，可在 Service 层配合 PageHelper 分页。
     *
     * @return List<Role> 所有角色的列表
     */
    List<Role> selectList();

    /**
     * 根据查询条件查询角色列表
     * @param queryDTO
     * @return
     */
    List<Role> selectListByCondition(RolePageQueryDTO queryDTO);

    /**
     * 根据角色标识 (roleKey) 查询角色
     * 
     * roleKey 应具有唯一性约束。
     *
     * @param roleKey 角色标识字符串 (例如 "admin", "employee")
     * @return Role 角色实体，未找到返回 null
     */
    Role selectByRoleKey(String roleKey);

    /**
     * 根据角色名称查询角色
     * 
     * 用于检查角色名称唯一性，应查询未删除的角色 (del_flag = 0)。
     *
     * @param name 角色名称
     * @return Role 角色实体，未找到返回 null
     */
    Role selectByName(String name);
} 