package cn.edu.ncu.hrms.salary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(value="PositionVO", description="账套匹配VO")
public class PositionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "职位ID")
    private Integer id;

    @ApiModelProperty(value = "职位名称")
    private String name;

    @ApiModelProperty(value = "职级")
    private Byte level;

    @ApiModelProperty(value = "账套ID")
    private Integer accountId;

    @ApiModelProperty(value = "账套名称")
    private String accountName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}
