package cn.edu.ncu.hrms.system.controller;

import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.system.dto.RolePageQueryDTO;
import cn.edu.ncu.hrms.system.vo.RoleVO;
import cn.edu.ncu.hrms.system.dto.RoleAddDTO;
import cn.edu.ncu.hrms.system.dto.RoleUpdateDTO;
import cn.edu.ncu.hrms.system.vo.PermissionVO;
import cn.edu.ncu.hrms.system.dto.RolePermissionUpdateDTO;
import cn.edu.ncu.hrms.system.service.SystemPermissionService;
import cn.edu.ncu.hrms.system.service.SystemRoleService;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.vo.UserVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统角色管理控制器
 */
@RestController
@RequestMapping("/system/role")
@Api(tags = "系统角色管理接口")
public class SystemRoleController {

    @Autowired
    private SystemRoleService systemRoleService;

    @Autowired
    private SystemPermissionService systemPermissionService;

    /**
     * 获取角色列表
     */
    @GetMapping("/list")
    @ApiOperation("获取角色列表")
    @PreAuthorize("@hrms.hasAuthority('system:role:list')")
    public Result<PageResult<RoleVO>> getRoleList(RolePageQueryDTO queryDTO) {
        PageInfo<RoleVO> pageInfo = systemRoleService.listRoles(queryDTO);
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }

    /**
     * 根据ID获取角色信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取角色信息")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:role:query')")
    public Result<RoleVO> getRoleById(@PathVariable Integer id) {
        return systemRoleService.getRoleVOById(id);
    }

    /**
     * 创建角色
     */
    @PostMapping
    @ApiOperation("创建角色")
    @PreAuthorize("@hrms.hasAuthority('system:role:add')")
    public Result<Void> createRole(@Valid @RequestBody RoleAddDTO addDTO) {
        return systemRoleService.createRole(addDTO);
    }

    /**
     * 更新角色信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新角色信息")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:role:edit')")
    public Result<Void> updateRole(
            @PathVariable Integer id,
            @Valid @RequestBody RoleUpdateDTO updateDTO) {
        return systemRoleService.updateRole(id, updateDTO);
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除角色")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:role:delete')")
    public Result<Void> deleteRole(@PathVariable Integer id) {
        return systemRoleService.deleteRole(id);
    }

    /**
     * 获取角色的权限列表
     */
    @GetMapping("/{id}/permissions")
    @ApiOperation("获取角色的权限列表")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:role:query')")
    public Result<List<PermissionVO>> getRolePermissions(@PathVariable Integer id) {
        return systemPermissionService.getPermissionsByRoleId(id);
    }

    /**
     * 设置角色权限
     */
    @PostMapping("/{id}/permissions")
    @ApiOperation("设置角色权限")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:role:edit')")
    public Result<Void> setRolePermissions(
            @PathVariable Integer id,
            @Valid @RequestBody RolePermissionUpdateDTO updateDTO) {
        return systemRoleService.setRolePermissions(id, updateDTO);
    }
} 