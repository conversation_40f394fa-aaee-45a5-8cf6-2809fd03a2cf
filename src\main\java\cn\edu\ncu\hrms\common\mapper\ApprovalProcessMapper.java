package cn.edu.ncu.hrms.common.mapper;

import cn.edu.ncu.hrms.common.entity.ApprovalProcess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 审批流程Mapper接口，负责审批流程表的数据库操作
 */
@Mapper
public interface ApprovalProcessMapper {
    /**
     * 插入单个审批流程节点
     * @param process 审批流程节点实体
     * @return 影响行数
     */
    int insert(ApprovalProcess process);

    /**
     * 批量插入审批流程节点
     * @param processList 审批流程节点列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<ApprovalProcess> processList);

    /**
     * 根据主键ID查询审批流程节点
     * @param id 节点ID
     * @return 审批流程节点实体
     */
    ApprovalProcess selectById(@Param("id") Integer id);

    /**
     * 查询某申请的全部审批流程节点，按顺序返回
     * @param requestId 申请ID
     * @param type 申请类型（RESIGN/LEAVE）
     * @return 审批流程节点列表
     */
    List<ApprovalProcess> selectByRequestIdAndType(@Param("requestId") Integer requestId, @Param("type") String type);

    /**
     * 查询下一个待审批节点
     * @param requestId 申请ID
     * @param stepOrder 当前审批顺序(Byte类型)
     * @return 下一个待审批节点，若无则返回null
     */
    ApprovalProcess selectNextPending(@Param("requestId") Integer requestId, @Param("stepOrder") Byte stepOrder, @Param("type") String type);

    /**
     * 更新审批流程节点（如审批状态、意见等）
     * @param process 审批流程节点实体
     * @return 影响行数
     */
    int update(ApprovalProcess process);

    /**
     * 批量更新某申请下所有新创建节点的状态
     * @param requestId 申请ID
     * @param type 申请类型（RESIGN/LEAVE）
     * @param status 状态
     * @return 影响行数
     */
    int updateAllNewStatus(@Param("requestId") Integer requestId, @Param("type") String type, @Param("status") String status);


    /**
     * 批量删除审批流程节点
     * @param requestId
     * @return
     */
    int cancel(@Param("requestId") Integer requestId, @Param("type") String type);


    /**
     * 查询当前待审批节点
     * @param requestId
     * @param type
     * @return
     */
    Integer selectCurrentPending(@Param("requestId") Integer requestId, @Param("type") String type);


    /**
     * 查询申请当前是否由employeeId审批
     * @param requestId
     * @param type
     * @param employeeId
     * @return
     */
    Boolean selectIsPendingByMe(@Param("requestId")Integer requestId, @Param("type") String type, @Param("employeeId") Integer employeeId);
}
