import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './plugins/element.js'
import moment from 'moment'

import 'normalize.css/normalize.css' // 重置CSS样式
import '@/styles/index.scss' // 全局样式

import './icons' // 图标
import './permission' // 权限控制
import { initFirefoxCompatibility } from '@/utils/firefox-compatibility' // Firefox兼容性处理

// 初始化Firefox兼容性处理
initFirefoxCompatibility()

// Firefox兼容性：全局错误处理，特别针对高德地图API
Vue.config.errorHandler = function (err, vm, info) {
  // 检查是否是高德地图相关错误
  if (err.message && (err.message.includes('AMap') || err.message.includes('amap') || err.message.includes('webapi.amap.com'))) {
    console.warn('高德地图相关错误（已忽略）:', err.message, info)
    return
  }
  // 其他错误正常处理
  console.error('Vue应用错误:', err, info)
}

// 初始化token过期状态
// 确保在应用启动时就创建这个全局变量，防止出现未定义的情况
if (!window.tokenExpiredState) {
  window.tokenExpiredState = {
    messageShown: false,
    lastShownTimestamp: 0,
    cooldownPeriod: 3000
  }
}

// 创建事件总线
Vue.prototype.$bus = new Vue()

// 全局组件
import TreeSelect from '@/components/TreeSelect'
Vue.component('el-tree-select', TreeSelect)

// 注册全局指令
Vue.directive('permission', {
  inserted(el, binding) {
    const permissions = store.getters.permissions
    if (permissions && permissions.length > 0) {
      const hasPermission = permissions.some(permission => {
        return permission === binding.value
      })
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
})

// 注册全局过滤器
Vue.filter('dateFormat', function(value, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return ''
  return moment(value).format(format)
})

// 注册全局方法
Vue.prototype.$download = function(res, fileName) {
  if (!res) return
  const blob = new Blob([res])
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  window.URL.revokeObjectURL(link.href)
}

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
