package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 创建用户数据传输对象 (DTO) - 根据 User.java 实体调整
 */
@Data
@ApiModel(value="UserAddDTO", description="创建用户数据传输对象")
public class UserAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名", required = true)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3到20之间")
    private String username;

    @ApiModelProperty(value = "密码", required = true, notes = "密码将在后端加密存储")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6到20之间")
    private String password;

    @ApiModelProperty(value = "是否管理员", required = true)
    @NotNull(message = "是否管理员不能为空")
    private Boolean isAdmin;

    @ApiModelProperty(value = "关联的员工ID", notes = "如果是员工类型用户，需要关联员工档案")
    private Integer employeeId;

    @ApiModelProperty(value = "分配的角色ID列表", notes = "创建用户时同时分配角色")
    private List<Integer> roleIds;

} 