package cn.edu.ncu.hrms.attendance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 员工月度考勤报表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonthlyAttendanceReportDTO {
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 员工姓名
     */
    private String employeeName;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 报表年份
     */
    private Integer year;
    
    /**
     * 报表月份
     */
    private Integer month;
    
    /**
     * 应出勤天数
     */
    private Integer requiredWorkDays;
    
    /**
     * 实际出勤天数
     */
    private Integer actualWorkDays;
    
    /**
     * 正常打卡天数
     */
    private Integer normalDays;
    
    /**
     * 迟到次数
     */
    private Integer lateTimes;
    
    /**
     * 早退次数
     */
    private Integer earlyTimes;
    
    /**
     * 缺勤天数
     */
    private Integer absentDays;
    
    /**
     * 病假天数
     */
    private Integer sickLeaveDays;
    
    /**
     * 事假天数
     */
    private Integer personalLeaveDays;
    
    /**
     * 年假天数
     */
    private Integer annualLeaveDays;
    
    /**
     * 其他请假天数
     */
    private Integer otherLeaveDays;
    
    /**
     * 出勤率
     */
    private BigDecimal attendanceRate;
    
    /**
     * 扣款金额
     */
    private BigDecimal deductionAmount;
    
    /**
     * 考勤异常备注
     */
    private String remark;
    
    /**
     * 每日考勤详情
     */
    private List<DailyAttendanceDTO> dailyDetails;
    
    /**
     * 报表生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime generateTime;
    
    /**
     * 日考勤详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyAttendanceDTO {
        /**
         * 日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private LocalDate date;
        
        /**
         * 星期
         */
        private String dayOfWeek;
        
        /**
         * 是否为工作日
         */
        private Boolean isWorkday;
        
        /**
         * 考勤状态（正常、迟到、早退、缺勤、请假、加班等）
         */
        private String status;
        
        /**
         * 签到时间
         */
        @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
        private LocalTime clockInTime;
        
        /**
         * 签退时间
         */
        @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
        private LocalTime clockOutTime;
        
        /**
         * 工作时长（小时）
         */
        private BigDecimal workHours;
        
        
        /**
         * 请假类型
         */
        private String leaveType;
        
        /**
         * 请假时长（小时）
         */
        private BigDecimal leaveHours;
        
        /**
         * 备注
         */
        private String remark;
    }
} 