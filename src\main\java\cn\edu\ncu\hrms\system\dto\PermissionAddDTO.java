package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 创建权限数据传输对象 (DTO) - 根据 Permission.java 实体调整
 */
@Data
@ApiModel(value="PermissionAddDTO", description="创建权限数据传输对象")
public class PermissionAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "权限标识", notes = "例如: system:user:add")
    private String permissionKey;

    @ApiModelProperty(value = "权限描述")
    private String description;

} 