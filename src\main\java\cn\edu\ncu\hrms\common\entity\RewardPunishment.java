package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.DecimalMax;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel("奖惩信息")
@Data
public class RewardPunishment implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖惩ID", required = true, example = "1")
    private Integer id;

    @ApiModelProperty(value = "员工ID", required = true, example = "2")
    private Integer employeeId;

    @ApiModelProperty(value = "操作人员工ID", required = true, example = "1")
    private Integer operatorId;

    @ApiModelProperty(value = "奖惩类型（0奖励 1惩罚）", allowableValues = "0,1", example = "0")
    private Integer type;

    @ApiModelProperty(value = "奖惩标题", required = true, example = "优秀员工奖励")
    private String title;

    @ApiModelProperty(value = "奖惩内容", required = true, example = "因表现优异，给予奖金奖励")
    private String content;

    @ApiModelProperty(value = "奖惩金额", example = "500.00")
    @DecimalMax(value = "10000.00", message = "金额不能超过10000元")
    private BigDecimal amount;

    @ApiModelProperty(value = "奖惩日期", example = "2023-10-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate rewardDate;

    @ApiModelProperty(value = "备注", example = "无")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2023-10-01 10:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-10-01 12:00:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
