<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.edu.ncu.hrms.auth.mapper.UserMapper">

    <sql id="Base_Column_List">
        id, username, password, employee_id, is_admin, create_time, update_time, del_flag
    </sql>
    
    <!-- 根据用户名查询用户详情 -->
    <select id="selectByUsername" resultType="User" parameterType="java.lang.String">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            user
        WHERE 
            username = #{username}
            AND del_flag = 0
    </select>
    
    <!-- 根据用户ID查询用户详情 -->
    <select id="selectById" resultType="User" parameterType="java.lang.Integer">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            user
        WHERE 
            id = #{id}
            AND del_flag = 0
    </select>
    
    <!-- 增加新用户 -->
    <insert id="insert" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (
            username, password, employee_id, is_admin, create_time, update_time, del_flag
        ) VALUES (
            #{username}, #{password}, #{employeeId}, #{isAdmin}, NOW(), NOW(), 0
        )
    </insert>
    
    <!-- 更新用户信息 -->
    <update id="update" parameterType="User">
        UPDATE user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="isAdmin != null">is_admin = #{isAdmin},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 逻辑删除用户 -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE user
        SET del_flag = null, update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <!-- 查询所有未删除的用户列表 -->
    <select id="selectList" resultType="User">
        SELECT *
        FROM user
        WHERE del_flag = 0
        <if test="isAdmin != null">
            AND is_admin = #{isAdmin, jdbcType=BIT}
        </if>
        ORDER BY create_time DESC
    </select>
    
    <!-- 校验用户名是否已存在（用于注册、唯一性校验） -->
    <select id="existsByUsername" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT 
            COUNT(1)
        FROM 
            user
        WHERE 
            username = #{username}
            AND del_flag = 0
    </select>
    
    <!-- 根据员工ID查询用户 -->
    <select id="selectByEmployeeId" resultType="User" parameterType="java.lang.Integer">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            user
        WHERE 
            employee_id = #{employeeId}
            AND del_flag = 0
    </select>
    
    <!-- 根据条件分页查询用户列表 -->
    <select id="selectListByConditions" resultType="User" parameterType="cn.edu.ncu.hrms.system.dto.UserPageQueryDTO">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            user
        <where>
            del_flag = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="isAdmin != null">
                AND is_admin = #{isAdmin}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE user
        SET 
            password = #{password},
            update_time = NOW()
        WHERE 
            id = #{id}
    </update>

    <!-- 更新用户的逻辑删除标志 -->
    <update id="updateDelFlag">
        UPDATE user
        SET del_flag = #{delFlag},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!--根据用户名查询用户-->
    <select id="selectByUsernameIgnoreDelFlag" resultType="cn.edu.ncu.hrms.common.entity.User">
        SELECT
        <include refid="Base_Column_List" />
        FROM user
        WHERE username = #{username}
    </select>
</mapper>