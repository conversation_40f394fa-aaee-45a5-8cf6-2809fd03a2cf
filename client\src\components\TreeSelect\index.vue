<template>
  <div class="tree-select">
    <el-popover
      ref="popover"
      placement="bottom-start"
      trigger="click"
      :width="width"
      @show="onShowPopover"
      @hide="onHidePopover"
    >
      <el-input
        v-if="filterable"
        v-model="filterText"
        placeholder="请输入关键字进行过滤"
        size="small"
        prefix-icon="el-icon-search"
        clearable
      />
      <el-tree
        ref="tree"
        :data="data"
        :props="props"
        :node-key="nodeKey"
        :default-expanded-keys="defaultExpandedKeys"
        :show-checkbox="multiple"
        :check-strictly="checkStrictly"
        :default-checked-keys="defaultCheckedKeys"
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
        @check="handleCheck"
      />
      <el-input
        slot="reference"
        v-model="selectedLabel"
        :placeholder="placeholder"
        :size="size"
        :disabled="disabled"
        :clearable="clearable"
        :readonly="true"
        @clear="handleClear"
      >
        <i slot="suffix" :class="suffixIcon" class="el-input__icon" />
      </el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    // 绑定值
    value: {
      type: [String, Number, Array],
      default: ''
    },
    // 数据源
    data: {
      type: Array,
      default: () => []
    },
    // 配置项
    props: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value'
      })
    },
    // 唯一标识
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 默认展开的节点
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    // 默认选中的节点
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 在多选模式下，是否严格遵循父子不互相关联的做法
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 尺寸
    size: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 后缀图标
    suffixIcon: {
      type: String,
      default: 'el-icon-arrow-down'
    },
    // 宽度
    width: {
      type: Number,
      default: 220
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedValue: this.value,
      selectedLabel: '',
      filterText: ''
    }
  },
  watch: {
    value(val) {
      this.selectedValue = val
      this.setSelectedLabel()
    },
    selectedValue(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.setSelectedLabel()
  },
  methods: {
    // 设置选中项的标签
    setSelectedLabel() {
      if (!this.selectedValue) {
        this.selectedLabel = ''
        return
      }
      
      const findLabel = (data, value) => {
        let label = ''
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          if (node[this.nodeKey] === value) {
            label = node[this.props.label]
            break
          }
          if (node[this.props.children] && node[this.props.children].length) {
            const childLabel = findLabel(node[this.props.children], value)
            if (childLabel) {
              label = childLabel
              break
            }
          }
        }
        return label
      }
      
      if (this.multiple && Array.isArray(this.selectedValue)) {
        const labels = []
        this.selectedValue.forEach(value => {
          const label = findLabel(this.data, value)
          if (label) {
            labels.push(label)
          }
        })
        this.selectedLabel = labels.join(', ')
      } else {
        this.selectedLabel = findLabel(this.data, this.selectedValue)
      }
    },
    // 处理节点点击
    handleNodeClick(node) {
      if (this.multiple) return
      this.selectedValue = node[this.nodeKey]
      this.selectedLabel = node[this.props.label]
      this.$refs.popover.doClose()
    },
    // 处理复选框选中
    handleCheck(node, data) {
      if (!this.multiple) return
      this.selectedValue = data.checkedKeys
      this.setSelectedLabel()
    },
    // 处理清空
    handleClear() {
      this.selectedValue = this.multiple ? [] : ''
      this.selectedLabel = ''
      this.$refs.tree.setCheckedKeys([])
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data[this.props.label].indexOf(value) !== -1
    },
    // 显示弹出层
    onShowPopover() {
      this.$nextTick(() => {
        if (this.multiple) {
          if (Array.isArray(this.selectedValue)) {
            this.$refs.tree.setCheckedKeys(this.selectedValue)
          }
        } else {
          if (this.selectedValue) {
            const node = this.$refs.tree.getNode(this.selectedValue)
            if (node) {
              this.$refs.tree.setCurrentKey(this.selectedValue)
            }
          }
        }
      })
    },
    // 隐藏弹出层
    onHidePopover() {
      this.filterText = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-select {
  width: 100%;
}
</style>
