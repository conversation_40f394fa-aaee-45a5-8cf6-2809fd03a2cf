package cn.edu.ncu.hrms.system.controller;

import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.service.SystemUserService;
import cn.edu.ncu.hrms.system.vo.UserVO;
import cn.edu.ncu.hrms.system.vo.RoleVO;
import cn.edu.ncu.hrms.system.dto.UserPageQueryDTO;
import cn.edu.ncu.hrms.system.dto.UserAddDTO;
import cn.edu.ncu.hrms.system.dto.UserUpdateDTO;
import cn.edu.ncu.hrms.system.dto.UserPasswordResetDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统用户管理控制器
 */
@RestController
@RequestMapping("/system/user")
@Api(tags = "系统用户管理接口")
public class SystemUserController {

    @Autowired
    private SystemUserService systemUserService;

    /**
     * 获取用户列表 (分页)
     */
    @GetMapping("/list")
    @ApiOperation("获取用户列表 (分页)")
    @PreAuthorize("@hrms.hasAuthority('system:user:list')")
    public Result<PageResult<UserVO>> getUserList(@Valid UserPageQueryDTO queryDTO) {
        PageInfo<UserVO> pageInfo = systemUserService.getUserList(queryDTO);
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }

    /**
     * 根据ID获取用户信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取用户信息")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:user:query')")
    public Result<UserVO> getUserById(@PathVariable Integer id) {
        return systemUserService.getUserVOById(id);
    }

    /**
     * 创建用户
     */
    @PostMapping
    @ApiOperation("创建用户")
    @PreAuthorize("@hrms.hasAuthority('system:user:add')")
    public Result<Void> createUser(@Valid @RequestBody UserAddDTO addDTO) {
        return systemUserService.createUser(addDTO);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新用户信息")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:user:edit')")
    public Result<Void> updateUser(@PathVariable Integer id, @Valid @RequestBody UserUpdateDTO updateDTO) {
        return systemUserService.updateUser(id, updateDTO);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除用户")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:user:delete')")
    public Result<Void> deleteUser(@PathVariable Integer id) {
        return systemUserService.deleteUser(id);
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/reset-password/{id}")
    @ApiOperation("重置用户密码")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:user:resetpwd')")
    public Result<Void> resetPassword(@PathVariable Integer id, @Valid @RequestBody UserPasswordResetDTO resetDTO) {
        return systemUserService.resetPassword(id, resetDTO);
    }

    /**
     * 获取用户的角色列表
     */
    @GetMapping("/{id}/roles")
    @ApiOperation("获取用户的角色列表")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:user:query')")
    public Result<List<RoleVO>> getUserRoles(@PathVariable Integer id) {
        return systemUserService.getUserRoles(id);
    }

    /**
     * 为用户分配角色
     */
    @PostMapping("/{id}/roles")
    @ApiOperation("为用户分配角色")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:user:edit')")
    public Result<Void> assignRolesToUser(@PathVariable Integer id, @RequestBody List<Integer> roleIds) {
        return systemUserService.assignRolesToUser(id, roleIds);
    }
} 