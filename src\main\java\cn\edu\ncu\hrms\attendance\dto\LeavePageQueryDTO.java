package cn.edu.ncu.hrms.attendance.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@ApiModel(description = "请假申请分页查询数据传输对象")
public class LeavePageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "查询者（Leader或HR）用户ID")
    private Integer currentEmployeeId;

    @ApiModelProperty(value = "申请请假的员工工号")
    private Integer workNo;

    @ApiModelProperty(value = "请假类型")
    private String leaveType;

    @ApiModelProperty(value = "审批状态 (例如：PENDING, APPROVED, REJECTED, CANCELLED)")
    private String status;

    @ApiModelProperty(value = "查询范围 - 开始日期", example = "2025-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate queryStartDate;

    @ApiModelProperty(value = "查询范围 - 结束日期", example = "2025-01-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate queryEndDate;

} 