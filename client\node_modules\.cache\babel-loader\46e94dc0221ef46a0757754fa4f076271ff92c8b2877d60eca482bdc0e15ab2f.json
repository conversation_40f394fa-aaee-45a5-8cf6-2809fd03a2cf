{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"firefox-test\"\n  }, [_c(\"el-card\", [_c(\"div\", {\n    attrs: {\n      slot: \"header\"\n    },\n    slot: \"header\"\n  }, [_c(\"span\", [_vm._v(\"Firefox兼容性测试页面\")])]), _c(\"div\", {\n    staticClass: \"test-info\"\n  }, [_c(\"p\", [_c(\"strong\", [_vm._v(\"浏览器信息：\")]), _vm._v(_vm._s(_vm.browserInfo))]), _c(\"p\", [_c(\"strong\", [_vm._v(\"是否为Firefox：\")]), _vm._v(_vm._s(_vm.isFirefoxBrowser ? \"是\" : \"否\"))]), _c(\"p\", [_c(\"strong\", [_vm._v(\"高德地图API状态：\")]), _vm._v(_vm._s(_vm.amapStatus))])]), _c(\"div\", {\n    staticClass: \"test-actions\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.testAmapAPI\n    }\n  }, [_vm._v(\"测试高德地图API\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"success\"\n    },\n    on: {\n      click: _vm.testMapInit\n    }\n  }, [_vm._v(\"测试地图初始化\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"warning\"\n    },\n    on: {\n      click: _vm.clearConsole\n    }\n  }, [_vm._v(\"清空控制台\")])], 1), _vm.testResults.length > 0 ? _c(\"div\", {\n    staticClass: \"test-results\"\n  }, [_c(\"h4\", [_vm._v(\"测试结果：\")]), _c(\"ul\", _vm._l(_vm.testResults, function (result, index) {\n    return _c(\"li\", {\n      key: index,\n      class: result.type\n    }, [_c(\"strong\", [_vm._v(_vm._s(result.time) + \":\")]), _vm._v(\" \" + _vm._s(result.message) + \" \")]);\n  }), 0)]) : _vm._e(), _vm.showMap ? _c(\"div\", {\n    staticClass: \"map-container\"\n  }, [_c(\"h4\", [_vm._v(\"地图测试区域：\")]), _c(\"div\", {\n    staticStyle: {\n      width: \"100%\",\n      height: \"300px\",\n      border: \"1px solid #ddd\"\n    },\n    attrs: {\n      id: \"testMapContainer\"\n    }\n  })]) : _vm._e()])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "slot", "_v", "_s", "browserInfo", "isFirefoxBrowser", "amapStatus", "type", "on", "click", "testAmapAPI", "testMapInit", "clearConsole", "testResults", "length", "_l", "result", "index", "key", "class", "time", "message", "_e", "showMap", "staticStyle", "width", "height", "border", "id", "staticRenderFns", "_withStripped"], "sources": ["D:/Desktop/GP/HRMS/client/src/views/test/firefox-test.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"firefox-test\" },\n    [\n      _c(\"el-card\", [\n        _c(\"div\", { attrs: { slot: \"header\" }, slot: \"header\" }, [\n          _c(\"span\", [_vm._v(\"Firefox兼容性测试页面\")]),\n        ]),\n        _c(\"div\", { staticClass: \"test-info\" }, [\n          _c(\"p\", [\n            _c(\"strong\", [_vm._v(\"浏览器信息：\")]),\n            _vm._v(_vm._s(_vm.browserInfo)),\n          ]),\n          _c(\"p\", [\n            _c(\"strong\", [_vm._v(\"是否为Firefox：\")]),\n            _vm._v(_vm._s(_vm.isFirefoxBrowser ? \"是\" : \"否\")),\n          ]),\n          _c(\"p\", [\n            _c(\"strong\", [_vm._v(\"高德地图API状态：\")]),\n            _vm._v(_vm._s(_vm.amapStatus)),\n          ]),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"test-actions\" },\n          [\n            _c(\n              \"el-button\",\n              { attrs: { type: \"primary\" }, on: { click: _vm.testAmapAPI } },\n              [_vm._v(\"测试高德地图API\")]\n            ),\n            _c(\n              \"el-button\",\n              { attrs: { type: \"success\" }, on: { click: _vm.testMapInit } },\n              [_vm._v(\"测试地图初始化\")]\n            ),\n            _c(\n              \"el-button\",\n              { attrs: { type: \"warning\" }, on: { click: _vm.clearConsole } },\n              [_vm._v(\"清空控制台\")]\n            ),\n          ],\n          1\n        ),\n        _vm.testResults.length > 0\n          ? _c(\"div\", { staticClass: \"test-results\" }, [\n              _c(\"h4\", [_vm._v(\"测试结果：\")]),\n              _c(\n                \"ul\",\n                _vm._l(_vm.testResults, function (result, index) {\n                  return _c(\"li\", { key: index, class: result.type }, [\n                    _c(\"strong\", [_vm._v(_vm._s(result.time) + \":\")]),\n                    _vm._v(\" \" + _vm._s(result.message) + \" \"),\n                  ])\n                }),\n                0\n              ),\n            ])\n          : _vm._e(),\n        _vm.showMap\n          ? _c(\"div\", { staticClass: \"map-container\" }, [\n              _c(\"h4\", [_vm._v(\"地图测试区域：\")]),\n              _c(\"div\", {\n                staticStyle: {\n                  width: \"100%\",\n                  height: \"300px\",\n                  border: \"1px solid #ddd\",\n                },\n                attrs: { id: \"testMapContainer\" },\n              }),\n            ])\n          : _vm._e(),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,SAAS,EAAE,CACZA,EAAE,CAAC,KAAK,EAAE;IAAEG,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAS,CAAC;IAAEA,IAAI,EAAE;EAAS,CAAC,EAAE,CACvDJ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CACvC,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAChCN,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,WAAW,CAAC,CAAC,CAChC,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EACrCN,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACS,gBAAgB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CACjD,CAAC,EACFR,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EACpCN,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACU,UAAU,CAAC,CAAC,CAC/B,CAAC,CACH,CAAC,EACFT,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;IAAEG,KAAK,EAAE;MAAEO,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACc;IAAY;EAAE,CAAC,EAC9D,CAACd,GAAG,CAACM,EAAE,CAAC,WAAW,CAAC,CACtB,CAAC,EACDL,EAAE,CACA,WAAW,EACX;IAAEG,KAAK,EAAE;MAAEO,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACe;IAAY;EAAE,CAAC,EAC9D,CAACf,GAAG,CAACM,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDL,EAAE,CACA,WAAW,EACX;IAAEG,KAAK,EAAE;MAAEO,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACgB;IAAa;EAAE,CAAC,EAC/D,CAAChB,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,CACF,EACD,CACF,CAAC,EACDN,GAAG,CAACiB,WAAW,CAACC,MAAM,GAAG,CAAC,GACtBjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BL,EAAE,CACA,IAAI,EACJD,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACiB,WAAW,EAAE,UAAUG,MAAM,EAAEC,KAAK,EAAE;IAC/C,OAAOpB,EAAE,CAAC,IAAI,EAAE;MAAEqB,GAAG,EAAED,KAAK;MAAEE,KAAK,EAAEH,MAAM,CAACT;IAAK,CAAC,EAAE,CAClDV,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACa,MAAM,CAACI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EACjDxB,GAAG,CAACM,EAAE,CAAC,GAAG,GAAGN,GAAG,CAACO,EAAE,CAACa,MAAM,CAACK,OAAO,CAAC,GAAG,GAAG,CAAC,CAC3C,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,GACFzB,GAAG,CAAC0B,EAAE,CAAC,CAAC,EACZ1B,GAAG,CAAC2B,OAAO,GACP1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC7BL,EAAE,CAAC,KAAK,EAAE;IACR2B,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MAAE4B,EAAE,EAAE;IAAmB;EAClC,CAAC,CAAC,CACH,CAAC,GACFhC,GAAG,CAAC0B,EAAE,CAAC,CAAC,CACb,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIO,eAAe,GAAG,EAAE;AACxBlC,MAAM,CAACmC,aAAa,GAAG,IAAI;AAE3B,SAASnC,MAAM,EAAEkC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}