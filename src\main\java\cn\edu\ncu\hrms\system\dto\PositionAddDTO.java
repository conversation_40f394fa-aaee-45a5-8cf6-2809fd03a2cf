package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 创建职位数据传输对象 (DTO)
 */
@Data
@ApiModel(value="PositionAddDTO", description="创建职位数据传输对象")
public class PositionAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "职位名称", required = true)
    @NotBlank(message = "职位名称不能为空")
    @Size(max = 50, message = "职位名称长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "职级", required = true)
    @NotNull(message = "职级不能为空")
    @Min(value = 5, message = "职级不能小于5")
    @Max(value = 10, message = "职级不能大于10")
    private Byte level;

    @ApiModelProperty(value = "关联账套ID", required = true)
    @NotNull(message = "关联账套ID不能为空")
    private Integer accountId;

} 