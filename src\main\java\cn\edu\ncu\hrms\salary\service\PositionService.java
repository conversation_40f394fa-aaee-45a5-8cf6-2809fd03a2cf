package cn.edu.ncu.hrms.salary.service;

import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.salary.dto.PositionPageQueryDTO;
import cn.edu.ncu.hrms.salary.vo.PositionVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

public interface PositionService {

    /**
     * 根据ID获取职位信息
     *
     * @param id 职位ID
     * @return Result<Position> 包含职位信息的Result对象
     */
    Result<Position> getPositionById(Integer id);


    /**
     * 获取所有职位列表 (用于下拉选择等)
     *
     * @return Result<List<Position>> 包含职位列表的Result对象
     */
    Result<List<Position>> listAllPositions();

    /**
     * 获取所有职位名称列表 (用于下拉选择等)
     * @return
     */
    Result<List<String>> listAllPositionNames();


    /**
     * 条件查询职位分页列表
     * @param queryDTO
     * @return
     */
    PageInfo<PositionVO> getPositionPage(PositionPageQueryDTO queryDTO);


    /**
     * 为职位匹配账套
     * @param positionId
     * @param accountId
     * @return
     */
    Result<Void> matchingAccountSet(Integer positionId, Integer accountId);
}
