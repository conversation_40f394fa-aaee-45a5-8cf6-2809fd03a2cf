import request from '@/utils/request'

// 提交请假申请
export function applyLeave(data) {
  return request({
    url: '/attendance/leave/apply',
    method: 'post',
    data: data
  })
}

// 根据ID获取请假申请详情
export function getLeave(id) {
  return request({
    url: `/attendance/leave/${id}`,
    method: 'get'
  })
}

// 查询个人请假申请列表
export function listPersonalLeave(params) {
  return request({
    url: '/attendance/leave/self',
    method: 'get',
    params
  })
}

// 分页查询请假申请列表
export function listLeavePage(params) {
  return request({
    url: '/attendance/leave/page',
    method: 'get',
    params
  })
}

// 撤回请假申请
export function cancelLeave(id) {
  return request({
    url: `/attendance/leave/cancel/${id}`,
    method: 'put'
  })
}

// 审批请假申请
export function approveLeave(id, data) {
  return request({
    url: `/attendance/leave/approve/${id}`,
    method: 'put',
    data: data
  })
}
