package cn.edu.ncu.hrms.attendance.mapper;

import cn.edu.ncu.hrms.attendance.dto.LeavePageQueryDTO;
import cn.edu.ncu.hrms.common.entity.LeaveRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 请假申请Mapper接口
 */
@Mapper
public interface LeaveRequestMapper {
    
    /**
     * 根据ID查询请假申请
     *
     * @param id 请假申请ID
     * @return 请假申请信息
     */
    LeaveRequest selectById(Integer id);
    
    /**
     * 新增请假申请
     *
     * @param leaveRequest 请假申请信息
     * @return 影响行数
     */
    int insert(LeaveRequest leaveRequest);

    
    /**
     * 根据动态条件查询请假申请列表 (配合 PageHelper 使用)
     *
     * @param queryDTO 查询条件 DTO
     * @return 请假申请列表
     */
    List<LeaveRequest> selectByCondition(LeavePageQueryDTO queryDTO);


    /**
     * 更新指定ID的请假申请状态
     * @param id 请假申请ID
     * @param status 新的状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Integer id, @Param("status") String status);


    /**
     * 根据员工ID查询请假申请列表
     *
     * @param currentEmployeeId 员工ID
     * @return 请假申请列表
     */
    List<LeaveRequest> selectByEmployeeId(Integer currentEmployeeId);
}