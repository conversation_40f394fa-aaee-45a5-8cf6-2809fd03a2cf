<template>
  <div class="firefox-test">
    <el-card>
      <div slot="header">
        <span>Firefox兼容性测试页面</span>
      </div>
      
      <div class="test-info">
        <p><strong>浏览器信息：</strong>{{ browserInfo }}</p>
        <p><strong>是否为Firefox：</strong>{{ isFirefoxBrowser ? '是' : '否' }}</p>
        <p><strong>高德地图API状态：</strong>{{ amapStatus }}</p>
      </div>

      <div class="test-actions">
        <el-button type="primary" @click="testAmapAPI">测试高德地图API</el-button>
        <el-button type="success" @click="testMapInit">测试地图初始化</el-button>
        <el-button type="warning" @click="clearConsole">清空控制台</el-button>
      </div>

      <div class="test-results" v-if="testResults.length > 0">
        <h4>测试结果：</h4>
        <ul>
          <li v-for="(result, index) in testResults" :key="index" :class="result.type">
            <strong>{{ result.time }}:</strong> {{ result.message }}
          </li>
        </ul>
      </div>

      <!-- 地图容器 -->
      <div class="map-container" v-if="showMap">
        <h4>地图测试区域：</h4>
        <div id="testMapContainer" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { isFirefox, checkAmapAvailability } from '@/utils/firefox-compatibility'
import { initMap } from '@/utils/amap'

export default {
  name: 'FirefoxTest',
  data() {
    return {
      browserInfo: navigator.userAgent,
      isFirefoxBrowser: false,
      amapStatus: '检查中...',
      testResults: [],
      showMap: false
    }
  },
  mounted() {
    this.initTest()
  },
  methods: {
    async initTest() {
      // 检测浏览器
      this.isFirefoxBrowser = isFirefox()
      this.addTestResult('info', `浏览器检测完成，Firefox: ${this.isFirefoxBrowser}`)

      // 检查高德地图API状态
      try {
        const available = await checkAmapAvailability()
        this.amapStatus = available ? '可用' : '不可用'
        this.addTestResult(available ? 'success' : 'error', `高德地图API状态: ${this.amapStatus}`)
      } catch (error) {
        this.amapStatus = '检查失败'
        this.addTestResult('error', `高德地图API检查失败: ${error.message}`)
      }
    },

    testAmapAPI() {
      this.addTestResult('info', '开始测试高德地图API...')
      
      try {
        if (typeof window.AMap === 'undefined') {
          this.addTestResult('error', 'AMap对象不存在')
          return
        }

        // 测试基本API
        const version = window.AMap.version || '未知版本'
        this.addTestResult('success', `AMap版本: ${version}`)

        // 测试创建LngLat对象
        const lngLat = new window.AMap.LngLat(116.397428, 39.90923)
        this.addTestResult('success', `创建LngLat对象成功: ${lngLat.toString()}`)

        // 测试Geolocation
        if (window.AMap.Geolocation) {
          this.addTestResult('success', 'Geolocation插件可用')
        } else {
          this.addTestResult('warning', 'Geolocation插件不可用')
        }

      } catch (error) {
        this.addTestResult('error', `API测试失败: ${error.message}`)
      }
    },

    testMapInit() {
      this.addTestResult('info', '开始测试地图初始化...')
      
      try {
        // 显示地图容器
        this.showMap = true
        
        // 等待DOM更新
        this.$nextTick(() => {
          try {
            const map = initMap('testMapContainer', {
              zoom: 10,
              center: [116.397428, 39.90923]
            })
            
            if (map) {
              this.addTestResult('success', '地图初始化成功')
              
              // 测试地图方法
              setTimeout(() => {
                try {
                  const center = map.getCenter()
                  this.addTestResult('success', `地图中心点: ${center.toString()}`)
                  
                  const zoom = map.getZoom()
                  this.addTestResult('success', `地图缩放级别: ${zoom}`)
                } catch (error) {
                  this.addTestResult('error', `地图方法测试失败: ${error.message}`)
                }
              }, 1000)
            } else {
              this.addTestResult('error', '地图初始化返回null')
            }
          } catch (error) {
            this.addTestResult('error', `地图初始化失败: ${error.message}`)
          }
        })
      } catch (error) {
        this.addTestResult('error', `地图测试准备失败: ${error.message}`)
      }
    },

    addTestResult(type, message) {
      const time = new Date().toLocaleTimeString()
      this.testResults.push({
        type,
        time,
        message
      })
      
      // 同时输出到控制台
      const logMethod = type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'log'
      console[logMethod](`[Firefox测试] ${time}: ${message}`)
    },

    clearConsole() {
      console.clear()
      this.testResults = []
      this.addTestResult('info', '控制台已清空')
    }
  }
}
</script>

<style scoped>
.firefox-test {
  padding: 20px;
}

.test-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.test-info p {
  margin: 5px 0;
}

.test-actions {
  margin-bottom: 20px;
}

.test-actions .el-button {
  margin-right: 10px;
}

.test-results {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.test-results ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.test-results li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.test-results li:last-child {
  border-bottom: none;
}

.test-results li.success {
  color: #67c23a;
}

.test-results li.error {
  color: #f56c6c;
}

.test-results li.warning {
  color: #e6a23c;
}

.test-results li.info {
  color: #409eff;
}

.map-container {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
