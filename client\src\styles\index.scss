@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// 主容器全局样式
.app-container {
  padding: 20px;
}

// 定义一些尺寸的样式
.wp50 {
  width: 50%
}

// 表单样式
.form-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .form-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;

    h3 {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }

  .form-footer {
    margin-top: 20px;
    text-align: center;
  }
}

// 表格样式
.table-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left {
      h3 {
        font-size: 18px;
        font-weight: 500;
        color: #333;
      }
    }

    .right {
      display: flex;
      align-items: center;

      .search-input {
        width: 200px;
        margin-right: 10px;
      }
    }
  }

  .table-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

// 卡片样式
.card-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;

  .card-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
}

// 通用卡片标题样式
.el-card__header {
  position: relative;

  h3 {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    padding: 0;
    line-height: 24px;

    i {
      margin-right: 5px;
      // color: #409EFF;
    }
  }

  // 标题右侧按钮容器
  .header-buttons {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
  }
}
