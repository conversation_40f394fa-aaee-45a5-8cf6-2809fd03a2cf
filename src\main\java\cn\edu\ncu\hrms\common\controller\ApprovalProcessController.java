package cn.edu.ncu.hrms.common.controller;


import cn.edu.ncu.hrms.attendance.vo.ApprovalProcessVO;
import cn.edu.ncu.hrms.common.service.ApprovalProcessService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/approval-process")
@Api(tags = "审批流程接口")
public class ApprovalProcessController {

    @Autowired
    private ApprovalProcessService approvalProcessService;

    @GetMapping("/{type}/{requestId}")
    public List<ApprovalProcessVO> getApprovalProcessVOList(@PathVariable String type, @PathVariable Integer requestId) {
        return approvalProcessService.getApprovalProcessListByRequestId(requestId, type);
    }

}
