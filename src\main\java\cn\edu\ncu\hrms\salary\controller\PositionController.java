package cn.edu.ncu.hrms.salary.controller;

import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.salary.service.PositionService;
import cn.edu.ncu.hrms.salary.dto.PositionPageQueryDTO;
import cn.edu.ncu.hrms.salary.vo.PositionVO;
import cn.edu.ncu.hrms.system.dto.PositionAddDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 职位控制器 (salary 模块)
 */
@RestController
@RequestMapping("/salary/position")
@Api(tags = "职位信息接口")
public class PositionController {

    @Autowired
    private PositionService positionService;

    /**
     * 根据ID获取职位信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取职位信息")
    @PreAuthorize("@hrms.hasAuthority('salary:position:query')")
    public Result<Position> getPositionById(@PathVariable @ApiParam("职位ID") Integer id) {
        return positionService.getPositionById(id);
    }

    /**
     * 获取所有职位列表 (用于下拉选择等)
     */
    @GetMapping("/all")
    @ApiOperation("获取所有职位列表")
    @PreAuthorize("@hrms.hasAuthority('salary:position:list')")
    public Result<List<Position>> listAllPositions() {
        return positionService.listAllPositions();
    }

    /**
     * 获取所有职位名称
     * @return
     */
    @GetMapping("/name")
    @ApiOperation("获取所有职位名称")
    @PreAuthorize("@hrms.hasAuthority('salary:position:list')")
    public Result<List<String>> listAllPositionNames() {
        return positionService.listAllPositionNames();
    }

    /**
     * 分页获取获取职位与账套匹配列表
     * @param queryDTO
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("获取职位与账套匹配列表")
    @PreAuthorize("@hrms.hasAuthority('salary:position:list')")
    public Result<PageResult<PositionVO>> getPositionPage(PositionPageQueryDTO queryDTO) {
        PageInfo<PositionVO> positionPage = positionService.getPositionPage(queryDTO);
        return PageResult.success(positionPage.getTotal(), positionPage.getList());
    }

    /**
     * 为职位匹配账套
     */
    @PutMapping("/{positionId}/{accountId}")
    @ApiOperation("匹配账套")
    @PreAuthorize("@hrms.hasAuthority('salary:position:edit')")
    public Result<Void> matchingAccountSet(@PathVariable Integer positionId, @PathVariable Integer accountId) {
        return positionService.matchingAccountSet(positionId, accountId);
    }

}
