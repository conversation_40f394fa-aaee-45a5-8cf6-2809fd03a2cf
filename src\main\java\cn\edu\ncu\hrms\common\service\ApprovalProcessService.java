package cn.edu.ncu.hrms.common.service;

import cn.edu.ncu.hrms.attendance.vo.ApprovalProcessVO;
import cn.edu.ncu.hrms.common.entity.LeaveRequest;
import cn.edu.ncu.hrms.common.entity.ApprovalProcess;
import java.util.List;

/**
 * 审批流程服务接口，定义审批推进、审批流查询等方法
 */
public interface ApprovalProcessService {

    /**
     * 审批人对某审批节点进行审批操作
     * @param processId 审批节点ID
     * @param approverId 审批人ID
     * @param action 审批动作（APPROVED/REJECTED）
     * @param comment 审批意见
     * @param requestType 申请类型（请假/离职）
     * @return 是否审批成功
     */
    boolean approve(Integer processId, Integer approverId, String action, String comment, String requestType);

    /**
     * 查询某申请的全部审批流程节点
     * @param requestId 申请ID（不同类型的申请ID可能相同，需要type区分）
     * @param type 申请类型（RESIGN/LEAVE）
     * @return 审批流程节点列表
     */
    List<ApprovalProcessVO> getApprovalProcessListByRequestId(Integer requestId, String type);


    /**
     * 取消审批流程
     * @param requestId
     */
    Boolean cancelApprovalProcessByRequestId(Integer requestId,  String type);

    /**
     * 发送审批通知邮件给指定的审批人。
     *
     * 1. 根据审批人ID查询员工信息以获取邮箱。
     * 2. 如果找到邮箱，调用 EmailService 发送邮件。
     * 3. 处理可能的异常，邮件发送失败不应阻塞主流程，仅记录错误日志。
     *
     * @param approverId 待通知的审批人员工 ID
     * @param requestId  相关的请假申请 ID
     */
    void sendApprovalNotification(Integer approverId, Integer requestId, String requestType);
}
