package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审批流程")
public class ApprovalProcess {
    @ApiModelProperty(value = "审批步骤ID", required = true, example = "1101")
    private Integer id;

    @ApiModelProperty(value = "申请ID", required = true, example = "901")
    private Integer requestId;

    @ApiModelProperty(value = "申请类型", allowableValues = "LEAVE,RESIGN", example = "LEAVE")
    private String type;

    @ApiModelProperty(value = "审批人ID", required = true, example = "1002")
    private Integer approverId;

    @ApiModelProperty(value = "审批顺序", required = true, example = "1")
    private Byte stepOrder;

    @ApiModelProperty(value = "审批状态", allowableValues = "NEW,PENDING,APPROVED,REJECTED,AUTO_REJECTED,TERMINATE,CANCELED", example = "PENDING")
    private String status;

    @ApiModelProperty(value = "审批意见", example = "同意申请")
    private String comment;

    @ApiModelProperty(value = "审批时间", example = "2025-01-15 10:00:00")
    private LocalDateTime approveTime;

    @ApiModelProperty(value = "审批截止时间", required = true, example = "2025-01-18 23:59:59")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
