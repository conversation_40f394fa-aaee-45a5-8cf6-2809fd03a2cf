package cn.edu.ncu.hrms.common.constant;

/**
 * 考勤模块常量
 */
public class AttendanceConstant {
    /**
     * 打卡类型
     */
    public static final String CLOCK_IN = "IN";
    public static final String CLOCK_OUT = "OUT";

    /**
     * 考勤状态
     */
    public static final String CLOCK_NORMAL = "NORMAL";   // 正常
    public static final String CLOCK_LATE = "LATE";       // 迟到
    public static final String CLOCK_EARLY = "EARLY";     // 早退
    public static final String CLOCK_ABSENT = "ABSENT";   // 缺勤
    public static final String CLOCK_LEAVE = "LEAVE";     // 请假

    /**
     * 申请状态
     */
    public static final String STATUS_NEW = "NEW";              // 未开始
    public static final String STATUS_PENDING = "PENDING";      // 待审批
    public static final String STATUS_APPROVED = "APPROVED";    // 已通过
    public static final String STATUS_REJECTED = "REJECTED";    // 已驳回
    public static final String AUTO_REJECTED = "AUTO_REJECTED"; // 自动驳回
    public static final String STATUS_TERMINATE = "TERMINATE";  // 已终止
    public static final String STATUS_CANCELED = "CANCELED";    // 已撤回

    /**
     * 获取状态名称
     */
    public static String getStatusName(String status) {
        if (status == null) return "未知";
        switch (status) {
            case STATUS_PENDING:
                return "待审批";
            case STATUS_APPROVED:
                return "已批准";
            case STATUS_REJECTED:
                return "已驳回";
            case STATUS_CANCELED:
                return "已撤回";
            default:
                return status;
        }
    }

    /**
     * 申请类型
     */
    public static final String REQUEST_TYPE_LEAVE = "LEAVE";    // 请假
    public static final String REQUEST_TYPE_RESIGN = "RESIGN";   // 离职

    /**
     * 获取申请名称
     */
    public static String getTypeName(String status) {
        if (status == null) return null;
        switch (status) {
            case REQUEST_TYPE_LEAVE:
                return "请假";
            case REQUEST_TYPE_RESIGN:
                return "离职";
            default:
                return "未知";
        }
    }

    /**
     * 请假类型
     */
    public static final String LEAVE_TYPE_ANNUAL = "ANNUAL";         // 年假
    public static final String LEAVE_TYPE_SICK = "SICK";             // 病假
    public static final String LEAVE_TYPE_PERSONAL = "PERSONAL";     // 事假
    public static final String LEAVE_TYPE_MARRIAGE = "MARRIAGE";     // 婚假
    public static final String LEAVE_TYPE_MATERNITY = "MATERNITY";   // 产假
    public static final String LEAVE_TYPE_OTHER = "OTHER";           // 其他
    
    /**
     * 工作时间配置(单位:小时)
     */
    public static final String WORK_START_TIME = "09:00:00";  // 上班时间
    public static final String WORK_END_TIME = "18:00:00";    // 下班时间
    public static final String LUNCH_START_TIME = "12:00:00"; // 午休开始时间
    public static final String LUNCH_END_TIME = "13:00:00";   // 午休结束时间
    
    /**
     * 考勤规则
     */
    public static final int LATE_THRESHOLD_MINUTES = 15;      // 迟到阈值(分钟)
    public static final int EARLY_THRESHOLD_MINUTES = 15;     // 早退阈值(分钟)
    public static final double LATE_DEDUCTION = 50.0;         // 迟到扣款(元)
    public static final double EARLY_DEDUCTION = 50.0;        // 早退扣款(元)
    public static final double ABSENT_DEDUCTION = 200.0;      // 缺勤扣款(元)
    
    /**
     * 业务类型 - 用于系统通知
     */
    public static final Integer BUSINESS_TYPE_ATTENDANCE = 2;    // 考勤打卡
    public static final Integer BUSINESS_TYPE_LEAVE = 3;         // 请假申请

} 