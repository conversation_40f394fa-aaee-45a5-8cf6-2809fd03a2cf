package cn.edu.ncu.hrms.attendance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "部门考勤配置传输对象")
public class AttendanceConfigDTO {
    @ApiModelProperty(value = "部门ID", required = true, example = "201")
    private Integer departmentId;

    @ApiModelProperty(value = "上班打卡开始时间", example = "08:00")
    private String clockInStartTime;

    @ApiModelProperty(value = "上班打卡结束时间", example = "09:00")
    private String clockInEndTime;

    @ApiModelProperty(value = "迟到阈值（分钟）", example = "30")
    private Integer lateThreshold;

    @ApiModelProperty(value = "迟到扣款（元）", example = "50.00")
    private BigDecimal lateDeduction;

    @ApiModelProperty(value = "下班打卡开始时间", example = "17:00")
    private String clockOutStartTime;

    @ApiModelProperty(value = "下班打卡结束时间", example = "18:00")
    private String clockOutEndTime;

    @ApiModelProperty(value = "早退阈值（分钟）", example = "30")
    private Integer earlyThreshold;

    @ApiModelProperty(value = "早退扣款（元）", example = "50.00")
    private BigDecimal earlyDeduction;

    @ApiModelProperty(value = "打卡地点（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockLocation;

    @ApiModelProperty(value = "打卡范围（米）", example = "100")
    private Integer clockRadius;
}
