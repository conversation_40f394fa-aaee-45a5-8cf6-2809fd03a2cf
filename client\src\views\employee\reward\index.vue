<template>
  <div class="app-container">
    <div class="page-header">
      <h2>奖惩管理</h2>
      <div class="header-actions">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="self">个人奖惩</el-radio-button>
          <el-radio-button label="subordinates" v-if="$store.getters['userInfo/roles'].includes('ROLE_hr') || $store.getters['userInfo/roles'].includes('ROLE_leader')">员工奖惩</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 个人奖惩记录 -->
    <div v-if="viewMode === 'self'" class="profile-container">

      <el-card class="profile-card" shadow="hover">
        <div slot="header" class="card-header-self">
          <h3>
          <span><i class="el-icon-trophy"></i> 我的奖惩记录</span>
          </h3>
        </div>
        <el-table
          v-loading="loading"
          :data="personalRewards"
          border
          stripe
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="日期" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.rewardDate">{{ scope.row.rewardDate }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" min-width="80" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.type === 0 ? 'success' : 'danger'" effect="plain">
                {{ scope.row.type === 0 ? '奖励' : '惩罚' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="标题" min-width="150" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.title">{{ scope.row.title }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="部门" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column> -->
          <el-table-column label="金额" min-width="100" align="center">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.type === 0 ? '#67c23a' : '#f56c6c' }">
                {{ scope.row.amount ? `¥${scope.row.amount.toFixed(2)}` : '无' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.operatorName">{{ scope.row.operatorName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  circle
                  @click="handleViewReward(scope.row)"
                  title="查看详情"
                ></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSelfSizeChange"
            @current-change="handleSelfCurrentChange"
            :current-page="selfCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="selfPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="selfTotal"
          />
        </div>
      </el-card>
    </div>

    <!-- 员工奖惩管理 -->
    <div v-else-if="viewMode === 'subordinates'" class="subordinates-container">
      <el-card class="table-card" shadow="hover">
        <div slot="header" class="card-header">
          <h3>
            <i class="el-icon-trophy"></i>
            <!-- 获取用户角色信息 -->
            <span v-if="$store.getters['userInfo/roles'].includes('ROLE_hr')">关联员工奖惩</span>
            <!-- 只有当用户没有HR角色但有Leader角色时才显示Leader标题 -->
            <span v-else-if="$store.getters['userInfo/roles'].includes('ROLE_leader')">部门员工奖惩</span>
            <span v-else>员工奖惩</span>

            <!-- 操作按钮 -->
            <div class="header-buttons">
              <el-button-group>
                <el-button type="success" size="medium" icon="el-icon-plus" @click="handleAddReward">新增奖惩</el-button>
              </el-button-group>
            </div>
          </h3>
        </div>

        <div class="filter-section">
          <div class="filter-form">
            <el-form :inline="true" :model="filterForm" class="demo-form-inline">
              <el-form-item label="员工姓名">
                <el-input
                  v-model="searchQuery"
                  placeholder="请输入姓名"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 170px;"
                  @keyup.enter.native="handleSearch"
                />
              </el-form-item>
              <el-form-item label="工号">
                <el-input
                  v-model="workNoQuery"
                  placeholder="请输入工号"
                  clearable
                  prefix-icon="el-icon-document"
                  style="width: 170px;"
                  @keyup.enter.native="handleSearch"
                />
              </el-form-item>
              <el-form-item label="奖惩类型">
                <el-select v-model="typeFilter" placeholder="请选择类型" clearable style="width: 130px;">
                  <el-option label="奖励" value="REWARD" />
                  <el-option label="惩罚" value="PUNISHMENT" />
                </el-select>
              </el-form-item>
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 250px;"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetFilter">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="subordinatesRewards"
          border
          stripe
          highlight-current-row
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column label="工号" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.workNo">{{ scope.row.workNo }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.employeeName">{{ scope.row.employeeName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="部门" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="日期" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.rewardDate">{{ scope.row.rewardDate }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" min-width="80" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.type === 0 ? 'success' : 'danger'" effect="plain">
                {{ scope.row.type === 0 ? '奖励' : '惩罚' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="标题" min-width="150" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.title">{{ scope.row.title }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="金额" min-width="100" align="center">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.type === 0 ? '#67c23a' : '#f56c6c' }">
                {{ scope.row.amount ? `¥${scope.row.amount.toFixed(2)}` : '无' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="150" align="center">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  circle
                  @click="handleViewReward(scope.row)"
                  title="查看详情"
                ></el-button>
                <el-button
                  size="mini"
                  type="warning"
                  icon="el-icon-edit"
                  circle
                  @click="handleEditReward(scope.row)"
                  title="编辑"
                ></el-button>
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="handleDeleteReward(scope.row)"
                  title="删除"
                ></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleSubSizeChange"
            @current-change="handleSubCurrentChange"
            :current-page="subCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="subPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="subTotal"
          />
        </div>
      </el-card>
    </div>

    <!-- 奖惩编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false">
      <el-form ref="rewardForm" :model="rewardForm" :rules="rules" label-width="100px">
        <el-form-item label="员工" prop="employeeId">
          <el-select
            v-model="rewardForm.employeeId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入员工姓名或工号搜索"
            style="width: 100%;"
            :remote-method="remoteSearchEmployee"
            :loading="employeeSelectLoading"
            :disabled="!isAddMode"
          >
            <el-option
              v-for="item in filteredEmployeeOptions"
              :key="item.id"
              :label="`${item.name} (${item.workNo})`"
              :value="item.id"
            >
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.workNo }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="rewardForm.type">
            <el-radio :label="0">奖励</el-radio>
            <el-radio :label="1">惩罚</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="日期" prop="rewardDate">
          <el-date-picker
            v-model="rewardForm.rewardDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%;"
            value-format="yyyy-MM-dd"
            :picker-options="datePickerOptions"
          />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="rewardForm.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            type="textarea"
            v-model="rewardForm.content"
            placeholder="请输入内容"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input-number
            v-model="rewardForm.amount"
            :min="0"
            :max="10000"
            :precision="2"
            :step="100"
            style="width: 100%;"
          />
          <div class="form-tip">金额不能超过10000元</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="rewardForm.remark"
            placeholder="请输入备注"
            :rows="2"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 奖惩详情对话框 -->
    <el-dialog title="奖惩详情" :visible.sync="detailVisible" width="700px" :close-on-click-modal="true">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ detailForm.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="员工工号">{{ detailForm.workNo }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ detailForm.departmentName }}</el-descriptions-item>
        <el-descriptions-item label="奖惩类型">
          <el-tag :type="detailForm.type === 0 ? 'success' : 'danger'" effect="plain">
            {{ detailForm.type === 0 ? '奖励' : '惩罚' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="奖惩日期">{{ detailForm.rewardDate }}</el-descriptions-item>
        <el-descriptions-item label="奖惩金额">
          <span :style="{ color: detailForm.type === 0 ? '#67c23a' : '#f56c6c' }">
            {{ detailForm.amount ? `¥${detailForm.amount.toFixed(2)}` : '无' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="奖惩标题" :span="2">{{ detailForm.title }}</el-descriptions-item>
        <el-descriptions-item label="奖惩内容" :span="2">{{ detailForm.content }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark || '无' }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ detailForm.operatorName || '无' }}</el-descriptions-item>
        <el-descriptions-item label="操作人工号">{{ detailForm.operatorWorkNo || '无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailForm.updateTime }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserInfo } from '@/api/user'
import { listEmployeesByHr, listEmployeesByLeader } from '@/api/employee/list'
import {
  listPersonalReward,
  listReward,
  addReward,
  updateReward,
  deleteReward,
  getReward
} from '@/api/employee/reward'

export default {
  name: 'EmployeeReward',
  data() {
    return {
      // 视图模式：个人奖惩/下属奖惩
      viewMode: 'self',
      // 是否有下属
      hasSubordinates: false,
      // 是否为HR
      isHR: false,
      // 是否为领导
      isLeader: false,
      // 加载状态
      loading: false,
      // 个人奖惩记录
      personalRewards: [],
      // 个人奖惩分页
      selfCurrentPage: 1,
      selfPageSize: 10,
      selfTotal: 0,
      // 下属奖惩记录
      subordinatesRewards: [],
      // 下属奖惩分页
      subCurrentPage: 1,
      subPageSize: 10,
      subTotal: 0,
      // 搜索参数
      searchQuery: '',
      workNoQuery: '',
      typeFilter: '',
      dateRange: [],
      // 筛选表单
      filterForm: {},
      // 编辑对话框相关
      dialogVisible: false,
      dialogTitle: '',
      isAddMode: false,
      // 详情对话框相关
      detailVisible: false,
      // 奖惩表单
      rewardForm: {
        id: undefined,
        employeeId: undefined,
        type: 0,
        rewardDate: '',
        title: '',
        content: '',
        amount: 0,
        remark: ''
      },
      // 详情表单
      detailForm: {
        id: undefined,
        employeeId: undefined,
        employeeName: '',
        workNo: '',
        departmentId: undefined,
        departmentName: '',
        operatorName: '',
        operatorWorkNo: '',
        type: 0,
        rewardDate: '',
        title: '',
        content: '',
        amount: 0,
        remark: '',
        createTime: '',
        updateTime: ''
      },
      // 表单验证规则
      rules: {
        employeeId: [
          { required: true, message: '请选择员工', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        rewardDate: [
          { required: true, message: '请选择日期', trigger: 'change' },
          {
            validator: (_, value, callback) => {
              if (!value) {
                callback();
                return;
              }

              // 获取当前日期
              const today = new Date();
              const selectedDate = new Date(value);

              // 获取当月第一天
              const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

              // 检查日期是否在当月且不超过今天
              if (selectedDate > today) {
                callback(new Error('不能选择未来日期'));
              } else if (selectedDate < firstDayOfMonth) {
                callback(new Error('只能选择当月日期'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { max: 500, message: '内容长度不能超过500个字符', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { type: 'number', max: 10000, message: '金额不能超过10000元', trigger: 'blur' }
        ]
      },
      // 员工选项
      employeeOptions: [],
      // 筛选后的员工选项（用于远程搜索）
      filteredEmployeeOptions: [],
      // 员工选择加载状态
      employeeSelectLoading: false,
      // 日期选择器配置
      datePickerOptions: {
        disabledDate: (time) => {
          // 获取当前日期
          const today = new Date();
          // 获取当月第一天
          const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          // 禁用当天之后的日期和当月之前的日期
          return time.getTime() > today.getTime() || time.getTime() < firstDayOfMonth.getTime();
        }
      }
    }
  },
  created() {
    this.getUserRole()

    // 检查URL查询参数中是否有viewMode
    const viewModeParam = this.$route.query.viewMode
    if (viewModeParam && (viewModeParam === 'self' || viewModeParam === 'subordinates')) {
      // 如果URL中有viewMode参数，则使用该参数
      this.viewMode = viewModeParam
      console.log('从URL查询参数设置视图模式:', this.viewMode)

      // 根据视图模式加载数据
      if (this.viewMode === 'self') {
        this.getPersonalRewards()
      } else if (this.viewMode === 'subordinates') {
        this.getSubordinatesRewards()
      }
    } else {
      // 否则使用默认初始化
      this.initViewMode()
    }
  },
  watch: {
    // 监听视图模式变化
    viewMode(newVal) {
      if (newVal === 'self') {
        this.getPersonalRewards()
      } else if (newVal === 'subordinates') {
        this.getSubordinatesRewards()
      }
    }
  },
  computed: {
    // 计算员工显示名称
    employeeDisplayName() {
      if (!this.rewardForm.employeeId) return '';

      // 在编辑模式下，从详情中获取员工信息
      if (!this.isAddMode && this.detailForm && this.detailForm.employeeName) {
        return `${this.detailForm.employeeName} (${this.detailForm.workNo || '无工号'})`;
      }

      // 从选项中查找员工信息
      const employee = this.employeeOptions.find(emp => emp.id === this.rewardForm.employeeId);
      if (employee) {
        return `${employee.name} (${employee.workNo || '无工号'})`;
      }

      return '未知员工';
    }
  },
  methods: {
    // 获取用户角色信息
    async getUserRole() {
      try {
        const response = await getUserInfo()
        const { roles } = response.data
        this.isHR = roles.includes('ROLE_hr')
        this.isLeader = roles.includes('ROLE_leader')
        this.hasSubordinates = this.isHR || this.isLeader
      } catch (error) {
        console.error('获取用户角色信息失败', error)
      }
    },

    // 初始化视图模式
    initViewMode() {
      // 从Vuex中获取用户角色信息
      const roles = this.$store.getters['userInfo/roles'] || []
      const isHR = roles.includes('ROLE_hr')
      const isLeader = roles.includes('ROLE_leader')

      // 如果用户有HR或Leader角色，默认显示员工奖惩界面
      if (isHR || isLeader) {
        this.viewMode = 'subordinates'
        this.getSubordinatesRewards()
      } else {
        this.viewMode = 'self'
        this.getPersonalRewards()
      }
    },
    // 获取个人奖惩记录
    async getPersonalRewards() {
      try {
        this.loading = true
        // 构建查询参数
        const queryParams = {
          pageNum: this.selfCurrentPage,
          pageSize: this.selfPageSize
        }

        // 调用获取个人奖惩记录的API
        const response = await listPersonalReward(queryParams)
        if (response.code === 1 && response.data) {
          // 处理返回的数据
          this.personalRewards = response.data.records
          this.selfTotal = response.data.total
        } else {
          this.personalRewards = []
          this.selfTotal = 0
          if (response.msg) {
            this.$message.warning(response.msg)
          }
        }
        this.loading = false
      } catch (error) {
        console.error('获取个人奖惩记录失败', error)
        this.$message.error('获取个人奖惩记录失败')
        this.loading = false
      }
    },
    // 获取下属奖惩记录
    async getSubordinatesRewards() {
      try {
        this.loading = true
        // 构建查询参数
        const queryParams = {
          pageNum: this.subCurrentPage,
          pageSize: this.subPageSize,
          employeeName: this.searchQuery || undefined,
          workNo: this.workNoQuery || undefined,
          type: this.typeFilter === 'REWARD' ? 0 : (this.typeFilter === 'PUNISHMENT' ? 1 : undefined),
          startDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : undefined,
          endDate: this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : undefined
        }

        // 调用获取奖惩记录的API
        const response = await listReward(queryParams)
        if (response.code === 1 && response.data) {
          // 处理返回的数据
          this.subordinatesRewards = response.data.records
          this.subTotal = response.data.total
        } else {
          this.subordinatesRewards = []
          this.subTotal = 0
          if (response.msg) {
            this.$message.warning(response.msg)
          }
        }
        this.loading = false
      } catch (error) {
        console.error('获取下属奖惩记录失败', error)
        this.$message.error('获取下属奖惩记录失败')
        this.loading = false
      }
    },
    // 获取员工选项
    async getEmployeeOptions() {
      try {
        this.employeeSelectLoading = true
        let response
        // 从Vuex中获取用户角色信息
        const roles = this.$store.getters['userInfo/roles'] || []
        const isHR = roles.includes('ROLE_hr')
        const isLeader = roles.includes('ROLE_leader') && !isHR // 如果同时拥有HR和Leader角色，优先使用HR接口

        // 根据角色选择不同的API
        if (isHR) {
          // HR只能选择自己管理的员工
          console.log('使用HR接口获取员工列表')
          response = await listEmployeesByHr()
        } else if (isLeader) {
          // 领导只能选择自己部门的员工
          console.log('使用Leader接口获取员工列表')
          response = await listEmployeesByLeader()
        } else {
          console.log('当前用户没有HR或Leader角色')
          this.$message.warning('您没有权限添加奖惩记录')
          this.employeeSelectLoading = false
          return
        }

        if (response.code === 1 && response.data) {
          this.employeeOptions = response.data
          // 初始时显示所有员工选项
          this.filteredEmployeeOptions = this.employeeOptions
          console.log(`成功获取${this.employeeOptions.length}条员工记录`)
        } else {
          this.employeeOptions = []
          this.filteredEmployeeOptions = []
          if (response.msg) {
            this.$message.warning(response.msg)
          }
        }
        this.employeeSelectLoading = false
      } catch (error) {
        console.error('获取员工选项失败', error)
        this.$message.error('获取员工选项失败')
        this.employeeSelectLoading = false
      }
    },

    // 远程搜索员工
    remoteSearchEmployee(query) {
      if (query !== '') {
        this.employeeSelectLoading = true
        // 使用setTimeout模拟远程请求
        setTimeout(() => {
          // 在本地进行搜索过滤
          this.filteredEmployeeOptions = this.employeeOptions.filter(item => {
            // 匹配姓名或工号
            return item.name.toLowerCase().includes(query.toLowerCase()) ||
                   (item.workNo && item.workNo.toLowerCase().includes(query.toLowerCase()))
          })
          this.employeeSelectLoading = false
        }, 200)
      } else {
        // 如果查询为空，显示所有选项
        this.filteredEmployeeOptions = this.employeeOptions
      }
    },

    // 查看奖惩详情
    async handleViewReward(row) {
      try {
        this.loading = true
        const response = await getReward(row.id)
        if (response.code === 1 && response.data) {
          this.detailForm = response.data
          this.detailVisible = true
        } else {
          this.$message.error(response.msg || '获取奖惩详情失败')
        }
        this.loading = false
      } catch (error) {
        console.error('获取奖惩详情失败', error)
        this.$message.error('获取奖惩详情失败')
        this.loading = false
      }
    },
    // 处理个人奖惩页码变化
    handleSelfCurrentChange(val) {
      this.selfCurrentPage = val
      this.getPersonalRewards()
    },
    // 处理个人奖惩每页条数变化
    handleSelfSizeChange(val) {
      this.selfPageSize = val
      this.selfCurrentPage = 1
      this.getPersonalRewards()
    },
    // 处理下属奖惩页码变化
    handleSubCurrentChange(val) {
      this.subCurrentPage = val
      this.getSubordinatesRewards()
    },
    // 处理下属奖惩每页条数变化
    handleSubSizeChange(val) {
      this.subPageSize = val
      this.subCurrentPage = 1
      this.getSubordinatesRewards()
    },
    // 处理搜索
    handleSearch() {
      this.subCurrentPage = 1
      this.getSubordinatesRewards()
    },

    // 重置筛选条件
    resetFilter() {
      this.searchQuery = ''
      this.workNoQuery = ''
      this.typeFilter = ''
      this.dateRange = []
      this.subCurrentPage = 1
      this.getSubordinatesRewards()
    },
    // 处理添加奖惩
    async handleAddReward() {
      this.dialogTitle = '新增奖惩'
      this.isAddMode = true

      // 获取当前日期
      const today = new Date();
      // 格式化为YYYY-MM-DD
      const formattedDate = today.toISOString().split('T')[0];

      this.rewardForm = {
        id: undefined,
        employeeId: undefined,
        type: 0, // 默认奖励
        rewardDate: formattedDate, // 默认今天
        title: '',
        content: '',
        amount: 0,
        remark: ''
      }
      // 获取员工选项
      await this.getEmployeeOptions()
      this.dialogVisible = true
    },
    // 处理编辑奖惩
    async handleEditReward(row) {
      try {
        this.loading = true
        const response = await getReward(row.id)
        if (response.code === 1 && response.data) {
          this.dialogTitle = '编辑奖惩'
          this.isAddMode = false

          // 先设置详情表单，用于显示员工信息
          this.detailForm = response.data;

          // 获取员工选项
          await this.getEmployeeOptions()

          // 如果当前员工不在选项列表中，手动添加
          const employeeExists = this.employeeOptions.some(emp => emp.id === response.data.employeeId)
          if (!employeeExists && response.data.employeeId) {
            // 添加当前员工到选项列表
            const employeeOption = {
              id: response.data.employeeId,
              name: response.data.employeeName || '未知员工',
              workNo: response.data.workNo || ''
            }
            this.employeeOptions.push(employeeOption)
            this.filteredEmployeeOptions = [...this.employeeOptions]
            console.log('添加员工到选项列表:', employeeOption)
          }

          // 设置表单数据
          this.rewardForm = {
            id: response.data.id,
            employeeId: response.data.employeeId,
            type: response.data.type,
            rewardDate: response.data.rewardDate,
            title: response.data.title,
            content: response.data.content,
            amount: response.data.amount,
            remark: response.data.remark || ''
          }

          // 确保employeeId已正确设置
          this.$nextTick(() => {
            console.log('编辑奖惩表单数据:', this.rewardForm);
            console.log('员工ID:', this.rewardForm.employeeId);

            // 强制更新表单，确保验证规则应用
            if (this.$refs.rewardForm) {
              this.$refs.rewardForm.clearValidate('employeeId');
            }
          });

          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || '获取奖惩详情失败')
        }
        this.loading = false
      } catch (error) {
        console.error('获取奖惩详情失败', error)
        this.$message.error('获取奖惩详情失败')
        this.loading = false
      }
    },
    // 处理删除奖惩
    handleDeleteReward(row) {
      this.$confirm(`确定要删除"${row.title}"这条奖惩记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 调用删除奖惩记录的API
          const response = await deleteReward(row.id)
          if (response.code === 1) {
            this.$message.success('删除成功')
            this.getSubordinatesRewards()
          } else {
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除奖惩记录失败', error)
          this.$message.error('删除奖惩记录失败')
        }
      }).catch(() => {
        // 取消删除
      })
    },
    // 提交表单
    submitForm() {
      this.$refs.rewardForm.validate(async valid => {
        if (valid) {
          try {
            console.log('提交前的表单数据:', this.rewardForm);

            // 确保employeeId存在
            if (!this.rewardForm.employeeId) {
              this.$message.error('请选择员工');
              return;
            }

            // 准备提交的数据
            const submitData = {
              employeeId: this.rewardForm.employeeId,
              type: this.rewardForm.type, // 0表示奖励，1表示惩罚
              rewardDate: this.rewardForm.rewardDate,
              title: this.rewardForm.title,
              content: this.rewardForm.content,
              amount: this.rewardForm.amount || 0,
              remark: this.rewardForm.remark
            }

            console.log('准备提交的数据:', submitData);

            let response
            if (this.isAddMode) {
              // 新增奖惩记录
              response = await addReward(submitData)
            } else {
              // 更新奖惩记录
              submitData.id = this.rewardForm.id
              response = await updateReward(submitData)
            }

            if (response.code === 1) {
              this.$message.success(this.isAddMode ? '新增奖惩记录成功' : '更新奖惩记录成功')
              this.dialogVisible = false
              // 如果当前是下属奖惩视图，则刷新下属奖惩列表
              if (this.viewMode === 'subordinates') {
                this.getSubordinatesRewards()
              } else {
                // 否则刷新个人奖惩列表
                this.getPersonalRewards()
              }
            } else {
              this.$message.error(response.msg || (this.isAddMode ? '新增奖惩记录失败' : '更新奖惩记录失败'))
            }
          } catch (error) {
            console.error('提交奖惩记录失败', error)
            this.$message.error('提交奖惩记录失败')
          }
        } else {
          console.error('表单验证失败');
          // 输出每个字段的验证状态
          if (this.$refs.rewardForm.fields) {
            this.$refs.rewardForm.fields.forEach(field => {
              console.log(`字段 ${field.prop} 验证状态:`, field.validateState);
            });
          }
        }
      })
    }
  },
  // 删除重复的watch块
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  // background-color: #f5f7fa;
  // min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }
}

.profile-container {
  margin-top: 20px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .profile-info {
    .profile-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .profile-subtitle {
      margin: 5px 0 0;
      font-size: 14px;
      color: #606266;
    }
  }

  .profile-actions {
    display: flex;
    gap: 10px;
  }
}

.table-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 5px;
        color: #409EFF;
      }

    }
  }
}

.profile-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    color: #303133;

    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;

      i {
        // color: #409EFF;
        margin-right: 5px;
      }
    }
  }

  .card-header-self {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    color: #303133;
    height: 25px;

    h3 {
      margin-top: 0;
      margin-bottom: 5px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;

      i {
        // color: #409EFF;
        margin-right: 5px;
      }
    }
  }

}

.profile-item {
  margin-bottom: 15px;

  label {
    font-weight: bold;
    color: #606266;
    margin-right: 8px;
  }
}

.subordinates-container {
  margin-top: 20px;

  .table-operations {
    margin-top: 15px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-operations-left {
      display: flex;
      align-items: center;
    }

    .table-operations-right {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.filter-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  height: 70px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.table-operations {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table-operations-left {
    display: flex;
    align-items: center;
  }

  .table-operations-right {
    display: flex;
    justify-content: flex-end;
  }
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.empty-text {
  color: #909399;
  font-style: italic;
}

// 表单样式优化
.el-dialog {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    margin: 0;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    border-top: 1px solid #e4e7ed;
    padding: 15px 20px;
  }
}

// 按钮样式优化
.el-button {
  // &.el-button--primary {
  //   background-color: #409eff;
  //   border-color: #409eff;
  // }

  &.el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
  }

  &.el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
  }
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
}

.employee-display {
  position: relative;

  input[type="hidden"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }
}
</style>
