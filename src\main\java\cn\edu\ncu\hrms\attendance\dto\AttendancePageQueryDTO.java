package cn.edu.ncu.hrms.attendance.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 考勤分页查询DTO（包含所有条件参数）
 */
@Data
@ApiModel("考勤分页查询参数")
public class AttendancePageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "HRBP员工ID（HR查询专用）", example = "1001")
    private Integer hrbpId;

    @ApiModelProperty(value = "部门ID（Leader查询专用）", example = "201")
    private Integer departmentId;

    @ApiModelProperty(value = "员工工号（精确搜索）", example = "zhangsan03")
    private String workNo;

    @ApiModelProperty(value = "开始日期", example = "2025-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期", example = "2025-12-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "上班状态", allowableValues = "NORMAL,LATE,ABSENT,LEAVE", example = "NORMAL")
    private String clockInStatus;

    @ApiModelProperty(value = "下班状态", allowableValues = "NORMAL,EARLY,ABSENT,LEAVE", example = "NORMAL")
    private String clockOutStatus;
}
