<template>
  <div class="app-container">
    <div class="table-container">
      <div class="table-header">
        <div class="left">
          <h3>角色管理</h3>
        </div>
        <div class="right">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入角色名称"
            clearable
            size="small"
            class="search-input"
            @keyup.enter.native="handleQuery"
          />
          <el-input
            v-model="queryParams.id"
            placeholder="请输入角色ID"
            clearable
            size="small"
            class="search-input"
            @keyup.enter.native="handleQuery"
          />
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button type="info" icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="roleList"
        border
      >
        <el-table-column prop="id" label="角色ID" align="center" min-width="40" />
        <el-table-column prop="name" label="角色名称" align="center" min-width="80" />
        <el-table-column prop="roleKey" label="角色标识" align="center" min-width="100" />
        <el-table-column prop="description" label="角色描述" align="center" min-width="150" />
        <el-table-column prop="createTime" label="创建时间" align="center" min-width="120"  />
        <el-table-column label="操作" align="center" min-width="250" >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改信息</el-button>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-setting"
              @click="handlePermission(scope.row)"
            >分配权限</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除角色</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="table-footer">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加或修改角色对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="roleKey">
          <el-input
            v-model="form.roleKey"
            placeholder="请输入角色标识"
            @input="handleRoleKeyInput"
            :disabled="form.id && isCurrentUserRole(form.id)"
          />
          <div class="form-tip">角色标识必须以ROLE_开头，例如：ROLE_admin</div>
          <div class="form-tip warning" v-if="form.id && isCurrentUserRole(form.id)">
            不允许修改当前用户关联的角色的角色标识
          </div>
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入角色描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分配权限对话框 -->
    <el-dialog title="分配权限" :visible.sync="permissionOpen" width="600px" append-to-body :close-on-click-modal="false">
      <el-form :model="form" label-width="80px">
        <el-form-item label="角色名称">
          <el-input v-model="form.name" disabled />
        </el-form-item>
        <el-form-item label="权限分配">
          <el-tree
            ref="permissionTree"
            :data="permissionOptions"
            :props="{ label: 'name', children: 'children' }"
            show-checkbox
            node-key="id"
            empty-text="加载中，请稍后..."
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPermission">确 定</el-button>
        <el-button @click="cancelPermission">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRole, getRole, addRole, updateRole, deleteRole, getRolePermissions, setRolePermissions } from '@/api/system/role'
import { getPermissionTree } from '@/api/system/permission'
import { getUserRoles } from '@/api/system/user'

export default {
  name: 'Role',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 角色表格数据
      roleList: [],
      // 权限树选项
      permissionOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示权限弹出层
      permissionOpen: false,
      // 总条数
      total: 0,
      // 当前用户ID
      currentUserId: this.$store.getters.userId,
      // 当前用户的角色列表
      currentUserRoles: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        id: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        roleKey: undefined,
        description: undefined
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '角色名称不能包含空格', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '角色标识不能为空', trigger: 'blur' },
          {
            pattern: /^ROLE_[a-zA-Z0-9_]+$/,
            message: '角色标识必须以ROLE_开头，后跟字母、数字或下划线',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getCurrentUserRoles()
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true
      listRole(this.queryParams).then(response => {
        // 适配后端返回的数据结构
        if (response && response.code === 1 && response.data) {
          this.roleList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response && response.msg || '获取角色列表失败')
          this.roleList = []
          this.total = 0
        }
        this.loading = false
      }).catch(error => {
        this.$message.error(error.message || '获取角色列表失败')
        this.roleList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        id: undefined
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加角色'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getRole(row.id).then(response => {
        if (response && response.code === 1) {
          this.form = response.data || {}
        } else {
          this.$message.error(response && response.msg || '获取角色详情失败')
          this.form = { ...row }
        }
        this.open = true
        this.title = '修改角色'
      }).catch(error => {
        this.$message.error(error.message || '获取角色详情失败')
        this.form = { ...row }
        this.open = true
        this.title = '修改角色'
      })
    },
    /** 分配权限按钮操作 */
    handlePermission(row) {
      this.reset()
      this.form = { ...row }
      this.permissionOpen = true
      this.getRolePermissionTree(row.id)
    },
    /** 获取角色权限树 */
    getRolePermissionTree(roleId) {
      // 获取权限树结构
      getPermissionTree().then(response => {
        if (response && response.code === 1) {
          // 直接使用后端返回的树结构数据
          this.permissionOptions = response.data || []

          // 获取角色已有权限
          getRolePermissions(roleId).then(res => {
            if (res && res.code === 1) {
              const permIds = res.data || []
              this.$nextTick(() => {
                permIds.forEach(id => {
                  const node = this.$refs.permissionTree.getNode(id)
                  if (node) {
                    this.$refs.permissionTree.setChecked(node, true, false)
                  }
                })
              })
            } else {
              this.$message.error(res && res.msg || '获取角色权限失败')
            }
          }).catch(error => {
            this.$message.error(error.message || '获取角色权限失败')
          })
        } else {
          this.$message.error(response && response.msg || '获取权限树失败')
        }
      }).catch(error => {
        this.$message.error(error.message || '获取权限树失败')
      })
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 检查是否是当前用户关联的角色
          if (this.form.id && this.isCurrentUserRole(this.form.id)) {
            // 获取原始角色数据进行比较
            getRole(this.form.id).then(response => {
              if (response && response.code === 1 && response.data) {
                const originalRole = response.data
                // 如果尝试修改角色标识，阻止提交
                if (originalRole.roleKey !== this.form.roleKey) {
                  this.$message.error('不允许修改当前用户关联的角色的角色标识')
                  return
                }
                // 其他字段可以修改
                updateRole(this.form.id, this.form).then(response => {
                  if (response && response.code === 1) {
                    this.$message.success('修改成功')
                    this.open = false
                    this.getList()
                  } else {
                    this.$message.error(response && response.msg || '修改失败')
                  }
                }).catch(error => {
                  this.$message.error(error.message || '修改失败')
                })
              } else {
                this.$message.error(response && response.msg || '获取角色详情失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '获取角色详情失败')
            })
          } else if (this.form.id != null) {
            // 非当前用户关联的角色，可以正常修改
            updateRole(this.form.id, this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '修改失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '修改失败')
            })
          } else {
            // 新增角色
            addRole(this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '新增失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '新增失败')
            })
          }
        }
      })
    },
    /** 提交权限 */
    submitPermission() {
      const roleId = this.form.id
      const permissionIds = this.$refs.permissionTree.getCheckedKeys()
      setRolePermissions(roleId, { permissionIds }).then(response => {
        if (response && response.code === 1) {
          this.$message.success('权限分配成功')
          this.permissionOpen = false
        } else {
          this.$message.error(response && response.msg || '权限分配失败')
        }
      }).catch(error => {
        this.$message.error(error.message || '权限分配失败')
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的角色?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return deleteRole(row.id)
      }).then(response => {
        if (response && response.code === 1) {
          this.getList()
          this.$message.success('删除成功')
        } else {
          this.$message.error(response && response.msg || '删除失败')
        }
      }).catch(error => {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除失败')
        }
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        roleKey: undefined,
        description: undefined
      }
      this.resetForm('form')
    },
    /** 重置表单 */
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 取消权限分配 */
    cancelPermission() {
      this.permissionOpen = false
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    // 分页页码变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 处理角色标识输入
    handleRoleKeyInput(val) {
      // 如果用户输入的不是以ROLE_开头，自动添加前缀
      if (val && !val.startsWith('ROLE_')) {
        // 如果用户输入的是以ROLE_开头的一部分（例如R, RO, ROL等），不做处理
        if (!('ROLE_'.startsWith(val))) {
          this.form.roleKey = 'ROLE_' + val
        }
      }
    },

    // 获取当前用户的角色列表
    getCurrentUserRoles() {
      if (!this.currentUserId) {
        return
      }
      getUserRoles(this.currentUserId).then(response => {
        if (response && response.code === 1 && response.data) {
          this.currentUserRoles = response.data
        } else {
          this.$message.error(response && response.msg || '获取当前用户角色失败')
          this.currentUserRoles = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取当前用户角色失败')
        this.currentUserRoles = []
      })
    },

    // 检查角色是否是当前用户关联的角色
    isCurrentUserRole(roleId) {
      return this.currentUserRoles.some(role => role.id === roleId)
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
}

.form-tip.warning {
  color: #E6A23C;
  font-weight: bold;
}
</style>
