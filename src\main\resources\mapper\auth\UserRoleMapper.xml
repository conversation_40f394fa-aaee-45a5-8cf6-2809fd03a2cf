<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.edu.ncu.hrms.auth.mapper.UserRoleMapper">

    <!-- 插入单条用户角色关联关系 -->
    <insert id="insert" parameterType="UserRole">
        INSERT INTO user_role (
            user_id, role_id, create_time, update_time, del_flag
        ) VALUES (
            #{userId}, #{roleId}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 根据用户ID逻辑删除其所有角色关联 -->
    <update id="deleteByUserId" parameterType="java.lang.Integer">
        UPDATE user_role
        SET del_flag = null, update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 根据角色ID逻辑删除其所有用户关联 -->
    <update id="deleteByRoleId" parameterType="java.lang.Integer">
        UPDATE user_role
        SET del_flag = null, update_time = NOW()
        WHERE role_id = #{roleId}
    </update>

    <!-- 批量插入用户角色关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_role (
            user_id, role_id, create_time, update_time, del_flag
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, NOW(), NOW(), 0)
        </foreach>
    </insert>
    
    <!-- 查询指定用户和角色的关联关系（判重/业务校验） -->
    <select id="selectByUserIdAndRoleId" resultType="UserRole">
        SELECT 
            user_id, role_id, create_time, update_time, del_flag
        FROM 
            user_role
        WHERE 
            user_id = #{userId}
            AND role_id = #{roleId}
            AND del_flag = 0
    </select>

    <!-- 根据角色ID统计关联的未删除用户数量 -->
    <select id="countByRoleId" resultType="int" parameterType="java.lang.Integer">
        SELECT COUNT(*)
        FROM user_role
        WHERE role_id = #{roleId}
          AND del_flag = 0
    </select>

</mapper> 