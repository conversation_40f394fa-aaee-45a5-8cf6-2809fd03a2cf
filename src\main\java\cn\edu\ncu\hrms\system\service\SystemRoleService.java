package cn.edu.ncu.hrms.system.service;

// TODO: Create RoleVO in cn.edu.ncu.hrms.system.vo
import cn.edu.ncu.hrms.system.dto.RolePageQueryDTO;
import cn.edu.ncu.hrms.system.vo.RoleVO;
// TODO: Create RoleAddDTO in cn.edu.ncu.hrms.system.dto
import cn.edu.ncu.hrms.system.dto.RoleAddDTO;
// TODO: Create RoleUpdateDTO in cn.edu.ncu.hrms.system.dto
import cn.edu.ncu.hrms.system.dto.RoleUpdateDTO;
// TODO: Create RolePermissionUpdateDTO in cn.edu.ncu.hrms.system.dto
import cn.edu.ncu.hrms.system.dto.RolePermissionUpdateDTO;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 系统角色管理服务接口 (对应 SystemRoleController)
 */
public interface SystemRoleService {
    /**
     * 获取角色列表
     * @return Result<List<RoleVO>> 角色VO列表
     */
    public PageInfo<RoleVO> listRoles(RolePageQueryDTO queryDTO);

    /**
     * 根据ID获取角色详情 VO
     * @param id 角色ID
     * @return Result<RoleVO> 角色VO
     */
    Result<RoleVO> getRoleVOById(Integer id);

    /**
     * 创建角色
     * @param addDTO 角色创建信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> createRole(RoleAddDTO addDTO);

    /**
     * 更新角色信息
     * @param id 角色ID
     * @param updateDTO 角色更新信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> updateRole(Integer id, RoleUpdateDTO updateDTO);

    /**
     * 删除角色 (需要处理用户关联)
     * @param id 角色ID
     * @return Result<Void> 操作结果
     */
    Result<Void> deleteRole(Integer id);

    // 注意：getRolePermissions 方法现在属于 SystemPermissionService
    // /**
    //  * 获取角色拥有的权限列表
    //  * @param id 角色ID
    //  * @return 权限列表
    //  */
    // Result<List<PermissionVO>> getRolePermissions(Integer id); // 返回 PermissionVO

    /**
     * 设置角色权限
     * @param roleId 角色ID
     * @param updateDTO 包含权限ID列表的 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> setRolePermissions(Integer roleId, RolePermissionUpdateDTO updateDTO);
}
