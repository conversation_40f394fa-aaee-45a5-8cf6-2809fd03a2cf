package cn.edu.ncu.hrms.auth.mapper;

import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.system.dto.UserPageQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户表 Mapper 接口
 */
@Mapper
public interface UserMapper {

    /**
     * 根据用户名查询用户
     * 
     * 用于登录验证。
     *
     * @param username 用户名
     * @return User 用户实体，未找到返回 null
     */
    User selectByUsername(String username);

    /**
     * 根据用户ID查询用户
     * 
     * 用于获取指定用户信息。
     *
     * @param id 用户ID
     * @return User 用户实体，未找到返回 null
     */
    User selectById(Integer id);

    /**
     * 新增用户
     * 
     * 用于用户注册或管理员添加用户。
     * XML 中需配置 useGeneratedKeys="true" keyProperty="id" 以返回自增主键。
     *
     * @param user 待插入的用户信息 (密码应为加密后的)
     * @return int 影响行数
     */
    int insert(User user);

    /**
     * 根据主键 ID 更新用户信息
     * 
     * 用于修改用户信息、修改密码等。
     * XML 中应使用 <set> 和 <if> 实现动态更新，仅更新非 null 字段。
     *
     * @param user 包含主键 ID 和待更新字段的用户信息
     * @return int 影响行数
     */
    int update(User user);

    /**
     * 根据主键 ID 逻辑删除用户
     * 
     * 通常是将某个标志位（如 is_deleted）更新为 1。
     *
     * @param id 用户ID
     * @return int 影响行数
     */
    int deleteById(Integer id);

    /**
     * 根据条件查询用户列表（支持分页）
     * 
     * 用于用户管理列表展示，应在 Service 层配合 PageHelper 使用实现分页。
     * XML 中应使用动态 SQL 处理各种查询条件。
     *
     * @param queryDTO 查询条件 DTO
     * @return List<User> 用户列表
     */
    List<User> selectListByConditions(UserPageQueryDTO queryDTO);

    /**
     * 更新指定用户的密码
     * @param id 用户ID
     * @param password 加密后的新密码
     * @param updateTime 更新时间
     * @return int 影响行数
     */
    int updatePassword(@Param("id") Integer id, @Param("password") String password, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询用户列表（支持根据是否管理员筛选） - 保留旧方法以防其他地方使用，但新逻辑用 selectListByConditions
     *
     * @param isAdmin 是否只查询管理员 (true: 是, false: 否, null: 查询所有)
     * @return List<User> 用户列表
     */
    List<User> selectList(@Param("isAdmin") Boolean isAdmin);

    /**
     * 统计指定用户名的用户数量
     * 
     * 用于检查用户名是否存在。
     *
     * @param username 用户名
     * @return int 存在的用户数量 (0 或 1)
     */
    int existsByUsername(String username);

    /**
     * 根据员工 ID 查询用户
     * 
     * 用于注册时检查员工是否已关联账户。
     *
     * @param employeeId 员工ID
     * @return User 用户实体，未找到返回 null
     */
    User selectByEmployeeId(@Param("employeeId") Integer employeeId);

    /**
     * 根据用户名查询用户信息 (忽略逻辑删除)
     * @param username 用户名
     * @return 用户实体
     */
    User selectByUsernameIgnoreDelFlag(String username);

    /**
     * 更新用户的逻辑删除标志
     * @param id 用户ID
     * @param delFlag 新的删除标志 (true表示删除, false表示未删除)
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateDelFlag(@Param("id") Integer id, @Param("delFlag") boolean delFlag, @Param("updateTime") LocalDateTime updateTime);
} 