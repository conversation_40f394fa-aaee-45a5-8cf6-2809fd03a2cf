<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.salary.mapper.SalaryReportMapper">


    <!-- 新增薪资报表记录 -->
    <insert id="insert" parameterType="cn.edu.ncu.hrms.common.entity.SalaryReport" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO salary_report (
            employee_id, account_id, month, base_salary, adjustment_coefficient, performance_bonus,
            work_bonus, work_fine, attendance_fine, insurance, total, create_time, update_time, del_flag
        ) VALUES (
            #{employeeId}, #{accountId}, #{month}, #{baseSalary}, #{adjustmentCoefficient}, #{performanceBonus},
            #{workBonus}, #{workFine}, #{attendanceFine}, #{insurance}, #{total}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新薪资报表记录 -->
    <update id="update" parameterType="cn.edu.ncu.hrms.common.entity.SalaryReport">
        UPDATE salary_report
        <set>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="month != null">month = #{month},</if>
            <if test="baseSalary != null">base_salary = #{baseSalary},</if>
            <if test="adjustmentCoefficient != null">adjustment_coefficient = #{adjustmentCoefficient},</if>
            <if test="performanceBonus != null">performance_bonus = #{performanceBonus},</if>
            <if test="workBonus != null">work_bonus = #{workBonus},</if>
            <if test="workFine != null">work_fine = #{workFine},</if>
            <if test="attendanceFine != null">attendance_fine = #{attendanceFine},</if>
            <if test="insurance != null">insurance = #{insurance},</if>
            <if test="total != null">total = #{total},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </set>
        WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 逻辑删除薪资报表记录 -->
    <update id="deleteById">
        UPDATE salary_report SET del_flag = null, update_time = NOW() WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 根据ID查询薪资报表 -->
    <select id="selectById" resultType="salaryReport">
        SELECT *
        FROM salary_report 
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据员工ID和月份查询薪资报表 -->
    <select id="selectByEmployeeIdAndMonth" resultType="salaryReport">
        SELECT *
        FROM salary_report 
        WHERE employee_id = #{employeeId} AND month = #{month} AND del_flag = 0
    </select>

    <!-- 根据条件分页查询薪资报表 (包含员工和部门信息) -->
    <select id="selectByCondition" parameterType="cn.edu.ncu.hrms.salary.dto.SalaryPageQueryDTO" resultType="salaryReport">
        SELECT sr.*
        FROM salary_report sr
        LEFT JOIN employee e ON sr.employee_id = e.id
        <where>
            sr.del_flag = 0
            <if test="employeeId != null">
                AND sr.employee_id = #{employeeId}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="hrbpId != null">
                AND e.hrbp_id = #{hrbpId}
            </if>
            <if test="workNo != null">
                AND e.work_no = #{workNo}
            </if>
            <if test="employeeName != null">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="startMonth != null and endMonth != null">
                AND sr.month BETWEEN #{startMonth} AND #{endMonth}
            </if>
        </where>
        ORDER BY sr.month DESC, e.department_id, e.id
    </select>

    <!-- 查询指定部门指定月份的所有薪资报表 -->
    <select id="selectByDepartmentAndMonth" resultType="salaryReport">
        SELECT sr.*
        FROM salary_report sr
        JOIN employee e ON sr.employee_id = e.id
        WHERE sr.del_flag = 0
          AND e.del_flag = 0
          AND e.department_id = #{departmentId}
          AND sr.month = #{month}
        ORDER BY sr.employee_id
    </select>

    <!-- 根据条件统计薪资报表数量 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*)
        FROM salary_report s
        LEFT JOIN employee e ON s.employee_id = e.id
        <where>
            s.del_flag = 0
            <if test="employeeId != null">
                AND s.employee_id = #{employeeId}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="startMonth != null and endMonth != null">
                AND s.month BETWEEN #{startMonth} AND #{endMonth}
            </if>
        </where>
    </select>

    <!-- 统计指定员工指定年份的总薪资 -->
    <select id="sumAnnualSalaryByEmployee" resultType="java.math.BigDecimal">
        SELECT SUM(total)
        FROM salary_report
        WHERE employee_id = #{employeeId}
          AND month LIKE CONCAT(#{year}, '-%')
          AND del_flag = 0
    </select>

    <!-- 统计指定部门指定年份的总薪资 -->
    <select id="sumAnnualSalaryByDepartment" resultType="java.math.BigDecimal">
        SELECT SUM(sr.total)
        FROM salary_report sr
                 JOIN employee e ON sr.employee_id = e.id
        WHERE e.department_id = #{departmentId}
          AND sr.month LIKE CONCAT(#{year}, '-%')
          AND sr.del_flag = 0
          AND e.del_flag = 0
    </select>
</mapper> 