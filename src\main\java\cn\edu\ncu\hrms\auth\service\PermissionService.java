package cn.edu.ncu.hrms.auth.service;

import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 权限服务接口
 */
public interface PermissionService {

    /**
     * 根据ID查询权限
     *
     * @param id 权限ID
     * @return 权限信息
     */
    Permission getPermissionById(Integer id);

    /**
     * 查询权限列表
     *
     * @return 权限列表
     */
    List<Permission> getPermissionList();

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByRoleId(Integer roleId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByUserId(Integer userId);
} 