package cn.edu.ncu.hrms.attendance.service;

import cn.edu.ncu.hrms.attendance.dto.AttendanceConfigDTO;
import cn.edu.ncu.hrms.attendance.dto.AttendancePageQueryDTO;
import cn.edu.ncu.hrms.attendance.dto.AttendanceUpdateDTO;
import cn.edu.ncu.hrms.attendance.vo.DashboardAttendanceStatsVO;
import cn.edu.ncu.hrms.common.entity.Attendance;
import cn.edu.ncu.hrms.attendance.vo.AttendanceVO;
import cn.edu.ncu.hrms.attendance.vo.AttendanceStatisticsVO;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;

import java.time.LocalDate;
import java.util.List;

/**
 * 考勤服务接口
 */
public interface AttendanceService {

    /**
     * 员工打卡
     *
     * @param employeeId 员工ID
     * @param type 打卡类型 (使用 AttendanceConstant.CLOCK_IN / CLOCK_OUT)
     * @param location 打卡位置 [经度, 纬度]
     * @return Result<AttendanceVO> 返回打卡记录的 VO
     */
    Result<AttendanceVO> clock(Integer employeeId, String type, double[] location);

    /**
     * 根据ID获取考勤记录 VO
     *
     * @param id 考勤记录ID
     * @return Result<AttendanceVO>
     */
    Result<AttendanceVO> getVOById(Integer id);

    /**
     * 获取员工某天的考勤记录
     *
     * @param employeeId 员工ID
     * @param date 日期
     * @return Result<Attendance>
     */
    Result<Attendance> getByEmployeeAndDate(Integer employeeId, LocalDate date);

    /**
     * 获取员工考勤记录列表
     *
     * @param employeeId 员工ID
     * @return Result<List<Attendance>>
     */
    Result<List<Attendance>> listByEmployee(Integer employeeId);

    /**
     * 按日期范围获取员工考勤记录
     *
     * @param employeeId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return Result<List<Attendance>>
     */
    Result<List<Attendance>> listByEmployeeAndDateRange(Integer employeeId, LocalDate startDate, LocalDate endDate);

    /**
     * 按部门获取考勤记录列表
     *
     * @param departmentId 部门ID
     * @param date 日期
     * @return Result<List<Attendance>>
     */
    Result<List<Attendance>> listByDepartment(Integer departmentId, LocalDate date);

    /**
     * 分页查询考勤记录 VO
     *
     * @param queryDTO 包含分页和过滤条件 (员工ID, 部门ID, 日期范围等)
     * @return Result<PageInfo<AttendanceVO>>
     */
    Result<PageInfo<AttendanceVO>> pageVOs(AttendancePageQueryDTO queryDTO);

    /**
     * 更新考勤记录
     *
     * @param id 考勤记录 ID
     * @param updateDTO 考勤记录 DTO
     * @return Result<Void>
     */
    Result<Void> update(Integer id, AttendanceUpdateDTO updateDTO);


    /**
     * 获取员工考勤统计数据
     *
     * @param employeeId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return Result<AttendanceStatisticsVO> 包含考勤统计信息的 VO
     */
    Result<AttendanceStatisticsVO> getStatistics(Integer employeeId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取管理员仪表盘 - 今日考勤统计
     * @return Result<DashboardAttendanceStatsVO>
     */
    Result<DashboardAttendanceStatsVO> getDashboardAttendanceStats();

    /**
     * 获取员工指定月份的考勤记录列表 (用于日历显示)
     * @param employeeId 员工ID
     * @param yearMonth 年月，格式如 "YYYY-MM"
     * @return Result<List<Attendance>>
     */
    Result<List<Attendance>> getEmployeeMonthlyRecords(Integer employeeId, String yearMonth);

    /**
     * 获取当前部门考勤配置
     * @return
     */
    Result<AttendanceConfigDTO> getDepartmentAttendanceConfig();

    /**
     * 更新当前部门考勤配置
     * @param configDTO
     * @return
     */
    Result<Void> updateDepartmentAttendanceConfig(AttendanceConfigDTO configDTO);


    /**
     * 获取当前登录员工的今日考勤记录
     * @return
     */
    Result<AttendanceVO> getTodayAttendanceRecord();
}