package cn.edu.ncu.hrms.employee.service.impl;

import cn.edu.ncu.hrms.common.constant.RoleConstant;
import cn.edu.ncu.hrms.common.entity.*;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import cn.edu.ncu.hrms.common.kafka.KafkaProducerService;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.ExcelUtil;
import cn.edu.ncu.hrms.common.utils.RedisDistributedLock;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.EmployeeQueryDTO;
import cn.edu.ncu.hrms.employee.dto.EmployeeUpdateDTO;
import cn.edu.ncu.hrms.employee.dto.ImportTaskDTO;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.service.EmployeeService;
import cn.edu.ncu.hrms.employee.vo.EmployeeDashboardVO;
import cn.edu.ncu.hrms.employee.vo.EmployeeVO;
import cn.edu.ncu.hrms.attendance.service.AttendanceService;
import cn.edu.ncu.hrms.attendance.vo.AttendanceStatisticsVO;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.system.dto.EmployeeAddDTO;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import cn.edu.ncu.hrms.system.service.SystemUserService;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.edu.ncu.hrms.common.constant.KafkaConstant.EMPLOYEE_IMPORT_TOPIC;

/**
 * 员工服务实现类
 */
@Slf4j
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private SecurityUtil securityUtil;

    @Autowired
    private RedisDistributedLock redisLock;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Override
    public Result<EmployeeVO> getMyProfile(Integer employeeId) {
        log.info("获取员工档案信息，employeeId: {}", employeeId);

        // 查询员工信息
        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) {
            log.error("员工不存在，员工ID: {}", employeeId);
            return Result.error("员工不存在");
        }

        // 转换为VO
        EmployeeVO employeeVO = convertToEmployeeVO(employee);

        return Result.success(employeeVO);
    }

    @Override
    @Transactional
    public Result<Void> updateMyProfile(EmployeeUpdateDTO employeeUpdateDTO) {
        log.info("更新我的档案信息，employeeId: {}", employeeUpdateDTO.getId());

        // 检查员工是否存在 (虽然 Controller 层已设置 ID，但最好再校验一次)
        Employee existingEmployee = employeeMapper.selectById(employeeUpdateDTO.getId());
        if (existingEmployee == null) {
            log.error("尝试更新不存在的员工档案，ID: {}", employeeUpdateDTO.getId());
            return Result.error("员工信息不存在");
        }

        // 将 DTO 转换为实体进行更新
        Employee employeeToUpdate = new Employee();
        BeanUtils.copyProperties(employeeUpdateDTO, employeeToUpdate);

        // 关键：防止用户自行修改敏感信息
        employeeToUpdate.setDepartmentId(null); // 不允许用户修改部门
        employeeToUpdate.setPositionId(null);  // 不允许用户修改职位
        employeeToUpdate.setStatus(null);      // 不允许用户修改在职状态 (使用 status 字段)
        employeeToUpdate.setWorkNo(null);      // 不允许用户修改工号
        employeeToUpdate.setEntryDate(null);   // 不允许修改入职日期 (使用 entryDate 字段)
        // 移除对不存在字段的设置：jobLevelId, engageForm, workState, beginDate, conversionTime, beginContract, endContract, contractTerm

        int rows = employeeMapper.update(employeeToUpdate);
        if (rows > 0) {
            log.info("我的档案信息更新成功，employeeId: {}", employeeUpdateDTO.getId());
            return Result.success(); // 返回 Result<Void>
        }
        log.error("我的档案信息更新失败，数据库未更新，employeeId: {}", employeeUpdateDTO.getId());
        return Result.error("更新失败");
    }

    @Override
    public Result<EmployeeDashboardVO> getDashboard(Integer employeeId) {
        log.info("获取员工主页仪表盘信息，employeeId: {}", employeeId);

        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) {
            log.error("员工不存在，员工ID: {}", employeeId);
            return Result.error("员工不存在");
        }

        EmployeeDashboardVO dashboardVO = new EmployeeDashboardVO();
        dashboardVO.setEmployeeName(employee.getName());

        // 1. 获取当月考勤统计数据
        try {
            LocalDate today = LocalDate.now();
            LocalDate firstDayOfMonth = today.withDayOfMonth(1);
            LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
            // 调用 AttendanceService 的 getStatistics 方法
            Result<AttendanceStatisticsVO> statsResult = attendanceService.getStatistics(employeeId, firstDayOfMonth, lastDayOfMonth);
            if (statsResult.getCode() == 1 && statsResult.getData() != null) {
                dashboardVO.setAttendanceStats(statsResult.getData());
            } else {
                log.warn("获取员工 {} 当月考勤统计失败或无数据", employeeId);
                dashboardVO.setAttendanceStats(new AttendanceStatisticsVO()); // 设置空对象避免前端报错
            }
        } catch (Exception e) {
            log.error("获取员工 {} 当月考勤统计时发生异常", employeeId, e);
            dashboardVO.setAttendanceStats(new AttendanceStatisticsVO());
        }

        // 2. 获取今日考勤记录
        try {
            LocalDate today = LocalDate.now();
            // 调用 AttendanceService 的 getByEmployeeAndDate 方法
            Result<Attendance> todayRecordResult = attendanceService.getByEmployeeAndDate(employeeId, today);
            if (todayRecordResult.getCode() == 1 && todayRecordResult.getData() != null) {
                dashboardVO.setTodayRecord(todayRecordResult.getData());
            } else {
                log.info("员工 {} 今日无考勤记录", employeeId);
                dashboardVO.setTodayRecord(null);
            }
        } catch (Exception e) {
            log.error("获取员工 {} 今日考勤记录时发生异常", employeeId, e);
            dashboardVO.setTodayRecord(null);
        }

        return Result.success(dashboardVO);
    }

    @Override
    public Result<EmployeeVO> getEmployeeById(Integer id) {
        log.info("根据ID查询员工信息，id: {}", id);

        // 查询员工信息
        Employee employee = employeeMapper.selectById(id);
        if (employee == null) {
            log.error("员工不存在，员工ID: {}", id);
            return Result.error("员工不存在");
        }

        // 转换为VO
        EmployeeVO employeeVO = convertToEmployeeVO(employee);

        return Result.success(employeeVO);
    }

    @Override
    @Transactional
    public Result<Void> createEmployee(EmployeeAddDTO addDTO) {
        log.info("创建新员工: {}", addDTO.getName());

        // 校验唯一身份证
        if (addDTO.getIdCard() != null) {
            Employee employee = employeeMapper.selectByIdCard(addDTO.getIdCard());
            if (employee != null) {
                log.warn("身份证号重复, ID: {}", addDTO.getIdCard());
                return Result.error("身份证号重复");
            }
        }

        // 生成员工工号
        String workNo = generateWorkNo(addDTO.getName());

        Employee employee = new Employee();
        BeanUtils.copyProperties(addDTO, employee);
        employee.setWorkNo(workNo);
        employee.setDelFlag(false);
        employee.setStatus(true);
        LocalDateTime now = LocalDateTime.now();
        employee.setCreateTime(now);
        employee.setUpdateTime(now);

        // 插入员工记录
        int empRows = employeeMapper.insert(employee);
        // 检查插入是否成功以及主键ID是否回填 (MyBatis配置useGeneratedKeys=true)
        if (empRows > 0 && employee.getId() != null) {
            log.info("员工记录创建成功, ID: {}", employee.getId());
            return Result.success(null, "员工创建成功");
        } else {
            log.error("创建员工失败, 返回行数: {}, 回填ID: {}", empRows, employee.getId());
            return Result.error("系统异常，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Result<Void> updateEmployee(EmployeeUpdateDTO updateDTO) {
        log.info("更新员工信息, ID: {}", updateDTO.getId());
        Integer id = updateDTO.getId();
        if (id == null) {
            return Result.error("更新失败：员工ID不能为空");
        }
        Employee existingEmployee = employeeMapper.selectById(id);
        if (existingEmployee == null || existingEmployee.getDelFlag()) { // 同时检查是否已逻辑删除
            log.warn("尝试更新不存在或已删除的员工, ID: {}", id);
            return Result.error("更新失败：员工不存在或已被删除");
        }
        if (updateDTO.getIdCard() != null && employeeMapper.selectByIdCard(updateDTO.getIdCard()) != null) {
            log.warn("身份证号重复, ID: {}", updateDTO.getIdCard());
            return Result.error("身份证号重复");
        }

        Employee employeeToUpdate = new Employee();
        BeanUtils.copyProperties(updateDTO, employeeToUpdate);
        employeeToUpdate.setUpdateTime(LocalDateTime.now());
        employeeToUpdate.setDelFlag(existingEmployee.getDelFlag());

        // 使用 mapper 更新
        int rows = employeeMapper.update(employeeToUpdate);
        if (rows > 0) {
            log.info("员工信息更新成功, ID: {}", id);
            return Result.success(null, "员工信息更新成功");
        } else {
            log.warn("更新员工信息数据库操作未影响行数, ID: {}. 可能数据未改变?", id);
            // 再次检查是否存在，以防并发删除
            Employee current = employeeMapper.selectById(id);
            if (current == null || current.getDelFlag()) {
                return Result.error("更新失败：员工已被删除");
            }
            return Result.success("系统异常，请稍后再试");
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteEmployee(Integer id) {
        log.info("逻辑删除员工, ID: {}", id);
        if (id == null) {
            return Result.error("员工ID不能为空");
        }
        Employee existingEmployee = employeeMapper.selectById(id);
        if (existingEmployee == null || existingEmployee.getDelFlag()) {
            log.warn("尝试删除不存在或已逻辑删除的员工, ID: {}", id);
            return Result.success("员工不存在或已被删除");
        }

        // 执行逻辑删除: 更新 delFlag 和 updateTime
        Employee employeeToDelete = new Employee();
        employeeToDelete.setId(id);
        employeeToDelete.setDelFlag(true);
        employeeToDelete.setUpdateTime(LocalDateTime.now());

        int rows = employeeMapper.update(employeeToDelete);
        if (rows > 0) {
            log.info("员工记录逻辑删除成功, ID: {}", id);
            // 同时删除关联的用户账号
            // 使用新的方法查找关联用户
            User associatedUser = systemUserService.findUserByEmployeeId(id);
            if (associatedUser != null) {
                log.info("尝试禁用员工 {} 关联的用户 {}", id, associatedUser.getId());
                Result result = systemUserService.deleteUser(associatedUser.getId());
                if (result.getCode() == Result.ERROR_CODE) {
                    log.error("员工 {} 关联的用户 {} 删除失败，错误信息: {}", id, associatedUser.getId(), result.getMsg());
                    return Result.error("系统异常，请稍后再试");
                } else {
                    log.info("员工 {} 关联的用户 {} 已成功删除", id, associatedUser.getId());
                }
            } else {
                log.warn("未找到员工 {} 关联的用户账号，无需禁用用户", id);
            }
            return Result.success(null, "员工删除成功");
        } else {
            log.error("删除员工失败, ID: {}", id);
            return Result.error("系统异常，请稍后再试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchImport(MultipartFile file) {
        try {
            // 1. 获取当前用户ID并校验权限
            Integer uid = securityUtil.getCurrentUserId();
            if (uid == null) {
                throw new SecurityException("未获取到用户身份");
            }

            // 2. 保存文件到临时目录
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "imports");
            Files.createDirectories(tempDir);
            String filePath = tempDir.resolve(UUID.randomUUID() + ".xlsx").toString();
            file.transferTo(new File(filePath));

            // 3. 构造消息体（包含UID和文件路径）
            ImportTaskDTO task = new ImportTaskDTO(filePath, uid);
            kafkaProducerService.sendObjectMessage(EMPLOYEE_IMPORT_TOPIC, task);

            return Result.success(null, "员工导入任务已提交");
        } catch (Exception e) {
            log.error("文件处理失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<byte[]> exportEmployees(EmployeeQueryDTO queryDTO) {
        log.info("导出员工信息，queryDTO: {}", queryDTO);

        // 1.根据角色获取数据
        List<String> currentUserRoles = securityUtil.getCurrentUserRoles();
        List<EmployeeVO> employees = null;
        if (currentUserRoles.contains(RoleConstant.ROLE_HR)) {
            employees = getAllEmployeeListByHr(queryDTO);
        } else if (currentUserRoles.contains(RoleConstant.ROLE_LEADER)) {
            employees = getAllEmployeeListByLeader(queryDTO);
        }
        if (employees == null || employees.isEmpty()) {
            log.warn("没有符合条件的员工数据可供导出");
            return Result.error("没有符合条件的员工数据");
        }

        // 2. 定义字段映射关系（模板占位符 -> 数据获取逻辑）
        Map<String, Function<EmployeeVO, Object>> fieldMapping = new LinkedHashMap<>();
        fieldMapping.put("工号", EmployeeVO::getWorkNo);
        fieldMapping.put("姓名", EmployeeVO::getName);
        fieldMapping.put("入职日期", EmployeeVO::getEntryDate);
        fieldMapping.put("性别", EmployeeVO::getGender);
        fieldMapping.put("身份证号", EmployeeVO::getIdCard);
        fieldMapping.put("联系电话", EmployeeVO::getPhone);
        fieldMapping.put("邮箱", EmployeeVO::getEmail);
        fieldMapping.put("家庭住址", EmployeeVO::getAddress);
        fieldMapping.put("部门", EmployeeVO::getDepartmentName);
        fieldMapping.put("职位名称", EmployeeVO::getPositionName);
        fieldMapping.put("职级", EmployeeVO::getLevel);
        fieldMapping.put("HR姓名", EmployeeVO::getHrbpName);
        fieldMapping.put("HR工号", EmployeeVO::getHrbpWorkNo);
        fieldMapping.put("在职状态", emp -> emp.getStatus() ? "✅ 在职" : "❌ 离职");


        try {
            byte[] excelBytes = ExcelUtil.exportWithTemplate(employees,
                    "/templates/employee_export.xlsx", fieldMapping);
            log.info("成功生成Excel文件，大小: {} bytes", excelBytes.length);
            return Result.success(excelBytes);
        } catch (Exception e) {
            log.error("导出失败", e);
            return Result.error("系统异常，导出失败");
        }
    }


    @Override
    public Result<List<Integer>> getApprovalChain(Integer employeeId, Integer type) {
        log.info("获取审批链，员工ID: {}, 类型: {}", employeeId, type);
        List<Integer> chain = new ArrayList<>();
        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) {
            return Result.error("员工不存在");
        }
        Integer leaderId = employeeMapper.getDepartmentLeaderId(employeeId);
        if (leaderId != null) {
            chain.add(leaderId);
            if (type == 3) {
                Integer leaderLeaderId = employeeMapper.getDepartmentLeaderId(leaderId);
                if (leaderLeaderId != null) {
                    chain.add(leaderLeaderId);
                }
            }
        }
        Integer hrbpId = employeeMapper.getHRBPId(employeeId);
        if (hrbpId != null && !chain.contains(hrbpId)) {
            chain.add(hrbpId);
        } else if (hrbpId == null) {
            log.warn("员工 {} 未配置 HRBP", employeeId);
        }

        log.info("员工 {} 的审批链 ({}) : {}", employeeId, type, chain);
        return Result.success(chain);
    }

    /**
     * 将Employee实体转换为EmployeeVO
     */
    private EmployeeVO convertToEmployeeVO(Employee employee) {
        if (employee == null) {
            return null;
        }

        EmployeeVO vo = new EmployeeVO();
        BeanUtils.copyProperties(employee, vo);

        // 获取部门及其上级部门的名称
        List<String> names = getDepartmentAndParents(employee.getDepartmentId());
        if (names != null && !names.isEmpty()) {
            String departmentNames = "";
            for (int i = names.size() - 1; i > 0; i--) {
                departmentNames  += names.get(i) + "/";
            }
            departmentNames += names.get(0);
            vo.setDepartmentName(names.get(0));
            vo.setDepartmentNames(departmentNames);
        }

        // 设置职位名称和职级
        if (employee.getPositionId() != null) {
            Position position = positionMapper.selectById(employee.getPositionId());
            if (position != null) {
                vo.setPositionName(position.getName());
                vo.setLevel(position.getLevel());
            }
        }

        // 设置HR姓名+工号
        if (employee.getHrbpId() != null) {
            Employee hrbp = employeeMapper.selectById(employee.getHrbpId());
            if (hrbp != null) {
                vo.setHrbpName(hrbp.getName());
                vo.setHrbpWorkNo(hrbp.getWorkNo());
            }
        }


        return vo;
    }


    /**
     * 获取部门及其所有上级部门的名称
     * @param departmentId
     * @return
     */
    @Override
    public List<String> getDepartmentAndParents(Integer departmentId) {
        List<String> names = new ArrayList<>();
        Department department = departmentMapper.selectById(departmentId);
        if (department != null) {
            names.add(department.getName());
            Integer parentId = department.getParentId();
            while (parentId != null) {
                Department parent = departmentMapper.selectById(parentId);
                if (parent != null) {
                    parentId = parent.getParentId();
                    names.add(parent.getName());
                }
            }
        }

        return names;
    }

    /**
     * 统一工号生成入口
     *
     * @param name 姓名
     * @param isBatch 是否批次操作（true: 使用批次缓存+分布式锁；false: 直接走分布式锁）
     * @return 工号（姓名拼音+工号）
     */
    @Override
    public String generateWorkNo(String name, boolean isBatch) {
        return isBatch ? generateWorkNoWithBatchCache(name) : generateWorkNoWithLock(name);
    }

    // 分布式锁版本
    private String generateWorkNoWithLock(String name) {
        String lockKey = "workNo:lock:" + name.replaceAll("\\s", "");
        try {
            if (!redisLock.tryLock(lockKey, 10)) {
                throw new BusinessException("系统繁忙，请重试");
            }

            // SELECT FOR UPDATE 会在查询时对选中的行加排他锁（X锁），确保查询到最新工号
            String lastWorkNo = employeeMapper.selectLastWorkNoForUpdate(name);
            String namePinyin = PinyinUtil.getPinyin(name).replaceAll("\\s", "");
            int suffixStartPos = namePinyin.length() + 1;
            String maxWorkNo = employeeMapper.selectMaxWorkNoByPinyin(namePinyin, suffixStartPos);

            if (lastWorkNo == null && maxWorkNo == null) {
                return namePinyin;
            } else {
                int lastNum = extractSuffixNumber(lastWorkNo);
                int maxNum = maxWorkNo == null ? 0 : extractSuffixNumber(maxWorkNo);
                int max = Math.max(lastNum, maxNum);
                if (max == 0) {
                    return namePinyin;
                }
                return namePinyin + String.format("%02d", max + 1);

            }
        } finally {
            redisLock.unlock(lockKey);
        }
    }

    Map<String, List<String>> batchGeneratedWorkMap = new HashMap<>();

    // 批次缓存版本
    private String generateWorkNoWithBatchCache(String name) {
        String nameKey = name.replaceAll("\\s", "");
        synchronized (batchGeneratedWorkMap) {
            List<String> existing = batchGeneratedWorkMap.getOrDefault(nameKey, new ArrayList<>());
            String workNo = generateWorkNoWithLock(name); // 强制走分布式锁

            if (existing.contains(workNo)) {
                // 提取当前批次中同名前缀工号的最大序号
                String namePinyin = workNo.replaceAll("\\d+$", "");
                int maxSuffix = existing.stream()
                        .map(s -> s.substring(namePinyin.length())) // 去掉名字部分
                        .mapToInt(s -> {
                            try {
                                return Integer.parseInt(s);
                            } catch (NumberFormatException e) {
                                return 0; // 非数字后缀处理
                            }
                        })
                        .max()
                        .orElse(0);
                if (maxSuffix > 0) {
                    workNo = namePinyin + String.format("%02d", maxSuffix + 1);
                }

            }

            existing.add(workNo);
            batchGeneratedWorkMap.put(nameKey, existing);
            return workNo;
        }
    }

    // 提取工号后缀数字
    private int extractSuffixNumber(String workNo) {
        Matcher matcher = Pattern.compile("(\\d+)$").matcher(workNo);
        return matcher.find() ? Integer.parseInt(matcher.group(1)) : 0;
    }



    /**
     * 生成员工工号（不适配批量插入）
     * 格式：姓名拼音+序号，如huangkang10
     * @param name 员工姓名
     * @return 工号
     */
    @Override
    @Transactional
    public String generateWorkNo(String name) {
        // 加分布式锁，防止并发时生成重复工号
        String lockKey = "workNo:lock:" + name;
        try {
            // 尝试获取锁，等待5秒，锁过期时间10秒
            if (!redisLock.tryLock(lockKey, 10)) {
                log.error("获取分布式锁时失败");
                throw new BusinessException("系统繁忙，请重试");
            }

            // 先去除姓名中的空格
            name = name.replaceAll("\\s", "");
            // 再转换为拼音并去除所有空格
            String workNo = PinyinUtil.getPinyin(name).replaceAll("\\s", "");
            // 获取所有相同姓名的员工（包括已删除和离职的）
            List<Employee> sameNameEmployees = employeeMapper.selectListByName(name);
            int num1 = sameNameEmployees.size() + 1;
            // 获取相同姓名员工中最后一个创建的员工
            Employee sameNameEmployee = employeeMapper.selectLastByName(name);
            int num2 = 1;
            if (sameNameEmployee != null && sameNameEmployee.getWorkNo() != null) {
                String lastWorkNo = sameNameEmployee.getWorkNo();
                Pattern pattern = Pattern.compile("(\\d+)$");
                Matcher matcher = pattern.matcher(lastWorkNo);
                if (matcher.find()) {
                    num2 = Integer.valueOf(matcher.group(1)) + 1;
                }
            }
            // 取大的值作为序号
            int num = Math.max(num1, num2);
            if (num > 1) {
                workNo += String.format("%02d", num);
            }
            return workNo;

        } finally {
            // 确保无论是否成功最后都要释放锁
            redisLock.unlock(lockKey);
        }
    }


    @Override
    public PageInfo<EmployeeVO> getEmployeePageByHr(EmployeeQueryDTO queryDTO) {
        log.info("分页条件查询HR关联员工列表，queryDTO: {}", queryDTO);

        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 条件查询HR关联的员工列表
        Integer hrbpId = securityUtil.getCurrentEmployeeId();
        if (hrbpId == null) {
            log.warn("当前用户 {} 未关联员工", securityUtil.getCurrentUserId());
            return null;
        }
        queryDTO.setHrbpId(hrbpId);
        List<Employee> employees = employeeMapper.selectEmployeeListByCondition(queryDTO);

        // 将实体列表转换为VO列表
        List<EmployeeVO> employeeVOList = employees.stream()
                .map(this::convertToEmployeeVO)
                .collect(Collectors.toList());

        PageInfo<EmployeeVO> pageInfo = new PageInfo<>(employeeVOList);

        return pageInfo;
    }


    @Override
    public Result<List<Employee>> getEmployeeListByHr() {
        Integer hrbpId = securityUtil.getCurrentEmployeeId();
        List<Employee> employees = employeeMapper.selectByHrbpId(hrbpId);
        return Result.success(employees);
    }



    private List<EmployeeVO> getAllEmployeeListByHr(EmployeeQueryDTO queryDTO) {
        log.info("条件查询所有HR关联员工列表，queryDTO: {}", queryDTO);

        // 条件查询所有HR关联的员工列表
        Integer hrbpId = securityUtil.getCurrentEmployeeId();
        if (hrbpId == null) {
            log.warn("当前用户 {} 未关联员工", securityUtil.getCurrentUserId());
            return null;
        }
        queryDTO.setHrbpId(hrbpId);
        List<Employee> employees = employeeMapper.selectEmployeeListByCondition(queryDTO);

        // 将实体列表转换为VO列表
        List<EmployeeVO> employeeVOList = employees.stream()
                .map(this::convertToEmployeeVO)
                .collect(Collectors.toList());


        return employeeVOList;
    }


    @Override
    public PageInfo<EmployeeVO> getEmployeePageByLeader(EmployeeQueryDTO queryDTO) {
        log.info("分页条件查询部门员工列表，queryDTO: {}", queryDTO);

        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询当前用户（部门领导）的部门id
        Integer departmentId = securityUtil.getCurrentDepartmentId();
        if (departmentId == null) {
            log.warn("当前用户 {} 未关联部门", securityUtil.getCurrentUserId());
            return null;
        }
        List<Integer> childDepartmentIds = getChildDepartmentIds(departmentId);
        queryDTO.setDepartmentIds(childDepartmentIds);
        List<Employee> employees = employeeMapper.selectEmployeeListByCondition(queryDTO);

        List<EmployeeVO> employeeVOList = employees.stream().map(this::convertToEmployeeVO).collect(Collectors.toList());

        PageInfo<EmployeeVO> pageInfo = new PageInfo<>(employeeVOList);

        return pageInfo;
    }

    @Override
    public Result<List<Employee>> getEmployeeListByLeader() {
        Integer departmentId = securityUtil.getCurrentDepartmentId();
        List<Employee> employees = employeeMapper.selectByDepartmentId(departmentId);
        return Result.success(employees);
    }

    private List<EmployeeVO> getAllEmployeeListByLeader(EmployeeQueryDTO queryDTO) {
        log.info("分页条件查询部门员工列表，queryDTO: {}", queryDTO);

        // 查询部门id
        Integer departmentId = securityUtil.getCurrentDepartmentId();
        if (departmentId == null) {
            log.warn("当前用户 {} 未关联部门", securityUtil.getCurrentUserId());
            return null;
        }

        // 查询所有子部门ID
        List<Integer> childDepartmentIds = getChildDepartmentIds(departmentId);
        queryDTO.setDepartmentIds(childDepartmentIds);

        List<Employee> employees = employeeMapper.selectEmployeeListByCondition(queryDTO);
        List<EmployeeVO> employeeVOList = employees.stream().map(this::convertToEmployeeVO).collect(Collectors.toList());


        return employeeVOList;
    }
    
    // 递归获取部门及其所有子部门id（包括子部门的子部门）
    private List<Integer> getChildDepartmentIds(Integer departmentId) {
        List<Integer> ids = new ArrayList<>();
        Department department = departmentMapper.selectById(departmentId);
        if (department != null) {
            ids.add(departmentId);
            List<Department> children = departmentMapper.selectByParentId(departmentId);
            if (!CollectionUtils.isEmpty(children)) {
                for (Department child : children) {
                    ids.addAll(getChildDepartmentIds(child.getId()));
                }
            }
        }
        
        return ids;
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<Employee> employees) {
        employeeMapper.batchInsert(employees);
    }
}
