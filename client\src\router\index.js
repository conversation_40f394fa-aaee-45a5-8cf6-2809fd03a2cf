import Vue from 'vue'
import VueRouter from 'vue-router'

// 引入布局组件
import Layout from '@/layout'

Vue.use(VueRouter)

// 公共路由，所有用户都可以访问
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '首页', icon: 'el-icon-s-home' }
    }]
  },
  // Firefox兼容性测试页面（开发环境）
  {
    path: '/firefox-test',
    component: () => import('@/views/test/firefox-test'),
    hidden: true
  }
]

// 动态路由，根据用户角色动态加载
export const asyncRoutes = [
  // 管理员路由 - 只对管理员可见
  {
    path: '/department',
    component: Layout,
    name: 'Department',
    meta: { title: '部门管理', icon: 'el-icon-office-building', roles: ['ROLE_admin'] },
    children: [
      {
        path: 'index',
        name: 'DepartmentIndex',
        component: () => import('@/views/system/department/index'),
        meta: { title: '部门管理', icon: 'el-icon-office-building' }
      }
    ]
  },
  {
    path: '/position',
    component: Layout,
    name: 'Position',
    meta: { title: '职位管理', icon: 'el-icon-suitcase', roles: ['ROLE_admin'] },
    children: [
      {
        path: 'index',
        name: 'PositionIndex',
        component: () => import('@/views/system/position/index'),
        meta: { title: '职位管理', icon: 'el-icon-suitcase' }
      }
    ]
  },
  {
    path: '/permission',
    component: Layout,
    name: 'Permission',
    meta: { title: '权限管理', icon: 'el-icon-key', roles: ['ROLE_admin'] },
    children: [
      {
        path: 'index',
        name: 'PermissionIndex',
        component: () => import('@/views/system/permission/index'),
        meta: { title: '权限管理', icon: 'el-icon-key' }
      }
    ]
  },
  {
    path: '/role',
    component: Layout,
    name: 'Role',
    meta: { title: '角色管理', icon: 'el-icon-s-check', roles: ['ROLE_admin'] },
    children: [
      {
        path: 'index',
        name: 'RoleIndex',
        component: () => import('@/views/system/role/index'),
        meta: { title: '角色管理', icon: 'el-icon-s-check' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    name: 'User',
    meta: { title: '用户管理', icon: 'el-icon-s-custom', roles: ['ROLE_admin'] },
    children: [
      {
        path: 'index',
        name: 'UserIndex',
        component: () => import('@/views/system/user/index'),
        meta: { title: '用户管理', icon: 'el-icon-s-custom' }
      }
    ]
  },

  // 员工路由 - 只对员工可见
  {
    path: '/employee',
    component: Layout,
    redirect: '/employee/profile',
    name: 'Employee',
    meta: { title: '员工', icon: 'el-icon-user-solid', roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr'] },
    children: [
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/employee/profile/index'),
        meta: { title: '档案管理', icon: 'el-icon-folder' }
      },
      {
        path: 'reward',
        name: 'Reward',
        component: () => import('@/views/employee/reward/index'),
        meta: { title: '奖惩管理', icon: 'el-icon-medal' }
      },
      {
        path: 'resign',
        name: 'Resign',
        component: () => import('@/views/employee/resign/index'),
        meta: { title: '离职管理', icon: 'el-icon-switch-button' }
      }
    ]
  },

  // 考勤路由 - 只对员工可见
  {
    path: '/attendance',
    component: Layout,
    redirect: '/attendance/record',
    name: 'Attendance',
    meta: { title: '考勤', icon: 'el-icon-date', roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr'] },
    children: [
      {
        path: 'record',
        name: 'Record',
        component: () => import('@/views/attendance/record/index'),
        meta: { title: '考勤管理', icon: 'el-icon-alarm-clock' }
      },
      {
        path: 'leave',
        name: 'Leave',
        component: () => import('@/views/attendance/leave/index'),
        meta: { title: '请假管理', icon: 'el-icon-stopwatch' }
      }
    ]
  },

  // 财务路由 - 只对员工可见
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/account',
    name: 'Finance',
    meta: { title: '财务', icon: 'el-icon-wallet', roles: ['ROLE_employee', 'ROLE_leader', 'ROLE_hr'] },
    children: [
      {
        path: 'account',
        name: 'Account',
        component: () => import('@/views/finance/account/index'),
        meta: { title: '账套管理', icon: 'el-icon-bank-card', roles: ['ROLE_hr'] }
      },
      {
        path: 'setting',
        name: 'Setting',
        component: () => import('@/views/finance/setting/index'),
        meta: { title: '账套匹配', icon: 'el-icon-connection', roles: ['ROLE_hr'] }
      },
      {
        path: 'report',
        name: 'Report',
        component: () => import('@/views/finance/report/index'),
        meta: { title: '薪资报表', icon: 'el-icon-document' }
      }
    ]
  },

  // 404页面必须放在最后
  { path: '*', redirect: '/404', hidden: true }
]

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location, onResolve, onReject) {
if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
return originalPush.call(this, location).catch(err => err)
}

const createRouter = () => new VueRouter({
  // mode: 'history', // 需要服务端支持
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// 重置路由方法
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router
