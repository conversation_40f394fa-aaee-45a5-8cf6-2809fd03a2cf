package cn.edu.ncu.hrms.auth.controller;

import cn.edu.ncu.hrms.auth.dto.LoginDTO;
import cn.edu.ncu.hrms.auth.dto.PasswordChangeDTO;
import cn.edu.ncu.hrms.auth.dto.RegisterDTO;
import cn.edu.ncu.hrms.auth.service.UserService;
import cn.edu.ncu.hrms.auth.vo.UserInfoVO;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@Api(tags = "认证服务接口")
public class AuthController {

    @Autowired
    private UserService userService;
    
    @Autowired
    private SecurityUtil securityUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<String> login(@RequestBody @Valid LoginDTO loginDTO) {
        return userService.login(loginDTO);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @ApiOperation("用户注册")
    public Result<Void> register(@RequestBody @Valid RegisterDTO registerDTO) {
        return userService.register(registerDTO);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> logout() {
        // 从上下文获取当前登录用户名
        String username = securityUtil.getCurrentUsername();
        return userService.logout(username);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    @ApiOperation("获取当前用户信息")
    //@PreAuthorize("isAuthenticated()")
    public Result<UserInfoVO> getUserInfo() {
        return userService.getCurrentUserInfo();
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/password")
    @ApiOperation("修改密码")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> changePassword(@RequestBody @Valid PasswordChangeDTO passwordChangeDTO) {
        // 从上下文获取当前登录用户名
        String username = securityUtil.getCurrentUsername();
        return userService.changePassword(username, passwordChangeDTO);
    }
} 