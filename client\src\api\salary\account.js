import request from '@/utils/request'

// 查询所有账套列表
export function listAccount() {
  return request({
    url: '/salary/account/all',
    method: 'get'
  })
}

// 分页查询账套列表
export function getAccountPage(query) {
  return request({
    url: '/salary/account/list',
    method: 'get',
    params: query
  })
}

// 查询账套详细
export function getAccount(id) {
  return request({
    url: '/salary/account/' + id,
    method: 'get'
  })
}

// 新增账套
export function addAccount(data) {
  return request({
    url: '/salary/account',
    method: 'post',
    data: data
  })
}

// 修改账套
export function updateAccount(id, data) {
  return request({
    url: '/salary/account/' + id,
    method: 'put',
    data: data
  })
}

// 删除账套
export function deleteAccount(id) {
  return request({
    url: '/salary/account/' + id,
    method: 'delete'
  })
}
