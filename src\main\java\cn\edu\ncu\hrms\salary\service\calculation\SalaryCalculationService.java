package cn.edu.ncu.hrms.salary.service.calculation;


import cn.edu.ncu.hrms.attendance.mapper.AttendanceMapper;
import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.constant.EmployeeConstant;
import cn.edu.ncu.hrms.common.entity.*;
import cn.edu.ncu.hrms.common.utils.DateUtil;
import cn.edu.ncu.hrms.employee.mapper.RewardPunishmentMapper;
import cn.edu.ncu.hrms.salary.mapper.AccountSetMapper;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.salary.mapper.SalaryReportMapper;
import cn.edu.ncu.hrms.salary.service.calculation.attendance.*;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class SalaryCalculationService {

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private AccountSetMapper accountSetMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private AttendanceMapper attendanceMapper;

    @Autowired
    private RewardPunishmentMapper rewardPunishmentMapper;

    @Autowired
    private SalaryReportMapper salaryReportMapper;

    @Autowired
    private List<PerformanceCoefficientStrategy> performanceStrategies;

    /**
     * 计算指定员工在指定月份的薪资
     *
     * @param employee  员工信息
     * @param yearMonth 计算月份
     */
    @Transactional // 保证单个员工薪资计算和保存的原子性
    public void calculateAndSaveEmployeeSalary(Employee employee, YearMonth yearMonth) {
        log.info("开始为员工 {} (ID: {}) 计算 {} 年 {} 月的薪资", employee.getName(), employee.getId(), yearMonth.getYear(), yearMonth.getMonthValue());

        Position position = positionMapper.selectById(employee.getPositionId());
        if (position == null) {
            log.error("员工 {} (ID: {}) 未找到职位信息，跳过薪资计算", employee.getName(), employee.getId());
            return;
        }

        AccountSet accountSet = accountSetMapper.selectById(position.getAccountId());
        if (accountSet == null) {
            log.error("员工 {} (ID: {}) 职位 {} 未关联有效账套，跳过薪资计算", employee.getName(), employee.getId(), position.getName());
            return;
        }

        Department department = departmentMapper.selectById(employee.getDepartmentId());
        if (department == null) {
            log.warn("员工 {} (ID: {}) 未找到部门信息，将无法计算迟到早退扣款的部门配置金额", employee.getName(), employee.getId());
            return;
        }

        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        int workDays = DateUtil.getWorkingDaysInMonth(startDate);

        // 获取当月考勤记录
        List<Attendance> monthlyAttendance = attendanceMapper.selectByEmployeeAndDateRange(employee.getId(), startDate, endDate);

        // 获取当月奖惩记录
        List<RewardPunishment> monthlyRewardsPunishments = rewardPunishmentMapper.selectByEmployeeAndDateRange(employee.getId(), startDate, endDate);

        long punishmentCount = monthlyRewardsPunishments.stream()
                .filter(rp -> Objects.equals(rp.getType(), EmployeeConstant.PUNISHMENT_TYPE))
                .count();
        long rewardCount = monthlyRewardsPunishments.stream()
                .filter(rp -> Objects.equals(rp.getType(), EmployeeConstant.REWARD_TYPE))
                .count();

        // --- 开始计算薪资组成部分 ---

        // 月基本工资
        BigDecimal baseSalary = accountSet.getBaseSalary();

        // 考核系数 (使用策略模式)
        BigDecimal performanceCoefficient = BigDecimal.ONE; // 默认为1
        for (PerformanceCoefficientStrategy strategy : performanceStrategies) { // 策略已通过 @Order 排序
            if (strategy.matches(monthlyAttendance, workDays, punishmentCount, rewardCount)) {
                performanceCoefficient = strategy.getCoefficient();
                log.debug("员工 {} 应用绩效策略: {}, 系数: {}", employee.getName(), strategy.getClass().getSimpleName(), performanceCoefficient);
                break;
            }
        }

        // 绩效奖金 = 月基本工资 * 绩效比例 * 考核系数
        BigDecimal performanceBonus = baseSalary
                .multiply(accountSet.getPerformanceRate())
                .multiply(performanceCoefficient)
                .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP);

        // 月工作奖金
        BigDecimal workBonus = monthlyRewardsPunishments.stream()
                .filter(rp -> Objects.equals(rp.getType(), EmployeeConstant.REWARD_TYPE) && rp.getAmount() != null)
                .map(RewardPunishment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);

        // 月工作罚款
        BigDecimal workFine = monthlyRewardsPunishments.stream()
                .filter(rp -> Objects.equals(rp.getType(), EmployeeConstant.PUNISHMENT_TYPE) && rp.getAmount() != null)
                .map(RewardPunishment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);

        // 月考勤扣款
        BigDecimal attendanceFine = calculateAttendanceDeduction(monthlyAttendance, baseSalary, department, workDays);


        // 社保扣除
        BigDecimal insuranceDeduction = baseSalary
                .multiply(accountSet.getInsuranceRate())
                .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP);


        // 应发总额 = 月基本工资 + 月绩效奖金 + 月工作奖金 - 月工作罚款 - 月考勤扣款 - 社保扣除
        BigDecimal totalSalary = baseSalary
                .add(performanceBonus)
                .add(workBonus)
                .subtract(workFine)
                .subtract(attendanceFine)
                .subtract(insuranceDeduction) // 减去社保
                .setScale(2, RoundingMode.HALF_UP);

        // --- 构建并保存薪资报表 ---
        SalaryReport report = SalaryReport.builder()
                .employeeId(employee.getId())
                .accountId(accountSet.getId())
                .month(yearMonth.toString()) // "YYYY-MM"
                .baseSalary(baseSalary)
                .adjustmentCoefficient(performanceCoefficient)
                .performanceBonus(performanceBonus)
                .workBonus(workBonus)
                .workFine(workFine)
                .attendanceFine(attendanceFine)
                .insurance(insuranceDeduction)
                .total(totalSalary)
                .build();

        // 为避免重复计算，可以先查询当月是否已存在报表，若存在则更新，否则插入
        SalaryReport existingReport = salaryReportMapper.selectByEmployeeIdAndMonth(employee.getId(), yearMonth.toString());
        if (existingReport != null) {
            report.setId(existingReport.getId());
            report.setUpdateTime(LocalDateTime.now());
            salaryReportMapper.update(report);
            log.info("已更新员工 {} (ID: {}) {} 的薪资报表", employee.getName(), employee.getId(), yearMonth.toString());
        } else {
            salaryReportMapper.insert(report);
            log.info("已生成员工 {} (ID: {}) {} 的薪资报表", employee.getName(), employee.getId(), yearMonth.toString());
        }
    }


    /**
     * 责任链模式计算考勤扣款
     *
     * @param monthlyAttendance 当月考勤记录
     * @param baseSalary        月基本工资
     * @param department        部门信息（用于获取迟到早退扣款标准）
     * @param workDays          当月工作日天数
     * @return 考勤扣款总额
     */
    private BigDecimal calculateAttendanceDeduction(List<Attendance> monthlyAttendance, BigDecimal baseSalary, Department department, int workDays) {
        // 计算考勤记录中已存在的扣款金额
        BigDecimal existingDeduction = monthlyAttendance.stream().map(Attendance::getDeduction)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 获取缺勤、迟到、早退次数和对应的扣款金额
        long absentDays = PerformanceCoefficientStrategy.getAbsentDays(monthlyAttendance);
        long lateTimes = PerformanceCoefficientStrategy.getLateTimes(monthlyAttendance);
        long earlyTimes = PerformanceCoefficientStrategy.getEarlyTimes(monthlyAttendance);
        double leaveDays = PerformanceCoefficientStrategy.getLeaveDays(monthlyAttendance);
        BigDecimal absentDeduction = baseSalary.multiply(BigDecimal.valueOf(absentDays))
                .divide(BigDecimal.valueOf(workDays), RoundingMode.HALF_UP);

        AtomicReference<BigDecimal> lateDeduction = new AtomicReference<>(BigDecimal.ZERO);;
        AtomicReference<BigDecimal> earlyDeduction = new AtomicReference<>(BigDecimal.ZERO);;

        /*if (department != null) {
            if (department.getLateDeduction() != null) {
                lateDeduction = department.getLateDeduction();
            }
            if (department.getEarlyDeduction() != null) {
                earlyDeduction = department.getEarlyDeduction();
            }
        }*/

        Optional.ofNullable(department)
                .ifPresent(dept -> {
                    lateDeduction.set(Optional.ofNullable(dept.getLateDeduction()).orElse(BigDecimal.ZERO));
                    earlyDeduction.set(Optional.ofNullable(dept.getEarlyDeduction()).orElse(BigDecimal.ZERO));
                });


        // 设置考勤扣款计算顺序：缺勤 -> 迟到 -> 早退 -> 请假
        DeductionHandler absentHandler = new AbsentDeductionHandler();
        DeductionHandler lateHandler = new LateDeductionHandler();
        DeductionHandler earlyHandler = new EarlyDeductionHandler();
        absentHandler.setNext(lateHandler);
        lateHandler.setNext(earlyHandler);

        // 设置责任链上下文
        DeductionContext context = new DeductionContext(absentDays, lateTimes, earlyTimes, leaveDays, absentDeduction, lateDeduction.get(), earlyDeduction.get());

        // 使用责任链模式计算考勤扣款
        BigDecimal totalDeduction = absentHandler.handle(context, BigDecimal.ZERO);

        if (existingDeduction.compareTo(totalDeduction) != 0) {
            log.warn("考勤扣款计算结果与已存在的扣款不一致，已存在的扣款 {}, 计算结果 {}, 核算的考勤扣款 {}", existingDeduction, totalDeduction, totalDeduction);
        }

        log.debug("考勤扣款计算：缺勤天数 {}, 迟到早退等扣款 {}, 总扣款 {}", absentDays, totalDeduction, totalDeduction);
        return totalDeduction.setScale(2, RoundingMode.HALF_UP);
    }


    private BigDecimal calculateAttendanceDeductionOld(List<Attendance> monthlyAttendance, BigDecimal baseSalary, Department department, int workDays) {
        BigDecimal totalDeduction = BigDecimal.ZERO;
        BigDecimal existingDeduction = BigDecimal.ZERO;
        long absentDays = 0;


        // 统计缺勤天数和非缺勤日的迟到早退扣款
        for (Attendance att : monthlyAttendance) {
            // 已有的扣款
            existingDeduction = existingDeduction.add(att.getDeduction());
            boolean isAbsentToday = AttendanceConstant.CLOCK_ABSENT.equals(att.getClockInStatus()) || AttendanceConstant.CLOCK_ABSENT.equals(att.getClockOutStatus());
            if (isAbsentToday) {
                absentDays++;
            } else {
                // 非缺勤，计算迟到早退扣款
                if (department != null) {
                    if (AttendanceConstant.CLOCK_LATE.equals(att.getClockInStatus()) && department.getLateDeduction() != null) {
                        totalDeduction = totalDeduction.add(department.getLateDeduction());
                    }
                    if (AttendanceConstant.CLOCK_EARLY.equals(att.getClockOutStatus()) && department.getEarlyDeduction() != null) {
                        totalDeduction = totalDeduction.add(department.getEarlyDeduction());
                    }
                }
            }
        }

        // 计算缺勤扣款
        if (absentDays > 0 && workDays > 0) {
            BigDecimal absentDeduction = baseSalary
                    .multiply(BigDecimal.valueOf(absentDays))
                    .divide(BigDecimal.valueOf(workDays), 2, RoundingMode.HALF_UP);
            totalDeduction = totalDeduction.add(absentDeduction);
        }

        if (existingDeduction.compareTo(totalDeduction) != 0) {
            log.warn("考勤扣款计算结果与已存在的扣款不一致，已存在的扣款 {}, 计算结果 {}, 考勤扣款 {}", existingDeduction, totalDeduction, totalDeduction);
            // 以已存在的扣款为准
            return existingDeduction.setScale(2, RoundingMode.HALF_UP);
        }

        log.debug("考勤扣款计算：缺勤天数 {}, 迟到早退等扣款 {}, 总扣款 {}", absentDays, totalDeduction, totalDeduction);
        return totalDeduction.setScale(2, RoundingMode.HALF_UP);
    }
}

