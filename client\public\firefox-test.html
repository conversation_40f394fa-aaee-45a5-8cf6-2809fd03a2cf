<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox兼容性测试 - HRMS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #mapContainer {
            width: 100%;
            height: 300px;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Firefox兼容性测试页面</h1>
        
        <div class="status info">
            <strong>浏览器信息：</strong><span id="browserInfo"></span>
        </div>
        
        <div class="status" id="firefoxStatus">
            <strong>Firefox检测：</strong><span id="firefoxResult"></span>
        </div>
        
        <div class="status" id="amapStatus">
            <strong>高德地图API状态：</strong><span id="amapResult">检查中...</span>
        </div>
        
        <div>
            <button onclick="testAMapAPI()">测试高德地图API</button>
            <button onclick="testMapInit()">测试地图初始化</button>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="window.location.href='/'">返回主页</button>
        </div>
        
        <div id="mapContainer" style="display: none;"></div>
        
        <div class="log" id="logContainer"></div>
    </div>

    <!-- Firefox兼容性处理：仅针对高德地图统计错误 -->
    <script type="text/javascript">
        // 仅在Firefox中处理高德地图统计错误
        if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
            // 保存原始的错误处理器
            var originalOnError = window.onerror;
            
            window.onerror = function(message, source, lineno, colno, error) {
                // 只处理高德地图count统计请求的错误
                if (source && source.includes('webapi.amap.com/count?type=UIInit')) {
                    console.warn('Firefox: 已忽略高德地图统计错误');
                    addLog('Firefox: 已忽略高德地图统计错误', 'warning');
                    return true; // 阻止错误冒泡
                }
                
                // 其他错误交给原始处理器处理
                if (originalOnError) {
                    return originalOnError.call(this, message, source, lineno, colno, error);
                }
                return false;
            };
        }
    </script>

    <!-- 高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662"></script>
    <script type="text/javascript" src="https://webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>

    <script>
        let map = null;
        
        // 页面加载完成后执行
        window.onload = function() {
            initTest();
        };
        
        function initTest() {
            // 显示浏览器信息
            document.getElementById('browserInfo').textContent = navigator.userAgent;
            
            // 检测Firefox
            const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
            const firefoxStatus = document.getElementById('firefoxStatus');
            const firefoxResult = document.getElementById('firefoxResult');
            
            if (isFirefox) {
                firefoxStatus.className = 'status warning';
                firefoxResult.textContent = '是 (已启用兼容性处理)';
                addLog('检测到Firefox浏览器，已启用兼容性处理', 'warning');
            } else {
                firefoxStatus.className = 'status success';
                firefoxResult.textContent = '否';
                addLog('非Firefox浏览器，无需特殊处理', 'info');
            }
            
            // 检查高德地图API
            setTimeout(checkAMapAPI, 1000);
        }
        
        function checkAMapAPI() {
            const amapStatus = document.getElementById('amapStatus');
            const amapResult = document.getElementById('amapResult');
            
            if (typeof window.AMap !== 'undefined') {
                amapStatus.className = 'status success';
                amapResult.textContent = '可用 (版本: ' + (window.AMap.version || '未知') + ')';
                addLog('高德地图API加载成功', 'success');
            } else {
                amapStatus.className = 'status error';
                amapResult.textContent = '不可用';
                addLog('高德地图API加载失败', 'error');
            }
        }
        
        function testAMapAPI() {
            addLog('开始测试高德地图API...', 'info');
            
            if (typeof window.AMap === 'undefined') {
                addLog('错误: AMap对象不存在', 'error');
                return;
            }
            
            try {
                // 测试基本API
                const version = window.AMap.version || '未知版本';
                addLog('AMap版本: ' + version, 'success');
                
                // 测试创建LngLat对象
                const lngLat = new window.AMap.LngLat(116.397428, 39.90923);
                addLog('创建LngLat对象成功: ' + lngLat.toString(), 'success');
                
                // 测试Geolocation
                if (window.AMap.Geolocation) {
                    addLog('Geolocation插件可用', 'success');
                } else {
                    addLog('Geolocation插件不可用', 'warning');
                }
                
                addLog('高德地图API测试完成', 'success');
            } catch (error) {
                addLog('API测试失败: ' + error.message, 'error');
            }
        }
        
        function testMapInit() {
            addLog('开始测试地图初始化...', 'info');
            
            if (typeof window.AMap === 'undefined') {
                addLog('错误: AMap对象不存在，无法初始化地图', 'error');
                return;
            }
            
            try {
                // 显示地图容器
                document.getElementById('mapContainer').style.display = 'block';
                
                // 销毁现有地图
                if (map) {
                    map.destroy();
                    map = null;
                }
                
                // 创建新地图
                map = new AMap.Map('mapContainer', {
                    zoom: 10,
                    center: [116.397428, 39.90923]
                });
                
                addLog('地图初始化成功', 'success');
                
                // 测试地图方法
                setTimeout(function() {
                    try {
                        const center = map.getCenter();
                        addLog('地图中心点: ' + center.toString(), 'success');
                        
                        const zoom = map.getZoom();
                        addLog('地图缩放级别: ' + zoom, 'success');
                        
                        addLog('地图功能测试完成', 'success');
                    } catch (error) {
                        addLog('地图方法测试失败: ' + error.message, 'error');
                    }
                }, 1000);
                
            } catch (error) {
                addLog('地图初始化失败: ' + error.message, 'error');
            }
        }
        
        function addLog(message, type) {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = '<strong>' + time + ':</strong> ' + message;
            logEntry.style.color = getLogColor(type);
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到控制台
            const logMethod = type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'log';
            console[logMethod]('[Firefox测试] ' + time + ': ' + message);
        }
        
        function getLogColor(type) {
            switch(type) {
                case 'success': return '#28a745';
                case 'error': return '#dc3545';
                case 'warning': return '#ffc107';
                case 'info': return '#17a2b8';
                default: return '#333';
            }
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            console.clear();
            addLog('日志已清空', 'info');
        }
    </script>
</body>
</html>
