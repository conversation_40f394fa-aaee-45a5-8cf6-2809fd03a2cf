<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.salary.mapper.PositionMapper">
    <!-- 映射职位实体的所有字段 -->
    <resultMap id="BaseResultMap" type="cn.edu.ncu.hrms.common.entity.Position">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="level" property="level" />
        <result column="account_id" property="accountId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 根据职位ID查询职位详情 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM position WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询所有未删除的职位列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM position WHERE del_flag = 0 ORDER BY create_time DESC
    </select>

    <!-- 插入新职位 -->
    <insert id="insert" parameterType="cn.edu.ncu.hrms.common.entity.Position">
        INSERT INTO position (name, level, account_id, create_time, update_time, del_flag)
        VALUES (#{name}, #{level}, #{accountId}, NOW(), NOW(), 0)
    </insert>

    <!-- 更新职位信息 -->
    <update id="update" parameterType="cn.edu.ncu.hrms.common.entity.Position">
        UPDATE position
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="level != null">level = #{level},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 逻辑删除职位 -->
    <update id="deleteById">
        UPDATE position SET del_flag = null, update_time = NOW() WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 根据职位名称查询职位列表 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT * FROM position WHERE del_flag = 0
          AND level = #{level}
        ORDER BY name ASC
    </select>

    <!-- 根据职位所属账户查询职位列表 -->
    <select id="selectByAccountId" resultMap="BaseResultMap">
        SELECT * FROM position WHERE del_flag = 0
          AND account_id = #{accountId}
        ORDER BY name ASC
    </select>

    <!-- 根据关键词查询职位列表 -->
    <select id="selectByKeyword" resultMap="BaseResultMap">
        SELECT * FROM position WHERE del_flag = 0
          AND (name LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY name ASC
    </select>

    <!--条件查询-->
    <select id="selectListByCondition" resultType="cn.edu.ncu.hrms.common.entity.Position" parameterType="cn.edu.ncu.hrms.system.dto.SystemPositionPageQueryDTO">
        SELECT * FROM position
        <where>
            del_flag = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="name != null">
                AND name = #{name}
            </if>
        </where>
    </select>


    <select id="selectNameList" resultType="java.lang.String">
        SELECT DISTINCT name FROM position WHERE del_flag = 0
    </select>


    <select id="selectByNameAndLevel" resultType="cn.edu.ncu.hrms.common.entity.Position">
        SELECT * FROM position WHERE del_flag = 0
          AND name = #{name}
          AND level = #{level}
    </select>


    <select id="selectVOListByCondition" resultType="cn.edu.ncu.hrms.salary.vo.PositionVO">
        SELECT p.id AS id,
               p.name AS name,
               p.level AS level,
               p.account_id AS accountId,
               a.name AS accountName,
               p.create_time AS createTime,
               p.update_time AS updateTime
        FROM position p
        JOIN account_set a ON p.account_id = a.id
        <where>
            p.del_flag = 0
            AND a.del_flag = 0
            <if test="positionId != null">
                AND p.id = #{positionId}
            </if>
            <if test="positionName != null">
                AND p.name LIKE CONCAT('%', #{positionName}, '%')
            </if>
            <if test="accountId != null">
                AND a.id = #{accountId}
            </if>
            <if test="accountSetName != null">
                AND a.name LIKE CONCAT('%', #{accountSetName}, '%')
            </if>
        </where>
    </select>


</mapper>
