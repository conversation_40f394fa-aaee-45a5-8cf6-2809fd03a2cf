package cn.edu.ncu.hrms.salary.service.impl;

import cn.edu.ncu.hrms.common.entity.AccountSet;
import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.salary.dto.PositionPageQueryDTO;
import cn.edu.ncu.hrms.salary.mapper.AccountSetMapper;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.salary.service.PositionService;
import cn.edu.ncu.hrms.salary.vo.PositionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 职位服务实现类 (salary 模块)
 * 注意：根据 standard.mdc 规则，职位的主要增删改查管理功能应在 system 模块实现 (例如 SystemPositionServiceImpl)。
 * 此类仅实现 salary 模块内部或通用查询可能需要的职位相关方法。
 */
@Service
public class PositionServiceImpl implements PositionService {

    private static final Logger logger = LoggerFactory.getLogger(PositionServiceImpl.class);

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private AccountSetMapper accountSetMapper;

    /**
     * 根据ID获取职位信息
     *
     * @param id 职位ID
     * @return Result<Position> 包含职位信息的Result对象
     */
    @Override
    public Result<Position> getPositionById(Integer id) {
        logger.debug("根据ID获取职位信息, ID: {}", id);
        if (id == null) {
            return Result.error("职位ID不能为空");
        }
        Position position = positionMapper.selectById(id);
        if (position == null) {
            logger.warn("未找到职位信息, ID: {}", id);
            return Result.error("职位信息不存在");
        }
        return Result.success(position);
    }

    /**
     * 获取所有职位列表 (用于下拉选择等)
     *
     * @return Result<List<Position>> 包含职位列表的Result对象
     */
    @Override
    public Result<List<Position>> listAllPositions() {
        logger.debug("获取所有职位列表");
        List<Position> positions = positionMapper.selectList();
        logger.info("获取到 {} 条职位信息", positions.size());
        return Result.success(positions);
    }

    @Override
    public Result<List<String>> listAllPositionNames() {
        logger.debug("获取所有职位名称列表");
        List<String> names = positionMapper.selectNameList();
        logger.info("获取到 {} 条职位名称信息", names.size());
        return Result.success(names);
    }


    @Override
    public PageInfo<PositionVO> getPositionPage(PositionPageQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<PositionVO> voList = positionMapper.selectVOListByCondition(queryDTO);
        PageInfo<PositionVO> pageInfoVO = new PageInfo<>(voList);

        return pageInfoVO;
    }

    @Override
    public Result<Void> matchingAccountSet(Integer positionId, Integer accountId) {
        Position position = positionMapper.selectById(positionId);
        if (position == null) {
            return Result.error("职位不存在");
        }
        AccountSet accountSet = accountSetMapper.selectById(accountId);
        if (accountSet == null) {
            return Result.error("账套不存在");
        }

        position.setAccountId(accountId);
        int result = positionMapper.update(position);
        if (result > 0) {
            logger.info("职位 {} 与账套 {} 匹配成功", position.getName(), accountSet.getName());
            return Result.success();
        }

        logger.warn("职位 {} 与账套 {} 匹配失败，职位ID: {}", position.getName(), accountSet.getName(), positionId);
        return Result.error("账套匹配失败，请稍后重试");
    }
}
