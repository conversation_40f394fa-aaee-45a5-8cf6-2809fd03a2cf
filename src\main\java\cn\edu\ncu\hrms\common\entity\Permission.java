package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("系统权限")
public class Permission {
    @ApiModelProperty(value = "权限ID", required = true, example = "101")
    private Integer id;

    @ApiModelProperty(value = "权限标识", required = true, example = "system:user:add")
    private String permissionKey;

    @ApiModelProperty(value = "权限名称", required = true, example = "添加用户")
    private String permissionName;

    @ApiModelProperty(value = "权限描述", required = true, example = "管理员手动添加用户")
    private String description;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}