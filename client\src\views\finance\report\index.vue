<template>
  <div class="app-container">
    <!-- 页面标题卡片 -->
    <el-card class="page-header-card">
      <div class="page-header">
        <h2>薪资报表</h2>
        <div class="header-actions">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="self">个人薪资</el-radio-button>
            <el-radio-button label="employee" v-if="hasSubordinates">员工薪资</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </el-card>

    <!-- 内容卡片 - 个人薪资视图 -->
    <el-card v-if="viewMode === 'self'" class="content-card" shadow="hover">
      <div slot="header" class="card-header-self">
        <span><i class="el-icon-money"></i>我的薪资报表</span>
      </div>

      <!-- 搜索区域 -->
      <div class="filter-form">
        <el-form :inline="true" :model="selfQueryParams" class="demo-form-inline">
          <el-form-item label="月份范围">
            <el-date-picker
              v-model="selfDateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              format="yyyy年MM月"
              style="width: 280px;"
              :picker-options="monthPickerOptions"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getSelfSalaryList">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetSelfQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="selfLoading"
        :data="selfSalaryList"
        border
        style="width: 100%; margin-top: 15px;"
      >
        <el-table-column prop="month" label="月份" min-width="100" align="center" />
        <el-table-column prop="adjustmentCoefficient" label="考核系数" min-width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.adjustmentCoefficient }}
          </template>
        </el-table-column>
        <el-table-column prop="baseSalary" label="基本工资" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="income-value">{{ formatCurrency(scope.row.baseSalary) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="performanceBonus" label="绩效奖金" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="income-value">{{ formatCurrency(scope.row.performanceBonus) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workBonus" label="工作奖金" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="income-value">{{ formatCurrency(scope.row.workBonus) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workFine" label="工作罚款" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="deduction-value">{{ formatCurrency(scope.row.workFine) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="attendanceFine" label="考勤罚款" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="deduction-value">{{ formatCurrency(scope.row.attendanceFine) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="insurance" label="社保扣除" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="deduction-value">{{ formatCurrency(scope.row.insurance) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total" label="应发总额" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="net-salary">{{ formatCurrency(scope.row.total) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleSelfSizeChange"
        @current-change="handleSelfCurrentChange"
        :current-page="selfQueryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="selfQueryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="selfTotal"
        style="margin-top: 15px; text-align: right;"
      />
    </el-card>

    <!-- 内容卡片 - 员工薪资视图 -->
    <el-card v-else-if="viewMode === 'employee'" class="content-card" shadow="hover">
      <div slot="header" class="card-header">
        <h3>
          <i class="el-icon-money"></i>
          <!-- 获取用户角色信息 -->
          <span v-if="isHR">关联员工薪资</span>
          <!-- 只有当用户没有HR角色但有Leader角色时才显示Leader标题 -->
          <span v-else-if="isLeader">部门员工薪资</span>
          <span v-else>员工薪资</span>

          <!-- 操作按钮 -->
          <div class="header-buttons">
            <el-button type="success" icon="el-icon-download" @click="exportEmployeeSalary">导出Excel</el-button>
          </div>
        </h3>
      </div>

      <!-- 搜索区域 -->
      <div class="filter-form">
        <el-form :inline="true" :model="employeeQueryParams" class="demo-form-inline">
          <el-form-item label="员工姓名">
            <el-input
              v-model="employeeQueryParams.employeeName"
              placeholder="请输入员工姓名"
              clearable
              style="width: 180px;"
              @keyup.enter.native="getEmployeeSalaryList"
            />
          </el-form-item>
          <el-form-item label="工号">
            <el-input
              v-model="employeeQueryParams.workNo"
              placeholder="请输入工号"
              clearable
              style="width: 180px;"
              @keyup.enter.native="getEmployeeSalaryList"
            />
          </el-form-item>
          <el-form-item label="月份范围">
            <el-date-picker
              v-model="employeeDateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              format="yyyy年MM月"
              style="width: 280px;"
              :picker-options="monthPickerOptions"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getEmployeeSalaryList">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetEmployeeQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="employeeLoading"
        :data="employeeSalaryList"
        border
        style="width: 100%; margin-top: 15px;"
      >
        <el-table-column prop="workNo" label="工号" min-width="120" align="center" />
        <el-table-column prop="employeeName" label="姓名" min-width="100" align="center" />
        <el-table-column prop="departmentName" label="部门" min-width="120" align="center" />
        <el-table-column prop="month" label="月份" min-width="100" align="center" />
        <el-table-column v-if="isHR" prop="accountId" label="账套ID" min-width="80" align="center" />
        <el-table-column prop="adjustmentCoefficient" label="考核系数" min-width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.adjustmentCoefficient }}
          </template>
        </el-table-column>
        <el-table-column prop="baseSalary" label="基本工资" min-width="110" align="center">
          <template slot-scope="scope">
            <span class="income-value">{{ formatCurrency(scope.row.baseSalary) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="performanceBonus" label="绩效奖金" min-width="100" align="center">
          <template slot-scope="scope">
            <span class="income-value">{{ formatCurrency(scope.row.performanceBonus) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workBonus" label="工作奖金" min-width="100" align="center">
          <template slot-scope="scope">
            <span class="income-value">{{ formatCurrency(scope.row.workBonus) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workFine" label="工作罚款" min-width="100" align="center">
          <template slot-scope="scope">
            <span class="deduction-value">{{ formatCurrency(scope.row.workFine) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="attendanceFine" label="考勤罚款" min-width="100" align="center">
          <template slot-scope="scope">
            <span class="deduction-value">{{ formatCurrency(scope.row.attendanceFine) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="insurance" label="社保扣除" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="deduction-value">{{ formatCurrency(scope.row.insurance) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total" label="应发总额" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="net-salary">{{ formatCurrency(scope.row.total) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleEmployeeSizeChange"
        @current-change="handleEmployeeCurrentChange"
        :current-page="employeeQueryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="employeeQueryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="employeeTotal"
        style="margin-top: 15px; text-align: right;"
      />
    </el-card>
  </div>
</template>

<script>
import { getSelfSalaryReport, pageSalaryReports, exportSalaryReport } from '@/api/finance/report'
import { mapGetters } from 'vuex'

export default {
  name: 'FinanceReport',
  data() {
    return {
      // 视图模式：个人薪资/员工薪资
      viewMode: 'self',

      // 用户角色信息
      isHR: false,
      isLeader: false,

      // 个人薪资相关数据
      selfLoading: false,
      selfSalaryList: [],
      selfTotal: 0,
      selfDateRange: [],
      selfQueryParams: {
        pageNum: 1,
        pageSize: 10
      },

      // 员工薪资相关数据
      employeeLoading: false,
      employeeSalaryList: [],
      employeeTotal: 0,
      employeeDateRange: [],
      employeeQueryParams: {
        pageNum: 1,
        pageSize: 10,
        employeeName: '',
        workNo: '',
        startMonth: '',
        endMonth: ''
      },

      // 日期选择器配置 - 限制只能选择当月之前的月份
      monthPickerOptions: {
        disabledDate(date) {
          // 获取当前月份的第一天
          const currentMonth = new Date()
          currentMonth.setDate(1)
          currentMonth.setHours(0, 0, 0, 0)

          // 禁用当月及之后的月份
          return date.getTime() >= currentMonth.getTime()
        }
      }
    }
  },
  computed: {
    ...mapGetters({
      userRoles: 'userInfo/roles'
    }),
    // 判断是否有下属（Leader或HR角色）
    hasSubordinates() {
      return this.userRoles && this.userRoles.some(role => role === 'ROLE_leader' || role === 'ROLE_hr')
    }
  },
  created() {
    // 初始化用户角色信息
    this.getUserRole()

    // 初始化日期范围 - 默认查询上个月
    // this.initDateRange()

    // 如果有下属且没有指定视图模式，默认显示员工薪资视图
    if (this.hasSubordinates && this.viewMode === 'self') {
      this.viewMode = 'employee'
    }
    this.loadData()
  },
  watch: {
    viewMode() {
      this.loadData()
    }
  },
  methods: {
    // 获取用户角色信息 - 从Vuex中获取，不再调用API
    getUserRole() {
      // 从Vuex中获取用户角色信息
      const roles = this.$store.getters['userInfo/roles'] || []

      // 判断用户角色
      this.isHR = roles.includes('ROLE_hr')
      // 如果同时拥有HR和Leader角色，则只显示HR角色的内容
      this.isLeader = roles.includes('ROLE_leader') && !this.isHR

      console.log('从Vuex获取用户角色信息:', {
        roles: roles,
        isHR: this.isHR,
        isLeader: this.isLeader,
        hasSubordinates: this.hasSubordinates
      })
    },

    // 初始化日期范围 - 默认查询上个月
    initDateRange() {
      // 获取当前日期
      const now = new Date()

      // 获取上个月的年月
      let lastMonth = new Date(now)
      lastMonth.setMonth(now.getMonth() - 1)
      const lastMonthStr = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`

      // 获取六个月前的年月
      let sixMonthsAgo = new Date(now)
      sixMonthsAgo.setMonth(now.getMonth() - 6)
      const sixMonthsAgoStr = `${sixMonthsAgo.getFullYear()}-${String(sixMonthsAgo.getMonth() + 1).padStart(2, '0')}`

      // 设置默认日期范围：六个月前到上个月
      this.selfDateRange = [sixMonthsAgoStr, lastMonthStr]
      this.employeeDateRange = [sixMonthsAgoStr, lastMonthStr]
    },

    // 加载数据
    loadData() {
      if (this.viewMode === 'self') {
        this.getSelfSalaryList()
      } else {
        this.getEmployeeSalaryList()
      }
    },

    // 格式化货币
    formatCurrency(value) {
      if (value === undefined || value === null) {
        return '¥0.00'
      }
      return '¥' + parseFloat(value).toFixed(2)
    },

    // ========== 个人薪资相关方法 ==========
    // 获取个人薪资列表
    getSelfSalaryList() {
      this.selfLoading = true

      // 构建查询参数
      const params = {
        pageNum: this.selfQueryParams.pageNum,
        pageSize: this.selfQueryParams.pageSize
      }

      // 添加日期范围参数 - 只有当有值时才添加
      if (this.selfDateRange && this.selfDateRange.length === 2) {
        if (this.selfDateRange[0]) {
          params.startMonth = this.selfDateRange[0]
        }
        if (this.selfDateRange[1]) {
          params.endMonth = this.selfDateRange[1]
        }
      }

      getSelfSalaryReport(params).then(response => {
        // 处理后端返回的数据格式，数据在 data.list 中
        this.selfSalaryList = response.data.list || []
        this.selfTotal = response.data.total || 0
        this.selfLoading = false
        console.log('个人薪资数据:', this.selfSalaryList)
      }).catch(error => {
        console.error('获取个人薪资列表失败', error)
        this.selfLoading = false
      })
    },

    // 重置个人薪资查询条件
    resetSelfQuery() {
      // 重新初始化日期范围
      // this.initDateRange()
      this.selfDateRange = []
      this.selfQueryParams = {
        pageNum: 1,
        pageSize: 10
      }
      this.getSelfSalaryList()
    },

    // 个人薪资分页大小变化
    handleSelfSizeChange(size) {
      this.selfQueryParams.pageSize = size
      this.getSelfSalaryList()
    },

    // 个人薪资页码变化
    handleSelfCurrentChange(page) {
      this.selfQueryParams.pageNum = page
      this.getSelfSalaryList()
    },

    // ========== 员工薪资相关方法 ==========
    // 获取员工薪资列表
    getEmployeeSalaryList() {
      this.employeeLoading = true

      // 构建查询参数 - 只包含分页参数
      const params = {
        pageNum: this.employeeQueryParams.pageNum,
        pageSize: this.employeeQueryParams.pageSize
      }

      // 添加其他查询参数 - 只有当有值时才添加
      if (this.employeeQueryParams.employeeName) {
        params.employeeName = this.employeeQueryParams.employeeName
      }

      if (this.employeeQueryParams.workNo) {
        params.workNo = this.employeeQueryParams.workNo
      }

      // 添加日期范围参数 - 只有当有值时才添加
      if (this.employeeDateRange && this.employeeDateRange.length === 2) {
        if (this.employeeDateRange[0]) {
          params.startMonth = this.employeeDateRange[0]
        }
        if (this.employeeDateRange[1]) {
          params.endMonth = this.employeeDateRange[1]
        }
      }

      pageSalaryReports(params).then(response => {
        // 处理后端返回的数据格式，数据在 data.list 中
        this.employeeSalaryList = response.data.list || []
        this.employeeTotal = response.data.total || 0
        this.employeeLoading = false
        console.log('员工薪资数据:', this.employeeSalaryList)
      }).catch(error => {
        console.error('获取员工薪资列表失败', error)
        this.employeeLoading = false
      })
    },

    // 重置员工薪资查询条件
    resetEmployeeQuery() {
      // 重新初始化日期范围
      // this.initDateRange()

      this.employeeDateRange = []
      this.employeeQueryParams = {
        pageNum: 1,
        pageSize: 10,
        employeeName: '',
        workNo: '',
        startMonth: '',
        endMonth: ''
      }
      this.getEmployeeSalaryList()
    },

    // 员工薪资分页大小变化
    handleEmployeeSizeChange(size) {
      this.employeeQueryParams.pageSize = size
      this.getEmployeeSalaryList()
    },

    // 员工薪资页码变化
    handleEmployeeCurrentChange(page) {
      this.employeeQueryParams.pageNum = page
      this.getEmployeeSalaryList()
    },

    // 导出员工薪资报表
    exportEmployeeSalary() {
      // 构建查询参数 - 初始为空对象
      const params = {}

      // 添加其他查询参数 - 只有当有值时才添加
      if (this.employeeQueryParams.employeeName) {
        params.employeeName = this.employeeQueryParams.employeeName
      }

      if (this.employeeQueryParams.workNo) {
        params.workNo = this.employeeQueryParams.workNo
      }

      // 添加日期范围参数 - 只有当有值时才添加
      if (this.employeeDateRange && this.employeeDateRange.length === 2) {
        if (this.employeeDateRange[0]) {
          params.startMonth = this.employeeDateRange[0]
        }
        if (this.employeeDateRange[1]) {
          params.endMonth = this.employeeDateRange[1]
        }
      }

      this.$confirm('确认导出所选薪资报表数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          message: '导出请求已提交，文件生成中',
          type: 'info',
          duration: 1500
        })

        exportSalaryReport(params).then(response => {
          // 创建a标签模拟点击下载
          const blob = new Blob([response])
          const fileName = `薪资报表_${new Date().getTime()}.xlsx`
          if ('download' in document.createElement('a')) {
            const link = document.createElement('a')
            link.download = fileName
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href)
            document.body.removeChild(link)
          } else {
            navigator.msSaveBlob(blob, fileName)
          }
        }).catch(error => {
          console.error('导出薪资报表失败', error)
          this.$message.error('导出失败，请重试')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header-card {
  margin-bottom: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.content-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-header-self {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 20px;

    span {
      font-size: 18px;
      font-weight: 600;

      i {
        margin-right: 5px;
      }
    }

    .card-actions {
      display: flex;
      gap: 10px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 18px;
      font-weight: 600;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }

    .card-actions {
      display: flex;
      gap: 10px;
    }
  }

  .filter-form {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    height: 70px;
  }

  .net-salary {
    color: #409EFF;
    font-weight: bold;
  }

  .income-value {
    color: #67C23A; /* 绿色 - Element UI 的 success 颜色 */
    font-weight: normal;
  }

  .deduction-value {
    color: #F56C6C; /* 红色 - Element UI 的 danger 颜色 */
    font-weight: normal;
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .el-form-item {
    margin-bottom: 10px;
  }
}


</style>
