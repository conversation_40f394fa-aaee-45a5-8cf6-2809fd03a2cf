package cn.edu.ncu.hrms.employee.service;

import cn.edu.ncu.hrms.common.entity.ResignRequest;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.employee.dto.ResignApplyDTO;
import cn.edu.ncu.hrms.employee.dto.ResignApproveDTO;
import cn.edu.ncu.hrms.employee.dto.ResignPageQueryDTO;
import cn.edu.ncu.hrms.employee.vo.ResignRequestVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 离职申请服务接口
 * 
 * 负责处理员工离职申请、审批、查询等相关业务逻辑。
 */
public interface ResignRequestService {

    /**
     * 提交离职申请
     *
     * @param applyDTO 申请信息 DTO
     * @return 操作结果
     */
    Result<Void> apply(ResignApplyDTO applyDTO);

    /**
     * 审批离职申请
     *
     * @param requestId 离职申请ID
     * @param approveDTO 审批信息 DTO
     * @return 操作结果
     */
    Result<Void> approve(Integer requestId, ResignApproveDTO approveDTO);

    /**
     * 取消离职申请 (申请人操作)
     *
     * @param requestId 离职申请ID
     * @return 操作结果
     */
    Result<Void> cancel(Integer requestId);

    /**
     * 根据ID获取离职申请详情 VO
     *
     * @param requestId 离职申请ID
     * @return 离职申请详情 VO
     */
    Result<ResignRequestVO> getVOById(Integer requestId);

    /**
     * 分页查询离职申请 VO
     *
     * @param queryDTO 查询条件 DTO
     * @return 分页结果
     */
    PageInfo<ResignRequestVO> pageVOs(ResignPageQueryDTO queryDTO);

    /**
     * 根据离职申请ID获取申请人员工ID (用于权限校验)
     *
     * @param requestId 离职申请ID
     * @return 申请人员工ID，如果申请不存在则返回 null
     */
    Integer getEmployeeIdByRequestId(Integer requestId);


    /**
     * 获取当前登录员工的离职申请列表
     * @return
     */
    Result<List<ResignRequest>> getSelfResignList();

}
