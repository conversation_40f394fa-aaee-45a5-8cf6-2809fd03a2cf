{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport './plugins/element.js';\nimport moment from 'moment';\nimport 'normalize.css/normalize.css'; // 重置CSS样式\nimport '@/styles/index.scss'; // 全局样式\n\nimport './icons'; // 图标\nimport './permission'; // 权限控制\nimport { initFirefoxCompatibility } from '@/utils/firefox-compatibility'; // Firefox兼容性处理\n\n// 初始化Firefox兼容性处理\ninitFirefoxCompatibility();\n\n// Firefox兼容性：全局错误处理，特别针对高德地图API\nVue.config.errorHandler = function (err, vm, info) {\n  // 检查是否是高德地图相关错误\n  if (err.message && (err.message.includes('AMap') || err.message.includes('amap') || err.message.includes('webapi.amap.com'))) {\n    console.warn('高德地图相关错误（已忽略）:', err.message, info);\n    return;\n  }\n  // 其他错误正常处理\n  console.error('Vue应用错误:', err, info);\n};\n\n// 初始化token过期状态\n// 确保在应用启动时就创建这个全局变量，防止出现未定义的情况\nif (!window.tokenExpiredState) {\n  window.tokenExpiredState = {\n    messageShown: false,\n    lastShownTimestamp: 0,\n    cooldownPeriod: 3000\n  };\n}\n\n// 创建事件总线\nVue.prototype.$bus = new Vue();\n\n// 全局组件\nimport TreeSelect from '@/components/TreeSelect';\nVue.component('el-tree-select', TreeSelect);\n\n// 注册全局指令\nVue.directive('permission', {\n  inserted(el, binding) {\n    const permissions = store.getters.permissions;\n    if (permissions && permissions.length > 0) {\n      const hasPermission = permissions.some(permission => {\n        return permission === binding.value;\n      });\n      if (!hasPermission) {\n        el.parentNode && el.parentNode.removeChild(el);\n      }\n    }\n  }\n});\n\n// 注册全局过滤器\nVue.filter('dateFormat', function (value, format = 'YYYY-MM-DD HH:mm:ss') {\n  if (!value) return '';\n  return moment(value).format(format);\n});\n\n// 注册全局方法\nVue.prototype.$download = function (res, fileName) {\n  if (!res) return;\n  const blob = new Blob([res]);\n  const link = document.createElement('a');\n  link.href = window.URL.createObjectURL(blob);\n  link.download = fileName;\n  link.click();\n  window.URL.revokeObjectURL(link.href);\n};\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "moment", "initFirefoxCompatibility", "config", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "info", "message", "includes", "console", "warn", "error", "window", "tokenExpiredState", "messageShown", "lastShownTimestamp", "cooldownPeriod", "prototype", "$bus", "TreeSelect", "component", "directive", "inserted", "el", "binding", "permissions", "getters", "length", "hasPermission", "some", "permission", "value", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "filter", "format", "$download", "res", "fileName", "blob", "Blob", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "click", "revokeObjectURL", "productionTip", "render", "h", "$mount"], "sources": ["D:/Desktop/GP/HRMS/client/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport './plugins/element.js'\r\nimport moment from 'moment'\r\n\r\nimport 'normalize.css/normalize.css' // 重置CSS样式\r\nimport '@/styles/index.scss' // 全局样式\r\n\r\nimport './icons' // 图标\r\nimport './permission' // 权限控制\r\nimport { initFirefoxCompatibility } from '@/utils/firefox-compatibility' // Firefox兼容性处理\r\n\r\n// 初始化Firefox兼容性处理\r\ninitFirefoxCompatibility()\r\n\r\n// Firefox兼容性：全局错误处理，特别针对高德地图API\r\nVue.config.errorHandler = function (err, vm, info) {\r\n  // 检查是否是高德地图相关错误\r\n  if (err.message && (err.message.includes('AMap') || err.message.includes('amap') || err.message.includes('webapi.amap.com'))) {\r\n    console.warn('高德地图相关错误（已忽略）:', err.message, info)\r\n    return\r\n  }\r\n  // 其他错误正常处理\r\n  console.error('Vue应用错误:', err, info)\r\n}\r\n\r\n// 初始化token过期状态\r\n// 确保在应用启动时就创建这个全局变量，防止出现未定义的情况\r\nif (!window.tokenExpiredState) {\r\n  window.tokenExpiredState = {\r\n    messageShown: false,\r\n    lastShownTimestamp: 0,\r\n    cooldownPeriod: 3000\r\n  }\r\n}\r\n\r\n// 创建事件总线\r\nVue.prototype.$bus = new Vue()\r\n\r\n// 全局组件\r\nimport TreeSelect from '@/components/TreeSelect'\r\nVue.component('el-tree-select', TreeSelect)\r\n\r\n// 注册全局指令\r\nVue.directive('permission', {\r\n  inserted(el, binding) {\r\n    const permissions = store.getters.permissions\r\n    if (permissions && permissions.length > 0) {\r\n      const hasPermission = permissions.some(permission => {\r\n        return permission === binding.value\r\n      })\r\n      if (!hasPermission) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    }\r\n  }\r\n})\r\n\r\n// 注册全局过滤器\r\nVue.filter('dateFormat', function(value, format = 'YYYY-MM-DD HH:mm:ss') {\r\n  if (!value) return ''\r\n  return moment(value).format(format)\r\n})\r\n\r\n// 注册全局方法\r\nVue.prototype.$download = function(res, fileName) {\r\n  if (!res) return\r\n  const blob = new Blob([res])\r\n  const link = document.createElement('a')\r\n  link.href = window.URL.createObjectURL(blob)\r\n  link.download = fileName\r\n  link.click()\r\n  window.URL.revokeObjectURL(link.href)\r\n}\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n"], "mappings": ";;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,sBAAsB;AAC7B,OAAOC,MAAM,MAAM,QAAQ;AAE3B,OAAO,6BAA6B,EAAC;AACrC,OAAO,qBAAqB,EAAC;;AAE7B,OAAO,SAAS,EAAC;AACjB,OAAO,cAAc,EAAC;AACtB,SAASC,wBAAwB,QAAQ,+BAA+B,EAAC;;AAEzE;AACAA,wBAAwB,CAAC,CAAC;;AAE1B;AACAL,GAAG,CAACM,MAAM,CAACC,YAAY,GAAG,UAAUC,GAAG,EAAEC,EAAE,EAAEC,IAAI,EAAE;EACjD;EACA,IAAIF,GAAG,CAACG,OAAO,KAAKH,GAAG,CAACG,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAIJ,GAAG,CAACG,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAIJ,GAAG,CAACG,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;IAC5HC,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAEN,GAAG,CAACG,OAAO,EAAED,IAAI,CAAC;IACjD;EACF;EACA;EACAG,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEP,GAAG,EAAEE,IAAI,CAAC;AACtC,CAAC;;AAED;AACA;AACA,IAAI,CAACM,MAAM,CAACC,iBAAiB,EAAE;EAC7BD,MAAM,CAACC,iBAAiB,GAAG;IACzBC,YAAY,EAAE,KAAK;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,cAAc,EAAE;EAClB,CAAC;AACH;;AAEA;AACApB,GAAG,CAACqB,SAAS,CAACC,IAAI,GAAG,IAAItB,GAAG,CAAC,CAAC;;AAE9B;AACA,OAAOuB,UAAU,MAAM,yBAAyB;AAChDvB,GAAG,CAACwB,SAAS,CAAC,gBAAgB,EAAED,UAAU,CAAC;;AAE3C;AACAvB,GAAG,CAACyB,SAAS,CAAC,YAAY,EAAE;EAC1BC,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAE;IACpB,MAAMC,WAAW,GAAG1B,KAAK,CAAC2B,OAAO,CAACD,WAAW;IAC7C,IAAIA,WAAW,IAAIA,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMC,aAAa,GAAGH,WAAW,CAACI,IAAI,CAACC,UAAU,IAAI;QACnD,OAAOA,UAAU,KAAKN,OAAO,CAACO,KAAK;MACrC,CAAC,CAAC;MACF,IAAI,CAACH,aAAa,EAAE;QAClBL,EAAE,CAACS,UAAU,IAAIT,EAAE,CAACS,UAAU,CAACC,WAAW,CAACV,EAAE,CAAC;MAChD;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA3B,GAAG,CAACsC,MAAM,CAAC,YAAY,EAAE,UAASH,KAAK,EAAEI,MAAM,GAAG,qBAAqB,EAAE;EACvE,IAAI,CAACJ,KAAK,EAAE,OAAO,EAAE;EACrB,OAAO/B,MAAM,CAAC+B,KAAK,CAAC,CAACI,MAAM,CAACA,MAAM,CAAC;AACrC,CAAC,CAAC;;AAEF;AACAvC,GAAG,CAACqB,SAAS,CAACmB,SAAS,GAAG,UAASC,GAAG,EAAEC,QAAQ,EAAE;EAChD,IAAI,CAACD,GAAG,EAAE;EACV,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,GAAG,CAAC,CAAC;EAC5B,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACxCF,IAAI,CAACG,IAAI,GAAGhC,MAAM,CAACiC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;EAC5CE,IAAI,CAACM,QAAQ,GAAGT,QAAQ;EACxBG,IAAI,CAACO,KAAK,CAAC,CAAC;EACZpC,MAAM,CAACiC,GAAG,CAACI,eAAe,CAACR,IAAI,CAACG,IAAI,CAAC;AACvC,CAAC;AAEDhD,GAAG,CAACM,MAAM,CAACgD,aAAa,GAAG,KAAK;AAEhC,IAAItD,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLoD,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACvD,GAAG;AACpB,CAAC,CAAC,CAACwD,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}