package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
// 移除未使用的导入
// import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

// 移除未使用的导入
// import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新权限数据传输对象 (DTO) - 根据 Permission.java 实体调整 (无可用字段)
 */
@Data
@ApiModel(value="PermissionUpdateDTO", description="更新权限数据传输对象")
public class PermissionUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权限ID")
    private Integer id;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "权限标识", notes = "例如: system:user:add")
    private String permissionKey;

    @ApiModelProperty(value = "权限描述")
    private String description;

} 