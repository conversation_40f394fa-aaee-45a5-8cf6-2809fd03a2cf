package cn.edu.ncu.hrms.employee.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 提交离职申请 DTO
 */
@Data
@ApiModel(value="ResignApplyDTO", description="提交离职申请数据")
public class ResignApplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "期望离职日期", required = true)
    @NotNull(message = "期望离职日期不能为空")
    @FutureOrPresent(message = "期望离职日期不能是过去")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate resignDate;

    @ApiModelProperty(value = "离职原因", required = true)
    @NotBlank(message = "离职原因不能为空")
    @Size(max = 500, message = "离职原因长度不能超过500个字符")
    private String reason;

} 