<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.salary.mapper.AccountSetMapper">


    <sql id="Base_Column_List">
        id, name, base_salary, performance_rate, insurance_rate,
        create_time, update_time, del_flag
    </sql>

    <!-- 根据ID查询薪资账套 -->
    <select id="selectById" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/> FROM account_set WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询所有薪资账套列表 -->
    <select id="selectList" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/> FROM account_set WHERE del_flag = 0 ORDER BY create_time DESC
    </select>

    <!-- 新增薪资账套 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO account_set (
            name,
            base_salary,
            performance_rate,
            insurance_rate,
            create_time,
            update_time,
            del_flag
        ) VALUES (
            #{name},
            #{baseSalary},
            #{performanceRate},
            #{insuranceRate},
            NOW(),
            NOW(),
             0
        )
    </insert>

    <!-- 更新薪资账套 -->
    <update id="update">
        UPDATE account_set
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="baseSalary != null">
                base_salary = #{baseSalary},
            </if>
            <if test="performanceRate != null">
                performance_rate = #{performanceRate},
            </if>
            <if test="insuranceRate != null">
                insurance_rate = #{insuranceRate},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 删除薪资账套（逻辑删除） -->
    <update id="deleteById">
        UPDATE account_set SET del_flag = 1, update_time = NOW() WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 根据基本工资范围查询薪资账套 -->
    <select id="selectBySalaryRange" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/>
        FROM account_set
        WHERE del_flag = 0
          AND base_salary BETWEEN #{minSalary} AND #{maxSalary}
        ORDER BY base_salary ASC
    </select>

    <!-- 模糊查询薪资账套（根据账套名称） -->
    <select id="searchByName" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/>
        FROM account_set
        WHERE del_flag = 0
          AND name LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 根据职位ID查询账套信息 -->
    <select id="selectByPositionId" resultType="AccountSet">
        SELECT acs.*
        FROM account_set acs
        JOIN position p ON acs.id = p.account_id
        WHERE p.id = #{positionId}
        AND acs.del_flag = 0
        AND p.del_flag = 0
    </select>

    <!-- 根据名称查询 -->
    <select id="selectByName" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/> FROM account_set WHERE name = #{name} AND del_flag = 0
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/> FROM account_set WHERE del_flag = 0 ORDER BY create_time DESC
    </select>

    <!-- 根据条件查询 (配合 PageHelper) -->
    <select id="selectByCondition" parameterType="cn.edu.ncu.hrms.salary.dto.AccountSetPageQueryDTO" resultType="AccountSet">
        SELECT <include refid="Base_Column_List"/>
        FROM account_set
        <where>
            del_flag = 0
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>