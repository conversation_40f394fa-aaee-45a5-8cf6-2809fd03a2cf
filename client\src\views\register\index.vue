<template>
  <div class="register-container">
    <div class="register-box">
      <div class="register-left">
        <div class="welcome-text">
          <h2>欢迎加入</h2>
          <h1>HRMS 企业人事管理系统</h1>
          <p>完成注册，开启企业人事管理之旅</p>
        </div>
      </div>
      <div class="register-right">
        <div class="register-form-container">
          <div class="title-container">
            <h3 class="title">员工注册</h3>
          </div>

          <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form" autocomplete="on" label-position="left">
            <el-form-item prop="username">
              <span class="svg-container">
                <i class="el-icon-user" />
              </span>
              <el-input
                ref="username"
                v-model="registerForm.username"
                placeholder="请输入用户名"
                name="username"
                type="text"
                tabindex="1"
                autocomplete="on"
              />
            </el-form-item>

            <el-form-item prop="password">
              <span class="svg-container">
                <i class="el-icon-lock" />
              </span>
              <el-input
                :key="passwordType"
                ref="password"
                v-model="registerForm.password"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                autocomplete="on"
              />
              <span class="show-pwd" @click="showPwd">
                <i :class="passwordType === 'password' ? 'el-icon-view' : 'el-icon-hide'" />
              </span>
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <span class="svg-container">
                <i class="el-icon-lock" />
              </span>
              <el-input
                :key="confirmPasswordType"
                ref="confirmPassword"
                v-model="registerForm.confirmPassword"
                :type="confirmPasswordType"
                placeholder="请再次输入密码"
                name="confirmPassword"
                tabindex="3"
                autocomplete="on"
              />
              <span class="show-pwd" @click="showConfirmPwd">
                <i :class="confirmPasswordType === 'password' ? 'el-icon-view' : 'el-icon-hide'" />
              </span>
            </el-form-item>

            <el-form-item prop="employeeId">
              <span class="svg-container">
                <i class="el-icon-postcard" />
              </span>
              <el-input
                ref="employeeId"
                v-model="registerForm.employeeId"
                placeholder="请输入工号"
                name="employeeId"
                type="text"
                tabindex="4"
                autocomplete="on"
              />
            </el-form-item>

            <div class="warning-tips">
              <i class="el-icon-warning-outline"></i>
              <span>注意：只有在系统中已存在员工信息的员工才能注册</span>
            </div>

            <el-button :loading="loading" type="primary" class="register-button" @click.native.prevent="handleRegister">注册</el-button>

            <div class="tips">
              <span>已有账号？</span>
              <router-link to="/login">
                <span class="login-link">立即登录</span>
              </router-link>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import { register } from '@/api/user'

export default {
  name: 'Register',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请输入正确的用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于6位'))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    const validateEmployeeId = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入工号'))
      } else {
        callback()
      }
    }
    return {
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        employeeId: ''
      },
      registerRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        confirmPassword: [{ required: true, trigger: 'blur', validator: validateConfirmPassword }],
        employeeId: [{ required: true, trigger: 'blur', validator: validateEmployeeId }]
      },
      loading: false,
      passwordType: 'password',
      confirmPasswordType: 'password'
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    showConfirmPwd() {
      if (this.confirmPasswordType === 'password') {
        this.confirmPasswordType = ''
      } else {
        this.confirmPasswordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.confirmPassword.focus()
      })
    },
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          register({
            username: this.registerForm.username,
            password: this.registerForm.password,
            employeeId: this.registerForm.employeeId
          }).then(response => {
            // 检查响应状态码
            if (response && response.code === 1) {
              // 注册成功
              this.$message({
                message: '注册成功，请登录',
                type: 'success',
                duration: 3 * 1000
              })
              this.$router.push('/login')
            } else {
              // 业务逻辑错误
              this.$message({
                message: response.msg || '注册失败，请检查输入信息',
                type: 'error',
                duration: 3 * 1000
              })
            }
            this.loading = false
          }).catch(error => {
            // 处理网络错误或其他异常
            if (error.response && error.response.data) {
              const { msg } = error.response.data
              this.$message({
                message: msg,
                type: 'error',
                duration: 3 * 1000
              })
            } else {
              this.$message({
                message: '注册失败，请稍后再试',
                type: 'error',
                duration: 3 * 1000
              })
            }
            this.loading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调问题 */
$bg:#f5f7fa;
$light_gray:#333;
$cursor: #333;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .register-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.register-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid #dcdfe6;
    background: #fff;
    border-radius: 4px;
    color: #454545;
    margin-bottom: 20px;
  }
}
</style>

<style lang="scss" scoped>
$primary-color: #409EFF;
$dark_gray: #606266;
$light_gray: #333;
$warning-color: #E6A23C;

.register-container {
  min-height: 100%;
  width: 100%;
  background-color: #f5f7fa;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  .register-box {
    width: 80%;
    max-width: 1200px;
    height: 650px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    overflow: hidden;
  }

  .register-left {
    flex: 1;
    background: linear-gradient(135deg, #36D1DC, #5B86E5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    padding: 40px;

    .welcome-text {
      text-align: center;

      h1 {
        font-size: 32px;
        margin-bottom: 20px;
        font-weight: bold;
      }

      h2 {
        font-size: 24px;
        margin-bottom: 10px;
      }

      p {
        font-size: 16px;
        opacity: 0.8;
      }
    }
  }

  .register-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .register-form-container {
    width: 80%;
    max-width: 400px;
  }

  .tips {
    font-size: 14px;
    color: $dark_gray;
    margin-top: 20px;
    text-align: center;

    .login-link {
      margin-left: 5px;
      color: $primary-color;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .warning-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: rgba($warning-color, 0.1);
    border-radius: 4px;
    color: $warning-color;

    i {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      font-size: 14px;
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 30px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .register-button {
    width: 100%;
    margin-bottom: 10px;
    height: 44px;
    font-size: 16px;
    border-radius: 4px;
  }
}
</style>
