package cn.edu.ncu.hrms.employee.controller;

import cn.edu.ncu.hrms.common.entity.ResignRequest;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.ResignApplyDTO;
import cn.edu.ncu.hrms.employee.dto.ResignApproveDTO;
import cn.edu.ncu.hrms.employee.dto.ResignPageQueryDTO;
import cn.edu.ncu.hrms.employee.service.ResignRequestService;
import cn.edu.ncu.hrms.employee.vo.ResignRequestVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 离职申请控制器
 */
@RestController
@RequestMapping("/employee/resign")
@Api(tags = "离职申请接口")
public class ResignRequestController {

    @Autowired
    private ResignRequestService resignRequestService;

    /**
     * 提交离职申请
     */
    @PostMapping("/apply")
    @ApiOperation("提交离职申请")
    @PreAuthorize("isAuthenticated()") // 任何认证用户都可以申请
    public Result<Void> applyResign(@Valid @RequestBody ResignApplyDTO applyDTO) {
        return resignRequestService.apply(applyDTO);
    }


    /**
     * 审批离职申请
     */
    @PutMapping("/approve/{requestId}")
    @ApiOperation("审批离职申请")
    @ApiImplicitParam(name = "requestId", value = "离职申请ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('employee:resign:approve')")
    public Result<Void> approveResign(@PathVariable Integer requestId, @Valid @RequestBody ResignApproveDTO approveDTO) {
        return resignRequestService.approve(requestId, approveDTO);
    }

    /**
     * 撤回离职申请
     */
    @PutMapping("/cancel/{id}")
    @ApiOperation("撤回离职申请")
    @ApiImplicitParam(name = "id", value = "离职申请ID", required = true, dataType = "int", paramType = "path")
    // 权限：只有申请人自己才能取消
    @PreAuthorize("T(java.util.Objects).equals(@securityUtil.getCurrentEmployeeId(),@resignRequestServiceImpl.getEmployeeIdByRequestId(#id))")
    public Result<Void> cancelResign(@PathVariable Integer id) {
        return resignRequestService.cancel(id);
    }

    /**
     * 根据ID获取离职申请详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取离职申请详情")
    @ApiImplicitParam(name = "id", value = "离职申请ID", required = true, dataType = "int", paramType = "path")
    // 权限：申请人自己 或 拥有查询权限者
    @PreAuthorize("@hrms.hasAuthority('employee:resign:query') or " +
            "@securityUtil.getCurrentEmployeeId().equals(@resignRequestServiceImpl.getEmployeeIdByRequestId(#id))")
    public Result<ResignRequestVO> getResignById(@PathVariable @Param("id") Integer id) {
        return resignRequestService.getVOById(id);
    }


    /**
     * 获取当前登录用户的离职申请
     * @return
     */
    @GetMapping("/self")
    @ApiOperation("获取当前登录用户的离职申请")
    @PreAuthorize("isAuthenticated()")
    public Result<List<ResignRequest>> getSelfResignList() {
        return resignRequestService.getSelfResignList();
    }


    /**
     * 分页查询下属的离职申请
     */
    @GetMapping("/page")
    @ApiOperation("分页查询离职申请")
    @PreAuthorize("@hrms.hasAuthority('employee:resign:list')")
    public Result<PageResult<ResignRequestVO>> pageResigns(ResignPageQueryDTO queryDTO) {
        PageInfo<ResignRequestVO> pageInfo = resignRequestService.pageVOs(queryDTO);
        if (pageInfo == null) {
            return Result.error("系统异常，请联系管理员");
        }
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }

}
