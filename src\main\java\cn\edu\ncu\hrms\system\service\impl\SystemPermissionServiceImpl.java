package cn.edu.ncu.hrms.system.service.impl;

import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.auth.mapper.PermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.RolePermissionMapper;
import cn.edu.ncu.hrms.system.dto.PermissionAddDTO;
import cn.edu.ncu.hrms.system.dto.PermissionTreeQueryDTO;
import cn.edu.ncu.hrms.system.dto.PermissionUpdateDTO;
import cn.edu.ncu.hrms.system.service.SystemPermissionService;
import cn.edu.ncu.hrms.system.vo.PermissionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统权限管理服务实现类
 */
@Service
@Slf4j
public class SystemPermissionServiceImpl implements SystemPermissionService {

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    /**
     * 获取所有权限列表
     * @return Result<List<PermissionVO>> 权限VO列表
     */
    @Override
    public Result<List<PermissionVO>> getPermissionList(PermissionTreeQueryDTO queryDTO) {
        log.info("获取权限列表");
        List<Permission> list = permissionMapper.selectListByCondition(queryDTO);
        List<PermissionVO> voList = list.stream()
                                    .map(this::convertToPermissionVO)
                                    .collect(Collectors.toList());
        log.info("查询到 {} 个权限", voList.size());
        return Result.success(voList);
    }


    /**
     * 获取所有权限列表（树形结构）
     * @return 树形权限列表
     */
    @Override
    public Result<List<Map<String, Object>>> getPermissionTree(PermissionTreeQueryDTO queryDTO) {
        log.info("获取权限列表（树形结构）");
        Result<List<PermissionVO>> listResult = getPermissionList(queryDTO);
        if (listResult.getCode() != 1 || listResult.getData() == null) {
            return Result.error("获取权限列表失败");
        }

        List<PermissionVO> permissionList = listResult.getData();

        // 构建树形结构
        List<Map<String, Object>> treeList = buildPermissionTree(permissionList);

        return Result.success(treeList);
    }

    private List<Map<String, Object>> buildPermissionTree(List<PermissionVO> permissionList) {
        List<Map<String, Object>> treeList = new ArrayList<>();
        Map<String, Map<String, Object>> nodeMap = new HashMap<>();

        for (PermissionVO permission : permissionList) {
            String permissionKey = permission.getPermissionKey();
            Map<String, Object> node = new HashMap<>();
            node.put("id", permission.getId());
            node.put("name", permission.getPermissionName());
            node.put("key", permissionKey);
            node.put("description", permission.getDescription());
            node.put("createTime", permission.getCreateTime());
            node.put("children", new ArrayList<Map<String, Object>>());
            nodeMap.put(permissionKey, node);

            String[] parts = permissionKey.split(":");
            if (parts.length == 1) {
                // 根节点
                treeList.add(node);
            } else {
                // 找到父节点
                String parentCode = String.join(":", java.util.Arrays.copyOf(parts, parts.length - 1));
                Map<String, Object> parentNode = nodeMap.get(parentCode);
                if (parentNode != null) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> children = (List<Map<String, Object>>) parentNode.get("children");
                    children.add(node);
                } else {
                    // 没有父节点
                    treeList.add(node);
                }
            }
        }

        return treeList;
    }


    /**
     * 根据ID获取权限信息
     * @param id 权限ID
     * @return Result<PermissionVO> 权限VO
     */
    @Override
    public Result<PermissionVO> getPermissionVOById(Integer id) {
        log.debug("根据ID获取权限详情 VO, ID: {}", id);
        if (id == null) {
            log.warn("请求获取权限详情，但ID为空");
            return Result.error("权限ID不能为空");
        }
        Permission permission = permissionMapper.selectById(id);
        if (permission == null) {
            log.warn("尝试获取ID为 {} 的权限，但未找到", id);
            return Result.error("权限不存在");
        }
        PermissionVO vo = convertToPermissionVO(permission);
        log.debug("成功获取权限详情 VO, ID: {}", id);
        return Result.success(vo);
    }

    /**
     * 创建新权限 (只处理 permissionKey)
     * @param addDTO 权限创建信息 DTO (只含 permissionKey)
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> createPermission(PermissionAddDTO addDTO) {
        log.info("开始创建权限, DTO: {}", addDTO);
        // 1. 校验 permissionKey 是否唯一
        String permissionKey = addDTO.getPermissionKey();
        Permission existing = permissionMapper.selectByPermissionKey(permissionKey);
        if (existing != null) {
            log.warn("创建权限失败：权限标识 '{}' 已存在", permissionKey);
            return Result.error("权限标识已存在");
        }

        // 2. 查询上级权限是否存在
        String[] parts = permissionKey.split(":");
        if (parts.length > 1) {
            for (int i = 0; i < parts.length - 1; i++) {
                String parentKey = String.join(":", Arrays.copyOf(parts, i + 1));
                Permission parent = permissionMapper.selectByPermissionKey(parentKey);
                if (parent == null) {
                    log.debug("'{}' 权限的上级权限 '{}' 不存在，创建权限失败 ",permissionKey, parentKey);
                    return Result.error("上级权限不存在，请先创建上级权限");
                }
            }
        }

        // 3. 创建实体插入数据库
        Permission permission = new Permission();
        BeanUtils.copyProperties(addDTO, permission);
        int rows = permissionMapper.insert(permission);
        if (rows > 0) {
            log.info("权限创建成功, ID: {}, Key: '{}'", permission.getId(), permission.getPermissionKey());
            return Result.success();
        } else {
            log.error("创建权限失败，数据库插入影响行数为 0");
            throw new RuntimeException("创建权限失败，请稍后重试");
        }
    }

    /**
     * 更新权限信息 (实际无内容可更新)
     * @param id 权限ID
     * @param updateDTO 权限更新信息 DTO (空)
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> updatePermission(Integer id, PermissionUpdateDTO updateDTO) {
        log.info("请求更新权限, ID: {}, DTO: {} (注意：当前Permission实体无name/type/desc可更新)", id, updateDTO);
        if (id == null) {
            log.warn("请求更新权限，但ID为空");
            return Result.error("权限ID不能为空");
        }

        // 1. 检查权限是否存在
        Permission existingPermission = permissionMapper.selectById(id);
        if (existingPermission == null) {
            log.warn("尝试更新ID为 {} 的权限，但未找到", id);
            return Result.error("要更新的权限不存在");
        }

        // 2. 更新数据库
        int rows = permissionMapper.update(updateDTO);
        if (rows > 0) {
            log.info("权限更新成功 (仅更新时间戳), ID: {}", id);
            return Result.success();
        } else {
            // 如果 selectById 存在，这里 rows <= 0 可能是并发删除或 update 语句问题
            // 即使没有实际内容更新，MyBatis 默认 update 也会返回影响行数 1 (如果记录存在)
            log.warn("更新权限时间戳数据库操作未影响行数, ID: {}. 可能已被删除或更新失败", id);
            // 考虑返回成功，因为意图是"确保记录存在并更新时间戳"
            // 或者返回错误，表示更新操作未按预期执行
            // throw new RuntimeException("更新权限失败");
             return Result.error("系统繁忙，请稍后再试");
        }
    }

    /**
     * 删除权限 (逻辑删除)
     * @param id 权限ID
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> deletePermission(Integer id) {
        log.info("开始删除权限, ID: {}", id);
        if (id == null) {
            log.warn("请求删除权限，但ID为空");
            return Result.error("权限ID不能为空");
        }
        Permission permission = permissionMapper.selectById(id);
        if (permission == null) {
             log.warn("尝试删除ID为 {} 的权限，但未找到或已被删除", id);
            return Result.success(null, "权限不存在或已被删除");
        }

        int roleCount = rolePermissionMapper.countByPermissionId(id);
        if (roleCount > 0) {
             log.warn("删除权限失败：权限 '{}' (ID: {}) 仍关联着 {} 个角色", permission.getPermissionKey(), id, roleCount);
            return Result.error("删除失败：请先解除该权限与所有角色的关联");
        }

        int rows = permissionMapper.deleteById(id);
        if (rows > 0) {
            log.info("权限删除成功 (逻辑删除), ID: {}", id);
            return Result.success();
        } else {
            log.warn("逻辑删除权限数据库操作未影响行数, ID: {}. 可能已被删除或删除失败", id);
            return Result.success(null, "权限不存在或已被删除");
        }
    }

    /**
     * 获取角色拥有的权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public Result<List<PermissionVO>> getPermissionsByRoleId(Integer roleId) {
        log.debug("获取角色 {} 的权限列表", roleId);
        if (roleId == null) return Result.success(Collections.emptyList());
        List<Permission> list = permissionMapper.selectPermissionsByRoleId(roleId);
        List<PermissionVO> voList = list.stream()
                                    .map(this::convertToPermissionVO)
                                    .collect(Collectors.toList());
        log.debug("角色 {} 查询到 {} 个权限", roleId, voList.size());
        return Result.success(voList);
    }

    /**
     * 获取用户拥有的权限列表
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Result<List<PermissionVO>> getPermissionsByUserId(Integer userId) {
        log.debug("获取用户 {} 的权限列表", userId);
        if (userId == null) return Result.success(Collections.emptyList());
        List<Permission> list = permissionMapper.selectPermissionsByUserId(userId);
        List<PermissionVO> voList = list.stream()
                                    .map(this::convertToPermissionVO)
                                    .collect(Collectors.toList());
        log.debug("用户 {} 查询到 {} 个权限", userId, voList.size());
        return Result.success(voList);
    }

    // --- 私有辅助方法 ---

    /**
     * 将 Permission 实体转换为 PermissionVO.
     * @param permission 权限实体
     * @return PermissionVO
     */
    private PermissionVO convertToPermissionVO(Permission permission) {
        if (permission == null) {
            return null;
        }
        PermissionVO vo = new PermissionVO();
        BeanUtils.copyProperties(permission, vo);
        return vo;
    }
}
