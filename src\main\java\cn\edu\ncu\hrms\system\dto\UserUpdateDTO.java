package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新用户数据传输对象 (DTO) - 根据 User.java 实体调整
 */
@Data
@ApiModel(value="UserUpdateDTO", description="更新用户数据传输对象")
public class UserUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 用户名通常不允许在此处修改，或需要特殊处理
    @ApiModelProperty(value = "用户名")
    @Size(min = 3, max = 20, message = "用户名长度必须在3到20之间")
    private String username;

    // 用户类型一般不允许修改
    // @ApiModelProperty(value = "用户类型", notes = "0-员工, 1-管理员")
    // private Integer userType;

    // 关联的员工ID通常在创建时确定，不允许更新
    // @ApiModelProperty(value = "关联的员工ID")
    // private Integer employeeId;

    // 用户角色通常通过专门的接口分配，不在更新用户信息接口中处理
    // @ApiModelProperty(value = "分配的角色ID列表")
    // private List<Integer> roleIds;

} 