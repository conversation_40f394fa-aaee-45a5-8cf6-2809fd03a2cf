package cn.edu.ncu.hrms.employee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class EmployeeImportDTO {

    @ExcelProperty("姓名")
    private String name;

    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("入职日期")
    private LocalDate entryDate;

    @ExcelProperty("性别")
    private String gender;

    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("联系电话")
    private String phone;

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty("家庭住址")
    private String address;

    @ExcelProperty("部门ID")
    private Integer departmentId;

    @ExcelProperty("职位名称")
    private String positionName;

    @ExcelProperty("职级")
    private Integer level;

    @ExcelProperty("HR工号")
    private String hrWorkNo;

    @ExcelProperty("在职状态")
    private Boolean status;
}
