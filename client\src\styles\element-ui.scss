// 覆盖一些element-ui样式

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

// 修复 el-dialog 的问题
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// 全局设置弹窗不允许点击遮罩关闭
.el-dialog__wrapper {
  .el-dialog {
    &.el-dialog--center {
      .el-dialog__header {
        padding-top: 20px;
      }
    }
    .el-dialog__body {
      padding: 20px 20px;
    }
  }
}

// 优化上传组件
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// 下拉菜单
.el-dropdown-menu {
  a {
    display: block
  }
}

// 修复日期选择器样式
.el-range-separator {
  box-sizing: content-box;
}

// 主要按钮样式
.el-button--primary {
  background-color: #2752fb;

  &:hover {
    background-color: #7088eb;
  }
}

// 树节点高度
.el-tree-node__content {
  height: 40px;
}

// 菜单样式优化
.el-menu {
  border-right: none !important;

  .el-menu-item {
    height: 56px;
    line-height: 56px;

    &:focus, &:hover {
      outline: none;
      background-color: #f0f7ff !important;
    }

    &.is-active {
      font-weight: 600;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #409EFF;
      }
    }
  }

  .el-submenu__title {
    height: 65px;
    line-height: 65px;
    margin-bottom: 5px;

    &:hover, &:focus {
      outline: none;
      background-color: #f0f7ff !important;
    }
  }

  .el-submenu.is-active .el-submenu__title {
    color: #409EFF !important;
    font-weight: 600;
  }
}
