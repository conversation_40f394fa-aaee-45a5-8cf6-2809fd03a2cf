package cn.edu.ncu.hrms.attendance.controller;

import cn.edu.ncu.hrms.attendance.dto.LeaveApplyDTO;
import cn.edu.ncu.hrms.attendance.dto.LeaveApproveDTO;
import cn.edu.ncu.hrms.attendance.dto.LeavePageQueryDTO;
import cn.edu.ncu.hrms.attendance.service.LeaveRequestService;
import cn.edu.ncu.hrms.attendance.vo.LeaveRequestVO;
import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import cn.edu.ncu.hrms.common.entity.LeaveRequest;
import cn.edu.ncu.hrms.common.entity.ResignRequest;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.employee.vo.ResignRequestVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 请假申请控制器
 */
@RestController
@RequestMapping("/attendance/leave")
@Api(tags = "请假申请接口")
public class LeaveController {

    @Autowired
    private LeaveRequestService leaveRequestService;

    /**
     * 提交请假申请
     */
    @PostMapping("/apply")
    @ApiOperation("提交请假申请")
    @PreAuthorize("isAuthenticated()") // 任何认证用户都可以申请
    public Result<Void> applyLeave(@Valid @RequestBody LeaveApplyDTO applyDTO) {
        return leaveRequestService.apply(applyDTO);
    }

    /**
     * 取消请假申请
     */
    @PutMapping("/cancel/{id}")
    @ApiOperation("撤回请假申请")
    @ApiImplicitParam(name = "id", value = "请假申请ID", required = true, dataType = "int", paramType = "path")
    // 权限：只有申请人自己才能撤回
    @PreAuthorize("T(java.util.Objects).equals(@securityUtil.getCurrentEmployeeId(),@leaveRequestServiceImpl.getEmployeeIdByRequestId(#id))")
    public Result<Void> cancelLeave(@PathVariable Integer id) {
        return leaveRequestService.cancel(id);
    }

    /**
     * 审批请假申请
     */
    @PutMapping("/approve/{requestId}")
    @ApiOperation("审批请假申请")
    @ApiImplicitParam(name = "requestId", value = "请假申请ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('attendance:leave:approve')")
    public Result<Void> approveLeave(@PathVariable Integer requestId, @Valid @RequestBody LeaveApproveDTO approveDTO) {
        return leaveRequestService.approve(requestId, approveDTO);
    }

    /**
     * 根据ID获取请假申请详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取请假申请详情")
    @ApiImplicitParam(name = "id", value = "请假申请ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('attendance:leave:query') or " +
            "@securityUtil.getCurrentEmployeeId().equals(@leaveRequestServiceImpl.getEmployeeIdByRequestId(#id))")
    public Result<LeaveRequestVO> getLeaveById(@PathVariable Integer id) {
        return leaveRequestService.getVOById(id);
    }



    /**
     * 获取个人请假申请分页列表
     * @return
     */
    @GetMapping("/self")
    @ApiOperation("获取个人请假申请")
    @PreAuthorize("isAuthenticated()")
    public Result<PageResult<LeaveRequest>> getSelfLeavePage(PageQueryDTO queryDTO) {
        return leaveRequestService.getSelfLeavePage(queryDTO);
    }


    /**
     * 分页查询请假申请
     */
    @GetMapping("/page")
    @ApiOperation("分页查询请假申请")
    @PreAuthorize("@hrms.hasAuthority('attendance:leave:list')")
    public Result<PageResult<LeaveRequestVO>> pageLeaves(LeavePageQueryDTO queryDTO) {
        PageInfo<LeaveRequestVO> pageInfo = leaveRequestService.pageVOs(queryDTO);
        if (pageInfo == null) {
            return Result.error("系统异常，请联系管理员");
        }
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }



}