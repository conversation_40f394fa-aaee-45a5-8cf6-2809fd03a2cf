package cn.edu.ncu.hrms.auth.service.impl;

import cn.edu.ncu.hrms.auth.mapper.PermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.RolePermissionMapper;
import cn.edu.ncu.hrms.auth.service.PermissionService;
import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 权限服务实现类
 */
@Slf4j
@Service
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionMapper permissionMapper;


    @Override
    public Permission getPermissionById(Integer id) {
        log.info("开始获取权限ID: {}", id);
        Permission permission = permissionMapper.selectById(id);
        if (permission == null) {
            log.error("权限ID: {} 不存在", id);
        } else {
            log.debug("获取到的权限信息: {}", permission);
        }
        return permission;
    }

    @Override
    public List<Permission> getPermissionList() {
        log.info("开始获取权限列表");
        List<Permission> permissions = permissionMapper.selectList();
        log.debug("获取到的权限列表: {}", permissions);
        return permissions;
    }


    @Override
    public List<Permission> getPermissionsByRoleId(Integer roleId) {
        log.info("开始获取角色ID: {} 的权限列表", roleId);
        List<Permission> permissions = permissionMapper.selectPermissionsByRoleId(roleId);
        if (permissions == null || permissions.isEmpty()) {
            log.warn("角色ID: {} 没有对应的权限", roleId);
        } else {
            log.debug("获取到的角色权限列表: {}", permissions);
        }
        return permissions;
    }

    @Override
    public List<Permission> getPermissionsByUserId(Integer userId) {
        log.info("开始获取用户ID: {} 的权限列表", userId);
        List<Permission> permissions = permissionMapper.selectPermissionsByUserId(userId);
        log.debug("获取到的用户权限列表: {}", permissions);
        return permissions;
    }
}