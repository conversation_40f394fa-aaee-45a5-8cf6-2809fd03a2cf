<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.common.mapper.ApprovalProcessMapper">

    <!-- 插入单个审批流程节点 -->
    <insert id="insert" parameterType="ApprovalProcess">
        INSERT INTO approval_process
        (type, request_id, approver_id, step_order, status, expire_time, comment, approve_time, create_time, update_time, del_flag)
        VALUES
        (#{type}, #{requestId}, #{approverId}, #{stepOrder}, #{status}, #{expireTime}, #{comment}, #{approveTime}, NOW(), NOW(), 0)
    </insert>

    <!-- 批量插入审批流程节点 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO approval_process
        (type, request_id, approver_id, step_order, status, expire_time, create_time, update_time, del_flag)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.type}, #{item.requestId}, #{item.approverId}, #{item.stepOrder}, #{item.status}, #{item.expireTime}, NOW(), NOW(), 0)
        </foreach>
    </insert>

    <!-- 根据主键ID查询审批流程节点 -->
    <select id="selectById" parameterType="int" resultType="ApprovalProcess">
        SELECT * FROM approval_process WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询某申请的全部审批流程节点，按顺序返回 -->
    <select id="selectByRequestIdAndType" resultType="ApprovalProcess">
        SELECT * FROM approval_process
        WHERE request_id = #{requestId}
          AND type = #{type}
          AND del_flag = 0
        ORDER BY step_order ASC
    </select>

    <!-- 查询下一个待审批节点 -->
    <select id="selectNextPending" resultType="ApprovalProcess">
        SELECT * FROM approval_process
        WHERE request_id = #{requestId}
          AND step_order > #{stepOrder}
          AND type = #{type}
          AND status = 'NEW'
          AND del_flag = 0
        ORDER BY step_order ASC
        LIMIT 1
    </select>

    <!-- 更新审批流程节点（如审批状态、意见等） -->
    <update id="update" parameterType="ApprovalProcess">
        UPDATE approval_process
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="approveTime != null">approve_time = #{approveTime},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>


    <update id="updateAllNewStatus">
        UPDATE approval_process
        SET status = '#{status}',
            update_time = NOW()
        WHERE request_id = #{requestId}
          AND type = #{type}
          AND status = 'NEW'
          AND del_flag = 0
    </update>


    <update id="cancel">
        UPDATE approval_process
        SET status = 'CANCELED', update_time = NOW()
        WHERE request_id = #{requestId}
          AND type = #{type}
          AND del_flag = 0
    </update>


    <select id="selectCurrentPending" resultType="java.lang.Integer">
        SELECT id
        FROM approval_process
        WHERE request_id = #{requestId}
          AND type = #{type}
          AND status = 'PENDING'
          AND del_flag = 0
    </select>

    <select id="selectIsPendingByMe" resultType="boolean">
        SELECT CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END
        FROM approval_process
        WHERE request_id = #{requestId}
          AND type = #{type}
          AND approver_id = #{employeeId}
          AND status = 'PENDING'
          AND del_flag = 0
          /*AND expire_time > NOW()*/
    </select>


</mapper>
