package cn.edu.ncu.hrms.system.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="PositionPageQueryDTO", description="查询职位条件传输对象")
public class SystemPositionPageQueryDTO extends PageQueryDTO {
    @ApiModelProperty(value = "职位ID")
    private Integer id;

    @ApiModelProperty(value = "职位名称")
    private String name;
}
