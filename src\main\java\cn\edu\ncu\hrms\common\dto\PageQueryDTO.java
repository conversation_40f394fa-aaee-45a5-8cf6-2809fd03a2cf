package cn.edu.ncu.hrms.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 通用分页查询数据传输对象 (基类)
 */
@Data
public class PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @Min(value = 1, message = "页码必须从1开始")
    private Integer pageNum = 1; // 默认页码为1

    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    @Min(value = 1, message = "每页数量必须大于0")
    // 可以添加 @Max 注解限制每页最大数量
    private Integer pageSize = 10; // 默认每页数量为10

} 