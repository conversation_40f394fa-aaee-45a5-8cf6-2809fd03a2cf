import { getUserInfo } from '@/api/user'

// 尝试从localStorage恢复用户信息
let cachedUserInfo = null
let cachedRoles = []
let cachedPermissions = []
let isLoaded = false

try {
  const storedUserInfo = localStorage.getItem('userInfo')
  const storedRoles = localStorage.getItem('userRoles')
  const storedPermissions = localStorage.getItem('userPermissions')

  if (storedUserInfo && storedRoles) {
    cachedUserInfo = JSON.parse(storedUserInfo)
    cachedRoles = JSON.parse(storedRoles)
    cachedPermissions = storedPermissions ? JSON.parse(storedPermissions) : []
    isLoaded = true
    console.log('从localStorage恢复了用户信息')
  }
} catch (e) {
  console.warn('无法从localStorage恢复用户信息', e)
}

const state = {
  userInfo: cachedUserInfo,
  roles: cachedRoles,
  permissions: cachedPermissions,
  isLoaded: isLoaded
}

const mutations = {
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_LOADED: (state, isLoaded) => {
    state.isLoaded = isLoaded
  }
}

const actions = {
  // 获取用户信息 - 优化版本，确保只调用一次API
  getUserInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 如果已经加载过用户信息，直接返回缓存的数据
      if (state.isLoaded && state.userInfo) {
        console.log('使用缓存的用户信息，避免重复调用API')
        resolve({
          userInfo: state.userInfo,
          roles: state.roles,
          permissions: state.permissions
        })
        return
      }

      // 首次加载时从服务器获取用户信息
      console.log('首次加载用户信息，调用API')
      getUserInfo().then(response => {
        if (response.code === 1 && response.data) {
          const { roleKeys, permissions, ...userInfo } = response.data

          commit('SET_USER_INFO', userInfo)
          commit('SET_ROLES', roleKeys || [])
          commit('SET_PERMISSIONS', permissions || [])
          commit('SET_LOADED', true)

          // 将数据存储在localStorage中，以便在页面刷新后恢复
          try {
            localStorage.setItem('userRoles', JSON.stringify(roleKeys || []))
            localStorage.setItem('userInfo', JSON.stringify(userInfo))
            localStorage.setItem('userPermissions', JSON.stringify(permissions || []))
          } catch (e) {
            console.warn('无法将用户信息存储到localStorage', e)
          }

          resolve({
            userInfo,
            roles: roleKeys || [],
            permissions: permissions || []
          })
        } else {
          reject(new Error('获取用户信息失败'))
        }
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 重置用户信息
  resetUserInfo({ commit }) {
    commit('SET_USER_INFO', null)
    commit('SET_ROLES', [])
    commit('SET_PERMISSIONS', [])
    commit('SET_LOADED', false)

    // 同时清除localStorage中的用户信息
    try {
      localStorage.removeItem('userInfo')
      localStorage.removeItem('userRoles')
      localStorage.removeItem('userPermissions')
    } catch (e) {
      console.warn('无法清除localStorage中的用户信息', e)
    }
  }
}

const getters = {
  userInfo: state => state.userInfo,
  roles: state => state.roles,
  permissions: state => state.permissions,
  isLoaded: state => state.isLoaded,
  hasRole: state => role => {
    return state.roles.includes(role)
  },
  hasPermission: state => permission => {
    return state.permissions.includes(permission)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
