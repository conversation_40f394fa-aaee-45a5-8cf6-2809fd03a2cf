package cn.edu.ncu.hrms.salary.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 薪资账套分页查询 DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("薪资账套分页查询条件")
public class AccountSetPageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "账套ID", required = true, example = "501")
    private Integer id;

    @ApiModelProperty(value = "账套名称", required = true, example = "后端开发L5")
    private String name;

} 