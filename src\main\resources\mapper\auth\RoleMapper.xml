<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.edu.ncu.hrms.auth.mapper.RoleMapper">

    <sql id="Base_Column_List">
        id, name, role_key, description, create_time, update_time, del_flag
    </sql>

    <!-- 查询指定用户拥有的角色列表 -->
    <select id="selectRolesByUserId" resultType="Role" parameterType="java.lang.Integer">
        SELECT 
            r.id, r.name, r.role_key, r.description, r.create_time, r.update_time, r.del_flag
        FROM 
            role r
        JOIN 
            user_role ur ON r.id = ur.role_id
        WHERE 
            ur.user_id = #{userId}
            AND r.del_flag = 0
            AND ur.del_flag = 0
        ORDER BY
            r.id ASC
    </select>

    <!-- 根据角色ID查询角色详情 -->
    <select id="selectById" resultType="Role" parameterType="java.lang.Integer">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            role
        WHERE 
            id = #{id}
            AND del_flag = 0
    </select>

    <!-- 插入新角色 -->
    <insert id="insert" parameterType="Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO role (
            name, role_key, description, create_time, update_time, del_flag
        ) VALUES (
            #{name}, #{roleKey}, #{description}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新角色信息 -->
    <update id="update" parameterType="Role">
        UPDATE role
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="roleKey != null">role_key = #{roleKey},</if>
            <if test="description != null">description = #{description},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除角色 -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE role
        SET del_flag = null, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询所有未删除的角色列表 -->
    <select id="selectList" resultType="Role">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            role
        WHERE 
            del_flag = 0
        ORDER BY 
            id ASC
    </select>



    <!--根据角色标识查询角色-->
    <select id="selectByRoleKey" resultType="Role">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            role
        WHERE
            role_key = #{roleKey}
    </select>

    <!-- 根据角色名称查询未删除的角色 -->
    <select id="selectByName" resultType="Role" parameterType="java.lang.String">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            role
        WHERE
            name = #{name}
            AND del_flag = 0
    </select>

    <!--条件查询-->
    <select id="selectListByCondition" resultType="cn.edu.ncu.hrms.common.entity.Role" parameterType="cn.edu.ncu.hrms.system.dto.RolePageQueryDTO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            role
        WHERE
            del_flag = 0
            <if test="name != null">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
    </select>

</mapper>