package cn.edu.ncu.hrms.employee.controller;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.EmployeeQueryDTO;
import cn.edu.ncu.hrms.employee.dto.EmployeeUpdateDTO;
import cn.edu.ncu.hrms.employee.service.EmployeeService;
import cn.edu.ncu.hrms.employee.vo.EmployeeDashboardVO;
import cn.edu.ncu.hrms.employee.vo.EmployeeVO;
import cn.edu.ncu.hrms.system.dto.EmployeeAddDTO;
import cn.edu.ncu.hrms.system.service.SystemEmployeeService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 员工档案控制器
 */
@RestController
@Slf4j
@RequestMapping("/employee")
@Api(tags = "员工档案接口")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;
    
    @Autowired
    private SecurityUtil securityUtil;

    /**
     * 获取我的档案信息
     */
    @GetMapping("/my-profile")
    @ApiOperation("获取我的档案信息")
    @PreAuthorize("@hrms.hasAuthority('employee:profile:query')")
    public Result<EmployeeVO> getMyProfile() {
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            return Result.error("未找到员工信息");
        }
        
        return employeeService.getMyProfile(currentEmployeeId);
    }

    /**
     * 更新我的档案信息
     */
    @PutMapping("/my-profile")
    @ApiOperation("更新我的档案信息")
    @PreAuthorize("@hrms.hasAuthority('employee:profile:edit')")
    public Result<Void> updateMyProfile(@Valid @RequestBody EmployeeUpdateDTO employeeUpdateDTO) {
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            return Result.error("未找到员工信息");
        }
        employeeUpdateDTO.setId(currentEmployeeId);
        return employeeService.updateMyProfile(employeeUpdateDTO);
    }

    /**
     * 获取个人主页报表信息
     */
    @GetMapping("/dashboard")
    @ApiOperation("获取个人主页仪表盘信息")
    @PreAuthorize("@hrms.hasAuthority('employee:dashboard:query')")
    public Result<EmployeeDashboardVO> getDashboard() {
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            return Result.error("未找到员工信息");
        }
        
        return employeeService.getDashboard(currentEmployeeId);
    }
    
    /**
     * HR分页条件查询员工列表
     */
    @GetMapping("/page/hr")
    @ApiOperation("HR分页查询员工列表")
    @PreAuthorize("hasRole('ROLE_hr')")
    public Result<PageResult<EmployeeVO>> getEmployeePageByHr(@ModelAttribute EmployeeQueryDTO queryDTO) {
        PageInfo<EmployeeVO> pageInfo = employeeService.getEmployeePageByHr(queryDTO);
        if (pageInfo == null) {
            return Result.error("未关联员工，请联系管理员");
        }
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }

    /**
     * Leader分页条件查询员工列表
     */
    @GetMapping("/page/leader")
    @ApiOperation("Leader分页查询员工列表")
    @PreAuthorize("hasRole('ROLE_leader')")
    public Result<PageResult<EmployeeVO>> getEmployeePageByLeader(EmployeeQueryDTO queryDTO) {
        PageInfo<EmployeeVO> pageInfo = employeeService.getEmployeePageByLeader(queryDTO);
        if (pageInfo == null) {
            return Result.error("未关联部门，无法查询下属员工信息，请联系管理员");
        }
        return PageResult.success(pageInfo.getTotal(), pageInfo.getList());
    }


    /**
     * HR查询员工列表（用于下拉选择）
     */
    @GetMapping("/list/hr")
    @ApiOperation("HR查询员工列表")
    @PreAuthorize("hasRole('ROLE_hr')")
    public Result<List<Employee>> getEmployeeListByHr() {
        return employeeService.getEmployeeListByHr();
    }

    /**
     * Leader查询员工列表（用于下拉选择）
     */
    @GetMapping("/list/leader")
    @ApiOperation("Leader查询员工列表")
    @PreAuthorize("hasRole('ROLE_leader')")
    public Result<List<Employee>> getEmployeeListByLeader() {
        return employeeService.getEmployeeListByLeader();
    }
    
    /**
     * 获取员工详情
     */
    @GetMapping("/{id}")
    @ApiOperation("获取员工详情")
    @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('employee:subordinate:query')")
    public Result<EmployeeVO> getEmployeeById(@PathVariable Integer id) {
        return employeeService.getEmployeeById(id);
    }
    
    /**
     * 新增员工
     */
    @PostMapping
    @ApiOperation("新增员工")
    @PreAuthorize("@hrms.hasAuthority('employee:subordinate:add')")
    public Result<Void> addEmployee(@Valid @RequestBody EmployeeAddDTO employee) {
        return employeeService.createEmployee(employee);
    }
    
    /**
     * 更新员工
     */
    @PutMapping
    @ApiOperation("更新员工")
    @PreAuthorize("@hrms.hasAuthority('employee:subordinate:edit')")
    public Result<Void> updateEmployee(@Valid @RequestBody EmployeeUpdateDTO employeeUpdateDTO) {
        return employeeService.updateEmployee(employeeUpdateDTO);
    }
    
    /**
     * 删除员工
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除员工")
    @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('employee:subordinate:delete')")
    public Result<Void> deleteEmployee(@PathVariable Integer id) {
        return employeeService.deleteEmployee(id);
    }
    
    /**
     * 批量导入员工
     */
    @PostMapping("/import")
    @ApiOperation("批量导入员工")
    @PreAuthorize("@hrms.hasAuthority('employee:subordinate:import')")
    public Result<Void> batchImport(MultipartFile file) {
        return employeeService.batchImport(file);
    }
    
    /**
     * 导出员工
     */
    @GetMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiOperation("导出员工")
    @PreAuthorize("@hrms.hasAuthority('employee:subordinate:export')")
    public byte[] exportEmployees(EmployeeQueryDTO queryDTO) {
        return employeeService.exportEmployees(queryDTO).getData(); // 直接返回byte[]
    }


    /**
     * 获取员工导入模板
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 1. 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=员工导入模版.xlsx");

            // 2. 获取模板文件流
            InputStream templateStream = this.getClass()
                    .getResourceAsStream("/templates/employee_import.xlsx");
            if (templateStream == null) {
                log.error("模板文件流为空，可能文件不存在或路径错误");
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 3. 写入响应流
            try (OutputStream out = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = templateStream.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        } catch (IOException e) {
            log.error("下载模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
} 