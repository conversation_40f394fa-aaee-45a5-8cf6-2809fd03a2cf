package cn.edu.ncu.hrms.employee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 员工信息VO
 */
@Data
@ApiModel(description = "员工信息")
public class EmployeeVO {

    @ApiModelProperty(value = "员工ID")
    private Integer id;

    @ApiModelProperty(value = "工号")
    private String workNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "入职日期")
    private LocalDate entryDate;

    @ApiModelProperty(value = "性别", allowableValues = "男,女,其他")
    private String gender;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "家庭住址")
    private String address;

    @ApiModelProperty(value = "部门ID")
    private Integer departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "部门及其上级部门名称")
    private String departmentNames;

    @ApiModelProperty(value = "职位ID")
    private Integer positionId;

    @ApiModelProperty(value = "职位名称")
    private String positionName;

    @ApiModelProperty(value = "职级")
    private Byte level;

    @ApiModelProperty(value = "HRBP员工ID")
    private Integer hrbpId;

    @ApiModelProperty(value = "HRBP姓名")
    private String hrbpName;

    @ApiModelProperty(value = "HRBP工号")
    private String hrbpWorkNo;

    @ApiModelProperty(value = "在职状态")
    private Boolean status;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 