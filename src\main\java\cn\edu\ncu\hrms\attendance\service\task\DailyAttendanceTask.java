package cn.edu.ncu.hrms.attendance.service.task;

import cn.edu.ncu.hrms.attendance.mapper.AttendanceMapper;
import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.entity.Attendance;
import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.utils.DateUtil;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DailyAttendanceTask {


    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private AttendanceMapper attendanceMapper;

    @Autowired
    private AttendanceProcessingService attendanceProcessingService;

    /**
     * 每天凌晨 00:00 执行
     * 1. 归档前一天的考勤状态和扣款
     * 2. 初始化当天的考勤记录
     * Cron: 秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void processDailyAttendance() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        boolean yesterdayIsWeekend = DateUtil.isWeekend(yesterday);
        LocalDate today = LocalDate.now();
        boolean todayIsWeekend = DateUtil.isWeekend(today);
        if (yesterdayIsWeekend && todayIsWeekend) {
            log.info("昨天和今天都是周末，跳过考勤处理。");
            return;
        }
        log.info("开始执行每日考勤处理任务：归档 {} 并初始化 {}", yesterday, today);

        // 0. 获取所有在职员工
        List<Employee> employeeList = employeeMapper.selectList();
        if (employeeList == null || employeeList.isEmpty()) {
            log.info("没有找到需要处理考勤的员工。");
            return;
        }
        log.info("共找到 {} 名在职员工。", employeeList.size());

        // 1. 批量获取员工对应的部门信息，减少数据库查询次数
        List<Integer> departmentIds = employeeList.stream()
                .map(Employee::getDepartmentId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, Department> departmentMap = departmentMapper.selectDepartmentByIds(departmentIds);

        List<Attendance> recordsToUpdate = new ArrayList<>();
        List<Attendance> recordsToInsert = new ArrayList<>();
        List<Attendance> recordsToInsertForToday = new ArrayList<>();

        // 2. 遍历员工，处理昨天的记录并准备今天的记录
        for (Employee employee : employeeList) {
            Department department = departmentMap.get(employee.getDepartmentId());

            if (!yesterdayIsWeekend) {
                // --- 处理昨天 ---
                try {
                    Attendance yesterdayAttendance = attendanceMapper.selectByEmployeeAndDate(employee.getId(), yesterday);
                    if (yesterdayAttendance != null) {
                        // 记录存在，进行状态和扣款最终确认
                        Attendance processedAttendance = attendanceProcessingService.finalizeAttendanceRecord(
                                yesterdayAttendance, department, employee.getId(), employee.getPositionId());
                        if (processedAttendance.isNeedsUpdate()) { // 使用临时标记判断
                            recordsToUpdate.add(processedAttendance);
                        }
                    } else {
                        // 昨天连记录都没有，说明完全缺勤
                        log.warn("员工 {} (ID: {}) 缺少 {} 的考勤记录，将创建缺勤记录。", employee.getName(), employee.getId(), yesterday);
                        Attendance absentRecord = Attendance.builder()
                                .employeeId(employee.getId())
                                .date(yesterday)
                                .clockInStatus(AttendanceConstant.CLOCK_ABSENT)
                                .clockOutStatus(AttendanceConstant.CLOCK_ABSENT)
                                .deduction(attendanceProcessingService.calculateAbsenteeismDeduction(employee.getPositionId(), yesterday))
                                .createTime(LocalDateTime.now())
                                .updateTime(LocalDateTime.now())
                                .delFlag(false)
                                .build();

                        recordsToInsert.add(absentRecord);

                    }
                } catch (Exception e) {
                    log.error("处理员工 {} (ID: {}) {} 的考勤记录时出错: {}",
                            employee.getName(), employee.getId(), yesterday, e.getMessage(), e);
                }
            }

            if (!todayIsWeekend) {
                // --- 准备今天 ---
                try {
                    // 检查今天是否已存在记录
                    Attendance todayAttendance = attendanceMapper.selectByEmployeeAndDate(employee.getId(), today);
                    if (todayAttendance == null) {
                        // 今天记录不存在，创建初始化记录
                        Attendance initialAttendance = Attendance.builder()
                                .employeeId(employee.getId())
                                .date(today)
                                .clockInTime(null)
                                .clockInStatus(null)
                                .clockOutTime(null)
                                .clockOutStatus(null)
                                .deduction(null) // 或者 BigDecimal.ZERO
                                .createTime(LocalDateTime.now())
                                .updateTime(LocalDateTime.now())
                                .delFlag(false)
                                .build();
                        recordsToInsertForToday.add(initialAttendance);
                    } else {
                        log.info("员工 {} (ID: {}) {} 的考勤记录已存在，跳过初始化。", employee.getName(), employee.getId(), today);
                    }
                } catch (Exception e) {
                    log.error("为员工 {} (ID: {}) 初始化 {} 的考勤记录时出错: {}",
                            employee.getName(), employee.getId(), today, e.getMessage(), e);
                }
            }

        }

        // 3. 批量更新昨天的记录
        if (!recordsToUpdate.isEmpty()) {
            log.info("开始批量更新 {} 条昨天的考勤记录...", recordsToUpdate.size());
            try {
                attendanceMapper.batchUpdate(recordsToUpdate);
            } catch (Exception e) {
                log.error("批量更新昨天考勤记录时出错: {}", e.getMessage(), e);
            }
        } else {
            log.info("没有需要更新的昨天考勤记录。");
        }

        // 4. 批量插入昨天的缺勤记录
        if (!recordsToInsert.isEmpty()) {
            log.info("开始批量插入 {} 条缺勤记录...", recordsToInsert.size());
            try {
                attendanceMapper.batchInsert(recordsToInsert);
            } catch (Exception e) {
                log.error("批量插入缺勤记录时出错: {}", e.getMessage(), e);
            }
            log.info("批量插入完成。");
        }

        // 5. 批量插入今天的记录
        if (!recordsToInsertForToday.isEmpty()) {
            log.info("开始批量插入 {} 条今天的考勤初始化记录...", recordsToInsertForToday.size());
            try {
                // 使用批量插入方法效率更高
                attendanceMapper.batchInsert(recordsToInsertForToday);
                log.info("批量插入完成。");
            } catch (Exception e) {
                log.error("批量插入今天考勤记录时出错: {}", e.getMessage(), e);
            }
        } else {
            log.info("没有需要初始化的今天考勤记录。");
        }

        log.info("每日考勤处理任务执行完毕。");
    }
}
