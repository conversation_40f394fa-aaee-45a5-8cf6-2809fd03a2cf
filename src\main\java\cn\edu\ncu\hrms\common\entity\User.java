package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("系统用户")
public class User {
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户名", required = true, example = "EMP1001")
    private String username;

    @ApiModelProperty(value = "加密密码", required = true, example = "$2a$10$X9...")
    private String password;

    @ApiModelProperty(value = "关联员工ID", example = "1001")
    private Integer employeeId;

    @ApiModelProperty(value = "是否管理员", example = "false")
    private Boolean isAdmin;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
