package cn.edu.ncu.hrms.system.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工分页查询 DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("员工分页查询条件")
public class EmployeePageQueryDTO extends PageQueryDTO {

    @ApiModelProperty("员工姓名 (模糊查询)")
    private String name;

    @ApiModelProperty("工号 (精确查询)")
    private String workNo;

    @ApiModelProperty("部门ID")
    private Integer departmentId;

    @ApiModelProperty("职位ID")
    private Integer positionId;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("在职状态(true-在职 false-离职 null-所有)")
    private Boolean status;

} 