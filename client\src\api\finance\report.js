import request from '@/utils/request'

/**
 * 获取个人薪资报表列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回个人薪资报表列表
 */
export function getSelfSalaryReport(params) {
  return request({
    url: '/salary/report/self',
    method: 'get',
    params
  })
}

/**
 * 分页查询薪资报表 (Leader-HR)
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回薪资报表列表
 */
export function pageSalaryReports(params) {
  return request({
    url: '/salary/report/page',
    method: 'get',
    params
  })
}

/**
 * 导出薪资报表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回导出结果
 */
export function exportSalaryReport(params) {
  return request({
    url: '/salary/report/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 根据ID获取薪资报表详情
 * @param {Number} id - 薪资报表ID
 * @returns {Promise} 返回薪资报表详情
 */
export function getSalaryReportById(id) {
  return request({
    url: `/salary/report/${id}`,
    method: 'get'
  })
}
