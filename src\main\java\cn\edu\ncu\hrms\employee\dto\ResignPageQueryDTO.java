package cn.edu.ncu.hrms.employee.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 离职申请分页查询 DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="ResignPageQueryDTO", description="离职申请分页查询条件")
public class ResignPageQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "查询者（Leader或HR）用户ID")
    private Integer currentEmployeeId;

    @ApiModelProperty(value = "申请人工号")
    private String workNo;

    @ApiModelProperty(value = "申请状态 (PENDING,APPROVED,REJECTED,AUTO_REJECTED,TERMINATE,CANCELED)")
    private String status;

    @ApiModelProperty(value = "期望离职开始日期")
    private LocalDate resignStartDate;

    @ApiModelProperty(value = "期望离职结束日期")
    private LocalDate resignEndDate;

} 