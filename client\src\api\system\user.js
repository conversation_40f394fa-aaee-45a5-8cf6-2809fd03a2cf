import request from '@/utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 查询管理员列表
export function listAdmin(query) {
  return request({
    url: '/system/user/admin/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(id) {
  return request({
    url: '/system/user/' + id,
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(id, data) {
  return request({
    url: '/system/user/' + id,
    method: 'put',
    data: data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: '/system/user/' + id,
    method: 'delete'
  })
}

// 获取用户角色
export function getUserRoles(id) {
  return request({
    url: '/system/user/' + id + '/roles',
    method: 'get'
  })
}

// 设置用户角色
export function setUserRoles(id, data) {
  return request({
    url: '/system/user/' + id + '/roles',
    method: 'post',
    data: data
  })
}

// 重置用户密码
export function resetUserPassword(id, data) {
  return request({
    url: '/system/user/reset-password/' + id,
    method: 'put',
    data: data
  })
}
