package cn.edu.ncu.hrms.attendance.service;

import cn.edu.ncu.hrms.attendance.dto.LeaveApplyDTO;
import cn.edu.ncu.hrms.attendance.dto.LeaveApproveDTO;
import cn.edu.ncu.hrms.attendance.dto.LeavePageQueryDTO;
import cn.edu.ncu.hrms.attendance.vo.LeaveRequestVO;
import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import cn.edu.ncu.hrms.common.entity.LeaveRequest;
import cn.edu.ncu.hrms.common.entity.ResignRequest;
import cn.edu.ncu.hrms.common.result.PageResult;
import cn.edu.ncu.hrms.common.result.Result;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 请假服务接口
 */
public interface LeaveRequestService {

    /**
     * 提交请假申请
     *
     * @param applyDTO 请假申请信息 DTO
     * @return Result<Void>
     */
    Result<Void> apply(LeaveApplyDTO applyDTO);

    /**
     * 审批请假申请
     *
     * @param id 申请ID
     * @param approveDTO 审批信息 DTO
     * @return Result<Void>
     */
    Result<Void> approve(Integer id, LeaveApproveDTO approveDTO);

    /**
     * 根据ID获取请假申请 VO
     *
     * @param id 申请ID
     * @return Result<LeaveRequestVO>
     */
    Result<LeaveRequestVO> getVOById(Integer id);

    /**
     * 分页查询请假申请 VO
     *
     * @param queryDTO 查询条件 DTO
     * @return PageInfo<LeaveRequestVO>
     */
    PageInfo<LeaveRequestVO> pageVOs(LeavePageQueryDTO queryDTO);

    /**
     * 取消请假申请 (申请人操作)
     *
     * @param id 申请ID
     * @return Result<Void>
     */
    Result<Void> cancel(Integer id);

    /**
     * 根据请假申请ID获取申请人员工ID (供权限注解使用)
     * @param requestId 申请ID
     * @return 申请人员工ID，或 null 如果找不到
     */
    Integer getEmployeeIdByRequestId(Integer requestId);


    /**
     * 获取个人请假申请记录
     * @param queryDTO
     * @return
     */
    Result<PageResult<LeaveRequest>> getSelfLeavePage(PageQueryDTO queryDTO);
}