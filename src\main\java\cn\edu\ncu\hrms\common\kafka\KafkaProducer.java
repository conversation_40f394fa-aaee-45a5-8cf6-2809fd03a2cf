package cn.edu.ncu.hrms.common.kafka;

import cn.edu.ncu.hrms.common.entity.Attendance;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.stereotype.Component;

import static cn.edu.ncu.hrms.common.constant.KafkaConstant.ATTENDANCE_TOPIC;


@Component
public class KafkaProducer {


    /**
     * 考勤主题
     */
    @Bean
    public NewTopic attendanceTopic() {
        return TopicBuilder.name(ATTENDANCE_TOPIC)
                .partitions(3)
                .replicas(1)
                .build();
    }



}
