package cn.edu.ncu.hrms.auth.filter;

import cn.edu.ncu.hrms.auth.security.LoginUser;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = getTokenFromRequest(request);
        
        log.info("请求路径: {}, 是否包含token: {}", request.getRequestURI(), token != null);
        
        // 检查请求中是否包含有效的 token 且当前 SecurityContext 中没有认证信息
        if (StringUtils.hasText(token) && SecurityContextHolder.getContext().getAuthentication() == null) {
            try {
                // 1. 从 token 中解析出用户名
                String username = jwtUtil.getUsernameFromToken(token);
                
                log.info("从Token解析的用户名: {}", username);
                
                if (StringUtils.hasText(username)) {
                    // 2. 根据用户名加载用户详情
                    // UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                    // 从Redis中获取用户信息和权限，减轻数据库压力
                    List<String> permissions = (List<String>) redisTemplate.opsForValue().get(String.format("permissions:%s", username));
                    User user = (User) redisTemplate.opsForValue().get(String.format("login:%s", username));
                    LoginUser loginUser = new LoginUser(user, permissions);


                    log.info("加载的用户详情: {}, 权限: {}", loginUser.getUsername(), loginUser.getPermissions());
                    
                    // 3. 验证 token 是否有效
                    boolean isValid = jwtUtil.validateToken(token, loginUser);
                    log.info("Token验证结果: {}", isValid);
                    
                    if (isValid) {
                        // 4. 如果 token 有效，则创建认证信息并设置到 Spring Security 上下文
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                loginUser, null, loginUser.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        log.info("认证成功，已设置SecurityContext");
                    }
                }
            } catch (Exception e) {
                // 日志记录 token 解析或验证过程中发生的异常
                log.error("认证过程中发生异常: {}", e.getMessage(), e);
            }
        }
        
        // 继续过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 从 HttpServletRequest 的 Authorization 请求头中提取 JWT
     * 
     * @param request HTTP 请求
     * @return 提取到的 JWT，如果不存在或格式不正确则返回 null
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
} 