package cn.edu.ncu.hrms.common.exception;

import java.util.Collections;
import java.util.List;

public class DataFormatException extends RuntimeException {
    private final List<String> fieldErrors;

    public DataFormatException(String message, List<String> fieldErrors) {
        super(message);
        this.fieldErrors = fieldErrors;
    }

    public DataFormatException(List<String> fieldErrors) {
        super(String.join("；", fieldErrors)); // 直接拼接错误信息
        this.fieldErrors = fieldErrors;
    }

    public List<String> getFieldErrors() {
        return Collections.unmodifiableList(fieldErrors);
    }

    public String getFormattedErrors() {
        return String.join("；", fieldErrors);
    }
}
