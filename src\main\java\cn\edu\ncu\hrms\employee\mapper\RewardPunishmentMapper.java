package cn.edu.ncu.hrms.employee.mapper;

import cn.edu.ncu.hrms.common.entity.RewardPunishment;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 奖惩信息Mapper接口
 */
@Mapper
public interface RewardPunishmentMapper {

    /**
     * 根据ID查询奖惩信息
     *
     * @param id 奖惩ID
     * @return 奖惩信息
     */
    RewardPunishment selectById(Long id);

    /**
     * 查询所有奖惩信息列表 (谨慎使用，可能数据量大)
     *
     * @return 奖惩信息列表
     */
    List<RewardPunishment> selectList();

    /**
     * 新增奖惩信息
     *
     * @param rewardPunishment 奖惩信息
     * @return 影响行数
     */
    int insert(RewardPunishment rewardPunishment);

    /**
     * 修改奖惩信息
     *
     * @param rewardPunishment 奖惩信息
     * @return 影响行数
     */
    int update(RewardPunishment rewardPunishment);

    /**
     * 根据ID删除奖惩信息
     *
     * @param id 奖惩ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 批量删除奖惩信息
     *
     * @param ids 奖惩ID列表
     * @return 影响行数
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 根据员工ID查询奖惩信息
     *
     * @param employeeId 员工ID
     * @return 奖惩信息列表
     */
    List<RewardPunishment> selectByEmployeeId(Integer employeeId);

    /**
     * 获取奖惩类型统计数据
     *
     * @return 包含type, count, totalAmount 的 Map 列表
     */
    List<Map<String, Object>> selectTypeStatistics();
    
    /**
     * 获取指定日期范围内的奖惩金额统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 包含type, totalAmount, count 的 Map 列表
     */
    List<Map<String, Object>> selectAmountStatistics(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 获取员工奖惩排行榜
     *
     * @param type 奖惩类型，null表示全部
     * @param limit 返回记录数量限制
     * @return 员工奖惩排行榜数据 (employee_id, employee_name, count, totalAmount)
     */
    List<Map<String, Object>> selectEmployeeRanking(@Param("type") Integer type, @Param("limit") Integer limit);


    /**
     * 根据动态条件查询奖惩列表 (用于分页, 配合 PageHelper 使用)
     *
     * @param queryDTO 查询条件 DTO
     * @return 奖惩列表
     */
    List<RewardPunishment> selectByCondition(RewardPunishmentQueryDTO queryDTO);


    /**
     * 根据员工ID和日期范围查询奖惩列表
     *
     * @param employeeId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 奖惩列表
     */
    List<RewardPunishment> selectByEmployeeAndDateRange(@Param("employeeId") Integer employeeId,
                                             @Param("startDate") LocalDate startDate,
                                             @Param("endDate") LocalDate endDate);


}