package cn.edu.ncu.hrms.employee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 首页仪表盘数据VO
 */
@Data
@ApiModel(description = "首页仪表盘数据")
public class DashboardVO {

    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ApiModelProperty(value = "正常出勤天数")
    private Integer attendanceDays;

    @ApiModelProperty(value = "请假天数")
    private Integer leaveDays;

    @ApiModelProperty(value = "迟到天数")
    private Integer lateDays;

    @ApiModelProperty(value = "早退天数")
    private Integer earlyDays;

    @ApiModelProperty(value = "缺勤天数")
    private Integer absentDays;
    
    @ApiModelProperty(value = "今日日期")
    private LocalDate today = LocalDate.now();
} 