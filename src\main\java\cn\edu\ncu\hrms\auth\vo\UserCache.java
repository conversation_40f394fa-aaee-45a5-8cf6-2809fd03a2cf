package cn.edu.ncu.hrms.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "用户登录缓存")
public class UserCache {
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户名", required = true, example = "EMP1001")
    private String username;

    @ApiModelProperty(value = "关联员工ID", example = "1001")
    private Integer employeeId;

    @ApiModelProperty(value = "是否管理员", example = "false")
    private Boolean isAdmin;
}
