package cn.edu.ncu.hrms.salary.service.calculation;

import cn.edu.ncu.hrms.common.constant.AttendanceConstant;
import cn.edu.ncu.hrms.common.constant.SalaryConstant;
import cn.edu.ncu.hrms.common.entity.Attendance;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考核系数计算策略接口
 */
public interface PerformanceCoefficientStrategy {
    /**
     * 判断是否匹配该策略
     *
     * @param monthlyAttendance
     * @param workDays
     * @param punishmentCount
     * @param rewardCount
     * @return
     */
    boolean matches(List<Attendance> monthlyAttendance, int workDays, long punishmentCount, long rewardCount);

    /**
     * 获取考核系数
     *
     * @return 考核系数
     */
    BigDecimal getCoefficient();


    /**
     * 判断是否没有缺勤
     *
     * @param monthlyAttendance 当月考勤记录
     * @param workDays          工作日天数
     * @return 是否为全勤
     */
    default boolean isFullAttendance(List<Attendance> monthlyAttendance, int workDays) {
        if (monthlyAttendance.size() < workDays) {
            return false;
        }
        long absentDays = getAbsentDays(monthlyAttendance);
        if (absentDays > 0) {
            return false;
        }

        return true;
    }


    /**
     * 判断考勤记录是否全部正常
     *
     * @param monthlyAttendance 当月考勤记录
     * @param workDays          工作日天数
     * @return 完美考勤
     */
    default boolean isPerfectAttendance(List<Attendance> monthlyAttendance, int workDays) {
        if (monthlyAttendance.size() < workDays) {
            return false;
        }

        /*for (Attendance att : monthlyAttendance) {
            if (!AttendanceConstant.CLOCK_NORMAL.equals(att.getClockInStatus()) || !AttendanceConstant.CLOCK_NORMAL.equals(att.getClockOutStatus())) {
                return false;
            }
        }
        return true;*/

        return monthlyAttendance.stream().allMatch(att ->
                AttendanceConstant.CLOCK_NORMAL.equals(att.getClockInStatus())
                        && AttendanceConstant.CLOCK_NORMAL.equals(att.getClockOutStatus()));
    }


    /**
     * 获取缺勤天数
     *
     * @param monthlyAttendance
     * @return
     */
    static long getAbsentDays(List<Attendance> monthlyAttendance) {
        long absentDays = monthlyAttendance.stream().filter(att ->
                AttendanceConstant.CLOCK_ABSENT.equals(att.getClockInStatus()) ||
                        AttendanceConstant.CLOCK_ABSENT.equals(att.getClockOutStatus())
        ).count();

        return absentDays;
    }

    /**
     * 获取迟到次数
     *
     * @param monthlyAttendance
     * @return
     */
    static long getLateTimes(List<Attendance> monthlyAttendance) {
        long lateTimes = monthlyAttendance.stream().filter(att ->
                AttendanceConstant.CLOCK_LATE.equals(att.getClockInStatus()) &&
                        !AttendanceConstant.CLOCK_ABSENT.equals(att.getClockOutStatus())
        ).count();

        return lateTimes;
    }

    /**
     * 获取早退次数
     *
     * @param monthlyAttendance
     * @return
     */
    static long getEarlyTimes(List<Attendance> monthlyAttendance) {
        long earlyTimes = monthlyAttendance.stream().filter(att ->
                !AttendanceConstant.CLOCK_ABSENT.equals(att.getClockInStatus()) &&
                AttendanceConstant.CLOCK_EARLY.equals(att.getClockOutStatus())
        ).count();

        return earlyTimes;
    }

    /**
     * 获取请假天数
     * @param monthlyAttendance
     * @return
     */
    static double getLeaveDays(List<Attendance> monthlyAttendance) {
        double leaveDays = monthlyAttendance.stream()
                .mapToDouble(att -> {
                    boolean isClockInLeave = AttendanceConstant.CLOCK_LEAVE.equals(att.getClockInStatus());
                    boolean isClockOutLeave = AttendanceConstant.CLOCK_LEAVE.equals(att.getClockOutStatus());

                    if (isClockInLeave && isClockOutLeave) {
                        return 1.0;
                    }
                    else if (isClockInLeave || isClockOutLeave) {
                        return 0.5;
                    }

                    return 0.0;
                })
                .sum();

        return leaveDays;
    }

}


@Component
@Order(1) // 优先级最高
class TopPerformanceStrategy implements PerformanceCoefficientStrategy {
    @Override
    public boolean matches(List<Attendance> monthlyAttendance, int workDays, long punishmentCount, long rewardCount) {
        boolean isPerfectAttendance = isPerfectAttendance(monthlyAttendance, workDays);
        // 考勤状态全部为正常且奖励达到5次以上，绩效为优秀
        return isPerfectAttendance && punishmentCount == 0 && rewardCount >= 5;
    }

    @Override
    public BigDecimal getCoefficient() {
        return new BigDecimal(SalaryConstant.COEFFICIENT_EXCELLENT);
    }

}

@Component
@Order(2)
class HighPerformanceStrategy implements PerformanceCoefficientStrategy {
    @Override
    public boolean matches(List<Attendance> monthlyAttendance, int workDays, long punishmentCount, long rewardCount) {
        boolean isFullAttendance = isFullAttendance(monthlyAttendance, workDays);
        long earlyTimes = PerformanceCoefficientStrategy.getEarlyTimes(monthlyAttendance);
        long lateTimes = PerformanceCoefficientStrategy.getLateTimes(monthlyAttendance);
        // 无缺勤，迟到和早退次数少于3，且无惩罚且奖励次数>=1
        return isFullAttendance && (earlyTimes + lateTimes <= 3) && ((punishmentCount == 0 && rewardCount >= 1));
    }

    @Override
    public BigDecimal getCoefficient() {
        return new BigDecimal(SalaryConstant.COEFFICIENT_GOOD);
    }

}

@Component
@Order(3)
class StandardPerformanceStrategy implements PerformanceCoefficientStrategy {
    @Override
    public boolean matches(List<Attendance> monthlyAttendance, int workDays, long punishmentCount, long rewardCount) {
        boolean isFullAttendance = isFullAttendance(monthlyAttendance, workDays);
        long earlyTimes = PerformanceCoefficientStrategy.getEarlyTimes(monthlyAttendance);
        long lateTimes = PerformanceCoefficientStrategy.getLateTimes(monthlyAttendance);

        // 无缺勤，迟到和早退次数少于3，且（无惩罚，或惩罚次数<3 且 奖励次数>惩罚次数）
        return isFullAttendance && (earlyTimes + lateTimes <= 3) && ((punishmentCount == 0) || (punishmentCount < 3 && rewardCount > punishmentCount));
    }

    @Override
    public BigDecimal getCoefficient() {
        return new BigDecimal(SalaryConstant.COEFFICIENT_NORMAL);
    }
}

@Component
@Order(4)
class BelowAveragePerformanceStrategy implements PerformanceCoefficientStrategy {
    @Override
    public boolean matches(List<Attendance> monthlyAttendance, int workDays, long punishmentCount, long rewardCount) {
        long absentDays = PerformanceCoefficientStrategy.getAbsentDays(monthlyAttendance);
        // 有少量缺勤（例如1-2天）或有惩罚记录但不太严重
        return (absentDays > 0 && absentDays <= 2) || (punishmentCount > 0 && punishmentCount < 3);
    }

    @Override
    public BigDecimal getCoefficient() {
        return new BigDecimal(SalaryConstant.COEFFICIENT_POOR);
    }
}

@Component
@Order(5)
class LowPerformanceStrategy implements PerformanceCoefficientStrategy {
    @Override
    public boolean matches(List<Attendance> monthlyAttendance, int workDays, long punishmentCount, long rewardCount) {
        long absentDays = PerformanceCoefficientStrategy.getAbsentDays(monthlyAttendance);
        return absentDays > 2 || punishmentCount >= 3;
    }

    @Override
    public BigDecimal getCoefficient() {
        return new BigDecimal(SalaryConstant.COEFFICIENT_BAD);
    }
}
