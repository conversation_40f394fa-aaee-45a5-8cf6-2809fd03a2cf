import request from '@/utils/request'

/**
 * 创建薪资账套
 * @param {Object} data - 账套信息
 * @returns {Promise} 返回结果
 */
export function addAccountSet(data) {
  return request({
    url: '/salary/account',
    method: 'post',
    data
  })
}

/**
 * 修改薪资账套
 * @param {Number} id - 账套ID
 * @param {Object} data - 账套信息
 * @returns {Promise} 返回结果
 */
export function updateAccountSet(id, data) {
  return request({
    url: `/salary/account/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除薪资账套
 * @param {Number} id - 账套ID
 * @returns {Promise} 返回结果
 */
export function deleteAccountSet(id) {
  return request({
    url: `/salary/account/${id}`,
    method: 'delete'
  })
}

/**
 * 根据ID获取账套详情
 * @param {Number} id - 账套ID
 * @returns {Promise} 返回账套详情
 */
export function getAccountSetById(id) {
  return request({
    url: `/salary/account/${id}`,
    method: 'get'
  })
}

/**
 * 分页查询账套列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回账套列表
 */
export function getAccountSetList(params) {
  return request({
    url: '/salary/account/list',
    method: 'get',
    params
  })
}

/**
 * 获取所有账套列表（不分页）
 * @returns {Promise} 返回所有账套列表
 */
export function getAllAccountSets() {
  return request({
    url: '/salary/account/all',
    method: 'get'
  })
}
