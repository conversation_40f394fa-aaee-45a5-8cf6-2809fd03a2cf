<template>
  <div class="dashboard-container">
    <div class="dashboard-welcome-banner">
      <div class="welcome-left">
        <div class="user-avatar">
          <img src="https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif" style="width: 80px; height: 80px; border-radius: 50%;" />
        </div>
        <div class="welcome-info">
          <div class="welcome-title">{{ greeting }}，{{ name }}！</div>
          <div class="welcome-subtitle">欢迎使用HRMS企业人事管理系统</div>
        </div>
      </div>
      <div class="welcome-right">
        <div v-if="isAdmin" class="welcome-stat">
          <i class="el-icon-user"></i>
          <div class="stat-info">
            <div class="stat-value">{{ systemData.employeeCount || 0 }}</div>
            <div class="stat-label">员工总数</div>
          </div>
        </div>
        <div v-else class="welcome-date">
          {{ today }} {{ currentTime }}
        </div>
      </div>
    </div>
    <div class="dashboard-info">

      <!-- 管理员显示的内容 -->
      <template v-if="isAdmin">
        <div class="dashboard-stats-row">
          <div class="stat-card">
            <div class="stat-icon" style="background-color: #409EFF;">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemData.employeeCount || 0 }}</div>
              <div class="stat-title">员工总数</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background-color: #67C23A;">
              <i class="el-icon-s-check"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemData.normalCount || 0 }}</div>
              <div class="stat-title">状态正常</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background-color: #E6A23C;">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemData.leaveCount || 0 }}</div>
              <div class="stat-title">今日请假</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background-color: #F56C6C;">
              <i class="el-icon-warning-outline"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemData.lateCount || 0 }}</div>
              <div class="stat-title">今日迟到</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background-color: #909399;">
              <i class="el-icon-alarm-clock"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemData.earlyLeaveCount || 0 }}</div>
              <div class="stat-title">今日早退</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon" style="background-color: #F56C6C;">
              <i class="el-icon-close"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemData.absentCount || 0 }}</div>
              <div class="stat-title">今日旷工</div>
            </div>
          </div>
        </div>

        <div class="dashboard-charts-row">
          <div class="chart-container">
            <div class="chart-header">
              <h3>员工入职情况</h3>
            </div>
            <div class="chart-content">
              <div ref="employeeChart" style="width: 100%; height: 280px;"></div>
            </div>
          </div>

          <div class="chart-container">
            <div class="chart-header">
              <h3>部门员工分布</h3>
            </div>
            <div class="chart-content">
              <div style="position: relative; width: 100%; height: 280px;">
                <div ref="departmentChart" style="width: 100%; height: 280px;"></div>
                <div v-if="departmentLegend.length > 0" class="department-legend" :class="{ 'legend-collapsed': legendCollapsed }">
                  <div class="legend-title">
                    <span>部门图例</span>
                    <div class="legend-actions">
                      <span class="legend-expand-all" @click="toggleExpandAll" title="展开/折叠所有部门">
                        <i :class="allExpanded ? 'el-icon-minus' : 'el-icon-plus'"></i>
                      </span>
                    </div>
                  </div>
                  <div class="legend-items">
                    <div
                      v-for="(item, index) in departmentLegendTree"
                      :key="index"
                      class="legend-item"
                    >
                      <div class="legend-item-content">
                        <span
                          v-if="item.children && item.children.length > 0"
                          class="legend-toggle"
                          @click="toggleLegendItem(item)"
                        >
                          <i :class="item.expanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
                        </span>
                        <span v-else class="legend-toggle-placeholder"></span>
                        <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
                        <span class="legend-name">
                          {{ item.name }} ({{ item.value }}人)
                        </span>
                      </div>

                      <!-- 递归显示子部门 -->
                      <div
                        v-if="item.expanded && item.children && item.children.length > 0"
                        class="legend-children"
                      >
                        <div
                          v-for="(child, childIndex) in item.children"
                          :key="childIndex"
                          class="legend-item"
                        >
                          <div class="legend-item-content">
                            <span
                              v-if="child.children && child.children.length > 0"
                              class="legend-toggle"
                              @click="toggleLegendItem(child)"
                            >
                              <i :class="child.expanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
                            </span>
                            <span v-else class="legend-toggle-placeholder"></span>
                            <span class="legend-color" :style="{ backgroundColor: child.color }"></span>
                            <span class="legend-name">
                              {{ child.name }} ({{ child.value }}人)
                            </span>
                          </div>

                          <!-- 递归显示孙部门 -->
                          <div
                            v-if="child.expanded && child.children && child.children.length > 0"
                            class="legend-children"
                          >
                            <div
                              v-for="(grandchild, grandchildIndex) in child.children"
                              :key="grandchildIndex"
                              class="legend-item"
                            >
                              <div class="legend-item-content">
                                <span class="legend-toggle-placeholder"></span>
                                <span class="legend-color" :style="{ backgroundColor: grandchild.color }"></span>
                                <span class="legend-name">
                                  {{ grandchild.name }} ({{ grandchild.value }}人)
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-shortcuts">
          <div class="shortcuts-header">
            <h3>功能快捷入口</h3>
          </div>
          <div class="shortcuts-content">
            <div class="shortcut-item" @click="handleQuickAction('addUser')">
              <div class="shortcut-icon" style="background-color: #409EFF;">
                <i class="el-icon-plus"></i>
              </div>
              <div class="shortcut-info">
                <div class="shortcut-title">添加用户</div>
                <div class="shortcut-desc">创建新用户账号</div>
              </div>
            </div>

            <div class="shortcut-item" @click="handleQuickAction('addDepartment')">
              <div class="shortcut-icon" style="background-color: #67C23A;">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="shortcut-info">
                <div class="shortcut-title">添加部门</div>
                <div class="shortcut-desc">创建新部门</div>
              </div>
            </div>

            <div class="shortcut-item" @click="handleQuickAction('addRole')">
              <div class="shortcut-icon" style="background-color: #E6A23C;">
                <i class="el-icon-s-check"></i>
              </div>
              <div class="shortcut-info">
                <div class="shortcut-title">创建角色</div>
                <div class="shortcut-desc">添加新角色</div>
              </div>
            </div>

            <div class="shortcut-item" @click="handleQuickAction('addPermission')">
              <div class="shortcut-icon" style="background-color: #F56C6C;">
                <i class="el-icon-key"></i>
              </div>
              <div class="shortcut-info">
                <div class="shortcut-title">新增权限</div>
                <div class="shortcut-desc">添加系统权限</div>
              </div>
            </div>

            <div class="shortcut-item" @click="handleQuickAction('addPosition')">
              <div class="shortcut-icon" style="background-color: #909399;">
                <i class="el-icon-s-management"></i>
              </div>
              <div class="shortcut-info">
                <div class="shortcut-title">添加职位</div>
                <div class="shortcut-desc">创建新职位</div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 员工显示的内容 -->
      <el-row :gutter="20" v-else style="margin-top: 20px;">
        <!-- 快捷功能入口 -->
        <el-col :span="8">
          <el-card shadow="hover" class="dashboard-card">
            <div slot="header" class="clearfix">
              <span>快捷功能</span>
            </div>
            <div class="dashboard-card-content">
              <div class="employee-shortcuts">
                <div class="shortcut-item" @click="goToProfile">
                  <div class="shortcut-icon" style="background-color: #409EFF;">
                    <i class="el-icon-user"></i>
                  </div>
                  <div class="shortcut-info">
                    <div class="shortcut-title">个人档案</div>
                    <div class="shortcut-desc">查看个人基本信息</div>
                  </div>
                </div>

                <div class="shortcut-item" @click="goToRewards">
                  <div class="shortcut-icon" style="background-color: #67C23A;">
                    <i class="el-icon-medal"></i>
                  </div>
                  <div class="shortcut-info">
                    <div class="shortcut-title">奖惩记录</div>
                    <div class="shortcut-desc">查看个人奖惩情况</div>
                  </div>
                </div>

                <div class="shortcut-item" @click="openLeaveDialog">
                  <div class="shortcut-icon" style="background-color: #E6A23C;">
                    <i class="el-icon-date"></i>
                  </div>
                  <div class="shortcut-info">
                    <div class="shortcut-title">请假申请</div>
                    <div class="shortcut-desc">提交请假申请</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 考勤打卡 -->
        <el-col :span="16">
          <el-card class="clock-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>考勤打卡</span>
              <span class="current-time">{{ currentTime }}</span>
            </div>
            <div class="clock-content">
              <el-row :gutter="20">
                <el-col :span="14">
                  <div class="clock-main">
                    <!-- 打卡状态和时间（水平对称布局） -->
                    <div class="clock-info-row">
                      <!-- 上班打卡时间（左侧） -->
                      <div class="clock-time-left">
                        <div class="time-label">签到时间</div>
                        <div class="time-value" :class="getClockInTimeClass()">{{ attendanceData.clockInTime || '--:--' }}</div>
                        <el-button type="primary" :disabled="clockInDisabled" @click="handleClockIn" icon="el-icon-position" size="small" round>上班签到</el-button>
                      </div>

                      <!-- 打卡状态（中间） -->
                      <div class="clock-status">
                        <div class="clock-circle" :class="clockStatusClass">
                          <span>{{ clockStatusText }}</span>
                        </div>
                      </div>

                      <!-- 下班打卡时间（右侧） -->
                      <div class="clock-time-right">
                        <div class="time-label">签退时间</div>
                        <div class="time-value" :class="getClockOutTimeClass()">{{ attendanceData.clockOutTime || '--:--' }}</div>
                        <el-button type="success" :disabled="clockOutDisabled" @click="handleClockOut" icon="el-icon-position" size="small" round>下班签退</el-button>
                      </div>
                    </div>

                    <div class="clock-info">
                      <!-- <div v-if="attendanceData.status && attendanceData.status !== '未签到'" class="clock-status-tag">
                        <el-tag :type="getStatusTagType(attendanceData.status)" effect="dark">{{ attendanceData.status }}</el-tag>
                      </div> -->

                      <div class="location-info">
                        <i class="el-icon-location"></i>
                        <span>{{ currentLocation ? currentLocation : '正在获取位置...' }}</span>
                      </div>

                      <!-- 部门考勤规则 -->
                      <div class="attendance-rules">
                        <div class="rules-header">
                          <i class="el-icon-info-circle"></i> 部门考勤规则
                        </div>
                        <div class="rules-content">
                          <div class="rule-item">
                            <span class="rule-label">签到时间</span>
                            <span class="rule-value">{{ clockRules.clockInStartTime || '00:00' }} - {{ clockRules.clockInEndTime || '未配置' }}</span>
                          </div>
                          <div class="rule-item">
                            <span class="rule-label">签退时间</span>
                            <span class="rule-value">{{ clockRules.clockOutStartTime || '未配置' }} - {{ clockRules.clockOutEndTime || '23:59' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>

                <el-col :span="10">
                  <!-- 地图容器 -->
                  <div id="dashboardMapContainer" class="map-container"></div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 考勤日历 -->
      <el-row :gutter="20" v-if="!isAdmin" style="margin-top: 10px">
        <el-col :span="24">
          <el-card class="calendar-card" shadow="hover">
            <div slot="header" class="title">
              <span>考勤月历</span>
            </div>

            <div class="calendar-legend">
              <div class="legend-item">
                <span class="legend-dot normal"></span>
                <span>正常</span>
              </div>
              <div class="legend-item">
                <span class="legend-dot late"></span>
                <span>迟到</span>
              </div>
              <div class="legend-item">
                <span class="legend-dot early"></span>
                <span>早退</span>
              </div>
              <div class="legend-item">
                <span class="legend-dot absent"></span>
                <span>缺勤</span>
              </div>
              <div class="legend-item">
                <span class="legend-dot leave"></span>
                <span>请假</span>
              </div>
            </div>

            <el-calendar v-model="calendarDate" @input="handleCalendarChange">
              <template slot="dateCell" slot-scope="{date, data}">
                <div :class="['calendar-day', getAttendanceClass(date), isWeekend(date) ? 'weekend' : '']">
                  <div class="day-number">{{ data.day.split('-').slice(2).join('') }}</div>
                  <div class="day-status-indicator" :class="getAttendanceClass(date)"></div>
                  <div class="day-status">{{ getAttendanceStatus(date) }}</div>
                </div>
              </template>
            </el-calendar>
          </el-card>
        </el-col>
      </el-row>

      <!-- 请假申请弹窗 -->
      <el-dialog title="请假申请" :visible.sync="leaveDialogVisible" width="500px" append-to-body :close-on-click-modal="false">
        <el-form ref="leaveForm" :model="leaveForm" :rules="leaveRules" label-width="100px">
          <el-form-item label="请假类型" prop="leaveType">
            <el-select v-model="leaveForm.leaveType" placeholder="请选择请假类型" style="width: 100%;">
              <el-option label="事假" value="PERSONAL"></el-option>
              <el-option label="病假" value="SICK"></el-option>
              <el-option label="年假" value="ANNUAL"></el-option>
              <el-option label="婚假" value="MARRIAGE"></el-option>
              <el-option label="产假" value="MATERNITY"></el-option>
              <el-option label="丧假" value="BEREAVEMENT"></el-option>
              <el-option label="其他" value="OTHER"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="leaveForm.startDate"
              type="date"
              placeholder="选择开始日期"
              style="width: 100%;"
              value-format="yyyy-MM-dd"
              :picker-options="datePickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="开始时间段" prop="startPeriod">
            <el-select v-model="leaveForm.startPeriod" placeholder="请选择时间段" style="width: 100%;">
              <el-option label="上午" value="AM"></el-option>
              <el-option label="下午" value="PM"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="leaveForm.endDate"
              type="date"
              placeholder="选择结束日期"
              style="width: 100%;"
              value-format="yyyy-MM-dd"
              :picker-options="datePickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间段" prop="endPeriod">
            <el-select v-model="leaveForm.endPeriod" placeholder="请选择时间段" style="width: 100%;">
              <el-option label="上午" value="AM"></el-option>
              <el-option label="下午" value="PM"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请假原因" prop="reason">
            <el-input
              v-model="leaveForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入请假原因">
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="leaveDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitLeaveForm">提 交</el-button>
        </div>
      </el-dialog>
    </div>

    <!-- 添加用户弹窗 -->
    <el-dialog :title="userDialogTitle" :visible.sync="userDialogVisible" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="userForm" :model="userForm" :rules="userRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="登录密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="是否管理员" prop="isAdmin">
          <el-switch v-model="userForm.isAdmin" active-text="管理员" />
        </el-form-item>
        <el-form-item label="关联员工" prop="employeeId">
          <el-select v-model="userForm.employeeId" placeholder="请选择关联员工" filterable clearable style="width: 100%;">
            <el-option
              v-for="item in employeeOptions"
              :key="item.id"
              :label="item.name + ' (' + item.workNo + ')'"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色分配" prop="roleIds">
          <el-checkbox-group v-model="userForm.roleIds">
            <el-checkbox v-for="role in roleOptions" :key="role.id" :label="role.id">
              {{ role.name }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="form-tip">请为用户分配角色，可多选</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUserForm">确 定</el-button>
        <el-button @click="userDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加部门弹窗 -->
    <el-dialog :title="departmentDialogTitle" :visible.sync="departmentDialogVisible" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="departmentForm" :model="departmentForm" :rules="departmentRules" label-width="100px">
        <el-form-item label="上级部门" prop="parentId">
          <div class="parent-department-container">
            <el-tree-select
              v-model="departmentForm.parentId"
              :data="departmentOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              placeholder="请选择上级部门"
              check-strictly
              clearable
            />
            <el-button
              v-if="departmentForm.parentId"
              type="text"
              icon="el-icon-close"
              class="clear-parent-btn"
              @click="departmentForm.parentId = null"
            ></el-button>
          </div>
          <div class="form-tip">顶级部门可不选</div>
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="departmentForm.name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门领导" prop="leaderId">
          <div class="leader-select-container">
            <div class="department-tree">
              <el-tree
                :data="departmentOptions"
                :props="{ label: 'name', children: 'children' }"
                node-key="id"
                highlight-current
                @node-click="handleDepartmentSelect"
              ></el-tree>
            </div>
            <div class="employee-list">
              <div class="employee-list-header">
                <span>{{ selectedDepartmentName || '请选择部门' }}</span>
                <div v-if="loadingDepartmentEmployees" class="loading-indicator">
                  <i class="el-icon-loading"></i> 加载中...
                </div>
              </div>
              <el-table
                v-loading="loadingDepartmentEmployees"
                :data="departmentEmployees"
                height="300"
                @row-click="handleEmployeeSelect"
                highlight-current-row
                :row-class-name="tableRowClassName"
                empty-text="该部门暂无员工"
              >
                <el-table-column prop="workNo" label="工号" width="120"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
              </el-table>
            </div>
          </div>
          <div class="selected-employee" v-if="selectedEmployee">
            <span class="label">已选择:</span>
            <el-tag size="medium" type="success">
              <i class="el-icon-user"></i>
              {{ selectedEmployee.name }}
              <span class="work-no" v-if="selectedEmployee.workNo">({{ selectedEmployee.workNo }})</span>
            </el-tag>
            <el-button type="text" icon="el-icon-close" class="clear-btn" @click="clearSelectedEmployee"></el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDepartmentForm">确 定</el-button>
        <el-button @click="departmentDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加角色弹窗 -->
    <el-dialog :title="roleDialogTitle" :visible.sync="roleDialogVisible" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="roleForm" :model="roleForm" :rules="roleRules" label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="roleKey">
          <el-input v-model="roleForm.roleKey" placeholder="请输入角色标识" />
          <div class="form-tip">角色标识必须以ROLE_开头，例如：ROLE_admin</div>
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="roleForm.description" type="textarea" placeholder="请输入角色描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRoleForm">确 定</el-button>
        <el-button @click="roleDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加权限弹窗 -->
    <el-dialog :title="permissionDialogTitle" :visible.sync="permissionDialogVisible" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="permissionForm" :model="permissionForm" :rules="permissionRules" label-width="100px">
        <el-form-item label="权限名称" prop="permissionName">
          <el-input v-model="permissionForm.permissionName" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限标识" prop="permissionKey">
          <el-input v-model="permissionForm.permissionKey" placeholder="请输入权限标识" />
          <div class="form-tip">一级(system)、二级(system:permission)、三级(system:permission:list)</div>
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input v-model="permissionForm.description" type="textarea" placeholder="请输入权限描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPermissionForm">确 定</el-button>
        <el-button @click="permissionDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加职位弹窗 -->
    <el-dialog :title="positionDialogTitle" :visible.sync="positionDialogVisible" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="positionForm" :model="positionForm" :rules="positionRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="positionForm.name" placeholder="请输入职位名称" />
        </el-form-item>
        <el-form-item label="职级" prop="level">
          <el-input-number v-model="positionForm.level" :min="5" :max="10" :step="1" controls-position="right" />
          <div class="form-tip">职级必须是5-10之间的整数</div>
        </el-form-item>
        <el-form-item label="账套" prop="accountId">
          <el-select v-model="positionForm.accountId" placeholder="请选择关联账套" style="width: 100%;" clearable>
            <el-option
              v-for="item in accountOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPositionForm">确 定</el-button>
        <el-button @click="positionDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable no-undef */
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { getOnboardingStats, getAttendanceStats, getDepartmentDistribution } from '@/api/dashboard'
import { listPersonalAttendance, clock, getDepartmentAttendanceConfig, getTodayAttendance } from '@/api/attendance/record'
import { applyLeave } from '@/api/attendance/leave'
import { addUser } from '@/api/system/user'
import { addDepartment, getDepartmentTree, getEmployeesByDepartmentId } from '@/api/system/department'
import { addRole, listRole } from '@/api/system/role'
import { addPermission } from '@/api/system/permission'
import { addPosition } from '@/api/system/position'
import { getUnregisteredEmployees } from '@/api/employee/employee'
import { listAccount } from '@/api/salary/account'
import { initMap, createMarker, getCurrentPosition, getAddressFromAPI } from '@/utils/amap'

export default {
  name: 'Dashboard',
  data() {
    // 当前日期和星期
    const now = dayjs();
    const currentYear = now.year();
    const currentMonth = now.month();

    return {
      currentTime: '',
      timer: null,
      systemData: {
        employeeCount: 0,
        normalCount: 0,
        leaveCount: 0,
        lateCount: 0,
        earlyLeaveCount: 0,
        absentCount: 0
      },
      attendanceData: {
        clockInTime: '',
        clockOutTime: '',
        status: '正常',
        clockInStatus: '',
        clockOutStatus: ''
      },
      clockStatusText: '未打卡',
      clockStatusClass: 'not-clocked',
      clockInDisabled: false,
      clockOutDisabled: true,
      // 图表实例
      employeeChart: null,
      departmentChart: null,
      // 图表数据
      onboardingData: [],
      departmentDistributionData: [],
      // 部门图例数据
      departmentLegend: [],
      // 部门图例树形数据
      departmentLegendTree: [],
      // 是否全部展开
      allExpanded: false,
      // 图例是否折叠
      legendCollapsed: false,
      // 加载状态
      loading: {
        attendance: false,
        onboarding: false,
        distribution: false
      },

      // 日历相关
      calendarDate: new Date(),
      calendarYear: currentYear,
      calendarMonth: currentMonth,
      attendanceRecords: {},

      // 请假申请相关
      leaveDialogVisible: false,
      leaveForm: {
        leaveType: 'PERSONAL',
        startDate: '',
        startPeriod: 'AM',
        endDate: '',
        endPeriod: 'PM',
        reason: ''
      },

      // 高德地图相关
      map: null,
      userMarker: null,
      locationMarker: null,
      locationCircle: null,
      currentLocation: '',

      // 考勤规则
      clockRules: {
        clockInStartTime: null,
        clockInEndTime: null,
        lateThreshold: null,
        lateDeduction: null,
        clockOutStartTime: null,
        clockOutEndTime: null,
        earlyThreshold: null,
        earlyDeduction: null,
        clockLocation: null,
        locationName: null,
        clockRadius: null
      },
      leaveRules: {
        leaveType: [
          { required: true, message: '请选择请假类型', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        startPeriod: [
          { required: true, message: '请选择开始时间段', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        endPeriod: [
          { required: true, message: '请选择结束时间段', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入请假原因', trigger: 'blur' },
          { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
        ]
      },
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400000; // 不能选择今天之前的日期
        }
      },

      // 用户弹窗相关数据
      userDialogVisible: false,
      userDialogTitle: '添加用户',
      userForm: {
        username: '',
        password: '',
        isAdmin: false,
        employeeId: null,
        roleIds: []
      },
      userRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      employeeOptions: [],
      roleOptions: [],

      // 部门弹窗相关数据
      departmentDialogVisible: false,
      departmentDialogTitle: '添加部门',
      departmentForm: {
        name: '',
        parentId: null,
        leaderId: null,
        description: ''
      },
      departmentRules: {
        name: [
          { required: true, message: '请输入部门名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '部门名称不能包含空格', trigger: 'blur' }
        ]
      },
      departmentOptions: [],
      // 部门领导选择相关数据
      selectedDepartmentName: '',
      selectedDepartmentId: null,
      departmentEmployees: [],
      loadingDepartmentEmployees: false,
      selectedEmployee: null,

      // 角色弹窗相关数据
      roleDialogVisible: false,
      roleDialogTitle: '添加角色',
      roleForm: {
        name: '',
        roleKey: 'ROLE_',
        description: ''
      },
      roleRules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '角色名称不能包含空格', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '请输入角色标识', trigger: 'blur' },
          { pattern: /^ROLE_[A-Za-z0-9_]+$/, message: '角色标识必须以ROLE_开头，且只能包含字母、数字和下划线', trigger: 'blur' }
        ]
      },

      // 权限弹窗相关数据
      permissionDialogVisible: false,
      permissionDialogTitle: '添加权限',
      permissionForm: {
        permissionName: '',
        permissionKey: '',
        description: ''
      },
      permissionRules: {
        permissionName: [
          { required: true, message: '请输入权限名称', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '权限名称不能包含空格', trigger: 'blur' }
        ],
        permissionKey: [
          { required: true, message: '权限标识不能为空', trigger: 'blur' },
          {
            pattern: /^[a-z]+(\:[a-z]+){0,2}$/,
            message: '权限标识格式：一级(system)、二级(system:permission)、三级(system:permission:list)',
            trigger: 'blur'
          }
        ]
      },

      // 职位弹窗相关数据
      positionDialogVisible: false,
      positionDialogTitle: '添加职位',
      positionForm: {
        name: '',
        level: 5,
        accountId: null,
        description: ''
      },
      positionRules: {
        name: [
          { required: true, message: '请输入职位名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '职位名称不能包含空格', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '请选择职级', trigger: 'blur' },
          { type: 'number', min: 5, max: 10, message: '职级必须在5-10之间', trigger: 'blur' }
        ],
        accountId: [
          { required: true, message: '请选择关联账套', trigger: 'change' }
        ]
      },
      // 账套选项
      accountOptions: []
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'userId',
      'isAdmin'
    ]),
    today() {
      const date = new Date()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const weekday = weekdays[date.getDay()]
      return `${year}年${month}月${day}日 ${weekday}`
    },
    greeting() {
      const hour = new Date().getHours()
      if (hour < 6) {
        return '凌晨好'
      } else if (hour < 9) {
        return '早上好'
      } else if (hour < 12) {
        return '上午好'
      } else if (hour < 14) {
        return '中午好'
      } else if (hour < 17) {
        return '下午好'
      } else if (hour < 19) {
        return '傍晚好'
      } else {
        return '晚上好'
      }
    }
  },
  created() {
    this.updateTime()
    this.timer = setInterval(this.updateTime, 1000)

    // 模拟获取数据
    this.fetchData()
  },
  mounted() {
    // 如果是管理员，初始化图表
    if (this.isAdmin) {
      this.$nextTick(() => {
        this.initEmployeeChart()
        this.initDepartmentChart()
      })
    } else {
      // 如果是员工，初始化考勤日历和打卡模块
      this.fetchAttendanceData()
      this.getTodayAttendance()
      this.getClockRules()

      // 初始化高德地图
      this.$nextTick(() => {
        this.initDashboardMap()
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    // 销毁图表实例
    if (this.employeeChart) {
      this.employeeChart.dispose()
    }
    if (this.departmentChart) {
      this.departmentChart.dispose()
    }
    // 销毁地图实例
    if (this.map) {
      this.map.destroy()
    }
  },
  methods: {
    updateTime() {
      const date = new Date()
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      this.currentTime = `${hours}:${minutes}:${seconds}`
    },
    fetchData() {
      // 获取真实数据
      if (this.isAdmin) {
        this.fetchAttendanceStats()
        this.fetchOnboardingStats()
        this.fetchDepartmentDistribution()
      }

      // 获取考勤数据 (员工视图)
      if (!this.isAdmin) {
        this.fetchTodayAttendance()
      }
    },

    // 获取考勤统计数据
    fetchAttendanceStats() {
      this.loading.attendance = true
      getAttendanceStats()
        .then(response => {
          if (response.code === 1 && response.data) {
            const data = response.data
            this.systemData = {
              employeeCount: data.totalEmployees || 0,
              normalCount: data.normalCount || 0,
              leaveCount: data.leaveCount || 0,
              lateCount: data.lateCount || 0,
              earlyLeaveCount: data.earlyLeaveCount || 0,
              absentCount: data.absentCount || 0
            }
          } else {
            this.$message.error(response.msg || '获取考勤统计数据失败')
          }
        })
        .catch(error => {
          // token过期错误已在请求拦截器中处理，这里不需要额外处理
          // 只处理非token过期的错误
          if (!error.isTokenExpired) {
            this.$message.error('获取考勤统计数据出错')
          }
        })
        .finally(() => {
          this.loading.attendance = false
        })
    },

    // 获取入职统计数据
    fetchOnboardingStats() {
      this.loading.onboarding = true
      getOnboardingStats()
        .then(response => {
          if (response.code === 1 && response.data) {
            this.onboardingData = response.data.monthlyCounts || []
            // 更新图表
            if (this.employeeChart) {
              this.updateEmployeeChart()
            }
          } else {
            this.$message.error(response.msg || '获取入职统计数据失败')
          }
        })
        .catch(error => {
          // token过期错误已在请求拦截器中处理，这里不需要额外处理
          // 只处理非token过期的错误
          if (!error.isTokenExpired) {
            this.$message.error('获取入职统计数据出错')
          }
        })
        .finally(() => {
          this.loading.onboarding = false
        })
    },

    // 获取部门分布数据
    fetchDepartmentDistribution() {
      this.loading.distribution = true
      getDepartmentDistribution()
        .then(response => {
          console.log('部门分布原始响应:', response)
          if (response.code === 1) {
            // 设置部门分布数据
            this.departmentDistributionData = response.data || []
            console.log('设置部门分布数据:', this.departmentDistributionData)

            // 更新图表
            if (this.departmentChart) {
              this.updateDepartmentChart()
            }
          } else {
            this.$message.error(response.msg || '获取部门分布数据失败')
            // 清空数据
            this.departmentDistributionData = []

            // 更新图表，显示无数据提示
            if (this.departmentChart) {
              this.updateDepartmentChart()
            }
          }
        })
        .catch(error => {
          // token过期错误已在请求拦截器中处理，这里不需要额外处理
          // 只处理非token过期的错误
          if (!error.isTokenExpired) {
            this.$message.error('获取部门分布数据出错')
          }

          // 清空数据
          this.departmentDistributionData = []

          // 更新图表，显示无数据提示
          if (this.departmentChart) {
            this.updateDepartmentChart()
          }
        })
        .finally(() => {
          this.loading.distribution = false
        })
    },
    // 获取今日考勤记录
    fetchTodayAttendance() {
      console.log('获取今日考勤记录')

      // 使用新的API接口获取今日考勤记录
      getTodayAttendance().then(response => {
        if (response.code === 1 && response.data) {
          const todayRecord = response.data
          console.log('获取到的今日考勤记录:', todayRecord)

          // 更新今日考勤数据
          if (todayRecord.clockInTime) {
            // 直接使用LocalTime格式，只需要截取到分钟
            const clockInTimeStr = todayRecord.clockInTime.substring(0, 5) // 截取HH:mm部分
            this.attendanceData.clockInTime = clockInTimeStr
            this.attendanceData.clockInStatus = todayRecord.clockInStatus || ''
          } else {
            this.attendanceData.clockInTime = ''
            this.attendanceData.clockInStatus = ''
          }

          if (todayRecord.clockOutTime) {
            // 直接使用LocalTime格式，只需要截取到分钟
            const clockOutTimeStr = todayRecord.clockOutTime.substring(0, 5) // 截取HH:mm部分
            this.attendanceData.clockOutTime = clockOutTimeStr
            this.attendanceData.clockOutStatus = todayRecord.clockOutStatus || ''
          } else {
            this.attendanceData.clockOutTime = ''
            this.attendanceData.clockOutStatus = ''
          }

          // 更新打卡按钮状态
          this.clockInDisabled = !!this.attendanceData.clockInTime
          this.clockOutDisabled = !this.attendanceData.clockInTime || !!this.attendanceData.clockOutTime

          // 更新考勤状态
          this.attendanceData.status = this.getStatusText(todayRecord.clockInStatus, todayRecord.clockOutStatus)

          // 更新打卡状态文本和样式
          this.updateClockStatusDisplay()
        } else {
          // 如果没有今日记录或请求失败，重置状态
          this.attendanceData = {
            clockInTime: '',
            clockOutTime: '',
            status: '未签到',
            clockInStatus: '',
            clockOutStatus: ''
          }
          this.clockInDisabled = false
          this.clockOutDisabled = true

          // 更新打卡状态文本和样式
          this.updateClockStatusDisplay()
        }
      }).catch(error => {
        console.error('获取今日考勤记录失败', error)
        this.$message.error('获取今日考勤记录失败')

        // 出错时重置状态
        this.attendanceData = {
          clockInTime: '',
          clockOutTime: '',
          status: '未签到',
          clockInStatus: '',
          clockOutStatus: ''
        }
        this.clockInDisabled = false
        this.clockOutDisabled = true

        // 更新打卡状态文本和样式
        this.updateClockStatusDisplay()
      })
    },

    // 处理上班打卡
    handleClockIn() {
      console.log('开始上班打卡流程')
      console.log('当前位置信息:', this.currentLocation)


      // 检查是否获取到位置
      if (!this.currentLocation) {
        console.log('未获取到位置信息，请求用户位置')
        this.$message.warning('正在获取您的位置，请稍候再试')
        this.getUserLocation()
        return
      }

      // 继续打卡流程
      this.processClockIn()
    },

    // 处理打卡流程（分离逻辑，便于复用）
    processClockIn(forceSubmit = false) {
      console.log('处理打卡流程，强制提交:', forceSubmit)

      // 如果强制提交或已有位置信息但没有userMarker
      if (forceSubmit || (this.currentLocation && !this.userMarker)) {
        console.log('强制提交或使用当前位置信息')
        this.submitClockIn()
        return
      }

      // 检查是否在打卡范围内
      if (this.clockRules.clockLocation && this.clockRules.clockRadius && this.userMarker) {
        const userPosition = this.userMarker.getPosition()
        const locationParts = this.clockRules.clockLocation.split(',')

        if (locationParts.length === 2) {
          const lng = parseFloat(locationParts[0])
          const lat = parseFloat(locationParts[1])

          if (!isNaN(lng) && !isNaN(lat)) {
            const clockPosition = new AMap.LngLat(lng, lat)
            const distance = userPosition.distance(clockPosition)
            console.log('用户距离打卡地点:', distance, '米')

            if (distance > this.clockRules.clockRadius) {
              this.$confirm(`您当前不在打卡范围内，距离打卡地点约${Math.round(distance)}米，是否继续打卡？`, '打卡提示', {
                confirmButtonText: '继续打卡',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.submitClockIn()
              }).catch(() => {
                // 用户取消打卡
                console.log('用户取消打卡')
              })
              return
            }
          }
        }
      }

      // 在打卡范围内或无法判断范围，直接打卡
      console.log('在打卡范围内或无法判断范围，直接打卡')
      this.submitClockIn()
    },

    // 提交上班打卡
    submitClockIn() {
      console.log('提交上班打卡')

      // 获取位置信息
      let location = null

      if (this.userMarker) {
        // 如果有用户标记，使用标记位置
        const position = this.userMarker.getPosition()
        location = [position.lng, position.lat]
        console.log('使用用户标记位置:', location)
      } else if (this.currentLocation) {
        // 如果没有用户标记但有位置描述，尝试从clockRules中获取位置
        if (this.clockRules.clockLocation) {
          try {
            // 尝试解析打卡地点坐标
            let clockLocationCoords = null

            if (this.clockRules.clockLocation.includes('[') && this.clockRules.clockLocation.includes(']')) {
              // 尝试解析JSON格式
              try {
                clockLocationCoords = JSON.parse(this.clockRules.clockLocation)
              } catch (e) {
                console.error('解析打卡位置JSON失败:', e)
              }
            } else {
              // 尝试解析逗号分隔的格式
              const locationParts = this.clockRules.clockLocation.split(',')
              if (locationParts.length === 2) {
                const lng = parseFloat(locationParts[0])
                const lat = parseFloat(locationParts[1])
                if (!isNaN(lng) && !isNaN(lat)) {
                  clockLocationCoords = [lng, lat]
                }
              }
            }

            if (clockLocationCoords && Array.isArray(clockLocationCoords) &&
                clockLocationCoords.length === 2 &&
                !isNaN(clockLocationCoords[0]) && !isNaN(clockLocationCoords[1])) {
              location = clockLocationCoords
              console.log('使用打卡地点位置:', location)
            }
          } catch (error) {
            console.error('获取打卡地点位置失败:', error)
          }
        }
      }

      // 如果仍然没有位置信息，使用默认位置
      if (!location) {
        // 使用默认位置（南昌大学坐标）
        location = [115.762609, 28.174721]
        console.log('使用默认位置:', location)
      }

      // 根据接口要求，传递location和type参数
      clock({
        location: location,
        type: 'IN'  // 上班打卡类型为IN
      }).then(response => {
        if (response.code === 1) {
          // 使用后端返回的消息，如果没有则使用默认消息
          this.$message.success(response.msg || '上班打卡成功')
          // 重新获取今日考勤记录
          this.getTodayAttendance()
          // 刷新考勤日历
          this.fetchAttendanceData()
          // 不再重新初始化地图或添加标记
        } else {
          this.$message.error(response.msg || '上班打卡失败')
        }
      }).catch(error => {
        console.error('上班打卡失败', error)
        this.$message.error('上班打卡失败')
      })
    },

    // 处理下班打卡
    handleClockOut() {
      console.log('开始下班打卡流程')
      console.log('当前位置信息:', this.currentLocation)

      // 检查是否获取到位置
      if (!this.currentLocation) {
        console.log('未获取到位置信息，请求用户位置')
        this.$message.warning('正在获取您的位置，请稍候再试')
        this.getUserLocation()
        return
      }

      // 继续打卡流程
      this.processClockOut()
    },

    // 处理下班打卡流程（分离逻辑，便于复用）
    processClockOut(forceSubmit = false) {
      console.log('处理下班打卡流程，强制提交:', forceSubmit)

      // 如果强制提交或已有位置信息但没有userMarker
      if (forceSubmit || (this.currentLocation && !this.userMarker)) {
        console.log('强制提交或使用当前位置信息')
        this.submitClockOut()
        return
      }

      // 检查是否在打卡范围内
      if (this.clockRules.clockLocation && this.clockRules.clockRadius && this.userMarker) {
        const userPosition = this.userMarker.getPosition()
        const locationParts = this.clockRules.clockLocation.split(',')

        if (locationParts.length === 2) {
          const lng = parseFloat(locationParts[0])
          const lat = parseFloat(locationParts[1])

          if (!isNaN(lng) && !isNaN(lat)) {
            const clockPosition = new AMap.LngLat(lng, lat)
            const distance = userPosition.distance(clockPosition)
            console.log('用户距离打卡地点:', distance, '米')

            if (distance > this.clockRules.clockRadius) {
              this.$confirm(`您当前不在打卡范围内，距离打卡地点约${Math.round(distance)}米，是否继续打卡？`, '打卡提示', {
                confirmButtonText: '继续打卡',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.submitClockOut()
              }).catch(() => {
                // 用户取消打卡
                console.log('用户取消打卡')
              })
              return
            }
          }
        }
      }

      // 在打卡范围内或无法判断范围，直接打卡
      console.log('在打卡范围内或无法判断范围，直接打卡')
      this.submitClockOut()
    },

    // 提交下班打卡
    submitClockOut() {
      console.log('提交下班打卡')

      // 获取位置信息
      let location = null

      if (this.userMarker) {
        // 如果有用户标记，使用标记位置
        const position = this.userMarker.getPosition()
        location = [position.lng, position.lat]
        console.log('使用用户标记位置:', location)
      } else if (this.currentLocation) {
        // 如果没有用户标记但有位置描述，尝试从clockRules中获取位置
        if (this.clockRules.clockLocation) {
          try {
            // 尝试解析打卡地点坐标
            let clockLocationCoords = null

            if (this.clockRules.clockLocation.includes('[') && this.clockRules.clockLocation.includes(']')) {
              // 尝试解析JSON格式
              try {
                clockLocationCoords = JSON.parse(this.clockRules.clockLocation)
              } catch (e) {
                console.error('解析打卡位置JSON失败:', e)
              }
            } else {
              // 尝试解析逗号分隔的格式
              const locationParts = this.clockRules.clockLocation.split(',')
              if (locationParts.length === 2) {
                const lng = parseFloat(locationParts[0])
                const lat = parseFloat(locationParts[1])
                if (!isNaN(lng) && !isNaN(lat)) {
                  clockLocationCoords = [lng, lat]
                }
              }
            }

            if (clockLocationCoords && Array.isArray(clockLocationCoords) &&
                clockLocationCoords.length === 2 &&
                !isNaN(clockLocationCoords[0]) && !isNaN(clockLocationCoords[1])) {
              location = clockLocationCoords
              console.log('使用打卡地点位置:', location)
            }
          } catch (error) {
            console.error('获取打卡地点位置失败:', error)
          }
        }
      }

      // 如果仍然没有位置信息，使用默认位置
      if (!location) {
        // 使用默认位置（南昌大学坐标）
        location = [115.762609, 28.174721]
        console.log('使用默认位置:', location)
      }

      // 根据接口要求，传递location和type参数
      clock({
        location: location,
        type: 'OUT'  // 下班打卡类型为OUT
      }).then(response => {
        if (response.code === 1) {
          // 使用后端返回的消息，如果没有则使用默认消息
          this.$message.success(response.msg || '下班打卡成功')
          // 重新获取今日考勤记录
          this.getTodayAttendance()
          // 刷新考勤日历
          this.fetchAttendanceData()
          // 不再重新初始化地图或添加标记
        } else {
          this.$message.error(response.msg || '下班打卡失败')
        }
      }).catch(error => {
        console.error('下班打卡失败', error)
        this.$message.error('下班打卡失败')
      })
    },

    // 获取考勤状态文本
    getStatusText(clockInStatus, clockOutStatus) {
      if (!clockInStatus && !clockOutStatus) {
        return '未签到'
      }

      let status = ''

      // 处理签到状态
      if (clockInStatus === 'NORMAL') {
        status = '正常'
      } else if (clockInStatus === 'LATE') {
        status = '迟到'
      } else if (clockInStatus === 'ABSENT') {
        status = '缺勤'
      } else if (clockInStatus === 'LEAVE') {
        status = '请假'
      }

      // 处理签退状态
      if (clockOutStatus) {
        if (clockOutStatus === 'EARLY') {
          status = status ? `${status}/早退` : '早退'
        } else if (clockOutStatus === 'ABSENT' && clockInStatus !== 'ABSENT') {
          status = status ? `${status}/未签退` : '未签退'
        }
      }

      return status || '未知'
    },

    // 获取上班打卡时间的样式类
    getClockInTimeClass() {
      if (!this.attendanceData.clockInTime) {
        return ''
      }

      // 根据打卡状态设置颜色
      if (this.attendanceData.clockInStatus === 'LATE') {
        return 'time-warning' // 迟到显示黄色
      } else if (this.attendanceData.clockInStatus === 'ABSENT') {
        return 'time-danger'  // 缺勤显示红色
      } else if (this.attendanceData.clockInStatus === 'LEAVE') {
        return 'time-info'    // 请假显示灰色
      }

      return 'time-success'   // 正常显示绿色
    },

    // 获取下班打卡时间的样式类
    getClockOutTimeClass() {
      if (!this.attendanceData.clockOutTime) {
        return ''
      }

      // 根据打卡状态设置颜色
      if (this.attendanceData.clockOutStatus === 'EARLY') {
        return 'time-warning' // 早退显示黄色
      } else if (this.attendanceData.clockOutStatus === 'ABSENT') {
        return 'time-danger'  // 缺勤显示红色
      } else if (this.attendanceData.clockOutStatus === 'LEAVE') {
        return 'time-info'    // 请假显示灰色
      }

      return 'time-success'   // 正常显示绿色
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      if (status.includes('正常')) {
        return 'success'
      } else if (status.includes('迟到') || status.includes('早退')) {
        return 'warning'
      } else if (status.includes('缺勤') || status.includes('未签到') || status.includes('未签退')) {
        return 'danger'
      } else if (status.includes('请假')) {
        return 'info'
      }
      return 'info'
    },

    // 获取考勤状态
    getAttendanceStatus(date) {
      const dateStr = dayjs(date).format('YYYY-MM-DD')
      const record = this.attendanceRecords[dateStr]

      if (!record) return ''

      const statusMap = {
        'NORMAL': '正常',
        'LATE': '迟到',
        'EARLY': '早退',
        'ABSENT': '缺勤',
        'LEAVE': '请假'
      }

      // 处理签到状态
      let status = statusMap[record.clockInStatus] || ''

      // 处理签退状态
      if (record.clockOutStatus === 'EARLY') {
        status = status ? `${status}/早退` : '早退'
      }

      return status
    },

    // 获取考勤状态对应的样式类
    getAttendanceClass(date) {
      const dateStr = dayjs(date).format('YYYY-MM-DD')
      const record = this.attendanceRecords[dateStr]

      if (!record) return ''

      if (record.clockInStatus === 'NORMAL' && record.clockOutStatus !== 'EARLY') {
        return 'normal'
      } else if (record.clockInStatus === 'LATE' || record.clockOutStatus === 'EARLY') {
        return record.clockInStatus === 'LATE' ? 'late' : 'early'
      } else if (record.clockInStatus === 'ABSENT') {
        return 'absent'
      } else if (record.clockInStatus === 'LEAVE') {
        return 'leave'
      }

      return ''
    },

    // 判断是否为周末
    isWeekend(date) {
      const day = new Date(date).getDay()
      return day === 0 || day === 6 // 0是周日，6是周六
    },

    // 获取考勤数据（日历用）
    fetchAttendanceData() {
      const yearMonth = `${this.calendarYear}-${String(this.calendarMonth + 1).padStart(2, '0')}`

      listPersonalAttendance({
        month: yearMonth
      }).then(response => {
        if (response.code === 1) {
          const records = response.data || []
          this.attendanceRecords = {}

          // 处理返回的考勤记录数据
          records.forEach(record => {
            // 日期格式可能是 "2023-01-01T00:00:00.000+00:00"
            const dateStr = record.date ? record.date.split('T')[0] : ''
            if (dateStr) {
              this.attendanceRecords[dateStr] = {
                clockInStatus: record.clockInStatus,
                clockOutStatus: record.clockOutStatus,
                clockInTime: record.clockInTime ?
                  `${String(record.clockInTime.hour).padStart(2, '0')}:${String(record.clockInTime.minute).padStart(2, '0')}:${String(record.clockInTime.second).padStart(2, '0')}` : '',
                clockOutTime: record.clockOutTime ?
                  `${String(record.clockOutTime.hour).padStart(2, '0')}:${String(record.clockOutTime.minute).padStart(2, '0')}:${String(record.clockOutTime.second).padStart(2, '0')}` : ''
              }
            }
          })
        }
      }).catch(error => {
        console.error('获取考勤数据失败', error)
      })
    },

    // 处理上一月
    prevMonth() {
      if (this.calendarMonth === 0) {
        this.calendarYear--
        this.calendarMonth = 11
      } else {
        this.calendarMonth--
      }
      this.calendarDate = new Date(this.calendarYear, this.calendarMonth, 1)
      this.fetchAttendanceData()
    },

    // 处理下一月
    nextMonth() {
      if (this.calendarMonth === 11) {
        this.calendarYear++
        this.calendarMonth = 0
      } else {
        this.calendarMonth++
      }
      this.calendarDate = new Date(this.calendarYear, this.calendarMonth, 1)
      this.fetchAttendanceData()
    },

    // 处理日历控件日期变化
    handleCalendarChange(date) {
      // 更新年月信息
      this.calendarYear = date.getFullYear()
      this.calendarMonth = date.getMonth()
      // 获取新月份的考勤数据
      this.fetchAttendanceData()
    },

    // 获取今日考勤记录
    getTodayAttendance() {
      console.log('获取今日考勤记录')

      // 使用新的API接口获取今日考勤记录
      getTodayAttendance().then(response => {
        if (response.code === 1 && response.data) {
          const todayRecord = response.data
          console.log('获取到的今日考勤记录:', todayRecord)

          // 更新今日考勤数据
          if (todayRecord.clockInTime) {
            // 直接使用LocalTime格式，只需要截取到分钟
            const clockInTimeStr = todayRecord.clockInTime.substring(0, 5) // 截取HH:mm部分
            this.attendanceData.clockInTime = clockInTimeStr
            this.attendanceData.clockInStatus = todayRecord.clockInStatus || ''
          } else {
            this.attendanceData.clockInTime = ''
            this.attendanceData.clockInStatus = ''
          }

          if (todayRecord.clockOutTime) {
            // 直接使用LocalTime格式，只需要截取到分钟
            const clockOutTimeStr = todayRecord.clockOutTime.substring(0, 5) // 截取HH:mm部分
            this.attendanceData.clockOutTime = clockOutTimeStr
            this.attendanceData.clockOutStatus = todayRecord.clockOutStatus || ''
          } else {
            this.attendanceData.clockOutTime = ''
            this.attendanceData.clockOutStatus = ''
          }

          // 更新打卡按钮状态
          this.clockInDisabled = !!this.attendanceData.clockInTime
          this.clockOutDisabled = !this.attendanceData.clockInTime || !!this.attendanceData.clockOutTime

          // 更新考勤状态
          this.attendanceData.status = this.getStatusText(todayRecord.clockInStatus, todayRecord.clockOutStatus)

          // 更新打卡状态文本和样式
          this.updateClockStatusDisplay()
        } else {
          // 如果没有今日记录或请求失败，重置状态
          this.attendanceData = {
            clockInTime: '',
            clockOutTime: '',
            status: '未签到',
            clockInStatus: '',
            clockOutStatus: ''
          }
          this.clockInDisabled = false
          this.clockOutDisabled = true

          // 更新打卡状态文本和样式
          this.updateClockStatusDisplay()
        }
      }).catch(error => {
        console.error('获取今日考勤记录失败', error)

        // 出错时重置状态
        this.attendanceData = {
          clockInTime: '',
          clockOutTime: '',
          status: '未签到',
          clockInStatus: '',
          clockOutStatus: ''
        }
        this.clockInDisabled = false
        this.clockOutDisabled = true

        // 更新打卡状态文本和样式
        this.updateClockStatusDisplay()
      })
    },

    // 更新打卡状态显示
    updateClockStatusDisplay() {
      console.log('更新打卡状态显示:', this.attendanceData)

      // 检查今天是否是周末
      const today = new Date();
      const isWeekend = today.getDay() === 0 || today.getDay() === 6;

      /* if (isWeekend) {
        // 如果是周末，显示无需打卡
        this.clockStatusText = '无需打卡'
        this.clockStatusClass = 'success-status'
        this.clockInDisabled = true
        this.clockOutDisabled = true
        return
      } */

      // 如果有下班打卡状态，优先显示下班打卡状态
      if (this.attendanceData.clockOutStatus) {
        switch (this.attendanceData.clockOutStatus) {
          case 'NORMAL':
            this.clockStatusText = '已签退'
            this.clockStatusClass = 'success-status'
            break
          case 'EARLY':
            this.clockStatusText = '早退'
            this.clockStatusClass = 'warning-status'
            break
          case 'ABSENT':
            this.clockStatusText = '缺勤'
            this.clockStatusClass = 'danger-status'
            break
          case 'LEAVE':
            this.clockStatusText = '请假'
            this.clockStatusClass = 'info-status'
            break
          default:
            this.clockStatusText = '已签退'
            this.clockStatusClass = 'success-status'
        }
      }
      // 如果没有下班打卡状态但有上班打卡状态，显示上班打卡状态
      else if (this.attendanceData.clockInStatus) {
        switch (this.attendanceData.clockInStatus) {
          case 'NORMAL':
            this.clockStatusText = '已签到'
            this.clockStatusClass = 'primary-status'
            break
          case 'LATE':
            this.clockStatusText = '迟到'
            this.clockStatusClass = 'warning-status'
            break
          case 'ABSENT':
            this.clockStatusText = '缺勤'
            this.clockStatusClass = 'danger-status'
            break
          case 'LEAVE':
            this.clockStatusText = '请假'
            this.clockStatusClass = 'info-status'
            break
          default:
            this.clockStatusText = '已签到'
            this.clockStatusClass = 'primary-status'
        }
      }
      // 如果既没有上班打卡状态也没有下班打卡状态，显示未打卡
      else {
        this.clockStatusText = '未打卡'
        this.clockStatusClass = 'not-clocked'
        /* if (isWeekend) {
          this.clockStatusText = '无需打卡'
          this.clockStatusClass = 'success-status'
        } else {
          this.clockStatusText = '未打卡'
          this.clockStatusClass = 'not-clocked'
        } */
      }

      console.log('更新后的状态:', this.clockStatusText, this.clockStatusClass)
    },

    // 获取考勤规则
    getClockRules() {
      console.log('开始获取考勤规则')
      getDepartmentAttendanceConfig().then(response => {
        if (response.code === 1 && response.data) {
          const config = response.data
          console.log('获取到的考勤规则:', config)

          // 更新考勤规则
          this.clockRules = {
            clockInStartTime: config.clockInStartTime,
            clockInEndTime: config.clockInEndTime,
            lateThreshold: config.lateThreshold,
            lateDeduction: config.lateDeduction,
            clockOutStartTime: config.clockOutStartTime,
            clockOutEndTime: config.clockOutEndTime,
            earlyThreshold: config.earlyThreshold,
            earlyDeduction: config.earlyDeduction,
            clockLocation: config.clockLocation,
            locationName: config.locationName,
            clockRadius: config.clockRadius
          }

          console.log('更新后的考勤规则:', this.clockRules)
          console.log('地图实例状态:', this.map ? '已创建' : '未创建')
          console.log('打卡位置:', this.clockRules.clockLocation)

          // 如果有打卡地点，在地图上显示
          if (this.map && this.clockRules.clockLocation) {
            console.log('准备在地图上显示打卡地点')
            // 确保在下一个事件循环中执行，以便地图完全初始化
            setTimeout(() => {
              this.showClockLocationOnMap()
            }, 500)
          } else {
            console.log('无法显示打卡地点，地图实例或打卡位置不存在')
          }
        } else {
          console.warn('获取考勤规则失败:', response.msg)
        }
      }).catch(error => {
        console.error('获取考勤规则失败', error)
      })
    },

    // 初始化地图
    initDashboardMap() {
      console.log('初始化地图')
      // 确保地图容器存在
      if (!document.getElementById('dashboardMapContainer')) {
        console.error('地图容器不存在')
        return
      }

      // 初始化地图
      this.map = initMap('dashboardMapContainer', {
        zoom: 15,
        resizeEnable: true
      })

      console.log('地图初始化完成:', this.map ? '成功' : '失败')

      // 添加定位控件
      this.map.plugin(['AMap.Geolocation'], () => {
        console.log('添加定位控件')
        /* eslint-disable no-undef */
        // 添加定位控件
        const geolocation = new AMap.Geolocation({
          enableHighAccuracy: true,
          timeout: 10000,
          buttonPosition: 'RB',
          buttonOffset: new AMap.Pixel(10, 10),
          zoomToAccuracy: false, // 不自动缩放到定位点
          showButton: true,
          showCircle: false // 不显示蓝色圆圈
        })
        this.map.addControl(geolocation)

        // 监听定位成功事件
        geolocation.on('complete', (data) => {
          console.log('定位成功')
          const userPosition = data.position

          // 更新用户位置标记
          /* if (this.userMarker) {
            console.log('更新已有用户位置标记')
            this.userMarker.setPosition(userPosition)
          } else {
            console.log('创建新的用户位置标记')
            this.userMarker = createMarker([userPosition.lng, userPosition.lat], this.map, {
              icon: new AMap.Icon({
                image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                size: new AMap.Size(25, 34),
                imageSize: new AMap.Size(25, 34)
              }),
              title: '您的位置'
            })
            console.log('用户位置标记创建结果:', this.userMarker ? '成功' : '失败')
          } */

          // 获取地址信息
          getAddressFromAPI([userPosition.lng, userPosition.lat]).then(address => {
            this.currentLocation = address
            console.log('获取到的地址:', address)
          }).catch(() => {
            this.currentLocation = '未能获取位置信息'
            console.warn('未能获取位置信息')
          })

          // 如果有打卡地点，显示打卡地点和范围
          if (this.clockRules.clockLocation) {
            console.log('地图初始化后显示打卡地点')
            this.showClockLocationOnMap()
          } else {
            console.log('没有打卡地点信息，重新获取考勤规则')
            // 如果没有打卡地点信息，重新获取考勤规则
            this.getClockRules()
          }

          // 调整地图视图
          this.adjustMapView()
        })

        // 自动触发一次定位
        console.log('触发定位')
        geolocation.getCurrentPosition()
      })
    },

    // 获取用户当前位置
    getUserLocation() {
      getCurrentPosition().then(result => {
        const position = result.position

        // 创建用户位置标记
        /* if (this.userMarker) {
          this.userMarker.setPosition(position)
        } else {
          this.userMarker = createMarker(position, this.map, {
            icon: new AMap.Icon({
              image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
              size: new AMap.Size(25, 34),
              imageSize: new AMap.Size(25, 34)
            }),
            title: '您的位置'
          })
        } */

        // 获取地址信息
        getAddressFromAPI([position.lng, position.lat]).then(address => {
          this.currentLocation = address
        }).catch(() => {
          this.currentLocation = '未能获取位置信息'
        })

        // 调整地图视图以包含用户位置和打卡地点
        this.adjustMapView()
      }).catch(error => {
        console.error('获取位置失败:', error)
        this.$message.warning('无法获取您的位置，请确保已授予位置权限')
      })
    },

    // 在地图上显示打卡地点
    showClockLocationOnMap() {
      console.log('执行showClockLocationOnMap方法')
      if (!this.map) {
        console.error('地图实例不存在')
        return
      }

      if (!this.clockRules.clockLocation) {
        console.error('打卡位置不存在')
        return
      }

      try {
        console.log('开始解析打卡位置:', this.clockRules.clockLocation)
        // 尝试解析打卡地点坐标（可能是JSON字符串或逗号分隔的字符串）
        let clockLocationCoords = null

        if (this.clockRules.clockLocation.includes('[') && this.clockRules.clockLocation.includes(']')) {
          // 尝试解析JSON格式
          try {
            clockLocationCoords = JSON.parse(this.clockRules.clockLocation)
            console.log('JSON解析结果:', clockLocationCoords)
          } catch (e) {
            console.error('解析打卡位置JSON失败:', e)
          }
        } else {
          // 尝试解析逗号分隔的格式
          const locationParts = this.clockRules.clockLocation.split(',')
          if (locationParts.length === 2) {
            const lng = parseFloat(locationParts[0])
            const lat = parseFloat(locationParts[1])
            if (!isNaN(lng) && !isNaN(lat)) {
              clockLocationCoords = [lng, lat]
              console.log('逗号分隔格式解析结果:', clockLocationCoords)
            }
          }
        }

        // 确保坐标有效
        if (!clockLocationCoords || !Array.isArray(clockLocationCoords) ||
            clockLocationCoords.length !== 2 ||
            isNaN(clockLocationCoords[0]) || isNaN(clockLocationCoords[1])) {
          console.error('无效的打卡位置坐标')
          return
        }

        console.log('有效的打卡位置坐标:', clockLocationCoords)

        // 只移除旧的圆形（如果存在）

        if (this.locationCircle) {
          this.map.remove(this.locationCircle);
          this.locationCircle = null;
        }

        // 创建中心点坐标对象
        const centerPosition = new AMap.LngLat(clockLocationCoords[0], clockLocationCoords[1]);
        console.log('创建中心点坐标:', centerPosition);

        // 不再创建标记点，只保留圆圈范围
        // 清除之前的标记引用
        this.locationMarker = null;

        // 创建打卡范围圆圈
        if (this.clockRules.clockRadius && this.clockRules.clockRadius > 0) {
          const radius = parseInt(this.clockRules.clockRadius, 10)
          console.log('打卡范围半径:', radius)

          // 直接创建圆形，使用相同的中心点坐标
          this.locationCircle = new AMap.Circle({
            center: centerPosition,  // 使用共用坐标对象
            radius: radius,
            strokeColor: '#1874DD',  // 更深的蓝色
            strokeWeight: 2,
            strokeOpacity: 1,        // 完全不透明的边框
            fillColor: '#1874DD',    // 更深的蓝色
            fillOpacity: 0.4,        // 更高的填充透明度
            zIndex: 100              // 确保在其他元素之上，但在标记之下
          });

          // 将圆形添加到地图
          this.map.add(this.locationCircle);

          console.log('圆形范围创建成功并添加到地图');
        } else {
          console.log('无有效打卡范围半径')
        }

        // 调整地图视图
        console.log('调整地图视图')
        this.adjustMapView()
      } catch (error) {
        console.error('显示打卡地点失败:', error)
      }
    },

    // 调整地图视图以包含所有标记
    adjustMapView() {
      if (!this.map) return

      try {
        // 创建边界对象
        const bounds = new AMap.Bounds()
        let hasPoints = false

        // 添加用户位置到边界
        if (this.userMarker) {
          bounds.extend(this.userMarker.getPosition())
          hasPoints = true
        }

        // 添加打卡地点到边界
        if (this.locationMarker) {
          bounds.extend(this.locationMarker.getPosition())
          hasPoints = true
        }

        // 如果有点，则调整视图
        if (hasPoints) {
          // 计算两点之间的距离（如果有两个点）
          let distance = 0
          if (this.userMarker && this.locationMarker) {
            const userPos = this.userMarker.getPosition()
            const locationPos = this.locationMarker.getPosition()
            distance = userPos.distance(locationPos)
          }

          // 根据距离动态计算合适的缩放级别
          let zoom = 15 // 默认缩放级别
          if (distance < 100) {
            zoom = 16
          } else if (distance < 500) {
            zoom = 15
          } else if (distance < 1000) {
            zoom = 14
          } else if (distance < 5000) {
            zoom = 13
          } else if (distance < 10000) {
            zoom = 12
          } else {
            zoom = 11
          }

          // 考虑打卡范围，扩大视野
          if (this.clockRules.clockRadius && this.clockRules.clockRadius > 0) {
            const radius = parseInt(this.clockRules.clockRadius, 10)
            // 如果范围较大，适当调整缩放级别
            if (radius > 1000) {
              zoom -= 1
            } else if (radius > 500) {
              zoom -= 0.5
            }
          }

          // 先设置缩放级别
          this.map.setZoom(zoom)

          // 然后调整地图视野，确保两个点都在视野内，并留有一定边距
          this.map.setBounds(bounds, true, [80, 80, 80, 80])
        } else if (this.userMarker) {
          // 如果只有用户位置，以用户位置为中心
          this.map.setCenter(this.userMarker.getPosition())
          this.map.setZoom(15)
        }
      } catch (error) {
        console.error('调整地图视图失败:', error)
      }
    },

    // 跳转到个人档案页面
    goToProfile() {
      this.$router.push({
        path: '/employee/profile',
        query: { viewMode: 'self' }
      })
    },

    // 跳转到奖惩记录页面
    goToRewards() {
      this.$router.push({
        path: '/employee/reward',
        query: { viewMode: 'self' }
      })
    },

    // 打开请假申请弹窗
    openLeaveDialog() {
      this.leaveForm = {
        leaveType: 'PERSONAL',
        startDate: dayjs().format('YYYY-MM-DD'),
        startPeriod: 'AM',
        endDate: dayjs().format('YYYY-MM-DD'),
        endPeriod: 'PM',
        reason: ''
      }
      this.leaveDialogVisible = true
    },

    // 提交请假申请
    submitLeaveForm() {
      this.$refs.leaveForm.validate(valid => {
        if (valid) {
          // 验证开始日期不能大于结束日期
          const startDate = new Date(this.leaveForm.startDate)
          const endDate = new Date(this.leaveForm.endDate)

          if (startDate > endDate) {
            this.$message.error('开始日期不能大于结束日期')
            return
          }

          // 如果开始日期等于结束日期，则开始时间段不能大于结束时间段
          if (startDate.getTime() === endDate.getTime() &&
              this.leaveForm.startPeriod === 'PM' &&
              this.leaveForm.endPeriod === 'AM') {
            this.$message.error('开始时间段不能大于结束时间段')
            return
          }

          // 提交请假申请
          applyLeave(this.leaveForm).then(response => {
            if (response.code === 1) {
              this.$message.success('请假申请提交成功')
              this.leaveDialogVisible = false
            } else {
              this.$message.error(response.msg || '请假申请提交失败')
            }
          }).catch(error => {
            console.error('请假申请提交失败', error)
            this.$message.error('请假申请提交失败')
          })
        }
      })
    },

    // 初始化员工入职情况图表
    initEmployeeChart() {
      this.employeeChart = echarts.init(this.$refs.employeeChart)
      this.updateEmployeeChart()

      // 响应式处理
      window.addEventListener('resize', () => {
        this.employeeChart.resize()
      })
    },

    // 更新员工入职情况图表
    updateEmployeeChart() {
      // 如果没有数据，使用空数组
      const monthlyData = this.onboardingData || []

      // 准备数据
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      const data = Array(12).fill(0)

      // 填充实际数据
      monthlyData.forEach(item => {
        if (item.month >= 1 && item.month <= 12) {
          data[item.month - 1] = item.count
        }
      })

      const option = {
        title: {
          text: '今年员工入职情况',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c} 人'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '60px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: months,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '入职人数',
          minInterval: 1,
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [{
          name: '入职人数',
          type: 'line',
          data: data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#409EFF'
          },
          lineStyle: {
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(64, 158, 255, 0.7)'
              }, {
                offset: 1, color: 'rgba(64, 158, 255, 0.1)'
              }]
            }
          }
        }]
      }

      this.employeeChart.setOption(option)
    },

    // 初始化部门员工分布图表
    initDepartmentChart() {
      // 确保DOM元素存在
      if (!this.$refs.departmentChart) {
        console.error('图表DOM元素不存在')
        return
      }

      // 销毁旧的图表实例
      if (this.departmentChart) {
        this.departmentChart.dispose()
      }

      // 创建新的图表实例
      this.departmentChart = echarts.init(this.$refs.departmentChart)
      console.log('初始化部门分布图表')

      // 更新图表数据
      this.$nextTick(() => {
        this.updateDepartmentChart()
      })

      // 响应式处理
      window.addEventListener('resize', () => {
        if (this.departmentChart) {
          this.departmentChart.resize()
        }
      })
    },

    // 更新部门员工分布图表
    updateDepartmentChart() {
      // 确保图表实例存在
      if (!this.departmentChart) {
        console.error('图表实例不存在')
        return
      }

      // 清空图表
      this.departmentChart.clear()

      // 获取部门分布数据
      const distributionData = this.departmentDistributionData || []
      console.log('更新图表的部门分布数据:', distributionData)

      // 检查是否有数据或所有部门员工数量都为0
      const hasNoData = !distributionData || distributionData.length === 0

      // 检查是否所有部门员工数量都为0
      const allDepartmentsEmpty = !hasNoData && (() => {
        // 递归检查部门及其子部门的员工数量
        const checkDepartmentEmpty = (department) => {
          if (!department) return true

          if (department.employeeCount > 0) {
            return false
          }

          if (department.children && department.children.length > 0) {
            // 如果任何子部门有员工，则返回false
            for (const child of department.children) {
              if (!checkDepartmentEmpty(child)) {
                return false
              }
            }
          }

          return true
        }

        // 检查所有顶级部门
        for (const dept of distributionData) {
          if (!checkDepartmentEmpty(dept)) {
            return false
          }
        }

        return true
      })()

      console.log('所有部门员工数量都为0:', allDepartmentsEmpty)

      // 如果没有数据或所有部门员工数量都为0，显示提示信息
      if (hasNoData || allDepartmentsEmpty) {
        console.log('没有部门分布数据或所有部门员工数量为0，显示提示信息')
        this.departmentChart.setOption({
          title: {
            text: '暂无部门员工分布数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          },
          series: [] // 清空系列
        }, true) // 使用true参数，完全替换之前的配置

        // 清空图例数据
        this.departmentLegend = []

        // 强制重绘
        this.departmentChart.resize()
        return
      }

      // 定义颜色
      const colors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7074'
      ]

      // 提取部门数据
      const extractDepartments = (data) => {
        // 创建部门映射，用于快速查找部门
        const departmentMap = new Map()

        // 第一步：提取所有部门，并建立映射关系
        data.forEach(dept => {
          if (!dept) return

          const id = dept.departmentId
          const name = dept.departmentName || '未知部门'
          const value = dept.employeeCount || 0

          departmentMap.set(id, {
            id,
            name,
            value,
            parentId: dept.parentId,
            children: [],
            level: 0, // 初始层级为0，后面会更新
            path: name // 初始路径为部门名称，后面会更新
          })
        })

        // 第二步：构建部门树结构
        const rootDepartments = []
        departmentMap.forEach(dept => {
          // 如果有父部门，则添加到父部门的子部门列表中
          if (dept.parentId !== null && dept.parentId !== 0 && departmentMap.has(dept.parentId)) {
            const parent = departmentMap.get(dept.parentId)
            parent.children.push(dept)
          } else {
            // 否则，作为根部门
            rootDepartments.push(dept)
          }
        })

        // 第三步：计算每个部门的层级和路径
        const calculateLevelAndPath = (dept, level = 0, parentPath = '') => {
          dept.level = level
          dept.path = parentPath ? `${parentPath} / ${dept.name}` : dept.name

          dept.children.forEach(child => {
            calculateLevelAndPath(child, level + 1, dept.path)
          })
        }

        rootDepartments.forEach(dept => {
          calculateLevelAndPath(dept)
        })

        // 第四步：为每个部门分配颜色
        let colorIndex = 0
        const assignColors = (dept) => {
          dept.color = colors[colorIndex % colors.length]
          colorIndex++

          dept.children.forEach(child => {
            assignColors(child)
          })
        }

        rootDepartments.forEach(dept => {
          assignColors(dept)
        })

        // 第五步：将部门树转换为扁平数组，用于图例
        const flattenDepartments = []
        const flatten = (dept) => {
          flattenDepartments.push({
            id: dept.id,
            name: dept.name,
            originalName: dept.name,
            value: dept.value,
            color: dept.color,
            level: dept.level,
            path: dept.path
          })

          dept.children.forEach(child => {
            flatten(child)
          })
        }

        rootDepartments.forEach(dept => {
          flatten(dept)
        })

        // 第六步：将部门树转换为旭日图数据
        const convertToSunburstData = (departments) => {
          return departments.map(dept => {
            const node = {
              name: dept.name,
              value: dept.value,
              itemStyle: {
                color: dept.color
              }
            }

            if (dept.children && dept.children.length > 0) {
              node.children = convertToSunburstData(dept.children)
            }

            return node
          })
        }

        const treeData = convertToSunburstData(rootDepartments)

        return {
          departments: flattenDepartments,
          treeData
        }
      }

      // 提取部门数据
      const { departments, treeData } = extractDepartments(distributionData)
      console.log('提取的部门列表:', departments)
      console.log('树形数据:', treeData)

      // 设置图例数据，按层级排序
      this.departmentLegend = departments
        .map(dept => ({
          id: dept.id,
          name: dept.originalName,
          value: dept.value,
          level: dept.level,
          color: dept.color,
          path: dept.path
        }))
        .sort((a, b) => {
          // 先按层级排序
          if (a.level !== b.level) {
            return a.level - b.level
          }
          // 同层级按名称排序
          return a.name.localeCompare(b.name)
        })

      // 构建树形图例数据
      this.departmentLegendTree = this.buildDepartmentLegendTree(this.departmentLegend)

      console.log('设置图例数据:', this.departmentLegend)
      console.log('设置树形图例数据:', this.departmentLegendTree)

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const { name, value, treePathInfo } = params
            // 计算占总人数的百分比
            let totalValue = 0
            if (treePathInfo && treePathInfo.length > 0) {
              totalValue = treePathInfo[0].value
            }
            const percentage = totalValue > 0 ? ((value / totalValue) * 100).toFixed(2) : 0
            return `${name}: ${value}人 (${percentage}%)`
          },
          backgroundColor: 'rgba(50,50,50,0.8)',
          borderColor: '#eee',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: false // 隐藏内置图例，使用自定义图例
        },
        series: [{
          name: '部门分布',
          type: 'sunburst',
          radius: ['15%', '75%'],
          center: ['35%', '50%'],
          data: treeData,
          nodeClick: 'rootToNode',
          sort: null, // 不排序，保持原始顺序
          silent: false, // 允许交互
          emphasis: {
            focus: 'ancestor',
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          levels: [
            {},
            {
              r0: '15%',
              r: '40%',
              itemStyle: {
                borderWidth: 2
              },
              label: {
                show: false
              }
            },
            {
              r0: '40%',
              r: '70%',
              label: {
                show: false
              }
            }
          ],
          label: {
            show: false
          },
          itemStyle: {
            borderWidth: 1.5,
            borderColor: '#fff'
          }
        }]
      }

      // 设置图表选项
      this.departmentChart.setOption(option, true)

      // 强制重绘
      this.departmentChart.resize()
    },

    // 切换图例项的展开/折叠状态
    toggleLegendItem(item) {
      if (item) {
        // 切换展开状态
        this.$set(item, 'expanded', !item.expanded)
      }
    },

    // 切换整个图例的折叠/展开状态
    toggleLegendCollapse() {
      this.legendCollapsed = !this.legendCollapsed
    },

    // 一键展开/折叠所有部门
    toggleExpandAll() {
      this.allExpanded = !this.allExpanded

      // 递归设置所有部门的展开状态
      const setExpandedState = (items, state) => {
        if (!items || !Array.isArray(items)) return

        items.forEach(item => {
          if (item) {
            this.$set(item, 'expanded', state)

            if (item.children && item.children.length > 0) {
              setExpandedState(item.children, state)
            }
          }
        })
      }

      // 设置所有部门的展开状态
      setExpandedState(this.departmentLegendTree, this.allExpanded)
    },

    // 构建部门图例树
    buildDepartmentLegendTree(departments) {
      // 创建部门映射，用于快速查找部门
      const departmentMap = new Map()

      // 第一步：创建所有部门节点
      departments.forEach(dept => {
        departmentMap.set(dept.id, {
          id: dept.id,
          name: dept.name,
          value: dept.value,
          color: dept.color,
          level: dept.level,
          children: [],
          expanded: false // 默认所有部门都折叠
        })
      })

      // 第二步：构建树结构
      const rootNodes = []
      departments.forEach(dept => {
        const node = departmentMap.get(dept.id)

        // 根据路径查找父节点
        if (dept.level > 0) {
          // 查找上一级部门
          const parentDept = departments.find(d =>
            d.level === dept.level - 1 && dept.path.startsWith(d.path)
          )

          if (parentDept && departmentMap.has(parentDept.id)) {
            const parentNode = departmentMap.get(parentDept.id)
            parentNode.children.push(node)
          } else {
            // 如果找不到父节点，则作为根节点
            rootNodes.push(node)
          }
        } else {
          // 顶级部门
          rootNodes.push(node)
        }
      })

      return rootNodes
    },

    // 处理快捷操作
    handleQuickAction(action) {
      // 根据操作类型打开对应的弹窗
      switch (action) {
        case 'addUser':
          this.openUserDialog();
          break;
        case 'addDepartment':
          this.openDepartmentDialog();
          break;
        case 'addRole':
          this.openRoleDialog();
          break;
        case 'addPermission':
          this.openPermissionDialog();
          break;
        case 'addPosition':
          this.openPositionDialog();
          break;
        default:
          this.$message.warning('功能开发中，敬请期待');
          break;
      }
    },

    // 打开添加用户弹窗
    openUserDialog() {
      this.userForm = {
        username: '',
        password: '',
        isAdmin: false,
        employeeId: null,
        roleIds: []
      };
      this.userDialogVisible = true;
      this.userDialogTitle = '添加用户';

      // 获取员工列表
      this.getEmployeeOptions();

      // 获取角色列表
      this.getRoleOptions();
    },

    // 获取角色选项
    getRoleOptions() {
      listRole().then(response => {
        // 检查响应格式
        if (response && response.code === 1 && response.data) {
          if (response.data.records && Array.isArray(response.data.records)) {
            this.roleOptions = response.data.records
          } else {
            this.$message.error('获取角色选项失败: 数据格式不正确')
            this.roleOptions = []
          }
        } else {
          this.$message.error(response && response.msg || '获取角色选项失败')
          this.roleOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取角色选项失败')
        this.roleOptions = []
      })
    },

    // 打开添加部门弹窗
    openDepartmentDialog() {
      this.departmentForm = {
        name: '',
        parentId: null,
        leaderId: null,
        description: ''
      };
      this.departmentDialogVisible = true;
      this.departmentDialogTitle = '添加部门';

      // 清空选中的部门和员工
      this.selectedDepartmentName = '';
      this.selectedDepartmentId = null;
      this.departmentEmployees = [];
      this.selectedEmployee = null;

      // 获取部门树
      this.getDepartmentTree();
    },

    // 处理部门选择
    handleDepartmentSelect(data) {
      this.selectedDepartmentId = data.id;
      this.selectedDepartmentName = data.name;
      this.loadDepartmentEmployees(data.id);
    },

    // 加载部门员工
    loadDepartmentEmployees(departmentId) {
      this.loadingDepartmentEmployees = true;
      this.departmentEmployees = [];

      getEmployeesByDepartmentId(departmentId)
        .then(response => {
          this.loadingDepartmentEmployees = false;
          if (response && response.code === 1) {
            this.departmentEmployees = response.data || [];

            // 如果有部门领导ID，尝试在新加载的员工列表中查找并选中
            if (this.departmentForm.leaderId) {
              const leader = this.departmentEmployees.find(emp => emp.id === this.departmentForm.leaderId);
              if (leader) {
                this.selectedEmployee = leader;
              }
            }

            if (this.departmentEmployees.length === 0) {
              this.$message.info('该部门下暂无员工');
            }
          } else {
            this.$message.error(response && response.msg || '获取部门员工失败');
          }
        })
        .catch(error => {
          this.loadingDepartmentEmployees = false;
          // 只有在不是token过期的情况下才显示错误消息
          if (!error.isTokenExpired) {
            this.$message.error('获取部门员工失败');
          }
        });
    },

    // 处理员工选择
    handleEmployeeSelect(row) {
      this.selectedEmployee = row;
      this.departmentForm.leaderId = row.id;
    },

    // 清除选中的员工
    clearSelectedEmployee() {
      this.selectedEmployee = null;
      this.departmentForm.leaderId = null;
    },

    // 设置表格行的样式
    tableRowClassName({ row }) {
      // 如果当前行是选中的员工，则添加高亮样式
      if (this.selectedEmployee && row.id === this.selectedEmployee.id) {
        return 'selected-row';
      }
      return '';
    },

    // 打开添加角色弹窗
    openRoleDialog() {
      this.roleForm = {
        name: '',
        roleKey: 'ROLE_',
        description: ''
      };
      this.roleDialogVisible = true;
      this.roleDialogTitle = '添加角色';
    },

    // 打开添加权限弹窗
    openPermissionDialog() {
      this.permissionForm = {
        permissionName: '',
        permissionKey: '',
        description: ''
      };
      this.permissionDialogVisible = true;
      this.permissionDialogTitle = '添加权限';
    },

    // 打开添加职位弹窗
    openPositionDialog() {
      this.positionForm = {
        name: '',
        level: 5,
        accountId: null,
        description: ''
      };
      this.positionDialogVisible = true;
      this.positionDialogTitle = '添加职位';

      // 获取账套选项
      this.getAccountOptions();
    },

    // 获取账套选项
    getAccountOptions() {
      // 显示加载中
      const loading = this.$loading({
        lock: true,
        text: '正在加载账套数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 查询账套列表
      listAccount()
        .then(response => {
          if (response.code === 1) {
            this.accountOptions = response.data || [];
          } else {
            this.$message.error(response.msg || '获取账套列表失败');
          }
        })
        .catch(error => {
          // token过期错误已在请求拦截器中处理，这里不需要额外处理
          // 只处理非token过期的错误
          if (!error.isTokenExpired) {
            this.$message.error('获取账套列表失败');
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    // 获取员工选项
    getEmployeeOptions() {
      // 显示加载中
      const loading = this.$loading({
        lock: true,
        text: '正在加载员工数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 查询未注册的员工列表
      getUnregisteredEmployees()
        .then(response => {
          if (response.code === 1 && response.data) {
            this.employeeOptions = response.data;
          } else {
            this.$message.error(response.msg || '获取未注册员工列表失败');
          }
        })
        .catch(error => {
          // token过期错误已在请求拦截器中处理，这里不需要额外处理
          // 只处理非token过期的错误
          if (!error.isTokenExpired) {
            this.$message.error('获取未注册员工列表出错');
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    // 获取部门树
    getDepartmentTree() {
      // 显示加载中
      const loading = this.$loading({
        lock: true,
        text: '正在加载部门数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 查询部门树
      getDepartmentTree()
        .then(response => {
          if (response.code === 1) {
            this.departmentOptions = response.data || [];
          } else {
            this.$message.error(response.msg || '获取部门树失败');
          }
        })
        .catch(error => {
          // token过期错误已在请求拦截器中处理，这里不需要额外处理
          // 只处理非token过期的错误
          if (!error.isTokenExpired) {
            this.$message.error('获取部门树出错');
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    // 提交用户表单
    submitUserForm() {
      this.$refs.userForm.validate(valid => {
        if (valid) {
          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: '正在添加用户...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 处理表单数据，将空字符串转换为null
          const userData = { ...this.userForm };

          // 处理可能为空的字段
          if (userData.employeeId === '') {
            userData.employeeId = null;
          }

          // 添加用户
          addUser(userData)
            .then(response => {
              if (response.code === 1) {
                this.$message.success('添加用户成功');
                this.userDialogVisible = false;
              } else {
                this.$message.error(response.msg || '添加用户失败');
              }
            })
            .catch(error => {
              // token过期错误已在请求拦截器中处理，这里不需要额外处理
              // 只处理非token过期的错误
              if (!error.isTokenExpired) {
                this.$message.error('添加用户出错');
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },

    // 提交部门表单
    submitDepartmentForm() {
      this.$refs.departmentForm.validate(valid => {
        if (valid) {
          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: '正在添加部门...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 添加部门
          addDepartment(this.departmentForm)
            .then(response => {
              if (response.code === 1) {
                this.$message.success('添加部门成功');
                this.departmentDialogVisible = false;
              } else {
                this.$message.error(response.msg || '添加部门失败');
              }
            })
            .catch(error => {
              // token过期错误已在请求拦截器中处理，这里不需要额外处理
              // 只处理非token过期的错误
              if (!error.isTokenExpired) {
                this.$message.error('添加部门出错');
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },

    // 提交角色表单
    submitRoleForm() {
      this.$refs.roleForm.validate(valid => {
        if (valid) {
          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: '正在添加角色...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 添加角色
          addRole(this.roleForm)
            .then(response => {
              if (response.code === 1) {
                this.$message.success('添加角色成功');
                this.roleDialogVisible = false;
              } else {
                this.$message.error(response.msg || '添加角色失败');
              }
            })
            .catch(error => {
              // token过期错误已在请求拦截器中处理，这里不需要额外处理
              // 只处理非token过期的错误
              if (!error.isTokenExpired) {
                this.$message.error('添加角色出错');
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },

    // 提交权限表单
    submitPermissionForm() {
      this.$refs.permissionForm.validate(valid => {
        if (valid) {
          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: '正在添加权限...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 添加权限
          addPermission(this.permissionForm)
            .then(response => {
              if (response.code === 1) {
                this.$message.success('添加权限成功');
                this.permissionDialogVisible = false;
              } else {
                this.$message.error(response.msg || '添加权限失败');
              }
            })
            .catch(error => {
              // token过期错误已在请求拦截器中处理，这里不需要额外处理
              // 只处理非token过期的错误
              if (!error.isTokenExpired) {
                this.$message.error('添加权限出错');
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },

    // 提交职位表单
    submitPositionForm() {
      this.$refs.positionForm.validate(valid => {
        if (valid) {
          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: '正在添加职位...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 构建提交数据，只包含接口需要的字段
          const positionData = {
            name: this.positionForm.name,
            level: this.positionForm.level,
            accountId: this.positionForm.accountId
          };

          // 添加职位
          addPosition(positionData)
            .then(response => {
              if (response.code === 1) {
                this.$message.success('添加职位成功');
                this.positionDialogVisible = false;
              } else {
                this.$message.error(response.msg || '添加职位失败');
              }
            })
            .catch(error => {
              // token过期错误已在请求拦截器中处理，这里不需要额外处理
              // 只处理非token过期的错误
              if (!error.isTokenExpired) {
                this.$message.error('添加职位出错');
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 50px);
  }
}

// 欢迎横幅
.dashboard-welcome-banner {
  background: linear-gradient(135deg, #409EFF 0%, #64B5F6 100%);
  border-radius: 12px;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .welcome-left {
    display: flex;
    align-items: center;

    .user-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .welcome-info {
      .welcome-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .welcome-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin-bottom: 10px;
      }

      .welcome-date {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }

  .welcome-right {
    display: flex;

    .welcome-stat {
      display: flex;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 15px 20px;
      border-radius: 10px;

      i {
        font-size: 36px;
        margin-right: 15px;
      }

      .stat-info {
        .stat-value {
          font-size: 28px;
          font-weight: bold;
        }

        .stat-label {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }

    .welcome-date {
      font-size: 18px;
      color: white;
      font-weight: bold;
      padding: 15px 20px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      display: inline-block;
    }
  }
}

// 统计卡片行
.dashboard-stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .stat-card {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    margin: 0 10px;
    transition: all 0.3s;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }
    }

    .stat-content {
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }

      .stat-title {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

// 图表行
.dashboard-charts-row {
  display: flex;
  margin-bottom: 20px;

  .chart-container {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin: 0 10px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    .chart-header {
      padding: 15px 20px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafafa;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }

    .chart-content {
      padding: 10px;
    }
  }
}

// 快捷入口
.dashboard-shortcuts {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .shortcuts-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }

  .shortcuts-content {
    padding: 20px;
    display: flex;
    justify-content: space-between;

    .shortcut-item {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      margin: 0 10px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        background-color: #f5f7fa;
        transform: translateY(-5px);
      }

      .shortcut-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .shortcut-info {
        .shortcut-title {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .shortcut-desc {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

// 员工考勤卡片
.attendance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.attendance-record, .today-info {
  p {
    margin: 12px 0;
    font-size: 15px;
    color: #606266;

    span {
      font-weight: bold;
      margin-right: 8px;
      color: #303133;
    }
  }
}

// 员工快捷功能
.dashboard-card {
  margin-bottom: 20px;
  // height: calc(100% - 20px); /* 减去margin-bottom的高度 */
  height: 370px; /* 减去margin-bottom的高度 */

  .dashboard-card-content {
    padding: 10px;
    height: calc(100% - 60px); /* 减去header的高度 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.employee-shortcuts {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;

  .shortcut-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 15px;
    flex: 1;
    background-color: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: #f5f7fa;
      transform: translateX(5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .shortcut-icon {
      width: 45px;
      height: 45px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

      i {
        font-size: 22px;
        color: white;
      }
    }

    .shortcut-info {
      .shortcut-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }

      .shortcut-desc {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

// 打卡卡片样式
.clock-card {
  margin-bottom: 20px;
  // height: calc(100% - 20px); /* 减去margin-bottom的高度 */
  height: 370px;

  .current-time {
    float: right;
    font-size: 16px;
    color: #606266;
  }

  .clock-content {
    padding: 10px 0;
    height: calc(100% - 60px); /* 减去header的高度 */
  }

  .clock-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .clock-info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 0 10px;
  }

  .clock-time-left, .clock-time-right {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 120px;

    .time-label {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }

    .time-value {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
      font-family: 'Courier New', monospace;

      &.time-success {
        color: #67C23A;
      }

      &.time-warning {
        color: #E6A23C;
      }

      &.time-danger {
        color: #F56C6C;
      }

      &.time-info {
        color: #909399;
      }
    }

    .el-button {
      padding: 8px 15px;
    }
  }

  .clock-status {
    display: flex;
    flex-direction: column;
    align-items: center;

    .clock-circle {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transition: all 0.3s;
      position: relative;
      overflow: hidden;
      border: 4px solid #fff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
        z-index: 1;
      }

      span {
        position: relative;
        z-index: 2;
        font-size: 18px;
        letter-spacing: 1px;
      }

      &.not-clocked {
        background: linear-gradient(145deg, #a0a4ad, #7d7f86);
        border-color: #e0e0e0;
      }

      &.primary-status {
        background: linear-gradient(145deg, #54aeff, #3080ff);
        border-color: #d6e9ff;
      }

      &.success-status {
        background: linear-gradient(145deg, #7ed321, #4eb31c);
        border-color: #e3f5d6;
      }

      &.warning-status {
        background: linear-gradient(145deg, #ffb650, #e69421);
        border-color: #ffecd6;
      }

      &.danger-status {
        background: linear-gradient(145deg, #ff7875, #f5222d);
        border-color: #ffd6d6;
      }

      &.info-status {
        background: linear-gradient(145deg, #a6a9ad, #7a7d81);
        border-color: #e6e8ea;
      }

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .clock-info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .clock-status-tag {
    text-align: center;
    margin-bottom: 15px;

    .el-tag {
      padding: 5px 15px;
      font-size: 14px;
    }
  }

  .location-info {
    text-align: center;
    color: #606266;
    font-size: 14px;
    margin-bottom: 15px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;

    i {
      margin-right: 5px;
      color: #409EFF;
    }
  }

  .attendance-rules {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 12px;

    .rules-header {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;

      i {
        color: #409EFF;
        margin-right: 5px;
      }
    }

    .rules-content {
      display: flex;
      justify-content: space-between;

      .rule-item {
        margin-bottom: 8px;
        flex: 1;
        text-align: center;

        .rule-label {
          color: #606266;
          display: block;
          margin-bottom: 4px;
          font-size: 13px;
        }

        .rule-value {
          font-weight: bold;
          color: #303133;
          font-size: 14px;
        }
      }
    }
  }

  .map-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: 250px;
  }
}

// 日历卡片样式
.calendar-card {
  .calendar-legend {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    flex-wrap: wrap;

    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 10px;
      font-size: 12px;

      .legend-dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 2px;
        margin-right: 5px;

        &.normal {
          background-color: #67c23a;
        }

        &.late {
          background-color: #e6a23c;
        }

        &.early {
          background-color: #e6a23c;
        }

        &.absent {
          background-color: #f56c6c;
        }

        &.leave {
          background-color: #409eff;
        }
      }
    }
  }

  ::v-deep .el-calendar {
    .el-calendar__header {
      padding: 0;
      margin-bottom: 15px;
    }

    .el-calendar__body {
      padding: 0;
    }

    .el-calendar-table {
      th {
        text-align: center;
        padding: 8px 0;
        font-weight: bold;

        &:first-child, &:last-child {
          color: #909399;
        }
      }

      .el-calendar-day {
        height: 70px;
        padding: 0;
      }

      td.prev, td.next {
        .calendar-day {
          opacity: 0.5;
        }
      }
    }
  }

  .calendar-day {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }

    &.weekend {
      background-color: #f5f7fa;

      .day-number {
        color: #909399;
      }
    }

    .day-number {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .day-status-indicator {
      width: 20px;
      height: 4px;
      border-radius: 2px;
      margin-bottom: 5px;

      &.normal {
        background-color: #67c23a;
      }

      &.late, &.early {
        background-color: #e6a23c;
      }

      &.absent {
        background-color: #f56c6c;
      }

      &.leave {
        background-color: #409eff;
      }
    }

    .day-status {
      font-size: 12px;
      text-align: center;
    }

    &.normal {
      border-left: 3px solid #67c23a;

      .day-status {
        color: #67c23a;
      }
    }

    &.late, &.early {
      border-left: 3px solid #e6a23c;

      .day-status {
        color: #e6a23c;
      }
    }

    &.absent {
      border-left: 3px solid #f56c6c;

      .day-status {
        color: #f56c6c;
      }
    }

    &.leave {
      border-left: 3px solid #409eff;

      .day-status {
        color: #409eff;
      }
    }
  }
}



// 部门图例样式
.department-legend {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 200px;
  max-height: 260px;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 8px;
  overflow: auto;
  z-index: 100;
  margin: 0;
  transition: all 0.3s ease;

  &.legend-collapsed {
    width: 30px;
    padding: 8px 4px;

    .legend-items {
      display: none;
    }

    .legend-title {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
      writing-mode: vertical-lr;
      height: auto;
      white-space: nowrap;
      text-align: center;

      span {
        margin-right: 0;
      }

      .legend-actions {
        margin-top: 8px;
        flex-direction: column;

        .legend-expand-all {
          display: none;
        }
      }
    }
  }

  .legend-title {
    font-size: 13px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #ebeef5;
    position: sticky;
    top: 0;
    background-color: rgba(255, 255, 255, 0.98);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .legend-actions {
      display: flex;
      align-items: center;

      .legend-expand-all,
      .legend-toggle-all {
        cursor: pointer;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-left: 4px;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        i {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .legend-items {
    max-height: 250px;
    overflow-y: auto;
    padding-right: 5px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .legend-item {
    margin-bottom: 4px;
    font-size: 12px;
    color: #606266;

    .legend-item-content {
      display: flex;
      align-items: center;
      padding: 2px 0;
      border-radius: 3px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .legend-toggle {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-right: 2px;

      i {
        font-size: 12px;
        color: #909399;
      }

      &:hover i {
        color: #409EFF;
      }
    }

    .legend-toggle-placeholder {
      width: 16px;
      height: 16px;
      margin-right: 2px;
    }

    .legend-color {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 6px;
      flex-shrink: 0;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .legend-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5;
      flex: 1;
    }

    .legend-children {
      padding-left: 16px;
      margin-top: 2px;
    }
  }
}

/* 部门领导选择组件样式 */
.leader-select-container {
  display: flex;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  height: 400px;
}

.leader-select-container .department-tree {
  width: 40%;
  border-right: 1px solid #dcdfe6;
  overflow: auto;
  padding: 10px;
}

.leader-select-container .employee-list {
  width: 60%;
  display: flex;
  flex-direction: column;
}

.leader-select-container .employee-list-header {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leader-select-container .loading-indicator {
  font-size: 12px;
  color: #909399;
}

.leader-select-container .employee-list ::v-deep .el-table__empty-text {
  width: 100%;
  text-align: center;
}

.leader-select-container .employee-list ::v-deep .el-table .el-table__row:hover > td {
  background-color: #f5f7fa;
}

.leader-select-container .employee-list ::v-deep .el-table .el-table__row td {
  padding: 8px 0;
}

.selected-employee {
  margin-top: 10px;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
}

.selected-employee .label {
  font-weight: bold;
  color: #67c23a;
  margin-right: 8px;
}

.selected-employee .el-tag {
  margin-right: 5px;
  padding: 0 10px;
  height: 28px;
  line-height: 26px;
}

.selected-employee .el-tag i {
  margin-right: 5px;
}

.selected-employee .el-tag .work-no {
  color: #85ce61;
  font-size: 12px;
  margin-left: 3px;
}

.selected-employee .clear-btn {
  padding: 2px;
  color: #909399;
}

.selected-employee .clear-btn:hover {
  color: #f56c6c;
}

::v-deep .selected-row {
  background-color: #f0f9eb;
  font-weight: bold;
}

::v-deep .selected-row:hover > td {
  background-color: #e6f7d9 !important;
}

::v-deep .selected-row td {
  background-color: #f0f9eb !important;
}

.form-tip {
  font-size: 12px;
  color: #E6A23C;
  line-height: 1.2;
  padding-top: 4px;
}

.title {
  text-align: center;
}
</style>