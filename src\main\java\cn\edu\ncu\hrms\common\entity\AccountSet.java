package cn.edu.ncu.hrms.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("薪资账套规则")
public class AccountSet {
    @ApiModelProperty(value = "账套ID", required = true, example = "501")
    private Integer id;

    @ApiModelProperty(value = "账套名称", required = true, example = "后端开发L5")
    private String name;

    @ApiModelProperty(value = "基本工资", required = true, example = "15000.00")
    private BigDecimal baseSalary;

    @ApiModelProperty(value = "绩效比例", required = true, example = "20（%）")
    private BigDecimal performanceRate;

    @ApiModelProperty(value = "社保比例", required = true, example = "12（%）")
    private BigDecimal insuranceRate;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
