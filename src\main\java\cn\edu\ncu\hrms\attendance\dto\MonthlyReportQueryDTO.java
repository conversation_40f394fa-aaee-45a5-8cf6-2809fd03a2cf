package cn.edu.ncu.hrms.attendance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@ApiModel(description = "月度考勤报表查询数据传输对象")
public class MonthlyReportQueryDTO {

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "员工ID")
    private Integer employeeId;

    @ApiModelProperty(value = "员工姓名(模糊查询)")
    private String employeeName;

    @ApiModelProperty(value = "部门ID")
    private Integer departmentId;

    @ApiModelProperty(value = "年份", example = "2024")
    private Integer year;

    @Min(value = 1, message = "月份必须在 1-12 之间")
    @Max(value = 12, message = "月份必须在 1-12 之间")
    @ApiModelProperty(value = "月份", example = "7")
    private Integer month;

    // 考勤状态可能不适合作为报表的直接查询条件，报表通常是聚合结果
    // @ApiModelProperty(value = "考勤状态")
    // private String attendanceStatus;
} 