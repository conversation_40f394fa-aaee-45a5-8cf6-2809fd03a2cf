import request from '@/utils/request'

// 查询员工列表
export function listEmployee(query) {
  return request({
    url: 'system/employee/list',
    method: 'get',
    params: query
  })
}

// 查询员工详细
export function getEmployee(id) {
  return request({
    url: '/employee/' + id,
    method: 'get'
  })
}

// 新增员工
export function addEmployee(data) {
  return request({
    url: '/employee',
    method: 'post',
    data: data
  })
}

// 修改员工
export function updateEmployee(data) {
  return request({
    url: '/employee',
    method: 'put',
    data: data
  })
}

// 删除员工
export function deleteEmployee(id) {
  return request({
    url: '/employee/' + id,
    method: 'delete'
  })
}

// 获取员工仪表盘信息
export function getEmployeeDashboard() {
  return request({
    url: '/employee/dashboard',
    method: 'get'
  })
}

// 批量导入员工
export function batchImportEmployee(data) {
  return request({
    url: '/employee/batch-import',
    method: 'post',
    data: data
  })
}

// 导出员工
export function exportEmployee(query) {
  return request({
    url: '/employee/export',
    method: 'get',
    params: query
  })
}
