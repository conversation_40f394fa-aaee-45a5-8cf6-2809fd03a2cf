import request from '@/utils/request'

// 查询职位列表
export function listPosition() {
  return request({
    url: '/system/position/list',
    method: 'get'
  })
}

// 获取所有职位列表
export function getAllPositions() {
  return request({
    url: '/salary/position/all',
    method: 'get'
  })
}

// 根据账套ID查询职位列表
export function getPositionsByAccountId(accountId) {
  return request({
    url: `/system/position/account/${accountId}`,
    method: 'get'
  })
}

// 分页查询职位列表
export function getPositionPage(query) {
  return request({
    url: '/system/position/page',
    method: 'get',
    params: query
  })
}

// 查询职位详细
export function getPosition(id) {
  return request({
    url: '/system/position/' + id,
    method: 'get'
  })
}

// 新增职位
export function addPosition(data) {
  return request({
    url: '/system/position',
    method: 'post',
    data: data
  })
}

// 修改职位
export function updatePosition(id, data) {
  return request({
    url: '/system/position/' + id,
    method: 'put',
    data: data
  })
}

// 删除职位
export function deletePosition(id) {
  return request({
    url: '/system/position/' + id,
    method: 'delete'
  })
}
