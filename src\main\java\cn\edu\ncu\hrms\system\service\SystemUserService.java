package cn.edu.ncu.hrms.system.service;

import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.UserAddDTO;
import cn.edu.ncu.hrms.system.dto.UserPageQueryDTO;
import cn.edu.ncu.hrms.system.dto.UserPasswordResetDTO;
import cn.edu.ncu.hrms.system.dto.UserUpdateDTO;
import cn.edu.ncu.hrms.system.vo.RoleVO;
import cn.edu.ncu.hrms.system.vo.UserVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 系统用户管理服务接口
 */
public interface SystemUserService {
    /**
     * 分页获取用户列表
     * @param queryDTO 查询条件及分页参数
     * @return PageInfo<UserVO> 分页用户视图对象
     */
    PageInfo<UserVO> getUserList(UserPageQueryDTO queryDTO);

    /**
     * 根据ID获取用户信息 (返回VO)
     * @param id 用户ID
     * @return Result<UserVO> 用户视图对象
     */
    Result<UserVO> getUserVOById(Integer id);

    /**
     * 创建新用户
     * @param addDTO 用户创建信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> createUser(UserAddDTO addDTO);

    /**
     * 更新用户信息
     * @param id 用户ID
     * @param updateDTO 用户更新信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> updateUser(Integer id, UserUpdateDTO updateDTO);

    /**
     * 删除用户 (逻辑删除)
     * @param id 用户ID
     * @return Result<Void> 操作结果
     */
    Result<Void> deleteUser(Integer id);

    /**
     * 重置用户密码
     * @param id 用户ID
     * @param resetDTO 包含新密码的 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> resetPassword(Integer id, UserPasswordResetDTO resetDTO);

    /**
     * 获取用户拥有的角色列表 (返回VO)
     * @param id 用户ID
     * @return Result<List<RoleVO>> 角色视图对象列表
     */
    Result<List<RoleVO>> getUserRoles(Integer id);

    /**
     * 为用户分配角色
     * @param id 用户ID
     * @param roleIds 角色ID列表
     * @return Result<Void> 操作结果
     */
    Result<Void> assignRolesToUser(Integer id, List<Integer> roleIds);

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return User 用户实体，如果未找到则返回null
     */
    User findUserByUsername(String username);


    /**
     * 根据员工ID查找用户
     * @param employeeId 员工ID
     * @return User 用户实体，如果未找到则返回null
     */
    User findUserByEmployeeId(Integer employeeId);
}

