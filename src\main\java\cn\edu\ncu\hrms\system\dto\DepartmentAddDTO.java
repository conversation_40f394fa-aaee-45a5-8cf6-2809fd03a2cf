package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 创建部门数据传输对象 (DTO)
 */
@Data
@ApiModel(value="DepartmentAddDTO", description="创建部门数据传输对象")
public class DepartmentAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门名称", required = true)
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 50, message = "部门名称长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "上级部门ID", notes = "顶级部门可不传或传 0")
    private Integer parentId;

    @ApiModelProperty(value = "部门领导员工ID")
    private Integer leaderId;
} 