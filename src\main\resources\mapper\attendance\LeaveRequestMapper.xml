<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.attendance.mapper.LeaveRequestMapper">


    <sql id="Base_Column_List">
        id, employee_id, leave_type, start_date, start_period, end_date, end_period, reason, status, create_time, update_time, del_flag
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" resultType="leaveRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_request
        WHERE id = #{id} AND del_flag = false
    </select>


    <!-- 新增请假申请 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO leave_request (employee_id, leave_type, start_date, start_period, end_date, end_period, reason,
                                   status, create_time, update_time, del_flag)
        VALUES (#{employeeId}, #{leaveType}, #{startDate}, #{startPeriod}, #{endDate}, #{endPeriod}, #{reason},
                #{status}, NOW(), NOW(), false)
    </insert>


    <!-- 根据动态条件查询 (配合 PageHelper) -->
    <select id="selectByCondition" resultType="leaveRequest">
        SELECT lr.*
        FROM leave_request lr
        JOIN approval_process ap ON lr.id = ap.request_id AND ap.type = 'LEAVE'
        JOIN employee e ON lr.employee_id = e.id
        WHERE lr.del_flag = 0
        AND ap.del_flag = 0
        AND e.del_flag = 0
        <if test="currentEmployeeId != null">
            AND ap.approver_id = #{currentEmployeeId}
        </if>
        <if test="workNo != null">
            AND e.work_no = #{workNo}
        </if>
        <if test="status == null">
            AND ap.status != 'NEW'
        </if>
        <if test="status != null">
            AND ap.status = #{status}
        </if>
        <if test="queryStartDate != null">
            AND lr.end_date &gt;= #{queryStartDate}
        </if>
        <if test="queryEndDate != null">
            AND lr.start_date &lt;= #{queryEndDate}
        </if>
        ORDER BY lr.create_time DESC
    </select>

    <!-- 更新指定ID的请假申请状态 -->
    <update id="updateStatus">
        UPDATE leave_request
        SET status      = #{status},
            update_time = NOW()
        WHERE id = #{id}
          AND del_flag = 0
    </update>


    <select id="selectByEmployeeId" resultType="cn.edu.ncu.hrms.common.entity.LeaveRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_request
        WHERE employee_id = #{employeeId} AND del_flag = false
        ORDER BY create_time DESC
    </select>

</mapper> 