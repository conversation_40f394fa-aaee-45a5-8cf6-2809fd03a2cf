package cn.edu.ncu.hrms.system.service;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.PositionAddDTO;
import cn.edu.ncu.hrms.system.dto.SystemPositionPageQueryDTO;
import cn.edu.ncu.hrms.system.dto.PositionUpdateDTO;
import cn.edu.ncu.hrms.system.vo.PositionVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 系统职位管理服务接口
 */
public interface SystemPositionService {

    /**
     * 获取所有职位列表
     * @return Result<List<PositionVO>> 职位VO列表
     */
    Result<List<PositionVO>> getPositionList(SystemPositionPageQueryDTO queryDTO);

    /**
     * 根据ID获取职位信息
     * @param id 职位ID
     * @return Result<PositionVO> 职位VO
     */
    Result<PositionVO> getPositionVOById(Integer id);

    /**
     * 创建新职位
     * @param addDTO 职位创建信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> createPosition(PositionAddDTO addDTO);

    /**
     * 更新职位信息
     * @param id 职位ID
     * @param updateDTO 职位更新信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> updatePosition(Integer id, PositionUpdateDTO updateDTO);

    /**
     * 删除职位
     * @param id 职位ID
     * @return Result<Void> 操作结果
     */
    Result<Void> deletePosition(Integer id);

    /**
     * 根据账套ID获取职位列表
     * @param accountId 账套ID
     * @return Result<List<PositionVO>> 职位VO列表
     */
    Result<List<PositionVO>> getPositionsByAccountId(Integer accountId);

    /**
     * 获取某职位下的员工列表 (暂不修改返回类型)
     * @param id 职位ID
     * @return Result<List<Employee>> 员工实体列表
     */
    Result<List<Employee>> getPositionEmployees(Integer id);


    /**
     * 分页获取职位列表
     * @param queryDTO
     * @return
     */
    PageInfo<PositionVO> getPositionPage(SystemPositionPageQueryDTO queryDTO);
}

