package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新职位数据传输对象 (DTO)
 */
@Data
@ApiModel(value="PositionUpdateDTO", description="更新职位数据传输对象")
public class PositionUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "职位名称")
    @NotNull(message = "职级不能为空")
    @Size(max = 50, message = "职位名称长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "职级")
    @NotNull(message = "职级不能为空")
    @Min(value = 5, message = "职级不能小于5")
    @Max(value = 9, message = "职级不能大于9")
    private Byte level;

    @ApiModelProperty(value = "关联账套ID")
    @NotNull(message = "职级不能为空")
    private Integer accountId;

} 