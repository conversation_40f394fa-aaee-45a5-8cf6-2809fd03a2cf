package cn.edu.ncu.hrms.system.service.impl;

import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.DepartmentAddDTO;
import cn.edu.ncu.hrms.system.dto.DepartmentUpdateDTO;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.system.service.SystemDepartmentService;
import cn.edu.ncu.hrms.system.vo.DepartmentVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门管理服务实现类
 * 实现部门的增删改查、树结构、领导设置等业务逻辑
 */
@Service
@Slf4j
public class SystemDepartmentServiceImpl implements SystemDepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    /**
     * 获取所有部门列表 (扁平结构)
     * @return Result<List<DepartmentVO>> 部门VO列表
     */
    @Override
    public Result<List<DepartmentVO>> listDepartments() {
        log.info("获取所有部门列表 (扁平结构)");
        List<Department> departments = departmentMapper.selectList();
        List<DepartmentVO> departmentVOs = departments.stream()
                .map(this::convertToDepartmentVO)
                .collect(Collectors.toList());
        log.info("查询到 {} 个部门", departmentVOs.size());
        return Result.success(departmentVOs);
    }

    /**
     * 获取部门树结构
     * @return Result<List<DepartmentVO>> 部门树VO列表
     */
    @Override
    public Result<List<DepartmentVO>> getDepartmentTree() {
        log.info("获取部门树结构");
        List<Department> allDepartments = departmentMapper.selectList();
        List<DepartmentVO> tree = buildDepartmentTree(allDepartments, null);
        log.info("构建部门树成功");
        return Result.success(tree);
    }

    /**
     * 根据ID获取部门信息
     * @param id 部门ID
     * @return Result<DepartmentVO> 部门VO
     */
    @Override
    public Result<DepartmentVO> getDepartmentVOById(Integer id) {
        log.debug("根据ID获取部门详情 VO, ID: {}", id);
        if (id == null) {
            return Result.error("部门ID不能为空");
        }
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            log.warn("尝试获取ID为 {} 的部门，但未找到", id);
            return Result.error("部门不存在");
        }
        DepartmentVO vo = convertToDepartmentVO(department);
        log.debug("成功获取部门详情 VO, ID: {}", id);
        return Result.success(vo);
    }

    /**
     * 创建新部门
     * @param addDTO 部门创建信息 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> createDepartment(DepartmentAddDTO addDTO) {
        log.info("开始创建部门, DTO: {}", addDTO);
        Integer leaderId = addDTO.getLeaderId();
        if (leaderId != null) {
            Employee leader = employeeMapper.selectById(leaderId);
            if (leader == null) {
                log.warn("创建部门失败：指定的领导ID {} 无效", leaderId);
                return Result.error("指定的部门领导不存在");
            }
        }

        Department department = new Department();
        BeanUtils.copyProperties(addDTO, department);
        department.setCreateTime(LocalDateTime.now());
        department.setUpdateTime(LocalDateTime.now());
        department.setDelFlag(false);

        // 处理 parentId 为 0 或 null 的情况，确保顶级部门 parentId 为 null 或 0
        if (department.getParentId() == null || department.getParentId() == 0) {
            department.setParentId(null);
        }

        int rows = departmentMapper.insert(department);
        if (rows > 0) {
            log.info("部门创建成功, ID: {}", department.getId());
            return Result.success();
        } else {
            log.error("创建部门失败，数据库插入影响行数为 0");
            return Result.error("创建部门失败，请稍后重试");
        }
    }

    /**
     * 更新部门信息
     * @param id 部门ID
     * @param updateDTO 部门更新信息 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> updateDepartment(Integer id, DepartmentUpdateDTO updateDTO) {
        log.info("开始更新部门, ID: {}, DTO: {}", id, updateDTO);
        if (id == null) {
            return Result.error("部门ID不能为空");
        }
        Department existingDepartment = departmentMapper.selectById(id);
        if (existingDepartment == null) {
            log.warn("尝试更新ID为 {} 的部门，但未找到", id);
            return Result.error("要更新的部门不存在");
        }

        // 检查更新的 leaderId 是否有效
        Integer leaderId = updateDTO.getLeaderId();
        if (leaderId != null) {
            Employee leader = employeeMapper.selectById(leaderId);
            if (leader == null) {
                log.warn("创建部门失败：指定的领导ID {} 无效", leaderId);
                return Result.error("指定的部门领导不存在");
            }
        }

        Integer newParentId = updateDTO.getParentId();
        if (newParentId == null || newParentId == 0) {
            existingDepartment.setParentId(null);
        } else {
            // parentId不能是本身或下级部门
            List<Integer> childrenIdList = departmentMapper.selectChildDepartments(id).stream().map(Department::getId).collect(Collectors.toList());
            if (id == newParentId || childrenIdList.contains(newParentId)) {
                return Result.error("上级部门不能是本身或下级部门");
            }
            existingDepartment.setParentId(newParentId);
        }

        BeanUtils.copyProperties(updateDTO, existingDepartment, "id", "parentId", "createTime", "delFlag");
        existingDepartment.setUpdateTime(LocalDateTime.now());

        int rows = departmentMapper.update(existingDepartment);
        if (rows > 0) {
            log.info("部门更新成功, ID: {}", id);
            return Result.success();
        } else {
            log.warn("更新部门数据库操作未影响行数, ID: {}. 可能已被删除或更新失败", id);
            return Result.error("系统异常，请稍后再试");
        }
    }

    /**
     * 删除部门 (逻辑删除)
     * @param id 部门ID
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> deleteDepartment(Integer id) {
        log.info("开始删除部门, ID: {}", id);
        if (id == null) {
            return Result.error("部门ID不能为空");
        }
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            log.warn("尝试删除ID为 {} 的部门，但未找到或已被删除", id);
            return Result.success(null, "部门不存在或已被删除");
        }

        // 1. 检查是否有子部门
        List<Department> children = departmentMapper.selectByParentId(id);
        if (!CollectionUtils.isEmpty(children)) {
            log.warn("删除部门失败：部门 ID: {} 存在 {} 个子部门", id, children.size());
            return Result.error("删除失败：请先删除或移动该部门下的所有子部门");
        }

        // 2. 检查是否有员工关联
        long employeeCount = employeeMapper.countByDepartmentId(id);
        if (employeeCount > 0) {
            log.warn("删除部门失败：部门 ID: {} 仍关联着 {} 个员工", id, employeeCount);
            return Result.error("删除失败：请先将该部门下的所有员工转移到其他部门");
        }

        // 3. 执行逻辑删除
        int rows = departmentMapper.deleteById(id);
        if (rows > 0) {
            log.info("部门删除成功 (逻辑删除), ID: {}", id);
            return Result.success();
        } else {
            log.warn("逻辑删除部门数据库操作未影响行数, ID: {}. 可能已被删除或删除失败", id);
            return Result.success(null, "部门不存在或已被删除");
        }
    }

    /**
     * 设置部门领导
     * @param id 部门ID
     * @param leaderId 领导员工ID
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> setDepartmentLeader(Integer id, Integer leaderId) {
        log.info("设置部门 {} 的领导为员工 {}", id, leaderId);
        if (id == null) return Result.error("部门ID不能为空");
        if (leaderId == null) return Result.error("领导员工ID不能为空"); // 或者允许设置为null?

        Department department = departmentMapper.selectById(id);
        if (department == null) return Result.error("部门不存在");


        Employee leader = employeeMapper.selectById(leaderId);
        if (leader == null) return Result.error("指定的员工不存在");

        int rows = departmentMapper.updateLeader(id, leaderId);
        if (rows > 0) {
            log.info("部门 {} 领导设置成功", id);
            return Result.success();
        } else {
            log.warn("设置部门 {} 领导失败，数据库更新影响行数为 0", id);
            return Result.error("设置部门领导失败");
        }
    }

    /**
     * 获取部门下员工列表 (返回 Employee 实体)
     * @param id 部门ID
     * @return Result<List<Employee>> 员工实体列表
     */
    @Override
    public Result<List<Employee>> getDepartmentEmployees(Integer id) {
        log.debug("获取部门 {} 下的员工列表", id);
        if (id == null) return Result.error("部门ID不能为空");
        Department department = departmentMapper.selectById(id);
        if (department == null) return Result.error("部门不存在");

        List<Employee> employees = employeeMapper.selectByDepartmentId(id);
        if (employees == null) {
            employees = new ArrayList<>(); // 返回空列表而不是 null
        }
        log.debug("部门 {} 查询到 {} 个员工", id, employees.size());
        return Result.success(employees);
    }


    /**
     * 递归获取部门及其所有子部门（包括子部门的子部门）
     * @param departmentId
     * @return
     */
    @Override
    public List<Department> getChildDepartments(Integer departmentId) {
        List<Department> departments = new ArrayList<>();
        Department department = departmentMapper.selectById(departmentId);
        if (department != null) {
            departments.add(department);
            List<Department> children = departmentMapper.selectByParentId(departmentId);
            if (!CollectionUtils.isEmpty(children)) {
                for (Department child : children) {
                    departments.addAll(getChildDepartments(child.getId()));
                }
            }
        }

        return departments;
    }


    /**
     * 将 Department 实体转换为 DepartmentVO
     * @param department Department 实体
     * @return DepartmentVO
     */
    private DepartmentVO convertToDepartmentVO(Department department) {
        if (department == null) {
            return null;
        }
        DepartmentVO vo = new DepartmentVO();
        BeanUtils.copyProperties(department, vo);

        // 设置领导姓名
        Employee leader = employeeMapper.selectById(department.getLeaderId());
        vo.setLeaderName(leader == null ? null : leader.getName() +  "（"+  leader.getWorkNo() + "）");

        return vo;
    }

    /**
     * 递归构建部门树
     * @param allDepartments 所有部门列表
     * @param parentId 父部门ID (null 表示查找顶级部门)
     * @return 构建好的部门树列表
     */
    private List<DepartmentVO> buildDepartmentTree(List<Department> allDepartments, Integer parentId) {
        List<DepartmentVO> treeNodes = new ArrayList<>();
        for (Department dept : allDepartments) {
            Integer currentParentId = dept.getParentId();
            // 检查父ID匹配 (顶级部门 parentId 为 null)
            boolean isMatch = (parentId == null && currentParentId == null) ||
                              (parentId != null && parentId.equals(currentParentId));

            if (isMatch) {
                DepartmentVO node = convertToDepartmentVO(dept);
                // 递归查找子部门
                List<DepartmentVO> children = buildDepartmentTree(allDepartments, dept.getId());
                if (!CollectionUtils.isEmpty(children)) {
                    node.setChildren(children);
                }
                treeNodes.add(node);
            }
        }
        return treeNodes;
    }
}
