package cn.edu.ncu.hrms.salary.service.calculation;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.YearMonth;
import java.util.List;

@Slf4j
@Component
public class SalaryCalculationTask {


    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private SalaryCalculationService salaryCalculationService;

    /**
     * 每月1号凌晨3点执行所有员工前一个月的薪资核算统计
     * Cron表达式: 秒 分 时 日 月 周
     * "0 0 3 1 * ?" 表示每月1号的3点0分0秒执行
     */
    @Scheduled(cron = "0 0 3 1 * ?")
    public void processMonthlySalaryCalculation() {
        YearMonth previousMonth = YearMonth.now().minusMonths(1);
        log.info("开始执行 {} 的月度薪资核算任务...", previousMonth);

        // 1. 获取所有在职员工
        List<Employee> employeeList = employeeMapper.selectList(); // 你需要实现这个Mapper方法
        if (employeeList == null || employeeList.isEmpty()) {
            log.info("没有需要核算薪资的员工");
            return;
        }

        log.info("共找到 {} 名员工需要核算薪资。", employeeList.size());

        for (Employee employee : employeeList) {
            try {
                salaryCalculationService.calculateAndSaveEmployeeSalary(employee, previousMonth);
            } catch (Exception e) {
                log.error("为员工 {} (ID: {}) 计算 {} 薪资时发生错误: {}",
                        employee.getName(), employee.getId(), previousMonth, e.getMessage(), e);
                // TODO 重试机制 + 通知管理员
            }
        }

        log.info("{} 的月度薪资核算任务执行完毕。", previousMonth);
    }
}

