package cn.edu.ncu.hrms.system.service.impl;

/**
 * 角色管理服务实现类
 * 实现角色的增删改查、权限分配等业务逻辑
 */

import cn.edu.ncu.hrms.common.constant.RoleConstant;
import cn.edu.ncu.hrms.common.entity.Role;
import cn.edu.ncu.hrms.common.entity.RolePermission;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.auth.mapper.RoleMapper;
import cn.edu.ncu.hrms.auth.mapper.RolePermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.UserRoleMapper;
import cn.edu.ncu.hrms.system.dto.RoleAddDTO;
import cn.edu.ncu.hrms.system.dto.RolePageQueryDTO;
import cn.edu.ncu.hrms.system.dto.RolePermissionUpdateDTO;
import cn.edu.ncu.hrms.system.dto.RoleUpdateDTO;
import cn.edu.ncu.hrms.system.service.SystemRoleService;
import cn.edu.ncu.hrms.system.vo.RoleVO;
import cn.edu.ncu.hrms.system.vo.UserVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SystemRoleServiceImpl implements SystemRoleService {
    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    /**
     * 获取角色列表
     * @return Result<List<RoleVO>> 角色VO列表
     */
    @Override
    public PageInfo<RoleVO> listRoles(RolePageQueryDTO queryDTO) {
        log.info("分页查询所有角色列表");
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<Role> list = roleMapper.selectListByCondition(queryDTO);
        PageInfo<Role> pageInfoRole = new PageInfo<>(list);
        List<RoleVO> voList = list.stream()
                                .map(this::convertToRoleVO)
                                .collect(Collectors.toList());
        PageInfo<RoleVO> pageInfoVO = new PageInfo<>();
        BeanUtils.copyProperties(pageInfoRole, pageInfoVO);
        pageInfoVO.setList(voList);
        log.info("分页查询到 {} 个角色，总计 {} 条", voList.size(), pageInfoVO.getTotal());
        return pageInfoVO;
    }

    /**
     * 根据ID获取角色详情 VO
     * @param id 角色ID
     * @return Result<RoleVO> 角色VO
     */
    @Override
    public Result<RoleVO> getRoleVOById(Integer id) {
        log.debug("根据ID获取角色详情 VO, ID: {}", id);
        if (id == null) {
            log.warn("请求获取角色详情，但ID为空");
            return Result.error("角色ID不能为空");
        }
        Role role = roleMapper.selectById(id);
        if (role == null) {
            log.warn("尝试获取ID为 {} 的角色，但未找到", id);
            return Result.error("角色不存在");
        }
        RoleVO vo = convertToRoleVO(role);
        log.debug("成功获取角色详情 VO, ID: {}", id);
        return Result.success(vo);
    }

    /**
     * 创建角色
     * @param addDTO 角色创建信息 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> createRole(RoleAddDTO addDTO) {
        log.info("开始创建角色, DTO: {}", addDTO);
        if (!addDTO.getRoleKey().startsWith(RoleConstant.ROLE_PREFIX)) {
            log.warn("创建角色失败：角色标识前缀不正确 '{}'", addDTO.getRoleKey());
            return Result.error("角色标识前缀不正确");
        }

        if (roleMapper.selectByName(addDTO.getName()) != null) {
            log.warn("创建角色失败：名称已存在 '{}'", addDTO.getName());
            return Result.error("角色名称已存在");
        }

        if (roleMapper.selectByRoleKey(addDTO.getRoleKey()) != null) {
            log.warn("创建角色失败：角色标识已存在 '{}'", addDTO.getRoleKey());
            return Result.error("角色标识已存在");
        }

        Role role = new Role();
        BeanUtils.copyProperties(addDTO, role);
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        role.setDelFlag(false);

        int rows = roleMapper.insert(role);
        if (rows > 0) {
            log.info("角色创建成功, ID: {}", role.getId());
            return Result.success();
        } else {
            log.error("创建角色失败，数据库插入影响行数为 0");
            return Result.error("创建角色失败，请稍后重试");
        }
    }

    /**
     * 更新角色信息
     * @param id 角色ID
     * @param updateDTO 角色更新信息 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> updateRole(Integer id, RoleUpdateDTO updateDTO) {
        log.info("开始更新角色, ID: {}, DTO: {}", id, updateDTO);
        if (id == null) {
            log.warn("请求更新角色，但ID为空");
            return Result.error("系统异常，请联系管理员");
        }

        if (!updateDTO.getRoleKey().startsWith(RoleConstant.ROLE_PREFIX)) {
            log.warn("创建角色失败：角色标识前缀不正确 '{}'", updateDTO.getRoleKey());
            return Result.error("角色标识前缀不正确");
        }

        Role existingRole = roleMapper.selectById(id);
        if (existingRole == null) {
            log.warn("尝试更新ID为 {} 的角色，但未找到", id);
            return Result.error("要更新的角色不存在");
        }

        if (!Objects.equals(updateDTO.getRoleKey(), existingRole.getRoleKey()) && roleMapper.selectByRoleKey(updateDTO.getRoleKey()) != null) {
            log.warn("创建角色失败：角色标识已存在 '{}'", updateDTO.getRoleKey());
            return Result.error("角色标识已存在");
        }

        if (updateDTO.getName() != null && !updateDTO.getName().equals(existingRole.getName())) {
            Role conflicting = roleMapper.selectByName(updateDTO.getName());
            if (conflicting != null && !conflicting.getId().equals(id)) {
                log.warn("更新角色失败：名称 '{}' 已被 ID 为 {} 的角色使用", updateDTO.getName(), conflicting.getId());
                return Result.error("角色名称已存在");
            }
        }

        BeanUtils.copyProperties(updateDTO, existingRole, "id", "createTime", "delFlag");
        existingRole.setUpdateTime(LocalDateTime.now());

        int rows = roleMapper.update(existingRole);
        if (rows > 0) {
            log.info("角色更新成功, ID: {}", id);
            return Result.success();
        } else {
            log.warn("更新角色数据库操作未影响行数, ID: {}. 可能已被删除或更新失败", id);
            return Result.error("更新角色失败，请稍后再试");
        }
    }

    /**
     * 删除角色 (逻辑删除)
     * @param id 角色ID
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> deleteRole(Integer id) {
        log.info("开始删除角色, ID: {}", id);
        if (id == null) {
            log.warn("请求删除角色，但ID为空");
            return Result.error("角色ID不能为空");
        }

        Role role = roleMapper.selectById(id);
        if (role == null) {
            log.warn("尝试删除ID为 {} 的角色，但未找到或已被删除", id);
            return Result.success(null, "角色不存在或已被删除");
        }

        int userCount = userRoleMapper.countByRoleId(id);
        if (userCount > 0) {
            log.warn("删除角色失败：角色 '{}' (ID: {}) 仍关联着 {} 个用户", role.getName(), id, userCount);
            return Result.error("删除失败：请先解除该角色与所有用户的关联");
        }

        int permDelRows = rolePermissionMapper.deleteByRoleId(id);
        log.info("删除角色 '{}' (ID: {}) 的 {} 条权限关联记录", role.getName(), id, permDelRows);

        int roleDelRows = roleMapper.deleteById(id);
        if (roleDelRows > 0) {
            log.info("角色删除成功 (逻辑删除), ID: {}", id);
            return Result.success();
        } else {
            log.warn("逻辑删除角色数据库操作未影响行数, ID: {}. 可能已被删除或删除失败", id);
            return Result.success(null, "角色不存在或已被删除");
        }
    }

    /**
     * 设置角色权限
     * @param roleId 角色ID
     * @param updateDTO 包含权限ID列表的 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> setRolePermissions(Integer roleId, RolePermissionUpdateDTO updateDTO) {
        log.info("开始设置角色 {} 的权限, DTO: {}", roleId, updateDTO);
        if (roleId == null) {
            log.warn("请求设置角色权限，但角色ID为空");
            return Result.error("角色ID不能为空");
        }
        Role role = roleMapper.selectById(roleId);
        if (role == null) {
            log.warn("尝试为ID为 {} 的角色设置权限，但该角色不存在", roleId);
            return Result.error("角色不存在");
        }

        int deletedRows = rolePermissionMapper.deleteByRoleId(roleId);
        log.debug("删除角色 {} 的旧权限关联 {} 条", roleId, deletedRows);

        List<Integer> permissionIds = updateDTO.getPermissionIds();
        if (!CollectionUtils.isEmpty(permissionIds)) {
            List<RolePermission> newPermissions = permissionIds.stream()
                    .distinct()
                    .map(permissionId -> {
                        RolePermission rp = new RolePermission();
                        rp.setRoleId(roleId);
                        rp.setPermissionId(permissionId);
                        return rp;
                    })
                    .collect(Collectors.toList());

            if (!newPermissions.isEmpty()) {
                int insertedRows = rolePermissionMapper.batchInsert(newPermissions);
                log.info("为角色 {} 插入 {} 条新权限关联", roleId, insertedRows);
                if (insertedRows != newPermissions.size()) {
                    log.warn("为角色 {} 设置权限时，预期插入 {} 条，实际插入 {} 条。可能部分权限ID无效或插入失败",
                             roleId, newPermissions.size(), insertedRows);
                }
            }
        } else {
            log.info("为角色 {} 设置权限，传入的权限列表为空，相当于清空权限", roleId);
        }

        log.info("角色 {} 的权限设置完成", roleId);
        return Result.success();
    }

    private RoleVO convertToRoleVO(Role role) {
        if (role == null) {
            return null;
        }
        RoleVO vo = new RoleVO();
        BeanUtils.copyProperties(role, vo);
        return vo;
    }
}
