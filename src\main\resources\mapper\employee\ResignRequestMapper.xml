<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.employee.mapper.ResignRequestMapper">

    <sql id="Base_Column_List">
        id
        , employee_id, resign_date, reason, status, create_time, update_time, del_flag
    </sql>

    <!-- 根据ID查询离职申请 -->
    <select id="selectById" resultType="ResignRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resign_request
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询员工的离职申请列表 -->
    <select id="selectByEmployeeId" resultType="ResignRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resign_request
        WHERE employee_id = #{employeeId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询员工在日期范围内的离职申请 -->
    <select id="selectByEmployeeAndDateRange" resultType="ResignRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resign_request
        WHERE employee_id = #{employeeId}
        AND resign_date BETWEEN #{startDate} AND #{endDate}
        AND del_flag = 0
        ORDER BY resign_date ASC
    </select>

    <!-- 查询待审批的离职申请列表 -->
    <select id="selectPendingByEmployeeId" resultType="ResignRequest">
        SELECT rr.
        <include refid="Base_Column_List"/>
        FROM resign_request rr
        JOIN approval_process ap ON rr.id = ap.request_id AND ap.type = 'RESIGN'
        WHERE ap.approver_id = #{employeeId}
        AND rr.status = 'PENDING'
        AND ap.status = 'PENDING'
        AND rr.del_flag = 0
        AND ap.del_flag = 0
    </select>

    <!-- 查询部门的离职申请列表 -->
    <select id="selectByDepartment" resultType="ResignRequest">
        SELECT rr.
        <include refid="Base_Column_List"/>
        FROM resign_request rr
        JOIN employee e ON rr.employee_id = e.id
        WHERE e.department_id = #{departmentId}
        <if test="status != null">
            AND rr.status = #{status}
        </if>
        AND rr.del_flag = 0
        AND e.del_flag = 0
        ORDER BY rr.create_time DESC
    </select>

    <!-- 分页查询离职申请 -->
    <select id="selectPage" resultType="ResignRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resign_request
        WHERE del_flag = 0
        <if test="employeeId != null">
            AND employee_id = #{employeeId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="startDate != null and endDate != null">
            AND resign_date BETWEEN #{startDate} AND #{endDate}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 新增离职申请 -->
    <insert id="insert" parameterType="ResignRequest" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resign_request (employee_id, resign_date, reason, status,
                                    create_time, update_time, del_flag)
        VALUES (#{employeeId}, #{resignDate}, #{reason}, #{status},
                NOW(), NOW(), 0)
    </insert>

    <!-- 更新离职申请 -->
    <update id="update" parameterType="ResignRequest">
        UPDATE resign_request
        SET employee_id = #{employeeId},
            resign_date = #{resignDate},
            reason      = #{reason},
            status      = #{status},
            update_time = NOW(),
            del_flag    = #{delFlag}
        WHERE id = #{id}
    </update>

    <!-- 删除离职申请(逻辑删除) -->
    <update id="deleteById">
        UPDATE resign_request
        SET del_flag = null
        WHERE id = #{id}
    </update>

    <!-- 批量删除离职申请(逻辑删除) -->
    <update id="batchDelete">
        UPDATE resign_request
        SET del_flag = null
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计员工在指定年份的离职天数 -->
    <select id="countByYear" resultType="map">
        SELECT COUNT(*)                                             AS total_count,
               SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_count,
               SUM(CASE WHEN status = 'REJECTED' THEN 1 ELSE 0 END) AS rejected_count
        FROM resign_request
        WHERE employee_id = #{employeeId}
            AND YEAR (
            resign_date) = #{year}
          AND del_flag = 0
    </select>

    <!-- 查询日期范围内有离职的员工ID列表 -->
    <select id="selectEmployeesOnLeave" resultType="integer">
        SELECT DISTINCT employee_id
        FROM resign_request
        WHERE resign_date BETWEEN #{startDate} AND #{endDate}
          AND status = 'APPROVED'
          AND del_flag = 0
    </select>

    <!-- 更新离职申请的审批状态 -->
    <update id="updateStatus">
        UPDATE resign_request
        SET status      = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>


    <select id="selectByCondition" resultType="cn.edu.ncu.hrms.common.entity.ResignRequest">
        SELECT rr.*
        FROM resign_request rr
        JOIN approval_process ap ON rr.id = ap.request_id AND ap.type = 'RESIGN'
        JOIN employee e ON rr.employee_id = e.id
        WHERE rr.del_flag = 0
        AND ap.del_flag = 0
        AND e.del_flag = 0
        <if test="currentEmployeeId != null">
            AND ap.approver_id = #{currentEmployeeId}
        </if>
        <if test="workNo != null">
            AND e.work_no = #{workNo}
        </if>
        <if test="status == null">
            AND ap.status != 'NEW'
        </if>
        <if test="status != null">
            AND ap.status = #{status}
        </if>
        <if test="resignStartDate != null and resignEndDate != null">
            AND rr.resign_date BETWEEN #{resignStartDate} AND #{resignEndDate}
        </if>
        ORDER BY rr.create_time DESC
    </select>

    <!-- 查询指定日期离职的申请列表 -->
    <select id="selectResignationsByDate" resultType="ResignRequest">
        SELECT
        <include refid="Base_Column_List"/>
        FROM resign_request
        WHERE resign_date = #{date}
        AND del_flag = 0
    </select>


    <!-- 查询今天离职的申请列表 -->
    <select id="selectResignationsForToday" resultType="ResignRequest">
        SELECT *
        FROM resign_request
        WHERE DATE(resign_date) = #{date}
        AND status = 'APPROVED'
        AND del_flag = 0
    </select>


</mapper>