package cn.edu.ncu.hrms.attendance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "请假申请视图对象")
public class LeaveRequestVO {

    @ApiModelProperty(value = "申请ID", example = "901")
    private Integer id;

    @ApiModelProperty(value = "是否待我审批")
    private Boolean isPendingByMe;

    @ApiModelProperty(value = "员工ID")
    private Integer employeeId;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName; // 需要 Service 层填充

    @ApiModelProperty(value = "员工工号")
    private String employeeWorkNo;// 需要 Service 层填充

    @ApiModelProperty(value = "部门ID")
    private Integer departmentId; // 需要 Service 层填充

    @ApiModelProperty(value = "部门名称")
    private String departmentName; // 需要 Service 层填充

    @ApiModelProperty(value = "请假类型", allowableValues = "ANNUAL,SICK,PERSONAL,MARRIAGE,MATERNITY,OTHER", example = "ANNUAL（年假）")
    private String leaveType;

    @ApiModelProperty(value = "开始日期", required = true, example = "2025-01-20")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "开始时段", allowableValues = "AM,PM", example = "AM", notes = "AM表示上午请假，PM表示下午请假")
    private String startPeriod;

    @ApiModelProperty(value = "结束日期", required = true, example = "2025-01-23")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "结束时段", allowableValues = "AM,PM", example = "PM", notes = "AM表示上午请假，PM表示下午请假")
    private String endPeriod;

    @ApiModelProperty(value = "请假原因", example = "家庭事务")
    private String reason;

    @ApiModelProperty(value = "申请状态", allowableValues = "PENDING,APPROVED,REJECTED,CANCELED", example = "PENDING")
    private String status;

    @ApiModelProperty(value = "申请状态名称")
    private String statusName; // Service 层填充

    @ApiModelProperty(value = "审批备注")
    private String remark;

    @ApiModelProperty(value = "申请提交时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "最后更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

} 