package cn.edu.ncu.hrms.employee.service;

import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.employee.dto.EmployeeQueryDTO;
import cn.edu.ncu.hrms.employee.dto.EmployeeUpdateDTO;
import cn.edu.ncu.hrms.employee.vo.EmployeeDashboardVO;
import cn.edu.ncu.hrms.employee.vo.EmployeeVO;
import cn.edu.ncu.hrms.system.dto.EmployeeAddDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 员工服务接口
 */
public interface EmployeeService {
    
    /**
     * 获取我的档案信息
     * 
     * @param employeeId 员工ID
     * @return 档案信息
     */
    Result<EmployeeVO> getMyProfile(Integer employeeId);
    
    /**
     * 更新我的档案信息
     * 
     * @param employeeUpdateDTO 包含要更新的档案信息的DTO (ID 应已设置为当前用户)
     * @return 操作结果
     */
    Result<Void> updateMyProfile(EmployeeUpdateDTO employeeUpdateDTO);
    
    /**
     * 获取员工个人主页仪表盘信息 (修改返回类型)
     *
     * @param employeeId 员工ID
     * @return 包含考勤统计和今日记录的仪表盘信息
     */
    Result<EmployeeDashboardVO> getDashboard(Integer employeeId);
    
    /**
     * 根据ID获取员工信息
     * 
     * @param id 员工ID
     * @return 员工信息
     */
    Result<EmployeeVO> getEmployeeById(Integer id);

    /**
     * 创建员工
     * @param addDTO 员工添加 DTO
     * @return 操作结果
     */
    Result<Void> createEmployee(EmployeeAddDTO addDTO);

    /**
     * 更新员工信息
     * @param updateDTO 员工更新 DTO
     * @return 操作结果
     */
    Result<Void> updateEmployee(EmployeeUpdateDTO updateDTO);
    
    /**
     * 删除员工
     * 
     * @param id 员工ID
     * @return 删除结果
     */
    Result<Void> deleteEmployee(Integer id);
    
    /**
     * 批量导入员工
     * 
     * @param file Excel文件
     * @return 导入结果
     */
    Result<Void> batchImport(MultipartFile file);
    
    /**
     * 导出员工信息
     * 
     * @param queryDTO 查询条件
     * @return 导出结果
     */
    Result<byte[]> exportEmployees(EmployeeQueryDTO queryDTO);
    
    /**
     * 根据员工ID获取审批链条
     * 
     * @param employeeId 员工ID
     * @param type 审批类型（1:请假 2:离职）
     * @return 审批人ID列表
     */
    Result<List<Integer>> getApprovalChain(Integer employeeId, Integer type);


    /**
     * 获取部门及其所有上级部门的名称
     * @param departmentId
     * @return
     */
    List<String> getDepartmentAndParents(Integer departmentId);

    /**
     * 统一工号生成入口
     *
     * @param name 姓名
     * @param isBatch 是否批次操作（true: 使用批次缓存+分布式锁；false: 直接走分布式锁）
     * @return 工号（姓名拼音+工号）
     */
    String generateWorkNo(String name, boolean isBatch);

    /**
     * 生成员工工号
     * 格式：姓名拼音+序号，如huangkang10
     * @param name 员工姓名
     * @return 工号
     */
    String generateWorkNo(String name);

    /**
     * 获取HR关联的员工列表
     * @param queryDTO 查询条件
     * @return 员工列表
     */
    PageInfo<EmployeeVO> getEmployeePageByHr(EmployeeQueryDTO queryDTO);

    /**
     * 获取部门员工列表
     * @param queryDTO 查询条件
     * @return 员工列表
     */
    PageInfo<EmployeeVO> getEmployeePageByLeader(EmployeeQueryDTO queryDTO);

    /**
     * 批量插入员工
     * @param employees
     */
    void batchInsert(List<Employee> employees);


    /**
     * 获取HR关联的员工列表
     * @return 员工列表
     */
    Result<List<Employee>> getEmployeeListByHr();


    /**
     * 获取部门员工列表
     * @return 员工列表
     */
    Result<List<Employee>> getEmployeeListByLeader();
}
