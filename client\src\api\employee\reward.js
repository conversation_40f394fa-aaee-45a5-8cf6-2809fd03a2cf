import request from '@/utils/request'

// 添加奖惩信息
export function addReward(data) {
  return request({
    url: '/employee/reward-punishment',
    method: 'post',
    data: data
  })
}

// 更新奖惩信息
export function updateReward(data) {
  return request({
    url: '/employee/reward-punishment',
    method: 'put',
    data: data
  })
}

// 删除奖惩信息
export function deleteReward(id) {
  return request({
    url: `/employee/reward-punishment/${id}`,
    method: 'delete'
  })
}

// 批量删除奖惩信息
export function batchDeleteReward(ids) {
  return request({
    url: '/employee/reward-punishment/batch',
    method: 'delete',
    data: ids
  })
}

// 根据ID获取奖惩信息详情
export function getReward(id) {
  return request({
    url: `/employee/reward-punishment/${id}`,
    method: 'get'
  })
}

// 分页查询奖惩信息
export function listReward(query) {
  return request({
    url: '/employee/reward-punishment/page',
    method: 'get',
    params: query
  })
}

// 获取个人奖惩信息列表
export function listPersonalReward(query) {
  return request({
    url: '/employee/reward-punishment/page/self',
    method: 'get',
    params: query
  })
}

// 导出奖惩信息Excel
export function exportReward(query) {
  return request({
    url: '/employee/reward-punishment/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入奖惩信息Excel
export function importReward(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/employee/reward-punishment/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载奖惩信息导入模板Excel
export function downloadRewardTemplate() {
  return request({
    url: '/employee/reward-punishment/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取奖惩类型统计数据
export function getRewardTypeStats() {
  return request({
    url: '/employee/reward-punishment/stats/type',
    method: 'get'
  })
}

// 获取员工奖惩排行榜
export function getRewardRanking(params) {
  return request({
    url: '/employee/reward-punishment/stats/ranking',
    method: 'get',
    params: params
  })
}

// 获取指定日期范围内的奖惩金额统计
export function getRewardAmountStats(params) {
  return request({
    url: '/employee/reward-punishment/stats/amount',
    method: 'get',
    params: params
  })
}
