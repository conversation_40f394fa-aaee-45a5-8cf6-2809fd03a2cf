package cn.edu.ncu.hrms.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("离职申请")
public class ResignRequest {
    @ApiModelProperty(value = "申请ID", required = true, example = "1001")
    private Integer id;

    @ApiModelProperty(value = "员工ID", required = true, example = "1001")
    private Integer employeeId;

    @ApiModelProperty(value = "离职日期", required = true, example = "2025-02-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate resignDate;

    @ApiModelProperty(value = "离职原因", example = "个人发展")
    private String reason;

    @ApiModelProperty(value = "申请状态", allowableValues = "PENDING,APPROVED,REJECTED,CANCELED", example = "PENDING")
    private String status;

    @ApiModelProperty(value = "创建时间", example = "2025-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-01-02 13:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志", allowableValues = "0,1", example = "0")
    private Boolean delFlag;
}
