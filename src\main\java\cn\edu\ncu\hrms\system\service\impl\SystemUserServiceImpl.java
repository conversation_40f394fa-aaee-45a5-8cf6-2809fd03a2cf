package cn.edu.ncu.hrms.system.service.impl;

/**
 * 用户管理服务实现类
 * 实现用户的增删改查、角色分配等业务逻辑
 */

import cn.edu.ncu.hrms.auth.mapper.RoleMapper;
import cn.edu.ncu.hrms.auth.mapper.UserMapper;
import cn.edu.ncu.hrms.auth.mapper.UserRoleMapper;
import cn.edu.ncu.hrms.common.entity.Role;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.entity.UserRole;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.UserAddDTO;
import cn.edu.ncu.hrms.system.dto.UserPageQueryDTO;
import cn.edu.ncu.hrms.system.dto.UserPasswordResetDTO;
import cn.edu.ncu.hrms.system.dto.UserUpdateDTO;
import cn.edu.ncu.hrms.system.service.SystemUserService;
import cn.edu.ncu.hrms.system.vo.RoleVO;
import cn.edu.ncu.hrms.system.vo.UserVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SystemUserServiceImpl implements SystemUserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 分页获取用户列表
     * @param queryDTO 查询条件
     * @return 分页用户结果
     */
    @Override
    public PageInfo<UserVO> getUserList(UserPageQueryDTO queryDTO) {
        log.info("分页查询用户列表, 查询条件: {}", queryDTO);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<User> userList = userMapper.selectListByConditions(queryDTO);
        PageInfo<User> pageInfoUser = new PageInfo<>(userList);
        List<UserVO> userVOList = userList.stream()
                                      .map(this::convertToUserVO)
                                      .collect(Collectors.toList());
        PageInfo<UserVO> pageInfoVO = new PageInfo<>();
        BeanUtils.copyProperties(pageInfoUser, pageInfoVO);
        pageInfoVO.setList(userVOList);
        log.info("查询到用户 {} 条, 总计 {} 条", userVOList.size(), pageInfoVO.getTotal());
        return pageInfoVO;
    }

    /**
     * 根据ID获取用户信息
     * @param id 用户ID
     * @return 用户实体
     */
    @Override
    public Result<UserVO> getUserVOById(Integer id) {
        log.debug("根据ID获取用户详情 VO, ID: {}", id);
        if (id == null) return Result.error("用户ID不能为空");
        User user = userMapper.selectById(id);
        if (user == null) return Result.error("用户不存在");
        return Result.success(convertToUserVO(user));
    }

    /**
     * 创建新用户
     * @param addDTO 用户添加DTO
     * @return 是否创建成功
     */
    @Override
    @Transactional
    public Result<Void> createUser(UserAddDTO addDTO) {
        log.info("开始创建新用户, DTO: {}", addDTO);
        if (userMapper.selectByUsername(addDTO.getUsername()) != null) {
            return Result.error("用户名已存在");
        }

        if (!addDTO.getIsAdmin() && addDTO.getEmployeeId() == null) {
            return Result.error("非管理员请关联员工");
        }

        User user = new User();
        BeanUtils.copyProperties(addDTO, user, "password", "roleIds");
        user.setPassword(passwordEncoder.encode(addDTO.getPassword()));
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setDelFlag(false);

        int userRows = userMapper.insert(user);
        if (userRows <= 0 || user.getId() == null) {
            log.error("创建用户失败，插入用户表影响行数为 0 或未能获取到用户ID");
            throw new RuntimeException("创建用户失败，请稍后重试");
        }
        log.info("用户主体创建成功, ID: {}, 用户名: '{}'", user.getId(), user.getUsername());

        // 给创建的新用户分配角色
        if (!CollectionUtils.isEmpty(addDTO.getRoleIds())) {
            assignRolesInternal(user.getId(), addDTO.getRoleIds());
        }
        return Result.success();
    }

    /**
     * 更新用户信息
     * @param updateDTO 用户更新DTO
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public Result<Void> updateUser(Integer id, UserUpdateDTO updateDTO) {
        log.info("开始更新用户, ID: {}, DTO: {}", id, updateDTO);
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            return Result.error("要更新的用户不存在");
        }

        if (StringUtils.hasText(updateDTO.getUsername()) && !updateDTO.getUsername().equals(existingUser.getUsername())) {
             User conflictingUser = userMapper.selectByUsername(updateDTO.getUsername());
             if (conflictingUser != null && !conflictingUser.getId().equals(id)) {
                 return Result.error("用户名已存在");
            }
            existingUser.setUsername(updateDTO.getUsername());
        }

        existingUser.setUpdateTime(LocalDateTime.now());

        int rows = userMapper.update(existingUser);
        if (rows <= 0) {
            log.warn("更新用户数据库操作未影响行数, ID: {}. 可能已被删除或更新失败", id);
             throw new RuntimeException("更新用户信息失败");
        }
        log.info("用户信息更新成功, ID: {}", id);
        return Result.success();
    }

    /**
     * 删除用户
     * @param id 用户ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public Result<Void> deleteUser(Integer id) {
        log.info("开始逻辑删除用户, ID: {}", id);
        if (userMapper.selectById(id) == null) {
            log.warn("尝试删除ID为 {} 的用户，但未找到或已被删除", id);
            return Result.success( "用户不存在或已被删除");
        }

        userRoleMapper.deleteByUserId(id);
        int userDelRows = userMapper.deleteById(id);
        if (userDelRows <= 0) {
            log.warn("逻辑删除用户数据库操作未影响行数, ID: {}. 可能已被删除或删除失败", id);
        }
        log.info("用户删除成功 (逻辑删除), ID: {}", id);
        return Result.success();
    }

    /**
     * 重置用户密码
     * @param id 用户ID
     * @param resetDTO 用户密码重置DTO
     * @return 是否重置成功
     */
    @Override
    @Transactional
    public Result<Void> resetPassword(Integer id, UserPasswordResetDTO resetDTO) {
        log.info("开始重置用户密码, ID: {}", id);
        if (userMapper.selectById(id) == null) {
            return Result.error("用户不存在");
        }
        String encodedPassword = passwordEncoder.encode(resetDTO.getNewPassword());
        int rows = userMapper.updatePassword(id, encodedPassword, LocalDateTime.now());
        if (rows <= 0) {
            log.error("用户 {} 密码重置失败，数据库更新影响行数为 0", id);
             throw new RuntimeException("密码重置失败");
        }
        log.info("用户 {} 密码重置成功", id);
        return Result.success();
    }

    /**
     * 获取用户拥有的角色列表
     * @param id 用户ID
     * @return 角色列表
     */
    @Override
    public Result<List<RoleVO>> getUserRoles(Integer id) {
        log.debug("获取用户 {} 的角色列表", id);
        if (id == null) return Result.success(Collections.emptyList());
        List<Role> roles = roleMapper.selectRolesByUserId(id);
        List<RoleVO> voList = roles.stream()
                                .map(this::convertToRoleVO)
                                .collect(Collectors.toList());
        return Result.success(voList);
    }

    /**
     * 设置用户角色
     * @param id 用户ID
     * @param roleIds 角色ID列表
     * @return 是否设置成功
     */
    @Override
    @Transactional
    public Result<Void> assignRolesToUser(Integer id, List<Integer> roleIds) {
        log.info("开始为用户 {} 分配角色, Role IDs: {}", id, roleIds);
        if (userMapper.selectById(id) == null) {
             return Result.error("用户不存在");
        }

        assignRolesInternal(id, roleIds);
        log.info("用户 {} 的角色分配完成", id);
        return Result.success();
    }


    private void assignRolesInternal(Integer userId, List<Integer> roleIds) {
        userRoleMapper.deleteByUserId(userId);
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<UserRole> newUserRoles = roleIds.stream()
                    .distinct()
                    .map(roleId -> {
                        UserRole ur = new UserRole();
                        ur.setUserId(userId);
                        ur.setRoleId(roleId);
                        ur.setDelFlag(false);
                        ur.setCreateTime(LocalDateTime.now());
                        ur.setUpdateTime(LocalDateTime.now());
                        return ur;
                    })
                    .collect(Collectors.toList());
            if (!newUserRoles.isEmpty()) {
                int insertedRows = userRoleMapper.batchInsert(newUserRoles);
                 if (insertedRows != newUserRoles.size()) {
                     log.warn("为用户 {} 分配角色时，预期插入 {} 条，实际插入 {} 条。", userId, newUserRoles.size(), insertedRows);
                 }
            }
        }
    }

    private UserVO convertToUserVO(User user) {
        if (user == null) return null;
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo, "password");
        return vo;
    }

    private RoleVO convertToRoleVO(Role role) {
        if (role == null) return null;
        RoleVO vo = new RoleVO();
        BeanUtils.copyProperties(role, vo);
        return vo;
    }

    @Override
    public cn.edu.ncu.hrms.common.entity.User findUserByUsername(String username) {
        log.debug("根据用户名查找用户: {}", username);
        return userMapper.selectByUsername(username);
    }


    @Override
    public User findUserByEmployeeId(Integer employeeId) {
        log.debug("根据员工ID查找用户: {}", employeeId);
        if (employeeId == null) {
            return null;
        }

        return userMapper.selectByEmployeeId(employeeId);
    }
}
