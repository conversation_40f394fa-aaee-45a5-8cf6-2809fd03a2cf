package cn.edu.ncu.hrms.system.controller;

import cn.edu.ncu.hrms.common.entity.Permission;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.PermissionTreeQueryDTO;
import cn.edu.ncu.hrms.system.service.SystemPermissionService;
import cn.edu.ncu.hrms.system.vo.PermissionVO;
import cn.edu.ncu.hrms.system.dto.PermissionAddDTO;
import cn.edu.ncu.hrms.system.dto.PermissionUpdateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统权限管理控制器
 */
@RestController
@RequestMapping("/system/permission")
@Api(tags = "系统权限管理接口")
public class SystemPermissionController {

    @Autowired
    private SystemPermissionService systemPermissionService;

    /**
     * 获取权限列表
     */
    @GetMapping("/list")
    @ApiOperation("获取权限列表")
    @PreAuthorize("@hrms.hasAuthority('system:permission:list')")
    public Result<List<PermissionVO>> getPermissionList(PermissionTreeQueryDTO queryDTO) {
        return systemPermissionService.getPermissionList(queryDTO);
    }

    /**
     * 获取权限树结构
     */
    @GetMapping("/tree")
    @ApiOperation("获取权限树结构")
    @PreAuthorize("@hrms.hasAuthority('system:permission:list')")
    public Result<List<Map<String, Object>>> getPermissionTree(PermissionTreeQueryDTO queryDTO) {
        return systemPermissionService.getPermissionTree(queryDTO);
    }


    /**
     * 根据ID获取权限信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取权限信息")
    @ApiImplicitParam(name = "id", value = "权限ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:permission:query')")
    public Result<PermissionVO> getPermissionById(@PathVariable Integer id) {
        return systemPermissionService.getPermissionVOById(id);
    }

    /**
     * 创建权限
     */
    @PostMapping
    @ApiOperation("创建权限")
    @PreAuthorize("@hrms.hasAuthority('system:permission:add')")
    public Result<Void> createPermission(@Valid @RequestBody PermissionAddDTO addDTO) {
        return systemPermissionService.createPermission(addDTO);
    }

    /**
     * 更新权限信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新权限信息")
    @ApiImplicitParam(name = "id", value = "权限ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:permission:edit')")
    public Result<Void> updatePermission(@PathVariable Integer id, @Valid @RequestBody PermissionUpdateDTO updateDTO) {
        return systemPermissionService.updatePermission(id, updateDTO);
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除权限")
    @ApiImplicitParam(name = "id", value = "权限ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:permission:delete')")
    public Result<Void> deletePermission(@PathVariable Integer id) {
        return systemPermissionService.deletePermission(id);
    }
} 