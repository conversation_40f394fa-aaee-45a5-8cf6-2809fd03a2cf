<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="管理员用户" name="admin">
        <div class="table-container">
          <div class="table-header">
            <div class="left">
              <h3>管理员用户</h3>
            </div>
            <div class="right">
              <el-input
                v-model="queryParams.id"
                placeholder="请输入用户ID"
                clearable
                size="small"
                class="search-input"
                @keyup.enter.native="handleQuery"
              />
              <el-input
                v-model="queryParams.username"
                placeholder="请输入用户名"
                clearable
                size="small"
                class="search-input"
                @keyup.enter.native="handleQuery"
              />
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              <el-button type="info" icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
              <el-button type="success" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
            </div>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading"
            :data="userList"
            border
          >
            <el-table-column prop="id" label="用户ID" min-width="20" align="center" />
            <el-table-column prop="username" label="用户名" min-width="30" align="center" />
            <el-table-column prop="employeeId" label="员工ID" min-width="20" align="center">
              <template slot-scope="scope">
                {{ scope.row.employeeId || '未关联' }}
              </template>
            </el-table-column>
            <el-table-column prop="isAdmin" label="是否管理员" min-width="20" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.isAdmin" type="success">是</el-tag>
                <el-tag v-else type="info">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" min-width="50" align="center" />
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <div class="operation-buttons">
                  <el-button
                    size="mini"
                    type="warning"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                  >修改账号</el-button>
                  <el-button
                    size="mini"
                    type="info"
                    icon="el-icon-key"
                    @click="handleResetPassword(scope.row)"
                  >重置密码</el-button>
                  <el-button
                    style="background-color: #00CED1; border-color: #00CED1; color: white"
                    size="mini"
                    type="Default"
                    icon="el-icon-setting"
                    @click="handleRole(scope.row)"
                  >分配角色</el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                  >删除用户</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="table-footer">
            <el-pagination
              :current-page="queryParams.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工用户" name="employee">
        <div class="table-container">
          <div class="table-header">
            <div class="left">
              <h3>员工用户</h3>
            </div>
            <div class="right">
              <div class="el-cascader search-input" style="width: 230px;">
                <div class="el-input el-input--suffix">
                  <input
                    type="text"
                    readonly="readonly"
                    autocomplete="off"
                    placeholder="请选择部门"
                    class="el-input__inner"
                    v-model="selectedDepartmentName"
                    @click="departmentPopoverVisible = !departmentPopoverVisible"
                  >
                  <span class="el-input__suffix">
                    <span class="el-input__suffix-inner">
                      <i
                        v-if="selectedDepartmentName"
                        class="el-icon-circle-close el-input__icon el-input__clear"
                        @click.stop="clearSelectedDepartment"
                      ></i>
                      <i
                        v-else
                        class="el-input__icon el-icon-arrow-down"
                        @click.stop="departmentPopoverVisible = !departmentPopoverVisible"
                      ></i>
                    </span>
                  </span>
                </div>
                <el-popover
                  placement="bottom-start"
                  width="230"
                  trigger="manual"
                  v-model="departmentPopoverVisible"
                  popper-class="department-popover"
                  :close-on-click-modal="true"
                >
                  <div class="department-tree-container">
                    <el-tree
                      :data="departmentOptions"
                      :props="{ label: 'name', children: 'children' }"
                      node-key="id"
                      highlight-current
                      default-expand-all
                      @node-click="handleDepartmentTreeSelect"
                    ></el-tree>
                  </div>
                </el-popover>
              </div>
              <el-input
                v-model="employeeQueryParams.name"
                placeholder="请输入员工姓名"
                clearable
                size="small"
                class="search-input"
                @keyup.enter.native="handleEmployeeQuery"
              />
              <el-input
                v-model="employeeQueryParams.workNo"
                placeholder="请输入工号"
                clearable
                size="small"
                class="search-input"
                @keyup.enter.native="handleEmployeeQuery"
              />
              <el-cascader
                v-model="positionSelection"
                :options="positionCascaderOptions"
                :props="{
                  expandTrigger: 'click',
                  checkStrictly: true,
                  emitPath: false,
                  value: 'id',
                  label: 'label',
                  children: 'children'
                }"
                placeholder="选择职位"
                clearable
                filterable
                size="small"
                class="search-input"
                style="width: 180px;"
                @change="handlePositionCascaderChange"
              ></el-cascader>
              <el-select
                v-model="employeeQueryParams.status"
                placeholder="在职状态"
                clearable
                size="small"
                class="search-input"
                style="width: 120px;"
              >
                <el-option label="在职" :value="true" />
                <el-option label="离职" :value="false" />
              </el-select>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleEmployeeQuery">搜索</el-button>
              <el-button type="info" icon="el-icon-refresh" size="small" @click="resetEmployeeQuery">重置</el-button>
              <el-button type="success" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
            </div>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading"
            :data="employeeList"
            border
          >
            <el-table-column label="用户ID" min-width="40" align="center">
              <template slot-scope="scope">
                {{ scope.row.userId || '未注册' }}
              </template>
            </el-table-column>
            <el-table-column label="用户名" min-width="60" align="center">
              <template slot-scope="scope">
                {{ scope.row.username || '未注册' }}
              </template>
            </el-table-column>
            <el-table-column prop="id" label="员工ID" min-width="40" align="center" />
            <el-table-column prop="workNo" label="工号" min-width="70" align="center" />
            <el-table-column prop="name" label="姓名" min-width="70" align="center" />
            <el-table-column label="在职状态" min-width="70" align="center">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :active-text="scope.row.status ? '在职' : '离职'"
                  @change="(val) => handleStatusChange(scope.row, val)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" min-width="280">
              <template slot-scope="scope">
                <div class="operation-buttons">
                  <el-button
                    style="background-color: #1E90FF; border-color: #1E90FF; color: white"
                    size="mini"
                    type="primary"
                    icon="el-icon-view"
                    @click="handleViewDetail(scope.row)"
                    plain
                  >查看详情</el-button>
                  <el-button
                    style="background-color: #6a5acd; border-color: #6a5acd; color: white"
                    size="mini"
                    icon="el-icon-edit-outline"
                    @click="handleEditEmployee(scope.row)"
                  >修改信息</el-button>
                  <el-button
                    size="mini"
                    type="warning"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-if="scope.row.userId"
                  >修改账号</el-button>
                  <el-button
                    size="mini"
                    type="info"
                    icon="el-icon-key"
                    @click="handleResetPassword(scope.row)"
                    v-if="scope.row.userId"
                  >重置密码</el-button>
                  <el-button
                    style="background-color: #00CED1; border-color: #00CED1; color: white"
                    size="mini"
                    type="Default"
                    icon="el-icon-setting"
                    @click="handleRole(scope.row)"
                    v-if="scope.row.userId"
                  >分配角色</el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-if="scope.row.userId"
                  >删除用户</el-button>
                  <el-button
                    size="mini"
                    type="success"
                    icon="el-icon-plus"
                    @click="handleRegisterUser(scope.row)"
                    v-if="!scope.row.userId && scope.row.status"
                  >注册用户</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="table-footer">
            <el-pagination
              :current-page="employeeQueryParams.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="employeeQueryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="employeeTotal"
              @size-change="handleEmployeeSizeChange"
              @current-change="handleEmployeeCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <!-- 管理员用户表单或注册用户表单 -->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" v-if="activeTab === 'admin' || isRegisterUser">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!form.id">
          <el-input v-model="form.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="是否管理员" prop="isAdmin" v-if="!form.id">
          <el-switch v-model="form.isAdmin" @change="handleAdminChange" active-text="管理员" />
        </el-form-item>
        <el-form-item label="关联员工" prop="employeeId" v-if="!form.id">
          <el-select
            ref="employeeIdSelect"
            v-model="form.employeeId"
            placeholder="请选择关联员工"
            clearable
            filterable
            :disabled="isRegisterUser">
            <el-option
              v-for="item in employeeOptions"
              :key="item.id"
              :label="item.name + (item.workNo ? ' (' + item.workNo + ')' : '')"
              :value="item.id"
            />
          </el-select>
          <div class="form-tip" v-if="isRegisterUser">关联员工不可修改</div>
          <div class="form-tip" v-else-if="!form.isAdmin">非管理员用户必须关联员工</div>
          <div class="form-tip" v-else>管理员用户可以关联员工，也可以不关联</div>
        </el-form-item>
        <el-form-item label="角色分配" prop="roleIds" v-if="!form.id">
          <el-checkbox-group v-model="form.roleIds">
            <el-checkbox v-for="role in roleOptions" :key="role.id" :label="role.id">
              {{ role.name }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="form-tip">请为用户分配角色，可多选</div>
        </el-form-item>
      </el-form>

      <!-- 员工用户表单 -->
      <el-form ref="employeeForm" :model="employeeForm" :rules="employeeRules" label-width="100px" v-if="activeTab === 'employee' && !isRegisterUser">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="employeeForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="入职日期" prop="entryDate">
          <el-date-picker
            v-model="employeeForm.entryDate"
            type="date"
            placeholder="选择入职日期"
            style="width: 100%;"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="employeeForm.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
            <el-radio label="其他">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="employeeForm.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="employeeForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="employeeForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="家庭住址" prop="address">
          <el-input v-model="employeeForm.address" placeholder="请输入家庭住址" />
        </el-form-item>
        <el-form-item label="部门" prop="departmentId">
          <div class="parent-department-container">
            <el-tree-select
              v-model="employeeForm.departmentId"
              :data="departmentOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              placeholder="请选择部门"
              check-strictly
              clearable
            />
            <el-button
              v-if="employeeForm.departmentId"
              type="text"
              icon="el-icon-close"
              class="clear-parent-btn"
              @click="employeeForm.departmentId = null"
            ></el-button>
          </div>
        </el-form-item>
        <el-form-item label="职位" prop="positionId" :rules="[{ required: true, message: '请选择职位', trigger: 'change' }]">
          <div class="position-selector">
            <el-select
              v-model="selectedPositionName"
              placeholder="请选择职位名称"
              clearable
              filterable
              style="width: 50%;"
              @change="handlePositionNameSelect"
            >
              <el-option
                v-for="group in positionGroups"
                :key="group.value"
                :label="group.label"
                :value="group.value"
              ></el-option>
            </el-select>
            <el-select
              v-model="employeeForm.positionId"
              placeholder="请选择职级"
              clearable
              filterable
              style="width: 50%;"
              :disabled="!selectedPositionName"
              @change="handlePositionLevelSelect"
            >
              <el-option
                v-for="level in currentPositionLevels"
                :key="level.id"
                :label="level.levelDisplay"
                :value="level.id"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="HRBP" prop="hrbpId">
          <div class="leader-select-container">
            <div class="department-tree">
              <el-tree
                :data="departmentOptions"
                :props="{ label: 'name', children: 'children' }"
                node-key="id"
                highlight-current
                @node-click="handleHRBPDepartmentSelect"
              ></el-tree>
            </div>
            <div class="employee-list">
              <div class="employee-list-header">
                <span>{{ selectedHRBPDepartmentName || '请选择部门' }}</span>
                <div v-if="loadingHRBPEmployees" class="loading-indicator">
                  <i class="el-icon-loading"></i> 加载中...
                </div>
              </div>
              <el-table
                v-loading="loadingHRBPEmployees"
                :data="hrbpEmployees"
                height="300"
                @row-click="handleHRBPSelect"
                highlight-current-row
                :row-class-name="tableRowClassName"
                empty-text="该部门暂无HR"
              >
                <el-table-column prop="workNo" label="工号" width="120"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
              </el-table>
            </div>
          </div>
          <div class="selected-employee" v-if="selectedHRBP">
            <span class="label">已选择:</span>
            <el-tag size="medium" type="success">
              <i class="el-icon-user"></i>
              {{ selectedHRBP.name }}
              <span class="work-no" v-if="selectedHRBP.workNo">({{ selectedHRBP.workNo }})</span>
            </el-tag>
            <el-button type="text" icon="el-icon-close" class="clear-btn" @click="clearSelectedHRBP"></el-button>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog :title="title" :visible.sync="roleOpen" width="600px" append-to-body :close-on-click-modal="false">
      <el-form :model="form" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="form.username" disabled />
        </el-form-item>
        <el-form-item label="角色分配">
          <el-checkbox-group v-model="roleIds">
            <el-checkbox v-for="role in roleOptions" :key="role.id" :label="role.id">
              {{ role.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRole">确 定</el-button>
        <el-button @click="cancelRole">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog :title="title" :visible.sync="passwordOpen" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="form.username" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password placeholder="请输入新密码" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password placeholder="请再次输入新密码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPassword">确 定</el-button>
        <el-button @click="cancelPassword">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改用户名对话框 -->
    <el-dialog :title="title" :visible.sync="usernameOpen" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="usernameForm" :model="usernameForm" :rules="usernameRules" label-width="100px">
        <el-form-item label="当前用户名">
          <el-input v-model="form.username" disabled />
        </el-form-item>
        <el-form-item label="新用户名" prop="newUsername">
          <el-input v-model="usernameForm.newUsername" placeholder="请输入新用户名" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUsername">确 定</el-button>
        <el-button @click="cancelUsername">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 员工详情对话框 -->
    <el-dialog title="员工详情" :visible.sync="employeeDetailOpen" width="900px" append-to-body :close-on-click-modal="false">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ employeeDetail.userId || '未注册' }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ employeeDetail.username || '未注册' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ employeeDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="工号">{{ employeeDetail.workNo }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ employeeDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ formatGender(employeeDetail.gender) }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ employeeDetail.departmentNames }}</el-descriptions-item>
        <el-descriptions-item label="职位">
          <span v-if="employeeDetail.positionName">
            {{ employeeDetail.positionName }}
            <el-tag size="mini" type="info" v-if="employeeDetail.level">L{{ employeeDetail.level }}</el-tag>
          </span>
          <span v-else class="empty-text">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="入职日期">{{ employeeDetail.entryDate }}</el-descriptions-item>
        <el-descriptions-item label="在职状态">
          <el-tag :type="employeeDetail.status ? 'success' : 'danger'">
            {{ employeeDetail.status ? '在职' : '离职' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="HRBP">
          <template v-if="employeeDetail.hrbpId">
            <span v-if="employeeDetail.hrbpName">
              {{ employeeDetail.hrbpName }}
              <span v-if="employeeDetail.hrbpWorkNo" style="color: #909399; font-size: 12px;">({{ employeeDetail.hrbpWorkNo }})</span>
            </span>
            <span v-else>ID: {{ employeeDetail.hrbpId }}</span>
          </template>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否管理员">
          <el-tag v-if="employeeDetail.isAdmin" type="success">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ employeeDetail.idCard || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ employeeDetail.phone || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ employeeDetail.email || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="家庭住址">{{ employeeDetail.address || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ employeeDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ employeeDetail.updateTime }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="employeeDetailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 修改员工信息对话框 -->
    <el-dialog title="修改员工信息" :visible.sync="editEmployeeOpen" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="editEmployeeForm" :model="editEmployeeForm" :rules="employeeRules" label-width="100px">
        <el-form-item label="员工ID" prop="id">
          <el-input v-model="editEmployeeForm.id" disabled />
        </el-form-item>
        <el-form-item label="工号">
          <el-input v-model="editEmployeeForm.workNo" disabled />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editEmployeeForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="入职日期" prop="entryDate">
          <el-date-picker
            v-model="editEmployeeForm.entryDate"
            type="date"
            placeholder="选择入职日期"
            style="width: 100%;"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="editEmployeeForm.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
            <el-radio label="其他">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="editEmployeeForm.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="editEmployeeForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editEmployeeForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="家庭住址" prop="address">
          <el-input v-model="editEmployeeForm.address" placeholder="请输入家庭住址" />
        </el-form-item>
        <el-form-item label="部门" prop="departmentId">
          <div class="parent-department-container">
            <el-tree-select
              v-model="editEmployeeForm.departmentId"
              :data="departmentOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              placeholder="请选择部门"
              check-strictly
              clearable
            />
            <el-button
              v-if="editEmployeeForm.departmentId"
              type="text"
              icon="el-icon-close"
              class="clear-parent-btn"
              @click="editEmployeeForm.departmentId = null"
            ></el-button>
          </div>
        </el-form-item>
        <el-form-item label="职位" prop="positionId" :rules="[{ required: true, message: '请选择职位', trigger: 'change' }]">
          <div class="position-selector">
            <el-select
              v-model="selectedEditPositionName"
              placeholder="请选择职位名称"
              clearable
              filterable
              style="width: 50%;"
              @change="handleEditPositionNameSelect"
            >
              <el-option
                v-for="group in positionGroups"
                :key="group.value"
                :label="group.label"
                :value="group.value"
              ></el-option>
            </el-select>
            <el-select
              v-model="editEmployeeForm.positionId"
              placeholder="请选择职级"
              clearable
              filterable
              style="width: 50%;"
              :disabled="!selectedEditPositionName"
              @change="handleEditPositionLevelSelect"
            >
              <el-option
                v-for="level in currentEditPositionLevels"
                :key="level.id"
                :label="level.levelDisplay"
                :value="level.id"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="HRBP" prop="hrbpId">
          <!-- 显示已选择的HRBP -->
          <el-input
            v-if="selectedEditHRBP"
            :value="selectedEditHRBP.name + (selectedEditHRBP.workNo ? ' (' + selectedEditHRBP.workNo + ')' : '')"
            placeholder="请选择HRBP"
            readonly
            class="hrbp-display-input"
          >
            <el-button slot="append" icon="el-icon-close" @click="clearSelectedEditHRBP"></el-button>
            <el-button slot="append" icon="el-icon-arrow-down" @click="showHRBPSelector = !showHRBPSelector"></el-button>
          </el-input>

          <!-- 未选择HRBP时显示选择按钮 -->
          <el-button v-else type="primary" icon="el-icon-plus" @click="showHRBPSelector = true">选择HRBP</el-button>

          <!-- HRBP选择器 -->
          <el-dialog
            title="选择HRBP"
            :visible.sync="showHRBPSelector"
            width="700px"
            append-to-body
            :close-on-click-modal="false"
          >
            <div class="leader-select-container">
              <div class="department-tree">
                <el-tree
                  :data="departmentOptions"
                  :props="{ label: 'name', children: 'children' }"
                  node-key="id"
                  highlight-current
                  @node-click="handleEditHRBPDepartmentSelect"
                ></el-tree>
              </div>
              <div class="employee-list">
                <div class="employee-list-header">
                  <span>{{ selectedEditHRBPDepartmentName || '请选择部门' }}</span>
                  <div v-if="loadingEditHRBPEmployees" class="loading-indicator">
                    <i class="el-icon-loading"></i> 加载中...
                  </div>
                </div>
                <el-table
                  v-loading="loadingEditHRBPEmployees"
                  :data="editHrbpEmployees"
                  height="300"
                  @row-click="handleEditHRBPSelect"
                  highlight-current-row
                  :row-class-name="tableRowClassName"
                  empty-text="该部门暂无HR"
                >
                  <el-table-column prop="workNo" label="工号" width="120"></el-table-column>
                  <el-table-column prop="name" label="姓名"></el-table-column>
                </el-table>
              </div>
            </div>
            <div slot="footer" class="dialog-footer">
              <el-button @click="showHRBPSelector = false">取 消</el-button>
              <el-button type="primary" @click="confirmHRBPSelection">确 定</el-button>
            </div>
          </el-dialog>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditEmployeeForm">确 定</el-button>
        <el-button @click="cancelEditEmployee">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, addUser, updateUser, deleteUser, getUserRoles, setUserRoles, resetUserPassword } from '@/api/system/user'
import { listRole } from '@/api/system/role'
import { pageEmployees, updateEmployeeStatus, getUnregisteredEmployees, createEmployee, getEmployeesByDepartmentIdAndRole, updateSystemEmployee } from '@/api/employee/employee'
import { getDepartmentTree } from '@/api/system/department'
import { listPosition } from '@/api/system/position'
import request from '@/utils/request'

export default {
  name: 'User',
  data() {
    // 验证确认密码是否与新密码一致
    const validateConfirmPassword = (_, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    return {
      // 激活的标签页
      activeTab: 'admin',
      // 是否是注册用户操作
      isRegisterUser: false,
      // 遮罩层
      loading: false,
      // 用户表格数据
      userList: [],
      // 角色选项
      roleOptions: [],
      // 员工选项
      employeeOptions: [],
      // 选中的角色ID数组
      roleIds: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示角色弹出层
      roleOpen: false,
      // 是否显示密码弹出层
      passwordOpen: false,
      // 是否显示用户名弹出层
      usernameOpen: false,
      // 总条数
      total: 0,
      // 当前登录用户ID
      currentUserId: this.$store.getters.userId,
      // 密码表单
      passwordForm: {
        newPassword: '',
        confirmPassword: ''
      },
      // 用户名表单
      usernameForm: {
        newUsername: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        username: undefined,
        isAdmin: undefined
      },
      // 员工查询参数
      employeeQueryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        workNo: undefined,
        departmentId: undefined,
        positionId: undefined,
        status: undefined
      },
      // 员工列表数据
      employeeList: [],
      // 员工总条数
      employeeTotal: 0,
      // 部门树选项
      departmentOptions: [],
      // 职位选项
      positionOptions: [],
      // 职位分组选项
      positionGroups: [],
      // 选择的职位名称
      selectedPositionName: '',
      // 当前职位名称对应的职级选项
      currentPositionLevels: [],
      // 职位选择相关数据
      positionSelection: null, // 职位级联选择器的值
      positionCascaderOptions: [], // 职位级联选择器的选项
      selectedPositionValue: null, // 保存选择的职位值
      selectedPositionType: null, // 保存选择的职位类型（name或level）
      // 部门选择弹出框是否可见
      departmentPopoverVisible: false,
      // 选中的部门名称
      selectedDepartmentName: '',
      // 是否显示员工详情弹窗
      employeeDetailOpen: false,
      // 员工详情数据
      employeeDetail: {},
      // 是否显示修改员工信息弹窗
      editEmployeeOpen: false,
      // 修改员工信息表单
      editEmployeeForm: {
        id: undefined,
        workNo: '',
        name: '',
        entryDate: '',
        gender: '男',
        idCard: '',
        phone: '',
        email: '',
        address: '',
        departmentId: null,
        positionId: null,
        hrbpId: null,
        status: true
      },
      // 修改员工信息相关数据
      selectedEditHRBPDepartmentName: '',
      selectedEditHRBPDepartmentId: null,
      editHrbpEmployees: [],
      loadingEditHRBPEmployees: false,
      selectedEditHRBP: null,
      showHRBPSelector: false, // 是否显示HRBP选择器
      // 修改员工职位选择相关数据
      selectedEditPositionName: '',
      currentEditPositionLevels: [],
      // 管理员用户表单参数
      form: {
        id: undefined,
        username: undefined,
        password: undefined,
        employeeId: undefined,
        isAdmin: false,
        roleIds: []
      },
      // 员工表单参数
      employeeForm: {
        name: '',
        entryDate: '',
        gender: 'MALE',
        idCard: '',
        phone: '',
        email: '',
        address: '',
        departmentId: null,
        positionId: null,
        hrbpId: null,
        status: true
      },
      // HRBP选择相关数据
      selectedHRBPDepartmentName: '',
      selectedHRBPDepartmentId: null,
      hrbpEmployees: [],
      loadingHRBPEmployees: false,
      selectedHRBP: null,
      // 管理员用户表单校验
      rules: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ]
      },
      // 员工表单校验
      employeeRules: {
        name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '姓名不能包含空格', trigger: 'blur' }
        ],
        entryDate: [
          { required: true, message: '入职日期不能为空', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
        ],
        departmentId: [
          { required: true, message: '部门不能为空', trigger: 'change' }
        ],
        positionId: [
          { required: true, message: '职位不能为空', trigger: 'change' }
        ],
        hrbpId: [
          { required: true, message: 'HRBP不能为空', trigger: 'change' }
        ]
      },
      // 密码表单校验
      passwordRules: {
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      // 用户名表单校验
      usernameRules: {
        newUsername: [
          { required: true, message: '请输入新用户名', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 扁平化部门树，用于下拉选择
    flatDepartments() {
      const result = []

      // 递归处理部门树
      const processDepartments = (departments, level = 0, parentPath = '') => {
        if (!departments || !departments.length) return

        departments.forEach(dept => {
          // 创建部门路径
          const path = parentPath ? `${parentPath} > ${dept.name}` : dept.name

          // 添加到结果中，包含层级信息
          result.push({
            ...dept,
            level,
            path,
            nameWithLevel: '　'.repeat(level) + (level > 0 ? '├─ ' : '') + dept.name
          })

          // 处理子部门
          if (dept.children && dept.children.length) {
            processDepartments(dept.children, level + 1, path)
          }
        })
      }

      processDepartments(this.departmentOptions)
      return result
    }
  },
  created() {
    this.getList()
    this.getDepartmentOptions()
    this.getPositionOptions()
    // 确保初始化时不会自动选择职位
    this.initEmployeeQuery()
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true
      this.queryParams.isAdmin = this.activeTab === 'admin' ? true : false
      listUser(this.queryParams).then(response => {
        // 适配后端返回的数据结构
        if (response && response.code === 1 && response.data) {
          this.userList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response && response.msg || '获取用户列表失败')
          this.userList = []
          this.total = 0
        }
        this.loading = false
      }).catch(error => {
        this.$message.error(error.message || '获取用户列表失败')
        this.userList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 标签页切换 */
    handleTabClick() {
      if (this.activeTab === 'admin') {
        this.resetQuery()
      } else {
        this.initEmployeeQuery()
        this.getEmployeeList()
      }
    },

    /** 查询员工列表 */
    getEmployeeList() {
      this.loading = true

      // 准备查询参数
      const queryParams = { ...this.employeeQueryParams }

      // 根据职位选择添加不同的参数
      if (this.positionSelection) {
        // 使用保存的选择类型
        if (this.selectedPositionType === 'name') {
          // 如果选择的是职位名称，添加positionName参数
          queryParams.positionName = this.positionSelection
          // 删除positionId参数
          delete queryParams.positionId
        } else {
          // 如果选择的是职级，添加positionId参数
          queryParams.positionId = this.positionSelection
        }
      } else if (this.employeeQueryParams.positionId) {
        // 兼容旧的选择方式
        queryParams.positionId = this.employeeQueryParams.positionId
      }

      pageEmployees(queryParams).then(response => {
        if (response && response.code === 1 && response.data) {
          this.employeeList = response.data.records || []
          this.employeeTotal = response.data.total || 0
        } else {
          this.$message.error(response && response.msg || '获取员工列表失败')
          this.employeeList = []
          this.employeeTotal = 0
        }
        this.loading = false
      }).catch(error => {
        this.$message.error(error.message || '获取员工列表失败')
        this.employeeList = []
        this.employeeTotal = 0
        this.loading = false
      })
    },

    /** 获取部门树选项 */
    getDepartmentOptions() {
      getDepartmentTree().then(response => {
        if (response && response.code === 1) {
          this.departmentOptions = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取部门树失败')
          this.departmentOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取部门树失败')
        this.departmentOptions = []
      })
    },

    /** 获取职位选项 */
    getPositionOptions() {
      listPosition().then(response => {
        if (response && response.code === 1) {
          // 保存原始职位数据
          this.positionOptions = response.data || []

          // 按职位名称分组
          const groups = {}
          this.positionOptions.forEach(position => {
            if (!groups[position.name]) {
              groups[position.name] = []
            }
            groups[position.name].push({
              id: position.id,
              name: position.name,
              level: position.level,
              levelDisplay: `L${position.level}`, // 格式化显示职级
              accountId: position.accountId
            })
          })

          // 转换为下拉选择需要的格式
          this.positionGroups = Object.keys(groups).map(name => ({
            label: name,
            value: name,
            name: name, // 添加name属性
            children: groups[name].map(item => ({
              ...item,
              label: item.levelDisplay // 确保子项有label属性
            }))
          }))

          // 构建级联选择器的选项
          this.positionCascaderOptions = []

          // 构建职位+职级的二级菜单
          Object.keys(groups).forEach(name => {
            const positionGroup = {
              id: name, // 使用职位名称作为id
              label: name,
              type: 'name', // 标记为职位名称类型
              children: []
            }

            // 添加该职位下的所有职级
            groups[name].forEach(position => {
              positionGroup.children.push({
                id: position.id, // 使用职位ID
                label: `L${position.level}`, // 显示职级
                type: 'level', // 标记为职级类型
                positionName: name // 保存职位名称，方便后续使用
              })
            })

            this.positionCascaderOptions.push(positionGroup)
          })

          console.log('获取职位选项成功，级联选择器选项:', this.positionCascaderOptions)
        } else {
          this.$message.error(response && response.msg || '获取职位选项失败')
          this.positionOptions = []
          this.positionGroups = []
          this.positionCascaderOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取职位选项失败')
        this.positionOptions = []
        this.positionGroups = []
        this.positionCascaderOptions = []
      })
    },

    // 处理职位名称选择
    handlePositionNameSelect(positionName) {
      this.selectedPositionName = positionName
      this.employeeForm.positionId = null // 清除之前选择的职位ID

      // 根据选择的职位名称筛选对应的职级选项
      if (positionName) {
        const group = this.positionGroups.find(g => g.value === positionName)
        if (group) {
          this.currentPositionLevels = group.children

          // 如果只有一个职级选项，自动选择
          if (this.currentPositionLevels.length === 1) {
            this.employeeForm.positionId = this.currentPositionLevels[0].id
          }
        } else {
          this.currentPositionLevels = []
        }
      } else {
        this.currentPositionLevels = []
      }

      // 如果表单已经验证过，重新验证
      if (this.$refs.employeeForm) {
        this.$refs.employeeForm.validateField('positionId')
      }
    },

    // 处理职级选择
    handlePositionLevelSelect(positionId) {
      this.employeeForm.positionId = positionId
    },

    /** 员工搜索按钮操作 */
    handleEmployeeQuery() {
      this.employeeQueryParams.pageNum = 1
      this.getEmployeeList()
    },

    /** 重置员工查询参数 */
    resetEmployeeQuery() {
      this.employeeQueryParams = {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        workNo: undefined,
        departmentId: undefined,
        positionId: undefined,
        status: undefined
      }
      this.selectedDepartmentName = ''
    },

    /** 初始化员工查询页面 */
    initEmployeeQuery() {
      // 重置查询参数
      this.resetEmployeeQuery()
      // 清空职位选择
      this.employeeQueryParams.positionId = undefined
      this.positionSelection = null
    },

    // 处理职位级联选择器变化
    handlePositionCascaderChange(value) {
      if (!value) {
        // 清空所有职位相关参数
        this.employeeQueryParams.positionId = undefined
        return
      }

      // 检查是否是职位ID（职级）
      let isPositionName = true;

      // 检查是否是职位ID（职级）
      for (const group of this.positionCascaderOptions) {
        if (group.children) {
          const levelOption = group.children.find(option => option.id === value)
          if (levelOption) {
            isPositionName = false;
            break;
          }
        }
      }

      // 保存选择的值，用于后续查询
      this.selectedPositionValue = value;
      this.selectedPositionType = isPositionName ? 'name' : 'level';

      if (isPositionName) {
        // 如果选择的是职位名称，清空职位ID
        this.employeeQueryParams.positionId = undefined
      } else {
        // 如果选择的是职级，设置职位ID
        this.employeeQueryParams.positionId = value
      }
    },

    /** 处理部门树选择 */
    handleDepartmentTreeSelect(data) {
      this.selectedDepartmentName = data.name
      this.employeeQueryParams.departmentId = data.id
      this.departmentPopoverVisible = false
    },

    /** 清除选中的部门 */
    clearSelectedDepartment() {
      this.selectedDepartmentName = ''
      this.employeeQueryParams.departmentId = undefined
    },

    /** 员工分页大小变化 */
    handleEmployeeSizeChange(val) {
      this.employeeQueryParams.pageSize = val
      this.getEmployeeList()
    },

    /** 员工分页页码变化 */
    handleEmployeeCurrentChange(val) {
      this.employeeQueryParams.pageNum = val
      this.getEmployeeList()
    },

    /** 查看员工详情 */
    handleViewDetail(row) {
      // 先显示基本信息
      this.employeeDetail = { ...row }
      this.employeeDetailOpen = true

      // 然后获取完整的员工信息
      // const loading = this.$loading({
      //   lock: true,
      //   text: '加载员工详情...',
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(0, 0, 0, 0.7)'
      // })

      // 员工详情在查询员工列表时已经包含了
      const employeeData = this.employeeDetail

      // 如果有HRBP ID，获取HRBP信息
      if (employeeData.hrbpId) {
        console.log('员工详情有HRBP ID:', employeeData.hrbpId)

        // 使用系统API获取HRBP员工信息
        request({
          url: '/system/employee/' + employeeData.hrbpId,
          method: 'get'
        }).then(hrbpResponse => {
          if (hrbpResponse && hrbpResponse.code === 1 && hrbpResponse.data) {
            const hrbpData = hrbpResponse.data
            console.log('获取HRBP信息成功:', hrbpData)

            // 更新员工详情中的HRBP信息
            this.employeeDetail.hrbpName = hrbpData.name
            this.employeeDetail.hrbpWorkNo = hrbpData.workNo

            // 强制更新视图
            this.$forceUpdate()
          } else {
            console.error('获取HRBP信息失败:', hrbpResponse)
          }
        }).catch(error => {
          console.error('获取HRBP信息失败:', error)
        })
      }
    },

    /** 格式化性别 */
    formatGender(gender) {
      // 直接返回性别值，不需要转换
      return gender || '未知'
    },

    /** 修改员工信息 */
    handleEditEmployee(row) {
      this.resetEditEmployeeForm()

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '加载员工信息...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 使用系统API获取完整的员工信息
      request({
        url: '/system/employee/' + row.id,
        method: 'get'
      }).then(response => {
        loading.close()
        if (response && response.code === 1 && response.data) {
          console.log('获取员工详情成功:', JSON.stringify(response.data, null, 2))
          const employeeData = response.data
          // 详细打印HRBP相关信息
          console.log('HRBP ID:', employeeData.hrbpId)
          console.log('HRBP Name:', employeeData.hrbpName)
          console.log('HRBP WorkNo:', employeeData.hrbpWorkNo)
          console.log('HRBP Department ID:', employeeData.hrbpDepartmentId)
          console.log('HRBP Department Name:', employeeData.hrbpDepartmentName)

          // 填充表单数据
          this.editEmployeeForm = {
            id: employeeData.id,
            workNo: employeeData.workNo,
            name: employeeData.name,
            entryDate: employeeData.entryDate,
            gender: employeeData.gender,
            idCard: employeeData.idCard,
            phone: employeeData.phone,
            email: employeeData.email,
            address: employeeData.address,
            departmentId: employeeData.departmentId,
            positionId: employeeData.positionId,
            hrbpId: employeeData.hrbpId,
            status: employeeData.status
          }

          // 如果有职位ID，设置职位名称和职级
          if (employeeData.positionId) {
            console.log('职位ID:', employeeData.positionId)
            console.log('职位名称:', employeeData.positionName)
            console.log('职级:', employeeData.level)

            const position = this.positionOptions.find(p => p.id === employeeData.positionId)
            if (position) {
              console.log('找到匹配的职位:', position)
              this.selectedEditPositionName = position.name
              this.handleEditPositionNameSelect(position.name)

              // 确保在职级选项加载完成后，选中正确的职级
              this.$nextTick(() => {
                console.log('当前职级选项:', this.currentEditPositionLevels)
                this.editEmployeeForm.positionId = employeeData.positionId
              })
            } else if (employeeData.positionName) {
              // 如果在职位选项中找不到匹配的职位，但有职位名称，尝试通过名称匹配
              console.log('通过名称匹配职位:', employeeData.positionName)
              this.selectedEditPositionName = employeeData.positionName
              this.handleEditPositionNameSelect(employeeData.positionName)

              // 在下一个事件循环中设置职位ID
              this.$nextTick(() => {
                // 如果有职级信息，尝试找到对应的职位ID
                if (employeeData.level && this.currentEditPositionLevels.length > 0) {
                  const levelPosition = this.currentEditPositionLevels.find(p => p.level === employeeData.level)
                  if (levelPosition) {
                    console.log('找到匹配的职级:', levelPosition)
                    this.editEmployeeForm.positionId = levelPosition.id
                  } else {
                    // 如果找不到匹配的职级，但有职位ID，直接使用
                    this.editEmployeeForm.positionId = employeeData.positionId
                  }
                } else {
                  // 如果没有职级信息，但有职位ID，直接使用
                  this.editEmployeeForm.positionId = employeeData.positionId
                }
              })
            }
          }

          // 如果有HRBP ID，获取HRBP信息
          if (employeeData.hrbpId) {
            console.log('员工有HRBP ID:', employeeData.hrbpId)
            // 使用fetchHRBPInfo方法获取HRBP信息
            this.fetchHRBPInfo(employeeData.hrbpId)
          } else {
            console.log('员工没有HRBP信息')
          }

          // 打开编辑对话框
          this.editEmployeeOpen = true
        } else {
          this.$message.error(response && response.msg || '获取员工信息失败')
          console.error('获取员工信息失败:', response)
        }
      }).catch(error => {
        loading.close()
        this.$message.error('获取员工信息失败')
        console.error('获取员工信息失败:', error)
      })
    },

    /** 查找HRBP所在部门 */
    async findHRBPDepartment(hrbpId) {
      console.log('开始查找HRBP所在部门, HRBP ID:', hrbpId)

      // 首先尝试直接获取HRBP信息
      this.fetchHRBPInfo(hrbpId)

      // 创建一个加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在查找HRBP所在部门...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 使用广度优先搜索遍历部门树
        const queue = [...this.departmentOptions]
        const visited = new Set()

        while (queue.length > 0) {
          const dept = queue.shift()

          if (visited.has(dept.id)) continue
          visited.add(dept.id)

          console.log('检查部门:', dept.name)

          try {
            // 检查当前部门
            const response = await getEmployeesByDepartmentIdAndRole(dept.id, 'ROLE_hr')

            if (response && response.code === 1 && response.data) {
              const hrList = response.data
              const foundHR = hrList.find(hr => hr.id === hrbpId)

              if (foundHR) {
                console.log('找到HRBP所在部门:', dept.name)
                this.selectedEditHRBPDepartmentId = dept.id
                this.selectedEditHRBPDepartmentName = dept.name
                this.loadEditHRBPEmployees(dept.id)
                loading.close()
                return true
              }
            }
          } catch (error) {
            console.error('检查部门失败:', dept.name, error)
          }

          // 将子部门加入队列
          if (dept.children && dept.children.length) {
            queue.push(...dept.children)
          }
        }

        console.log('未找到HRBP所在部门')
        // 如果已经通过fetchHRBPInfo获取到了HRBP信息，就不显示警告
        if (!this.selectedEditHRBP || this.selectedEditHRBP.id !== hrbpId) {
          this.$message.warning('未找到HRBP所在部门，请手动选择')
        }
        return false
      } finally {
        loading.close()
      }
    },

    /** 重置修改员工信息表单 */
    resetEditEmployeeForm() {
      this.editEmployeeForm = {
        id: undefined,
        workNo: '',
        name: '',
        entryDate: '',
        gender: '男',
        idCard: '',
        phone: '',
        email: '',
        address: '',
        departmentId: null,
        positionId: null,
        hrbpId: null,
        status: true
      }

      // 清空HRBP选择相关数据
      this.selectedEditHRBPDepartmentName = ''
      this.selectedEditHRBPDepartmentId = null
      this.editHrbpEmployees = []
      this.selectedEditHRBP = null

      // 清空职位选择相关数据
      this.selectedEditPositionName = ''
      this.currentEditPositionLevels = []

      // 重置表单验证
      if (this.$refs.editEmployeeForm) {
        this.$refs.editEmployeeForm.resetFields()
      }
    },

    // 处理修改员工信息职位名称选择
    handleEditPositionNameSelect(positionName) {
      this.selectedEditPositionName = positionName
      const currentPositionId = this.editEmployeeForm.positionId // 保存当前选择的职位ID

      // 根据选择的职位名称筛选对应的职级选项
      if (positionName) {
        const group = this.positionGroups.find(g => g.value === positionName)
        if (group) {
          this.currentEditPositionLevels = group.children
          console.log('职位名称对应的职级选项:', this.currentEditPositionLevels)

          // 检查当前选择的职位ID是否在新的职级选项中
          const positionStillValid = this.currentEditPositionLevels.some(p => p.id === currentPositionId)

          if (!positionStillValid) {
            // 如果当前选择的职位ID不在新的职级选项中，清除选择
            this.editEmployeeForm.positionId = null

            // 如果只有一个职级选项，自动选择
            if (this.currentEditPositionLevels.length === 1) {
              this.editEmployeeForm.positionId = this.currentEditPositionLevels[0].id
              console.log('自动选择唯一职级:', this.editEmployeeForm.positionId)
            }
          } else {
            // 如果当前选择的职位ID在新的职级选项中，保持选择
            console.log('保持当前职级选择:', currentPositionId)
          }
        } else {
          this.currentEditPositionLevels = []
          this.editEmployeeForm.positionId = null
        }
      } else {
        this.currentEditPositionLevels = []
        this.editEmployeeForm.positionId = null
      }

      // 如果表单已经验证过，重新验证
      if (this.$refs.editEmployeeForm) {
        this.$refs.editEmployeeForm.validateField('positionId')
      }
    },

    // 处理修改员工信息职级选择
    handleEditPositionLevelSelect(positionId) {
      this.editEmployeeForm.positionId = positionId
    },

    /** 处理修改员工信息HRBP部门选择 */
    handleEditHRBPDepartmentSelect(data) {
      this.selectedEditHRBPDepartmentId = data.id
      this.selectedEditHRBPDepartmentName = data.name
      this.loadEditHRBPEmployees(data.id)
    },

    /** 加载修改员工信息HRBP员工列表 */
    loadEditHRBPEmployees(departmentId) {
      this.loadingEditHRBPEmployees = true
      this.editHrbpEmployees = []

      getEmployeesByDepartmentIdAndRole(departmentId, 'ROLE_hr')
        .then(response => {
          this.loadingEditHRBPEmployees = false
          if (response && response.code === 1) {
            this.editHrbpEmployees = response.data || []
            console.log('加载HR列表成功:', this.editHrbpEmployees)

            // 如果有HRBP ID，尝试在新加载的员工列表中查找并选中
            if (this.editEmployeeForm.hrbpId) {
              console.log('尝试查找并选中HRBP, ID:', this.editEmployeeForm.hrbpId)
              const hrbp = this.editHrbpEmployees.find(emp => emp.id === this.editEmployeeForm.hrbpId)
              if (hrbp) {
                console.log('找到匹配的HRBP:', hrbp)
                this.selectedEditHRBP = hrbp
                // 确保表单中的hrbpId正确设置
                this.editEmployeeForm.hrbpId = hrbp.id
                // 强制更新视图
                this.$forceUpdate()
              } else {
                console.log('在当前部门未找到匹配的HRBP')

                // 如果在当前部门未找到匹配的HRBP，但已经有selectedEditHRBP，保留它
                if (this.selectedEditHRBP && this.selectedEditHRBP.id === this.editEmployeeForm.hrbpId) {
                  console.log('保留已选择的HRBP:', this.selectedEditHRBP)
                } else {
                  // 如果没有已选择的HRBP，尝试获取HRBP信息
                  this.fetchHRBPInfo(this.editEmployeeForm.hrbpId)
                }
              }
            }

            if (this.editHrbpEmployees.length === 0) {
              this.$message.info('该部门下暂无HR')
            }
          } else {
            this.$message.error(response && response.msg || '获取HR列表失败')
          }
        })
        .catch(error => {
          this.loadingEditHRBPEmployees = false
          this.$message.error(error.message || '获取HR列表失败')
        })
    },

    /** 获取HRBP信息 */
    fetchHRBPInfo(hrbpId) {
      if (!hrbpId) return

      console.log('获取HRBP信息, ID:', hrbpId)

      // 使用系统API获取HRBP员工信息
      request({
        url: '/system/employee/' + hrbpId,
        method: 'get'
      }).then(response => {
        if (response && response.code === 1 && response.data) {
          const hrbpData = response.data
          console.log('获取HRBP信息成功:', hrbpData)

          // 设置HRBP信息
          this.selectedEditHRBP = {
            id: hrbpData.id,
            name: hrbpData.name,
            workNo: hrbpData.workNo
          }

          // 确保表单中的hrbpId正确设置
          this.editEmployeeForm.hrbpId = hrbpData.id

          // 如果HRBP有部门信息，设置部门信息
          if (hrbpData.departmentId && hrbpData.departmentName) {
            this.selectedEditHRBPDepartmentId = hrbpData.departmentId
            this.selectedEditHRBPDepartmentName = hrbpData.departmentName
          }

          // 强制更新视图
          this.$forceUpdate()
        } else {
          console.error('获取HRBP信息失败:', response)
        }
      }).catch(error => {
        console.error('获取HRBP信息失败:', error)
      })
    },

    /** 处理修改员工信息HRBP选择 */
    handleEditHRBPSelect(row) {
      this.selectedEditHRBP = row
      this.editEmployeeForm.hrbpId = row.id
    },

    /** 确认HRBP选择 */
    confirmHRBPSelection() {
      if (this.selectedEditHRBP) {
        // 确保表单中的hrbpId正确设置
        this.editEmployeeForm.hrbpId = this.selectedEditHRBP.id
        this.showHRBPSelector = false
      } else {
        this.$message.warning('请选择一个HRBP')
      }
    },

    /** 清除选中的修改员工信息HRBP */
    clearSelectedEditHRBP() {
      this.selectedEditHRBP = null
      this.editEmployeeForm.hrbpId = null
    },

    /** 提交修改员工信息表单 */
    submitEditEmployeeForm() {
      this.$refs.editEmployeeForm.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '正在提交...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          // 构建提交数据
          const data = {
            id: this.editEmployeeForm.id,
            name: this.editEmployeeForm.name,
            entryDate: this.editEmployeeForm.entryDate,
            gender: this.editEmployeeForm.gender,
            idCard: this.editEmployeeForm.idCard,
            phone: this.editEmployeeForm.phone,
            email: this.editEmployeeForm.email,
            address: this.editEmployeeForm.address,
            departmentId: this.editEmployeeForm.departmentId,
            positionId: this.editEmployeeForm.positionId,
            hrbpId: this.editEmployeeForm.hrbpId
          }

          // 调用API更新员工信息
          updateSystemEmployee(data)
            .then(response => {
              loading.close()
              if (response && response.code === 1) {
                this.$message.success('修改员工信息成功')
                this.editEmployeeOpen = false
                // 重新获取员工列表
                this.getEmployeeList()
              } else {
                this.$message.error(response && response.msg || '修改员工信息失败')
              }
            })
            .catch(error => {
              loading.close()
              this.$message.error(error.message || '修改员工信息失败')
            })
        } else {
          this.$message.warning('请填写完整表单')
          return false
        }
      })
    },

    /** 取消修改员工信息 */
    cancelEditEmployee() {
      this.editEmployeeOpen = false
      this.resetEditEmployeeForm()
    },

    /** 处理员工状态变更 */
    handleStatusChange(row, status) {
      // 如果状态变为离职，需要确认
      if (!status) {
        this.$confirm('将员工设置为离职状态会删除关联的用户账号，且无法撤销，是否继续?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.updateStatus(row.id, status)
        }).catch(() => {
          // 取消操作，恢复状态
          row.status = true
        })
      } else {
        // 如果状态变为在职，直接更新
        this.updateStatus(row.id, status)
      }
    },

    /** 更新员工状态 */
    updateStatus(id, status) {
      updateEmployeeStatus(id, status).then(response => {
        if (response && response.code === 1) {
          this.$message.success('更新员工状态成功')
          // 重新获取员工列表
          this.getEmployeeList()
        } else {
          this.$message.error(response && response.msg || '更新员工状态失败')
        }
      }).catch(error => {
        this.$message.error(error.message || '更新员工状态失败')
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        username: undefined,
        isAdmin: this.activeTab === 'admin' ? true : false
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (this.activeTab === 'admin') {
        this.reset()
        // 获取未注册员工选项
        this.getEmployeeOptions()
        // 获取角色选项
        this.getRoleOptions()
        this.form.isAdmin = true
        this.open = true
        this.title = '添加用户'
      } else {
        this.resetEmployeeForm()
        this.open = true
        this.title = '添加员工'
      }
    },

    /** 重置员工表单 */
    resetEmployeeForm() {
      this.employeeForm = {
        name: '',
        entryDate: '',
        gender: '男',
        idCard: '',
        phone: '',
        email: '',
        address: '',
        departmentId: null,
        positionId: null,
        hrbpId: null,
        status: true
      };

      // 清空HRBP选择相关数据
      this.selectedHRBPDepartmentName = '';
      this.selectedHRBPDepartmentId = null;
      this.hrbpEmployees = [];
      this.selectedHRBP = null;

      // 重置职位选择相关数据
      this.selectedPositionName = '';
      this.currentPositionLevels = [];

      // 重置表单验证
      if (this.$refs.employeeForm) {
        this.$refs.employeeForm.resetFields();
      }
    },

    /** 处理HRBP部门选择 */
    handleHRBPDepartmentSelect(data) {
      this.selectedHRBPDepartmentId = data.id;
      this.selectedHRBPDepartmentName = data.name;
      this.loadHRBPEmployees(data.id);
    },

    /** 加载HRBP员工列表 */
    loadHRBPEmployees(departmentId) {
      this.loadingHRBPEmployees = true;
      this.hrbpEmployees = [];

      getEmployeesByDepartmentIdAndRole(departmentId, 'ROLE_hr')
        .then(response => {
          this.loadingHRBPEmployees = false;
          if (response && response.code === 1) {
            this.hrbpEmployees = response.data || [];

            // 如果有HRBP ID，尝试在新加载的员工列表中查找并选中
            if (this.employeeForm.hrbpId) {
              const hrbp = this.hrbpEmployees.find(emp => emp.id === this.employeeForm.hrbpId);
              if (hrbp) {
                this.selectedHRBP = hrbp;
              }
            }

            if (this.hrbpEmployees.length === 0) {
              this.$message.info('该部门下暂无HR');
            }
          } else {
            this.$message.error(response && response.msg || '获取HR列表失败');
          }
        })
        .catch(error => {
          this.loadingHRBPEmployees = false;
          if (!error.isTokenExpired) {
            this.$message.error('获取HR列表失败');
          }
        });
    },

    /** 处理HRBP选择 */
    handleHRBPSelect(row) {
      this.selectedHRBP = row;
      this.employeeForm.hrbpId = row.id;
    },

    /** 清除选中的HRBP */
    clearSelectedHRBP() {
      this.selectedHRBP = null;
      this.employeeForm.hrbpId = null;
    },

    /** 设置表格行的样式 */
    tableRowClassName({ row }) {
      // 如果当前行是选中的员工，则添加高亮样式
      if (this.selectedHRBP && row.id === this.selectedHRBP.id) {
        return 'selected-row';
      }
      return '';
    },
    /** 修改用户名按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form = row
      this.usernameOpen = true
      this.title = '修改用户名'
      this.usernameForm = {
        newUsername: ''
      }
    },
    /** 分配角色按钮操作 */
    handleRole(row) {
      this.reset()
      this.form = row
      this.roleOpen = true
      this.title = '分配角色'
      this.getRoleOptions()
      // 使用userId而不是employeeId来获取用户角色
      // 在员工用户表格中，row.userId是用户ID，row.id是员工ID
      const userId = row.userId || row.id
      this.getUserRoles(userId)
    },

    /** 重置密码按钮操作 */
    handleResetPassword(row) {
      this.reset()
      this.form = row
      this.passwordOpen = true
      this.title = '重置密码'
      this.passwordForm = {
        newPassword: '',
        confirmPassword: ''
      }
    },
    /** 获取角色选项 */
    getRoleOptions() {
      listRole().then(response => {
        // 检查响应格式
        if (response && response.code === 1 && response.data) {
          if (response.data.records && Array.isArray(response.data.records)) {
            this.roleOptions = response.data.records
          } else {
            this.$message.error('获取角色选项失败: 数据格式不正确')
            this.roleOptions = []
          }
        } else {
          this.$message.error(response && response.msg || '获取角色选项失败')
          this.roleOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取角色选项失败')
        this.roleOptions = []
      })
    },
    /** 获取员工选项 */
    getEmployeeOptions() {
      return getUnregisteredEmployees().then(response => {
        console.log('Unregistered employee response:', response)
        // 检查响应格式
        if (response && response.code === 1) {
          // 成功响应
          if (response.data && Array.isArray(response.data)) {
            this.employeeOptions = response.data
          } else {
            // 数据为空数组也是正常的
            this.employeeOptions = []
          }
        } else {
          this.$message.error(response && response.msg || '获取未注册员工选项失败')
        }
        return this.employeeOptions
      }).catch(error => {
        console.error('Get unregistered employee options error:', error)
        this.$message.error('获取未注册员工选项失败')
        this.employeeOptions = []
        return this.employeeOptions
      })
    },
    /** 获取用户角色 */
    getUserRoles(userId) {
      getUserRoles(userId).then(response => {
        console.log('User roles response:', response)
        // 检查响应格式
        if (response && response.code === 1 && response.data) {
          if (Array.isArray(response.data)) {
            this.roleIds = response.data.map(role => role.id)
          } else {
            this.$message.error('获取用户角色失败: 数据格式不正确')
          }
        } else if (Array.isArray(response)) {
          // 兼容直接返回数组的情况
          this.roleIds = response.map(role => role.id)
        } else {
          this.$message.error(response && response.msg || '获取用户角色失败')
        }
      }).catch(error => {
        console.error('Get user roles error:', error)
        this.$message.error('获取用户角色失败')
      })
    },
    /** 提交按钮 */
    submitForm() {
      // 根据当前标签页选择不同的表单验证和提交逻辑
      if (this.activeTab === 'admin' || this.isRegisterUser) {
        // 管理员用户表单提交或注册用户操作
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.id != null && !this.isRegisterUser) {
              // 修改用户
              updateUser(this.form.id, this.form).then(response => {
                if (response && response.code === 1) {
                  this.$message.success('修改成功')
                  this.open = false
                  this.getList()
                } else {
                  this.$message.error(response && response.msg || '修改失败')
                }
              }).catch(error => {
                this.$message.error(error.message || '修改失败')
              })
            } else {
              // 新增用户或注册用户
              addUser(this.form).then(response => {
                if (response && response.code === 1) {
                  this.$message.success(this.isRegisterUser ? '注册用户成功' : '新增成功')
                  this.open = false

                  // 根据当前标签页刷新不同的列表
                  if (this.activeTab === 'admin') {
                    this.getList()
                  } else {
                    this.getEmployeeList()
                  }
                } else {
                  this.$message.error(response && response.msg || (this.isRegisterUser ? '注册用户失败' : '新增失败'))
                }
              }).catch(error => {
                this.$message.error(error.message || (this.isRegisterUser ? '注册用户失败' : '新增失败'))
              })
            }
          }
        })
      } else {
        // 员工表单提交
        this.$refs['employeeForm'].validate(valid => {
          if (valid) {
            // 显示加载中
            const loading = this.$loading({
              lock: true,
              text: '正在添加员工...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });

            // 处理表单数据，将空字符串转换为null
            const employeeData = { ...this.employeeForm };

            // 处理可能为空的字段
            const fieldsToCheck = ['entryDate', 'gender', 'idCard', 'phone', 'email', 'address'];
            fieldsToCheck.forEach(field => {
              if (employeeData[field] === '') {
                employeeData[field] = null;
              }
            });

            // 创建员工
            createEmployee(employeeData).then(response => {
              loading.close();
              if (response && response.code === 1) {
                this.$message.success('添加员工成功');
                this.open = false;
                this.getEmployeeList();
              } else {
                this.$message.error(response && response.msg || '添加员工失败');
              }
            }).catch(error => {
              loading.close();
              if (!error.isTokenExpired) {
                this.$message.error(error.message || '添加员工失败');
              }
            });
          }
        });
      }
    },
    /** 提交角色 */
    submitRole() {
      // 验证是否选择了角色
      if (!this.roleIds || this.roleIds.length === 0) {
        this.$message.warning('请至少选择一个角色')
        return
      }

      // 使用userId而不是employeeId
      // 在员工用户表格中，form.userId是用户ID，form.id是员工ID
      const userId = this.form.userId || this.form.id

      setUserRoles(userId, this.roleIds).then(response => {
        // 检查响应结果
        if (response && response.code === 0) {
          this.$message.error(response.msg || '角色分配失败')
        } else {
          this.$message.success('角色分配成功')
          this.roleOpen = false
          // 刷新用户列表
          this.getList()
        }
      }).catch(error => {
        this.$message.error(error.message || '角色分配失败')
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除用户名为"' + row.username + '"的用户?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 使用userId而不是employeeId
        const userId = row.userId || row.id
        return deleteUser(userId)
      }).then(() => {
        // 直接使用响应数据，不检查code字段
        this.getList()
        this.$message.success('删除成功')

        // 如果是当前用户，强制退出登录
        // 使用userId而不是employeeId
        const userId = row.userId || row.id
        if (userId === this.currentUserId) {
          this.$alert('您删除了自己的账号，需要重新登录', '提示', {
            confirmButtonText: '确定',
            callback: () => {
              this.$store.dispatch('user/logout')
              this.$router.push('/login')
            }
          })
        }
      }).catch(error => {
        this.$message.error(error.message || '删除失败')
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        username: undefined,
        password: undefined,
        employeeId: undefined,
        isAdmin: this.activeTab === 'admin',
        roleIds: []
      }
      this.roleIds = []
      this.resetForm('form')
      // 更新表单校验规则
      this.updateFormRules()
    },
    /** 重置表单 */
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
      // 重置注册用户标志
      this.isRegisterUser = false
    },
    /** 取消角色分配 */
    cancelRole() {
      this.roleOpen = false
    },

    /** 提交密码 */
    submitPassword() {
      this.$refs['passwordForm'].validate(valid => {
        if (valid) {
          // 使用userId而不是employeeId
          const userId = this.form.userId || this.form.id
          resetUserPassword(userId, { newPassword: this.passwordForm.newPassword }).then(() => {
            this.$message.success('密码重置成功')
            this.passwordOpen = false

            // 如果是当前用户，强制退出登录
            if (userId === this.currentUserId) {
              this.$alert('您重置了自己的密码，需要重新登录', '提示', {
                confirmButtonText: '确定',
                callback: () => {
                  this.$store.dispatch('user/logout')
                  this.$router.push('/login')
                }
              })
            }
          }).catch(error => {
            this.$message.error(error.message || '密码重置失败')
          })
        }
      })
    },

    /** 取消密码重置 */
    cancelPassword() {
      this.passwordOpen = false
      this.passwordForm = {
        newPassword: '',
        confirmPassword: ''
      }
    },

    /** 提交用户名 */
    submitUsername() {
      this.$refs['usernameForm'].validate(valid => {
        if (valid) {
          // 使用userId而不是employeeId
          const userId = this.form.userId || this.form.id
          updateUser(userId, { username: this.usernameForm.newUsername }).then(response => {
            // 检查响应结果
            if (response && response.code === 0) {
              this.$message.error(response.msg || '用户名修改失败')
            } else {
              this.$message.success('用户名修改成功')
              this.usernameOpen = false

              // 刷新用户列表
              this.getList()

              // 如果是当前用户，刷新页面
              if (userId === this.currentUserId) {
                this.$alert('您修改了自己的用户名，需要重新登录', '提示', {
                  confirmButtonText: '确定',
                  callback: () => {
                    this.$store.dispatch('user/logout')
                    this.$router.push('/login')
                  }
                })
              }
            }
          }).catch(error => {
            this.$message.error(error.message || '用户名修改失败')
          })
        }
      })
    },

    /** 取消用户名修改 */
    cancelUsername() {
      this.usernameOpen = false
      this.usernameForm = {
        newUsername: ''
      }
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    // 分页页码变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },

    // 处理管理员切换
    handleAdminChange() {
      // 更新表单校验规则
      this.updateFormRules()
    },

    // 更新表单校验规则
    updateFormRules() {
      if (!this.form.isAdmin) {
        // 非管理员用户必须关联员工
        this.rules.employeeId = [
          { required: true, message: '非管理员用户必须关联员工', trigger: 'change' }
        ]
      } else {
        // 管理员用户可以关联员工，也可以不关联
        this.rules.employeeId = []
      }
    },

    /** 注册用户按钮操作 */
    handleRegisterUser(row) {
      this.reset()
      // 设置标志变量，表示这是注册用户操作
      this.isRegisterUser = true

      // 获取员工详细信息，以便显示姓名和工号
      this.getEmployeeOptions().then(() => {
        // 设置表单数据
        this.form.employeeId = row.id
        this.form.isAdmin = false

        // 获取角色选项
        this.getRoleOptions()
        this.open = true
        this.title = '注册用户'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.operation-buttons {
  display: flex;
  flex-direction: row; /* 横向排列 */
  flex-wrap: wrap; /* 允许换行 */
  justify-content: center;
  gap: 5px;

  .el-button {
    margin: 2px;
  }
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

.el-input__inner {
  height: 32px;
}

.department-tree-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 5px;
}

.el-dialog {
  z-index: 3000 !important; /* 提高z-index确保在最上层 */
}

.form-tip {
  font-size: 12px;
  color: #E6A23C;
  line-height: 1.2;
  padding-top: 4px;
}

.admin-label {
  font-size: 14px;
  color: #606266;
  vertical-align: middle;
}

/* 部门领导选择组件样式 */
.leader-select-container {
  display: flex;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  height: 400px;
}

.leader-select-container .department-tree {
  width: 40%;
  border-right: 1px solid #dcdfe6;
  overflow: auto;
  padding: 10px;
}

.leader-select-container .employee-list {
  width: 60%;
  display: flex;
  flex-direction: column;
}

.leader-select-container .employee-list-header {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leader-select-container .loading-indicator {
  font-size: 12px;
  color: #909399;
}

.leader-select-container .employee-list >>> .el-table__empty-text {
  width: 100%;
  text-align: center;
}

.leader-select-container .employee-list >>> .el-table .el-table__row:hover > td {
  background-color: #f5f7fa;
}

.leader-select-container .employee-list >>> .el-table .el-table__row td {
  padding: 8px 0;
}

.selected-employee {
  margin-top: 10px;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
}

.selected-employee .label {
  font-weight: bold;
  color: #67c23a;
  margin-right: 8px;
}

.selected-employee .el-tag {
  margin-right: 5px;
  padding: 0 10px;
  height: 28px;
  line-height: 26px;
}

.selected-employee .el-tag i {
  margin-right: 5px;
}

.selected-employee .el-tag .work-no {
  color: #85ce61;
  font-size: 12px;
  margin-left: 3px;
}

.selected-employee .clear-btn {
  padding: 2px;
  color: #909399;
}

.selected-employee .clear-btn:hover {
  color: #f56c6c;
}

.parent-department-container {
  position: relative;
  display: flex;
  align-items: center;
}

.clear-parent-btn {
  position: absolute;
  right: 30px;
  z-index: 2;
  color: #909399;
  font-size: 16px;
}

.clear-parent-btn:hover {
  color: #f56c6c;
}

>>> .selected-row {
  background-color: #f0f9eb;
  font-weight: bold;
}

>>> .selected-row:hover > td {
  background-color: #e6f7d9 !important;
}

>>> .selected-row td {
  background-color: #f0f9eb !important;
}

/* 职位选择器样式 */
.position-selector {
  display: flex;
  align-items: center;
}


</style>
