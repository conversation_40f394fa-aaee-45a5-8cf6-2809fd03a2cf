import request from '@/utils/request'

// 查询考勤记录列表
export function listAttendance(query) {
  return request({
    url: '/attendance/page',
    method: 'get',
    params: query
  })
}

// 查询考勤记录详细
export function getAttendance(id) {
  return request({
    url: '/attendance/' + id,
    method: 'get'
  })
}

// 查询个人考勤记录列表
export function listPersonalAttendance(query) {
  return request({
    url: '/attendance/records/monthly',
    method: 'get',
    params: query
  })
}

// 查询下属考勤记录列表
export function listSubordinateAttendance(query) {
  return request({
    url: '/attendance/page',
    method: 'get',
    params: query
  })
}

// 打卡（统一签到和签退）
export function clock(data) {
  // 确保data包含location和type参数
  const clockData = {
    location: data.location || [0, 0],
    type: data.type || 'IN' // 默认为签到
  }

  return request({
    url: '/attendance/clock',
    method: 'post',
    data: clockData
  })
}

// 签到 - 保留兼容旧代码
export function clockIn(data) {
  return clock({
    location: data.location || [0, 0],
    type: 'IN'
  })
}

// 签退 - 保留兼容旧代码
export function clockOut(data) {
  return clock({
    location: data.location || [0, 0],
    type: 'OUT'
  })
}

// 修改考勤记录
export function updateAttendance(data) {
  return request({
    url: '/attendance/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除考勤记录
export function deleteAttendance(id) {
  return request({
    url: '/attendance/' + id,
    method: 'delete'
  })
}

// 获取员工考勤统计数据
export function getAttendanceStatistics(employeeId, startDate, endDate) {
  return request({
    url: '/attendance/statistics/employee/' + employeeId,
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}

// 获取部门考勤配置
export function getDepartmentAttendanceConfig() {
  return request({
    url: '/attendance/config',
    method: 'get'
  })
}

// 获取今日考勤记录
export function getTodayAttendance() {
  return request({
    url: '/attendance/today',
    method: 'get'
  })
}

// 更新部门考勤配置
export function updateDepartmentAttendanceConfig(data) {
  return request({
    url: '/attendance/config',
    method: 'put',
    data
  })
}

// 更新部门考勤设置
export function updateDepartmentAttendanceSetting(data) {
  return request({
    url: '/attendance/setting',
    method: 'put',
    data: data
  })
}
