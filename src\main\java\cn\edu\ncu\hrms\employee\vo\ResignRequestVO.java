package cn.edu.ncu.hrms.employee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 离职申请视图对象 VO
 */
@Data
@ApiModel(value="ResignRequestVO", description="离职申请视图对象")
public class ResignRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "申请ID")
    private Integer id;

    @ApiModelProperty(value = "是否待我审批")
    private Boolean isPendingByMe;

    @ApiModelProperty(value = "员工ID")
    private Integer employeeId;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName; // 需要 Service 层填充

    @ApiModelProperty(value = "员工工号")
    private String employeeWorkNo;// 需要 Service 层填充

    @ApiModelProperty(value = "部门ID")
    private Integer departmentId; // 需要 Service 层填充

    @ApiModelProperty(value = "部门名称")
    private String departmentName; // 需要 Service 层填充

    @ApiModelProperty(value = "职位名称")
    private String positionName;

    @ApiModelProperty(value = "离职日期")
    private LocalDate resignDate;

    @ApiModelProperty(value = "离职原因")
    private String reason;

    @ApiModelProperty(value = "申请状态 (PENDING, APPROVED, REJECTED, CANCELLED)")
    private String status;

    @ApiModelProperty(value = "申请状态名称")
    private String statusName; // Service 层填充

    @ApiModelProperty(value = "申请提交时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "最后更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 