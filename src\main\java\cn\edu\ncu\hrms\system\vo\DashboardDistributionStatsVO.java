package cn.edu.ncu.hrms.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(description = "管理员仪表盘 - 员工分布视图对象")
public class DashboardDistributionStatsVO {
    @ApiModelProperty(value = "部门ID", required = true, example = "201")
    private Integer departmentId;

    @ApiModelProperty(value = "部门名称", required = true, example = "技术部")
    private String departmentName;

    @ApiModelProperty(value = "上级部门ID")
    private Integer parentId;

    @ApiModelProperty(value = "子部门列表", notes = "用于构建部门树")
    private List<DashboardDistributionStatsVO> children = new ArrayList<>();

    @ApiModelProperty(value = "员工数量", required = true, example = "10")
    private int employeeCount;
}
