package cn.edu.ncu.hrms.employee.dto;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel(description = "奖惩信息查询数据传输对象")
public class RewardPunishmentQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "操作人的员工ID")
    private Integer operatorId;

    @ApiModelProperty(value = "奖惩类型 (1: 奖励, 2: 惩罚)")
    private Integer type;

    @ApiModelProperty(value = "员工工号（精确查询）")
    private String workNo;

    @ApiModelProperty(value = "员工姓名(模糊查询)")
    private String employeeName;

    @ApiModelProperty(value = "开始日期 (格式: yyyy-MM-dd)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期 (格式: yyyy-MM-dd)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
}