package cn.edu.ncu.hrms.auth.mapper;

import cn.edu.ncu.hrms.common.entity.RolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色权限关系Mapper接口
 */
@Mapper
public interface RolePermissionMapper {

    /**
     * 添加角色权限关系
     * 
     * @param rolePermission 角色权限关系
     * @return 影响行数
     */
    int insert(RolePermission rolePermission);

    /**
     * 根据角色ID删除角色权限关系
     * 
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(Integer roleId);

    /**
     * 根据权限ID删除角色权限关系
     * 
     * @param permissionId 权限ID
     * @return 影响行数
     */
    int deleteByPermissionId(Integer permissionId);

    /**
     * 批量添加角色权限关系
     * 
     * @param rolePermissions 角色权限关系列表
     * @return 影响行数
     */
    int batchInsert(List<RolePermission> rolePermissions);
    
    /**
     * 根据角色ID和权限ID查询角色权限关系
     * 
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 角色权限关系
     */
    RolePermission selectByRoleIdAndPermissionId(@Param("roleId") Integer roleId, @Param("permissionId") Integer permissionId);

    /**
     * 根据权限ID统计关联的角色数量
     *
     * @param permissionId 权限ID
     * @return 关联的角色数量
     */
    int countByPermissionId(Integer permissionId);
} 