package cn.edu.ncu.hrms.salary.service.calculation.attendance;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 扣款责任链上下文
 * 用于在扣款处理链中传递考勤统计数据。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeductionContext {
    private long absentDays; // 缺勤天数
    private long lateTimes;  // 迟到次数
    private long earlyTimes; // 早退次数
    private double leaveDays;  // 请假天数
    private BigDecimal absentDeduction;// 缺勤扣款
    private BigDecimal lateDeduction;  // 迟到扣款
    private BigDecimal earlyDeduction; // 早退扣款
}
