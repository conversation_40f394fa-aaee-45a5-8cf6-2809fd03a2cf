{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n/**\n * 高德地图工具函数\n * Firefox兼容性优化版本\n */\n/* eslint-disable no-undef */\n\n// 检查AMap是否可用\nfunction checkAMapAvailable() {\n  if (typeof window === 'undefined' || !window.AMap) {\n    console.warn('高德地图API未加载或不可用');\n    return false;\n  }\n  return true;\n}\n\n// 初始化地图\nexport function initMap(containerId, options = {}) {\n  if (!checkAMapAvailable()) {\n    throw new Error('高德地图API未加载，请检查网络连接');\n  }\n  const defaultOptions = {\n    zoom: 15,\n    resizeEnable: true\n  };\n  try {\n    return new AMap.Map(containerId, {\n      ...defaultOptions,\n      ...options\n    });\n  } catch (error) {\n    console.error('初始化地图失败:', error);\n    throw error;\n  }\n}\n\n// 创建标记\nexport function createMarker(position, map, options = {}) {\n  if (!checkAMapAvailable()) {\n    console.warn('高德地图API不可用，无法创建标记');\n    return null;\n  }\n\n  // 确保位置是有效的\n  if (!position || typeof position === 'object' && (isNaN(position.lng) || isNaN(position.lat))) {\n    console.error('创建标记失败：无效的位置', position);\n    return null;\n  }\n  const defaultOptions = {\n    position,\n    map,\n    title: '标记点'\n  };\n  try {\n    return new AMap.Marker({\n      ...defaultOptions,\n      ...options\n    });\n  } catch (error) {\n    console.error('创建标记失败：', error);\n    return null;\n  }\n}\n\n// 创建用户位置标记（圆形标记）\nexport function createUserLocationMarker(position, map, options = {}) {\n  const defaultOptions = {\n    position,\n    map,\n    icon: new AMap.Icon({\n      // 使用圆形图标\n      image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',\n      size: new AMap.Size(20, 20),\n      imageSize: new AMap.Size(20, 20)\n    }),\n    offset: new AMap.Pixel(-10, -10),\n    title: '您的位置'\n  };\n  return new AMap.Marker({\n    ...defaultOptions,\n    ...options\n  });\n}\n\n// 创建圆形\nexport function createCircle(center, radius, map, options = {}) {\n  const defaultOptions = {\n    center,\n    radius,\n    strokeColor: '#409EFF',\n    strokeWeight: 2,\n    strokeOpacity: 0.8,\n    fillColor: '#409EFF',\n    fillOpacity: 0.2,\n    map\n  };\n  return new AMap.Circle({\n    ...defaultOptions,\n    ...options\n  });\n}\n\n// 获取当前位置\nexport function getCurrentPosition() {\n  return new Promise((resolve, reject) => {\n    if (!checkAMapAvailable()) {\n      reject(new Error('高德地图API不可用，无法获取位置'));\n      return;\n    }\n    try {\n      const geolocation = new AMap.Geolocation({\n        enableHighAccuracy: true,\n        timeout: 10000,\n        zoomToAccuracy: true\n      });\n      geolocation.getCurrentPosition((status, result) => {\n        if (status === 'complete') {\n          // 定位成功\n          resolve(result);\n        } else {\n          // 定位失败\n          let errorMessage = '获取位置信息失败';\n          switch (result.message) {\n            case 'Geolocation permission denied.':\n              errorMessage = '用户拒绝了位置请求';\n              break;\n            case 'Geolocation position unavailable.':\n              errorMessage = '位置信息不可用';\n              break;\n            case 'Geolocation timeout.':\n              errorMessage = '获取位置超时';\n              break;\n            default:\n              errorMessage = result.message || '发生未知错误';\n              break;\n          }\n          reject(new Error(errorMessage));\n        }\n      });\n    } catch (error) {\n      console.error('创建定位实例失败:', error);\n      reject(new Error('定位功能初始化失败'));\n    }\n  });\n}\n\n// 计算两点之间的距离（米）\nexport function calculateDistance(point1, point2) {\n  const [lng1, lat1] = point1;\n  const [lng2, lat2] = point2;\n  const p1 = new AMap.LngLat(lng1, lat1);\n  const p2 = new AMap.LngLat(lng2, lat2);\n  return p1.distance(p2);\n}\n\n// 创建地理编码实例\nexport function createGeocoder(options = {}) {\n  const defaultOptions = {\n    city: '全国'\n  };\n  return new AMap.Geocoder({\n    ...defaultOptions,\n    ...options\n  });\n}\n\n// 地点搜索 - 使用高德地图Web API\nexport function searchLocation(keywords) {\n  return new Promise((resolve, reject) => {\n    // 高德地图WEB服务API密钥\n    const key = '47d5e708a924649baf363b1e075fe14c';\n\n    // 构建API请求URL\n    const url = `https://restapi.amap.com/v5/place/text?key=${key}&keywords=${encodeURIComponent(keywords)}`;\n\n    // 发起请求\n    fetch(url).then(response => response.json()).then(data => {\n      if (data.status === '1' && data.pois && data.pois.length > 0) {\n        // 获取第一个POI的位置\n        const location = data.pois[0].location;\n        if (location) {\n          // 位置格式为\"经度,纬度\"，需要转换为AMap.LngLat对象\n          const [lng, lat] = location.split(',').map(Number);\n          resolve({\n            lng,\n            lat,\n            name: data.pois[0].name,\n            address: data.pois[0].address || '未知地址'\n          });\n        } else {\n          reject(new Error('无法获取位置信息'));\n        }\n      } else {\n        reject(new Error(data.info || '地点搜索失败，请尝试其他关键词'));\n      }\n    }).catch(error => {\n      console.error('地点搜索请求失败:', error);\n      reject(new Error('地点搜索请求失败，请检查网络连接'));\n    });\n  });\n}\n\n// 地点搜索 - 使用AMap.Geocoder (备用方法)\nexport function searchLocationWithGeocoder(address, geocoder) {\n  return new Promise((resolve, reject) => {\n    geocoder.getLocation(address, (status, result) => {\n      if (status === 'complete' && result.info === 'OK') {\n        resolve(result.geocodes[0].location);\n      } else {\n        reject(new Error('地点搜索失败，请尝试其他关键词'));\n      }\n    });\n  });\n}\n\n// 逆地理编码（坐标转地址）- 使用AMap.Geocoder\nexport function getAddress(position, geocoder) {\n  return new Promise((resolve, reject) => {\n    geocoder.getAddress(position, (status, result) => {\n      if (status === 'complete' && result.info === 'OK') {\n        resolve(result.regeocode.formattedAddress);\n      } else {\n        reject(new Error('获取地址信息失败'));\n      }\n    });\n  });\n}\n\n// 逆地理编码（坐标转地址）- 使用高德地图Web API\nexport function getAddressFromAPI(location) {\n  return new Promise((resolve, reject) => {\n    // 高德地图WEB服务API密钥（与Web端不同）\n    const key = '47d5e708a924649baf363b1e075fe14c';\n\n    // 如果location是数组，转换为逗号分隔的字符串\n    let locationStr = location;\n    if (Array.isArray(location)) {\n      locationStr = location.join(',');\n    }\n\n    // 构建API请求URL\n    const url = `https://restapi.amap.com/v3/geocode/regeo?key=${key}&location=${locationStr}&extensions=base`;\n\n    // 发起请求\n    fetch(url).then(response => response.json()).then(data => {\n      if (data.status === '1') {\n        // 如果有格式化地址，直接使用\n        if (data.regeocode && data.regeocode.formatted_address) {\n          resolve(data.regeocode.formatted_address);\n        }\n        // 否则，从地址组件中构建地址\n        else if (data.regeocode && data.regeocode.addressComponent) {\n          const addressComponent = data.regeocode.addressComponent;\n          let address = '';\n\n          // 从省份开始精确到街道\n          if (addressComponent.province) {\n            address += addressComponent.province;\n          }\n          if (addressComponent.city) {\n            address += addressComponent.city;\n          }\n          if (addressComponent.district) {\n            address += addressComponent.district;\n          }\n          if (addressComponent.township) {\n            address += addressComponent.township;\n          }\n          if (addressComponent.street) {\n            address += addressComponent.street;\n          }\n          if (addressComponent.streetNumber) {\n            address += addressComponent.streetNumber;\n          }\n          resolve(address || '未知地点');\n        } else {\n          reject(new Error('无法解析地址信息'));\n        }\n      } else {\n        reject(new Error(data.info || '获取地址信息失败'));\n      }\n    }).catch(error => {\n      console.error('逆地理编码请求失败:', error);\n      reject(error);\n    });\n  });\n}", "map": {"version": 3, "names": ["checkAMapAvailable", "window", "AMap", "console", "warn", "initMap", "containerId", "options", "Error", "defaultOptions", "zoom", "resizeEnable", "Map", "error", "createMarker", "position", "map", "isNaN", "lng", "lat", "title", "<PERSON><PERSON>", "createUserLocationMarker", "icon", "Icon", "image", "size", "Size", "imageSize", "offset", "Pixel", "createCircle", "center", "radius", "strokeColor", "strokeWeight", "strokeOpacity", "fillColor", "fillOpacity", "Circle", "getCurrentPosition", "Promise", "resolve", "reject", "geolocation", "Geolocation", "enableHighAccuracy", "timeout", "zoomToAccuracy", "status", "result", "errorMessage", "message", "calculateDistance", "point1", "point2", "lng1", "lat1", "lng2", "lat2", "p1", "LngLat", "p2", "distance", "createGeocoder", "city", "Geocoder", "searchLocation", "keywords", "key", "url", "encodeURIComponent", "fetch", "then", "response", "json", "data", "pois", "length", "location", "split", "Number", "name", "address", "info", "catch", "searchLocationWithGeocoder", "geocoder", "getLocation", "geocodes", "get<PERSON><PERSON><PERSON>", "regeocode", "formattedAddress", "getAddressFromAPI", "locationStr", "Array", "isArray", "join", "formatted_address", "addressComponent", "province", "district", "township", "street", "streetNumber"], "sources": ["D:/Desktop/GP/HRMS/client/src/utils/amap.js"], "sourcesContent": ["/**\n * 高德地图工具函数\n * Firefox兼容性优化版本\n */\n/* eslint-disable no-undef */\n\n// 检查AMap是否可用\nfunction checkAMapAvailable() {\n  if (typeof window === 'undefined' || !window.AMap) {\n    console.warn('高德地图API未加载或不可用')\n    return false\n  }\n  return true\n}\n\n// 初始化地图\nexport function initMap(containerId, options = {}) {\n  if (!checkAMapAvailable()) {\n    throw new Error('高德地图API未加载，请检查网络连接')\n  }\n\n  const defaultOptions = {\n    zoom: 15,\n    resizeEnable: true\n  }\n\n  try {\n    return new AMap.Map(containerId, { ...defaultOptions, ...options })\n  } catch (error) {\n    console.error('初始化地图失败:', error)\n    throw error\n  }\n}\n\n// 创建标记\nexport function createMarker(position, map, options = {}) {\n  if (!checkAMapAvailable()) {\n    console.warn('高德地图API不可用，无法创建标记')\n    return null\n  }\n\n  // 确保位置是有效的\n  if (!position || (typeof position === 'object' && (isNaN(position.lng) || isNaN(position.lat)))) {\n    console.error('创建标记失败：无效的位置', position)\n    return null\n  }\n\n  const defaultOptions = {\n    position,\n    map,\n    title: '标记点'\n  }\n\n  try {\n    return new AMap.Marker({ ...defaultOptions, ...options })\n  } catch (error) {\n    console.error('创建标记失败：', error)\n    return null\n  }\n}\n\n// 创建用户位置标记（圆形标记）\nexport function createUserLocationMarker(position, map, options = {}) {\n  const defaultOptions = {\n    position,\n    map,\n    icon: new AMap.Icon({\n      // 使用圆形图标\n      image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',\n      size: new AMap.Size(20, 20),\n      imageSize: new AMap.Size(20, 20)\n    }),\n    offset: new AMap.Pixel(-10, -10),\n    title: '您的位置'\n  }\n\n  return new AMap.Marker({ ...defaultOptions, ...options })\n}\n\n// 创建圆形\nexport function createCircle(center, radius, map, options = {}) {\n  const defaultOptions = {\n    center,\n    radius,\n    strokeColor: '#409EFF',\n    strokeWeight: 2,\n    strokeOpacity: 0.8,\n    fillColor: '#409EFF',\n    fillOpacity: 0.2,\n    map\n  }\n\n  return new AMap.Circle({ ...defaultOptions, ...options })\n}\n\n// 获取当前位置\nexport function getCurrentPosition() {\n  return new Promise((resolve, reject) => {\n    if (!checkAMapAvailable()) {\n      reject(new Error('高德地图API不可用，无法获取位置'))\n      return\n    }\n\n    try {\n      const geolocation = new AMap.Geolocation({\n        enableHighAccuracy: true,\n        timeout: 10000,\n        zoomToAccuracy: true\n      })\n\n      geolocation.getCurrentPosition((status, result) => {\n        if (status === 'complete') {\n          // 定位成功\n          resolve(result)\n        } else {\n          // 定位失败\n          let errorMessage = '获取位置信息失败'\n          switch (result.message) {\n            case 'Geolocation permission denied.':\n              errorMessage = '用户拒绝了位置请求'\n              break\n            case 'Geolocation position unavailable.':\n              errorMessage = '位置信息不可用'\n              break\n            case 'Geolocation timeout.':\n              errorMessage = '获取位置超时'\n              break\n            default:\n              errorMessage = result.message || '发生未知错误'\n              break\n          }\n          reject(new Error(errorMessage))\n        }\n      })\n    } catch (error) {\n      console.error('创建定位实例失败:', error)\n      reject(new Error('定位功能初始化失败'))\n    }\n  })\n}\n\n// 计算两点之间的距离（米）\nexport function calculateDistance(point1, point2) {\n  const [lng1, lat1] = point1\n  const [lng2, lat2] = point2\n\n  const p1 = new AMap.LngLat(lng1, lat1)\n  const p2 = new AMap.LngLat(lng2, lat2)\n\n  return p1.distance(p2)\n}\n\n// 创建地理编码实例\nexport function createGeocoder(options = {}) {\n  const defaultOptions = {\n    city: '全国'\n  }\n\n  return new AMap.Geocoder({ ...defaultOptions, ...options })\n}\n\n// 地点搜索 - 使用高德地图Web API\nexport function searchLocation(keywords) {\n  return new Promise((resolve, reject) => {\n    // 高德地图WEB服务API密钥\n    const key = '47d5e708a924649baf363b1e075fe14c'\n\n    // 构建API请求URL\n    const url = `https://restapi.amap.com/v5/place/text?key=${key}&keywords=${encodeURIComponent(keywords)}`\n\n    // 发起请求\n    fetch(url)\n      .then(response => response.json())\n      .then(data => {\n        if (data.status === '1' && data.pois && data.pois.length > 0) {\n          // 获取第一个POI的位置\n          const location = data.pois[0].location\n          if (location) {\n            // 位置格式为\"经度,纬度\"，需要转换为AMap.LngLat对象\n            const [lng, lat] = location.split(',').map(Number)\n            resolve({\n              lng,\n              lat,\n              name: data.pois[0].name,\n              address: data.pois[0].address || '未知地址'\n            })\n          } else {\n            reject(new Error('无法获取位置信息'))\n          }\n        } else {\n          reject(new Error(data.info || '地点搜索失败，请尝试其他关键词'))\n        }\n      })\n      .catch(error => {\n        console.error('地点搜索请求失败:', error)\n        reject(new Error('地点搜索请求失败，请检查网络连接'))\n      })\n  })\n}\n\n// 地点搜索 - 使用AMap.Geocoder (备用方法)\nexport function searchLocationWithGeocoder(address, geocoder) {\n  return new Promise((resolve, reject) => {\n    geocoder.getLocation(address, (status, result) => {\n      if (status === 'complete' && result.info === 'OK') {\n        resolve(result.geocodes[0].location)\n      } else {\n        reject(new Error('地点搜索失败，请尝试其他关键词'))\n      }\n    })\n  })\n}\n\n// 逆地理编码（坐标转地址）- 使用AMap.Geocoder\nexport function getAddress(position, geocoder) {\n  return new Promise((resolve, reject) => {\n    geocoder.getAddress(position, (status, result) => {\n      if (status === 'complete' && result.info === 'OK') {\n        resolve(result.regeocode.formattedAddress)\n      } else {\n        reject(new Error('获取地址信息失败'))\n      }\n    })\n  })\n}\n\n// 逆地理编码（坐标转地址）- 使用高德地图Web API\nexport function getAddressFromAPI(location) {\n  return new Promise((resolve, reject) => {\n    // 高德地图WEB服务API密钥（与Web端不同）\n    const key = '47d5e708a924649baf363b1e075fe14c'\n\n    // 如果location是数组，转换为逗号分隔的字符串\n    let locationStr = location\n    if (Array.isArray(location)) {\n      locationStr = location.join(',')\n    }\n\n    // 构建API请求URL\n    const url = `https://restapi.amap.com/v3/geocode/regeo?key=${key}&location=${locationStr}&extensions=base`\n\n    // 发起请求\n    fetch(url)\n      .then(response => response.json())\n      .then(data => {\n        if (data.status === '1') {\n          // 如果有格式化地址，直接使用\n          if (data.regeocode && data.regeocode.formatted_address) {\n            resolve(data.regeocode.formatted_address)\n          }\n          // 否则，从地址组件中构建地址\n          else if (data.regeocode && data.regeocode.addressComponent) {\n            const addressComponent = data.regeocode.addressComponent\n            let address = ''\n\n            // 从省份开始精确到街道\n            if (addressComponent.province) {\n              address += addressComponent.province\n            }\n            if (addressComponent.city) {\n              address += addressComponent.city\n            }\n            if (addressComponent.district) {\n              address += addressComponent.district\n            }\n            if (addressComponent.township) {\n              address += addressComponent.township\n            }\n            if (addressComponent.street) {\n              address += addressComponent.street\n            }\n            if (addressComponent.streetNumber) {\n              address += addressComponent.streetNumber\n            }\n\n            resolve(address || '未知地点')\n          } else {\n            reject(new Error('无法解析地址信息'))\n          }\n        } else {\n          reject(new Error(data.info || '获取地址信息失败'))\n        }\n      })\n      .catch(error => {\n        console.error('逆地理编码请求失败:', error)\n        reject(error)\n      })\n  })\n}\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,kBAAkBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;IACjDC,OAAO,CAACC,IAAI,CAAC,gBAAgB,CAAC;IAC9B,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACA,OAAO,SAASC,OAAOA,CAACC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACjD,IAAI,CAACP,kBAAkB,CAAC,CAAC,EAAE;IACzB,MAAM,IAAIQ,KAAK,CAAC,oBAAoB,CAAC;EACvC;EAEA,MAAMC,cAAc,GAAG;IACrBC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE;EAChB,CAAC;EAED,IAAI;IACF,OAAO,IAAIT,IAAI,CAACU,GAAG,CAACN,WAAW,EAAE;MAAE,GAAGG,cAAc;MAAE,GAAGF;IAAQ,CAAC,CAAC;EACrE,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAChC,MAAMA,KAAK;EACb;AACF;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,GAAG,EAAET,OAAO,GAAG,CAAC,CAAC,EAAE;EACxD,IAAI,CAACP,kBAAkB,CAAC,CAAC,EAAE;IACzBG,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;IACjC,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAACW,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ,KAAKE,KAAK,CAACF,QAAQ,CAACG,GAAG,CAAC,IAAID,KAAK,CAACF,QAAQ,CAACI,GAAG,CAAC,CAAE,EAAE;IAC/FhB,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEE,QAAQ,CAAC;IACvC,OAAO,IAAI;EACb;EAEA,MAAMN,cAAc,GAAG;IACrBM,QAAQ;IACRC,GAAG;IACHI,KAAK,EAAE;EACT,CAAC;EAED,IAAI;IACF,OAAO,IAAIlB,IAAI,CAACmB,MAAM,CAAC;MAAE,GAAGZ,cAAc;MAAE,GAAGF;IAAQ,CAAC,CAAC;EAC3D,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,SAASS,wBAAwBA,CAACP,QAAQ,EAAEC,GAAG,EAAET,OAAO,GAAG,CAAC,CAAC,EAAE;EACpE,MAAME,cAAc,GAAG;IACrBM,QAAQ;IACRC,GAAG;IACHO,IAAI,EAAE,IAAIrB,IAAI,CAACsB,IAAI,CAAC;MAClB;MACAC,KAAK,EAAE,yDAAyD;MAChEC,IAAI,EAAE,IAAIxB,IAAI,CAACyB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;MAC3BC,SAAS,EAAE,IAAI1B,IAAI,CAACyB,IAAI,CAAC,EAAE,EAAE,EAAE;IACjC,CAAC,CAAC;IACFE,MAAM,EAAE,IAAI3B,IAAI,CAAC4B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAChCV,KAAK,EAAE;EACT,CAAC;EAED,OAAO,IAAIlB,IAAI,CAACmB,MAAM,CAAC;IAAE,GAAGZ,cAAc;IAAE,GAAGF;EAAQ,CAAC,CAAC;AAC3D;;AAEA;AACA,OAAO,SAASwB,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAEjB,GAAG,EAAET,OAAO,GAAG,CAAC,CAAC,EAAE;EAC9D,MAAME,cAAc,GAAG;IACrBuB,MAAM;IACNC,MAAM;IACNC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,GAAG;IAChBtB;EACF,CAAC;EAED,OAAO,IAAId,IAAI,CAACqC,MAAM,CAAC;IAAE,GAAG9B,cAAc;IAAE,GAAGF;EAAQ,CAAC,CAAC;AAC3D;;AAEA;AACA,OAAO,SAASiC,kBAAkBA,CAAA,EAAG;EACnC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI,CAAC3C,kBAAkB,CAAC,CAAC,EAAE;MACzB2C,MAAM,CAAC,IAAInC,KAAK,CAAC,mBAAmB,CAAC,CAAC;MACtC;IACF;IAEA,IAAI;MACF,MAAMoC,WAAW,GAAG,IAAI1C,IAAI,CAAC2C,WAAW,CAAC;QACvCC,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEFJ,WAAW,CAACJ,kBAAkB,CAAC,CAACS,MAAM,EAAEC,MAAM,KAAK;QACjD,IAAID,MAAM,KAAK,UAAU,EAAE;UACzB;UACAP,OAAO,CAACQ,MAAM,CAAC;QACjB,CAAC,MAAM;UACL;UACA,IAAIC,YAAY,GAAG,UAAU;UAC7B,QAAQD,MAAM,CAACE,OAAO;YACpB,KAAK,gCAAgC;cACnCD,YAAY,GAAG,WAAW;cAC1B;YACF,KAAK,mCAAmC;cACtCA,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,sBAAsB;cACzBA,YAAY,GAAG,QAAQ;cACvB;YACF;cACEA,YAAY,GAAGD,MAAM,CAACE,OAAO,IAAI,QAAQ;cACzC;UACJ;UACAT,MAAM,CAAC,IAAInC,KAAK,CAAC2C,YAAY,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC8B,MAAM,CAAC,IAAInC,KAAK,CAAC,WAAW,CAAC,CAAC;IAChC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS6C,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChD,MAAM,CAACC,IAAI,EAAEC,IAAI,CAAC,GAAGH,MAAM;EAC3B,MAAM,CAACI,IAAI,EAAEC,IAAI,CAAC,GAAGJ,MAAM;EAE3B,MAAMK,EAAE,GAAG,IAAI1D,IAAI,CAAC2D,MAAM,CAACL,IAAI,EAAEC,IAAI,CAAC;EACtC,MAAMK,EAAE,GAAG,IAAI5D,IAAI,CAAC2D,MAAM,CAACH,IAAI,EAAEC,IAAI,CAAC;EAEtC,OAAOC,EAAE,CAACG,QAAQ,CAACD,EAAE,CAAC;AACxB;;AAEA;AACA,OAAO,SAASE,cAAcA,CAACzD,OAAO,GAAG,CAAC,CAAC,EAAE;EAC3C,MAAME,cAAc,GAAG;IACrBwD,IAAI,EAAE;EACR,CAAC;EAED,OAAO,IAAI/D,IAAI,CAACgE,QAAQ,CAAC;IAAE,GAAGzD,cAAc;IAAE,GAAGF;EAAQ,CAAC,CAAC;AAC7D;;AAEA;AACA,OAAO,SAAS4D,cAAcA,CAACC,QAAQ,EAAE;EACvC,OAAO,IAAI3B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC;IACA,MAAM0B,GAAG,GAAG,kCAAkC;;IAE9C;IACA,MAAMC,GAAG,GAAG,8CAA8CD,GAAG,aAAaE,kBAAkB,CAACH,QAAQ,CAAC,EAAE;;IAExG;IACAI,KAAK,CAACF,GAAG,CAAC,CACPG,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAI;MACZ,IAAIA,IAAI,CAAC3B,MAAM,KAAK,GAAG,IAAI2B,IAAI,CAACC,IAAI,IAAID,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC5D;QACA,MAAMC,QAAQ,GAAGH,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACE,QAAQ;QACtC,IAAIA,QAAQ,EAAE;UACZ;UACA,MAAM,CAAC7D,GAAG,EAAEC,GAAG,CAAC,GAAG4D,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAChE,GAAG,CAACiE,MAAM,CAAC;UAClDvC,OAAO,CAAC;YACNxB,GAAG;YACHC,GAAG;YACH+D,IAAI,EAAEN,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACK,IAAI;YACvBC,OAAO,EAAEP,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACM,OAAO,IAAI;UACnC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLxC,MAAM,CAAC,IAAInC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/B;MACF,CAAC,MAAM;QACLmC,MAAM,CAAC,IAAInC,KAAK,CAACoE,IAAI,CAACQ,IAAI,IAAI,iBAAiB,CAAC,CAAC;MACnD;IACF,CAAC,CAAC,CACDC,KAAK,CAACxE,KAAK,IAAI;MACdV,OAAO,CAACU,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC8B,MAAM,CAAC,IAAInC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACvC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS8E,0BAA0BA,CAACH,OAAO,EAAEI,QAAQ,EAAE;EAC5D,OAAO,IAAI9C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC4C,QAAQ,CAACC,WAAW,CAACL,OAAO,EAAE,CAAClC,MAAM,EAAEC,MAAM,KAAK;MAChD,IAAID,MAAM,KAAK,UAAU,IAAIC,MAAM,CAACkC,IAAI,KAAK,IAAI,EAAE;QACjD1C,OAAO,CAACQ,MAAM,CAACuC,QAAQ,CAAC,CAAC,CAAC,CAACV,QAAQ,CAAC;MACtC,CAAC,MAAM;QACLpC,MAAM,CAAC,IAAInC,KAAK,CAAC,iBAAiB,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASkF,UAAUA,CAAC3E,QAAQ,EAAEwE,QAAQ,EAAE;EAC7C,OAAO,IAAI9C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC4C,QAAQ,CAACG,UAAU,CAAC3E,QAAQ,EAAE,CAACkC,MAAM,EAAEC,MAAM,KAAK;MAChD,IAAID,MAAM,KAAK,UAAU,IAAIC,MAAM,CAACkC,IAAI,KAAK,IAAI,EAAE;QACjD1C,OAAO,CAACQ,MAAM,CAACyC,SAAS,CAACC,gBAAgB,CAAC;MAC5C,CAAC,MAAM;QACLjD,MAAM,CAAC,IAAInC,KAAK,CAAC,UAAU,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASqF,iBAAiBA,CAACd,QAAQ,EAAE;EAC1C,OAAO,IAAItC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC;IACA,MAAM0B,GAAG,GAAG,kCAAkC;;IAE9C;IACA,IAAIyB,WAAW,GAAGf,QAAQ;IAC1B,IAAIgB,KAAK,CAACC,OAAO,CAACjB,QAAQ,CAAC,EAAE;MAC3Be,WAAW,GAAGf,QAAQ,CAACkB,IAAI,CAAC,GAAG,CAAC;IAClC;;IAEA;IACA,MAAM3B,GAAG,GAAG,iDAAiDD,GAAG,aAAayB,WAAW,kBAAkB;;IAE1G;IACAtB,KAAK,CAACF,GAAG,CAAC,CACPG,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAI;MACZ,IAAIA,IAAI,CAAC3B,MAAM,KAAK,GAAG,EAAE;QACvB;QACA,IAAI2B,IAAI,CAACe,SAAS,IAAIf,IAAI,CAACe,SAAS,CAACO,iBAAiB,EAAE;UACtDxD,OAAO,CAACkC,IAAI,CAACe,SAAS,CAACO,iBAAiB,CAAC;QAC3C;QACA;QAAA,KACK,IAAItB,IAAI,CAACe,SAAS,IAAIf,IAAI,CAACe,SAAS,CAACQ,gBAAgB,EAAE;UAC1D,MAAMA,gBAAgB,GAAGvB,IAAI,CAACe,SAAS,CAACQ,gBAAgB;UACxD,IAAIhB,OAAO,GAAG,EAAE;;UAEhB;UACA,IAAIgB,gBAAgB,CAACC,QAAQ,EAAE;YAC7BjB,OAAO,IAAIgB,gBAAgB,CAACC,QAAQ;UACtC;UACA,IAAID,gBAAgB,CAAClC,IAAI,EAAE;YACzBkB,OAAO,IAAIgB,gBAAgB,CAAClC,IAAI;UAClC;UACA,IAAIkC,gBAAgB,CAACE,QAAQ,EAAE;YAC7BlB,OAAO,IAAIgB,gBAAgB,CAACE,QAAQ;UACtC;UACA,IAAIF,gBAAgB,CAACG,QAAQ,EAAE;YAC7BnB,OAAO,IAAIgB,gBAAgB,CAACG,QAAQ;UACtC;UACA,IAAIH,gBAAgB,CAACI,MAAM,EAAE;YAC3BpB,OAAO,IAAIgB,gBAAgB,CAACI,MAAM;UACpC;UACA,IAAIJ,gBAAgB,CAACK,YAAY,EAAE;YACjCrB,OAAO,IAAIgB,gBAAgB,CAACK,YAAY;UAC1C;UAEA9D,OAAO,CAACyC,OAAO,IAAI,MAAM,CAAC;QAC5B,CAAC,MAAM;UACLxC,MAAM,CAAC,IAAInC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/B;MACF,CAAC,MAAM;QACLmC,MAAM,CAAC,IAAInC,KAAK,CAACoE,IAAI,CAACQ,IAAI,IAAI,UAAU,CAAC,CAAC;MAC5C;IACF,CAAC,CAAC,CACDC,KAAK,CAACxE,KAAK,IAAI;MACdV,OAAO,CAACU,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC8B,MAAM,CAAC9B,KAAK,CAAC;IACf,CAAC,CAAC;EACN,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}