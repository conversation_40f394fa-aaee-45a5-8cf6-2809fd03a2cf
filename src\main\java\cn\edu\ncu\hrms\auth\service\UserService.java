package cn.edu.ncu.hrms.auth.service;

import cn.edu.ncu.hrms.auth.dto.LoginDTO;
import cn.edu.ncu.hrms.auth.dto.PasswordChangeDTO;
import cn.edu.ncu.hrms.auth.dto.RegisterDTO;
import cn.edu.ncu.hrms.auth.vo.UserInfoVO;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.result.Result;

/**
 * 用户认证服务接口
 * 
 * 负责处理用户登录、注册、登出、密码修改以及获取当前登录用户信息等认证相关操作。
 */
public interface UserService {

    /**
     * 根据用户名获取用户实体信息（主要供内部认证流程使用）
     *
     * @param username 用户名
     * @return User 用户实体，如果不存在则返回 null
     */
    User getUserByUsername(String username);

    /**
     * 处理用户登录请求
     *
     * @param loginDTO 包含用户名和密码的登录数据传输对象
     * @return Result<String> 包含JWT令牌的登录结果
     */
    Result<String> login(LoginDTO loginDTO);

    /**
     * 处理用户登出请求
     *
     * @param username 需要登出的用户名
     * @return Result<Void> 操作结果
     */
    Result<Void> logout(String username);

    /**
     * 处理员工注册请求
     *
     * @param registerDTO 包含注册信息的员工注册数据传输对象
     * @return Result<Void> 操作结果
     */
    Result<Void> register(RegisterDTO registerDTO);

    /**
     * 修改当前登录用户的密码
     *
     * @param username           当前登录的用户名
     * @param passwordChangeDTO 包含旧密码和新密码的数据传输对象
     * @return Result<Void> 操作结果
     */
    Result<Void> changePassword(String username, PasswordChangeDTO passwordChangeDTO);

    /**
     * 获取当前登录用户的详细信息
     *
     * @return Result<UserInfoVO> 包含用户详细信息的操作结果
     */
    Result<UserInfoVO> getCurrentUserInfo();
} 