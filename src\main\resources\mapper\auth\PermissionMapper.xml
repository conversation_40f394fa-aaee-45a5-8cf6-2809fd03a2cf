<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.edu.ncu.hrms.auth.mapper.PermissionMapper">

    <!-- 基础列：用于select语句复用权限表所有字段 -->
    <sql id="Base_Column_List">
        id, permission_key, permission_name, description, create_time, update_time, del_flag
    </sql>

    <!-- 查询指定角色拥有的权限列表 -->
    <select id="selectPermissionsByRoleId" resultType="Permission" parameterType="java.lang.Integer">
        SELECT 
            p.id, p.permission_key, p.create_time, p.update_time, p.del_flag
        FROM 
            permission p
        JOIN 
            role_permission rp ON p.id = rp.permission_id
        WHERE 
            rp.role_id = #{roleId}
            AND p.del_flag = 0
            AND rp.del_flag = 0
        ORDER BY
            p.id ASC
    </select>

    <!-- 根据权限ID查询权限详情 -->
    <select id="selectById" resultType="Permission" parameterType="java.lang.Integer">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            permission
        WHERE 
            id = #{id}
            AND del_flag = 0
    </select>

    <!-- 添加权限 -->
    <insert id="insert" parameterType="Permission" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO permission (
            permission_key, permission_name, description, create_time, update_time, del_flag
        ) VALUES (
            #{permissionKey}, #{permissionName}, #{description}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新权限 -->
    <update id="update" parameterType="cn.edu.ncu.hrms.system.dto.PermissionUpdateDTO">
        UPDATE permission
        <set>
            <if test="permissionKey != null">permission_key = #{permissionKey},</if>
            <if test="permissionName != null">permission_name = #{permissionName},</if>
            <if test="description != null">description = #{description},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除权限 -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE permission
        SET del_flag = null, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询权限列表 -->
    <select id="selectList" resultType="Permission">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            permission
        WHERE 
            del_flag = 0
        ORDER BY 
            id ASC
    </select>
    
    <!-- 根据用户ID查询权限列表（包含数据权限范围） -->
    <select id="selectPermissionsByUserId" resultType="Permission" parameterType="java.lang.Integer">
        SELECT DISTINCT
            p.id, p.permission_key, p.create_time, p.update_time, p.del_flag
        FROM 
            permission p
        JOIN 
            role_permission rp ON p.id = rp.permission_id
        JOIN 
            user_role ur ON rp.role_id = ur.role_id
        WHERE 
            ur.user_id = #{userId}
            AND p.del_flag = 0
            AND rp.del_flag = 0
            AND ur.del_flag = 0
        ORDER BY
            p.id ASC
    </select>

    <!-- 根据权限标识查询未删除的权限 -->
    <select id="selectByPermissionKey" resultType="cn.edu.ncu.hrms.common.entity.Permission" parameterType="java.lang.String">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            permission
        WHERE
            permission_key = #{permissionKey}
            AND del_flag = 0
    </select>

    <!--根据用户 ID 查询该用户拥有的所有权限列表（合并角色权限）-->
    <select id="selectPermKeysByUserId" resultType="java.lang.String">
        SELECT
            p.permission_key
        FROM
            permission p
        JOIN
            role_permission rp ON p.id = rp.permission_id
        JOIN
            user_role ur ON rp.role_id = ur.role_id
        WHERE
            ur.user_id = #{userId}
            AND p.del_flag = 0
            AND rp.del_flag = 0
           AND ur.del_flag = 0
    </select>


    <select id="selectListByCondition" resultType="cn.edu.ncu.hrms.common.entity.Permission" parameterType="cn.edu.ncu.hrms.system.dto.PermissionTreeQueryDTO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            permission
        WHERE
            del_flag = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="permissionName != null">
                AND permission_name LIKE CONCAT('%', #{permissionName}, '%')
            </if>
        ORDER BY
        id ASC
    </select>

    <!--条件查询-->


</mapper> 