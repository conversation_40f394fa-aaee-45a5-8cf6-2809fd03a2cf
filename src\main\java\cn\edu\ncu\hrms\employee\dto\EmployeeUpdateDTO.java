package cn.edu.ncu.hrms.employee.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 员工信息更新DTO
 */
@Data
@ApiModel(description = "员工信息更新参数")
public class EmployeeUpdateDTO {

    @ApiModelProperty(value = "员工ID", required = true, example = "1")
    private Integer id;

    @ApiModelProperty(value = "员工姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "性别", example = "男")
    private String gender;

    // @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证格式不正确")
    @ApiModelProperty(value = "身份证号", example = "110101199001011234")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$",
            message = "身份证号格式错误")
    private String idCard;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "地址", example = "北京市海淀区")
    private String address;

    @ApiModelProperty(value = "部门ID", required = true)
    @NotNull(message = "部门ID不能为空")
    private Integer departmentId;

    @ApiModelProperty(value = "职位ID", required = true)
    @NotNull(message = "职位ID不能为空")
    private Integer positionId;

    @ApiModelProperty(value = "HRBP员工ID")
    @NotNull(message = "HRBP员工ID不能为空")
    private Integer hrbpId;

    @ApiModelProperty(value = "在职状态", allowableValues = "0,1", example = "1")
    private Boolean status;

} 