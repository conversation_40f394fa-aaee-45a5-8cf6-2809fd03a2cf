<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.employee.mapper.EmployeeMapper">

    <!-- 映射员工实体的所有字段 -->
    <resultMap id="BaseResultMap" type="cn.edu.ncu.hrms.common.entity.Employee">
        <id column="id" property="id" />
        <result column="work_no" property="workNo" />
        <result column="name" property="name" />
        <result column="gender" property="gender" />
        <result column="id_card" property="idCard" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="address" property="address" />
        <result column="entry_date" property="entryDate" />
        <result column="department_id" property="departmentId" />
        <result column="position_id" property="positionId" />
        <result column="status" property="status" />
        <result column="hrbp_id" property="hrbpId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 新增：包含部门和职位名称的 ResultMap -->
    <resultMap id="EmployeeWithDetailsResultMap" type="cn.edu.ncu.hrms.system.vo.EmployeeVO" extends="BaseResultMap">
        <!-- 继承 BaseResultMap 的所有字段 -->
        <!-- 映射关联查询出的名称 -->
        <result column="department_name" property="departmentName"/>
        <result column="position_name" property="positionName"/>
        <result column="level" property="level"/>
    </resultMap>

    <!-- 通用字段列，供各查询语句复用 -->
    <sql id="Base_Column_List">
        id, work_no, name, entry_date, gender, id_card, phone, email, address,
        department_id, position_id, status, hrbp_id, create_time, update_time, del_flag
    </sql>

    <!-- 根据员工ID查询员工详情 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE id = #{id}
    </select>
    
    <!-- 根据工号查询员工详情 -->
    <select id="selectByWorkNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE work_no = #{workNo}
    </select>
    
    <!-- 查询所有在职员工列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE status = 1
        ORDER BY id ASC
    </select>
    
    <!-- 查询所有员工（含离职/历史） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        ORDER BY id ASC
    </select>
    
    <!-- 新增员工 -->
    <insert id="insert" parameterType="cn.edu.ncu.hrms.common.entity.Employee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO employee (
            work_no, name, gender, id_card, phone, email, address, entry_date,
            department_id, position_id, status, hrbp_id, create_time, update_time
        )
        VALUES (
            #{workNo}, #{name}, #{gender}, #{idCard}, #{phone}, #{email}, #{address}, #{entryDate},
            #{departmentId}, #{positionId}, #{status}, #{hrbpId}, NOW(), NOW()
        )
    </insert>

    <!-- 更新员工 -->
    <update id="update" parameterType="cn.edu.ncu.hrms.common.entity.Employee">
        UPDATE employee
        <set>
            <if test="workNo != null">work_no = #{workNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="entryDate != null">entry_date = #{entryDate},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="address != null">address = #{address},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hrbpId != null">hrbp_id = #{hrbpId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 更新员工状态（在职/离职） -->
    <update id="updateStatus">
        UPDATE employee SET status = #{status}, update_time = NOW() WHERE id = #{id} AND del_flag = 0
    </update>
    
    <!-- 删除员工 -->
    <delete id="deleteById">
        DELETE FROM employee WHERE id = #{id}
    </delete>
    
    <!-- 批量删除员工 -->
    <delete id="batchDelete">
        DELETE FROM employee
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 查询指定部门下的员工列表 -->
    <select id="selectByDepartmentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE department_id = #{departmentId}
        AND status = 1
        ORDER BY id ASC
    </select>
    
    <!-- 查询指定职位下的员工列表 -->
    <select id="selectByPositionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE position_id = #{positionId}
        AND status = 1
        ORDER BY id ASC
    </select>
    
    <!-- 查询指定HRBP负责的员工列表 -->
    <select id="selectByHrbpId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE hrbp_id = #{hrbpId}
        AND status = 1
        ORDER BY id ASC
    </select>
    
    <!-- 模糊查询员工信息（支持姓名、工号、电话） -->
    <select id="search" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE status = 1
        AND (
            name LIKE CONCAT('%', #{keyword}, '%') OR
            work_no LIKE CONCAT('%', #{keyword}, '%') OR
            phone LIKE CONCAT('%', #{keyword}, '%')
        )
        ORDER BY id ASC
    </select>
    
    <!-- 根据姓名模糊查询员工ID列表 -->
    <select id="selectIdsByName" resultType="java.lang.Integer">
        SELECT id
        FROM employee
        WHERE status = 1
        AND name LIKE CONCAT('%', #{name}, '%')
        ORDER BY id ASC
    </select>

    <!-- 根据姓名模糊查询员工列表 -->
    <select id="selectByNameLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        WHERE status = 1
        AND name LIKE CONCAT('%', #{name}, '%')
        ORDER BY id ASC
    </select>

    <!-- 统计部门员工数量 -->
    <select id="countByDepartmentId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee
        WHERE department_id = #{departmentId}
        AND status = 1
        AND del_flag = 0
    </select>

    <!-- 统计职位员工数量 -->
    <select id="countByPositionId" resultType="int" parameterType="int">
        SELECT COUNT(*)
        FROM employee
        WHERE position_id = #{positionId}
          AND status = 1
    </select>

    <!-- 分页查询员工列表（可按姓名、部门、职位、状态筛选） -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employee
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="departmentId != null">
                AND department_id = #{departmentId}
            </if>
            <if test="position != null and position != ''">
                AND position_id IN (
                    SELECT id FROM position WHERE name LIKE CONCAT('%', #{position}, '%')
                )
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY id ASC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 查询分页条件下的员工总数 -->
    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="departmentId != null">
                AND department_id = #{departmentId}
            </if>
            <if test="position != null and position != ''">
                AND position_id IN (
                    SELECT id FROM position WHERE name LIKE CONCAT('%', #{position}, '%')
                )
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>
    
    <!-- 查询员工的部门领导ID（即直接上级） -->
    <select id="getDepartmentLeaderId" resultType="java.lang.Integer">
        SELECT d.leader_id
        FROM employee e
        JOIN department d ON e.department_id = d.id
        WHERE e.id = #{employeeId}
    </select>


    <select id="selectSuperiorLeaderId" resultType="java.lang.Integer">
        SELECT parent.leader_id
        FROM employee e
        JOIN department dept ON e.department_id = dept.id
        JOIN department parent ON dept.parent_id = parent.id
        WHERE e.id = #{leaderId}
          AND dept.leader_id = #{leaderId}
          AND e.del_flag = 0
          AND dept.del_flag = 0
          AND parent.del_flag = 0
        LIMIT 1
    </select>

    <!-- 查询员工的HRBP ID -->
    <select id="getHRBPId" resultType="java.lang.Integer">
        SELECT hrbp_id FROM employee WHERE id = #{employeeId} AND del_flag = 0
    </select>

    <!-- 根据条件查询员工列表 (配合PageHelper) -->
    <select id="selectByConditions" resultMap="EmployeeWithDetailsResultMap">
        SELECT
        e.id,
        e.work_no,
        e.name,
        e.entry_date,
        e.gender,
        e.id_card,
        e.phone,
        e.email,
        e.address,
        e.department_id,
        e.position_id,
        e.status,
        e.hrbp_id,
        e.create_time,
        e.update_time,
        d.name AS department_name,            <!-- 关联部门名称 -->
        p.name AS position_name,              <!-- 关联职位名称 -->
        p.level AS level                      <!-- 关联职级 -->
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.id
        LEFT JOIN position p ON e.position_id = p.id
        <where>
            e.del_flag = 0
            <if test="name != null and name != ''">
                AND e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="workNo != null and workNo != ''">
                AND e.work_no = #{workNo}
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="positionId != null">
                AND e.position_id = #{positionId}
            </if>
            <if test="positionName != null">
                AND p.name LIKE CONCAT('%', #{positionName}, '%')
            </if>
            <if test="status != null">
                AND e.status = #{status}
            </if>
        </where>
        ORDER BY e.id ASC
    </select>

    <!-- 统计指定年份各个月份的入职人数 -->
    <select id="countMonthlyOnboarding" resultType="map">
        SELECT
            MONTH(entry_date) AS month,
            COUNT(*) AS count
        FROM employee
        WHERE YEAR(entry_date) = #{year}
          AND del_flag = 0
        GROUP BY MONTH(entry_date)
        ORDER BY month ASC
    </select>

    <!--统计在职员工数量-->
    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee
        WHERE status = 1
          AND del_flag = 0
    </select>


    <select id="selectVOList" resultType="cn.edu.ncu.hrms.system.vo.EmployeeVO">
        SELECT
            e.id,
            e.work_no,
            e.name,
            e.entry_date,
            e.gender,
            e.id_card,
            e.phone,
            e.email,
            e.address,
            e.department_id,
            e.position_id,
            e.hrbp_id,
            e.status,
            e.create_time,
            e.update_time,
            d.name AS department_name,
            p.name AS position_name
        FROM
            employee e
                LEFT JOIN
            department d ON e.department_id = d.id
                LEFT JOIN
            position p ON e.position_id = p.id
    </select>


    <select id="selectVOById" resultType="cn.edu.ncu.hrms.system.vo.EmployeeVO">
        SELECT
            e.id,
            e.work_no,
            e.name,
            e.entry_date,
            e.gender,
            e.id_card,
            e.phone,
            e.email,
            e.address,
            e.department_id,
            e.position_id,
            e.hrbp_id,
            e.status,
            e.create_time,
            e.update_time,
            d.name AS department_name,
            p.name AS position_name,
            p.level AS level
        FROM
            employee e
                LEFT JOIN
            department d ON e.department_id = d.id
                LEFT JOIN
            position p ON e.position_id = p.id
        WHERE
            e.id = #{id}
    </select>

    <!--根据身份证号查询员工-->
    <select id="selectByIdCard" resultType="cn.edu.ncu.hrms.common.entity.Employee">
        select * from employee where id_card = #{idCard} and del_flag = 0
    </select>

    <!--根据姓名查询员工-->
    <select id="selectListByName" resultType="cn.edu.ncu.hrms.common.entity.Employee">
        select * from employee where name = #{name}
    </select>

    <!--根据姓名查询最后一个创建的员工-->
    <select id="selectLastByName" resultType="cn.edu.ncu.hrms.common.entity.Employee">
        SELECT * FROM employee
        WHERE name = #{name}
          AND del_flag = 0
        ORDER BY create_time DESC
            LIMIT 1
    </select>

    <!--根据部门ID查询员工-->
    <select id="selectVOByDepartmentId" resultType="cn.edu.ncu.hrms.system.vo.EmployeeVO">
        SELECT
            e.id,
            e.work_no,
            e.name,
            e.entry_date,
            e.gender,
            e.id_card,
            e.phone,
            e.email,
            e.address,
            e.department_id,
            e.position_id,
            e.hrbp_id,
            e.status,
            e.create_time,
            e.update_time,
            d.name AS department_name,
            p.name AS position_name
        FROM
            employee e
                LEFT JOIN
            department d ON e.department_id = d.id
                LEFT JOIN
            position p ON e.position_id = p.id
        WHERE
            d.id = #{departmentId}
    </select>

    <!--查询未注册的员工列表-->
    <select id="selectUnregistered" resultType="cn.edu.ncu.hrms.system.vo.EmployeeVO">
        SELECT
            e.id,
            e.work_no,
            e.name,
            e.entry_date,
            e.gender,
            e.id_card,
            e.phone,
            e.email,
            e.address,
            e.department_id,
            e.position_id,
            e.hrbp_id,
            e.status,
            e.create_time,
            e.update_time,
            d.name AS department_name,
            p.name AS position_name
        FROM
            employee e
                LEFT JOIN
            department d ON e.department_id = d.id
                LEFT JOIN
            position p ON e.position_id = p.id
        WHERE
            e.del_flag = 0
            AND NOT EXISTS (
                SELECT 1
                FROM user
                WHERE del_flag = 0
                    AND user.employee_id = e.id
            )
    </select>

    <!--根据部门ID和角色标识获取员工列表-->
    <select id="selectByDepartmentIdAndRoleKey" resultType="cn.edu.ncu.hrms.common.entity.Employee">
        select e.*
        from employee e
        left join user u
            on u.employee_id = e.id
        left join user_role ur
            on ur.user_id = u.id
        left join role r
            on r.id = ur.role_id
        where e.del_flag = 0
          and u.del_flag = 0
          and r.del_flag = 0
          and ur.del_flag = 0
          and e.status = 1
          and e.department_id = #{departmentId}
          and r.role_key = #{roleKey}
    </select>

    <select id="selectEmployeeListByCondition" resultType="cn.edu.ncu.hrms.common.entity.Employee">
        select e.*
        from employee e
        left join position p
            on e.position_id = p.id
        <where>
            <if test="queryDTO.hrbpId != null">
                and e.hrbp_id = #{queryDTO.hrbpId}
            </if>
            <if test="queryDTO.departmentIds != null and queryDTO.departmentIds.size > 0">
                and e.department_id in
                <foreach item="departmentId" index="index" collection="queryDTO.departmentIds" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
            <if test="queryDTO.departmentId != null">
                and e.department_id = #{queryDTO.departmentId}
            </if>
            <if test="queryDTO.name != null">
                and e.name like concat('%', #{queryDTO.name}, '%')
            </if>
            <if test="queryDTO.workNo != null">
                and e.work_no = #{queryDTO.workNo}
            </if>
            <if test="queryDTO.positionName != null">
                and p.name = #{queryDTO.positionName}
            </if>
            <if test="queryDTO.positionId != null">
                and p.id = #{queryDTO.positionId}
            </if>
            <if test="queryDTO.status != null">
                and e.status = #{queryDTO.status}
            </if>
            <if test="queryDTO.entryStartDate != null">
                AND e.entry_date &gt;= #{queryDTO.entryStartDate}
            </if>
            <if test="queryDTO.entryEndDate != null">
                AND e.entry_date &lt;= #{queryDTO.entryEndDate}
            </if>
        </where>
    </select>

    <!--批量插入-->
    <insert id="batchInsert">
        INSERT INTO employee (
        work_no, name, entry_date, gender, id_card, phone, email, address, department_id, position_id, hrbp_id, status, create_time, update_time, del_flag
        ) VALUES
        <foreach collection="list" item="employee" separator=",">
            (
            #{employee.workNo},
            #{employee.name},
            #{employee.entryDate},
            #{employee.gender},
            #{employee.idCard},
            #{employee.phone},
            #{employee.email},
            #{employee.address},
            #{employee.departmentId},
            #{employee.positionId},
            #{employee.hrbpId},
            #{employee.status},
            NOW(),
            NOW(),
            0
            )
        </foreach>
    </insert>


    <select id="selectMaxWorkNoByPinyin" resultType="java.lang.String">
        SELECT work_no
        FROM employee
        WHERE work_no LIKE CONCAT(#{namePinyin}, '%')  -- 匹配以拼音开头的工号
            AND SUBSTRING(work_no, #{suffixStartPos}) REGEXP '^[0-9]+$'  -- 确保后缀是纯数字
        ORDER BY
            CAST(SUBSTRING(work_no, #{suffixStartPos}) AS SIGNED) DESC
        LIMIT 1
        FOR UPDATE
    </select>

    <select id="isLeader" resultType="java.lang.Boolean">
        SELECT
        FROM employee e
        JOIN department d ON e.department_id = d.id
        WHERE e.id = #{employeeId}
        AND d.leader_id = #{employeeId}
    </select>

</mapper> 