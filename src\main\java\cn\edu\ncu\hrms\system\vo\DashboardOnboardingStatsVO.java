package cn.edu.ncu.hrms.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel(description = "管理员仪表盘 - 年度入职统计视图对象")
public class DashboardOnboardingStatsVO {

    @ApiModelProperty("按月份统计的入职人数列表")
    private List<MonthlyCount> monthlyCounts;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "月度入职人数")
    public static class MonthlyCount {
        @ApiModelProperty("月份 (1-12)")
        private Integer month;

        @ApiModelProperty("该月入职人数")
        private Long count;
    }

    // @ApiModelProperty("年度总入职人数")
    // private Long totalCount;
} 