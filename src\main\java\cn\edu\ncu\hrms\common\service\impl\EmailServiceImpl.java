package cn.edu.ncu.hrms.common.service.impl;

import cn.edu.ncu.hrms.common.config.EmailConfig;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.service.EmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件服务实现类
 */
@Service
public class EmailServiceImpl implements EmailService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private EmailConfig emailConfig;

    @Autowired
    private TemplateEngine templateEngine;

    /**
     * 发送简单文本邮件
     * 
     * @param to      收件人邮箱地址
     * @param subject 邮件主题
     * @param content 邮件正文内容
     * @return 返回操作结果，包含成功或失败的信息
     */
    @Override
    public Result sendSimpleMail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(emailConfig.getFrom());
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            
            mailSender.send(message);
            logger.info("简单邮件已发送至: {}", to);
            return Result.success("邮件发送成功");
        } catch (Exception e) {
            logger.error("发送简单邮件时发生异常: ", e);
            return Result.error("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送HTML格式邮件
     * 
     * @param to      收件人邮箱地址
     * @param subject 邮件主题
     * @param content HTML格式的邮件正文内容
     * @return 返回操作结果，包含成功或失败的信息
     */
    @Override
    public Result sendHtmlMail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            helper.setFrom(emailConfig.getFrom());
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            
            mailSender.send(message);
            logger.info("HTML邮件已发送至: {}", to);
            return Result.success("邮件发送成功");
        } catch (MessagingException e) {
            logger.error("发送HTML邮件时发生异常: ", e);
            return Result.error("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送奖惩申请邮件
     * 
     * @param to            收件人邮箱地址
     * @param employeeName  员工姓名
     * @param type          奖惩类型（0表示奖励，非0表示惩罚）
     * @param title         奖惩申请标题
     * @param content       奖惩申请内容
     * @return 返回操作结果，包含成功或失败的信息
     */
    @Override
    public Result sendRewardPunishmentApplicationMail(String to, String employeeName, Integer type, String title, String content) {
        try {
            Context context = new Context();
            Map<String, Object> variables = new HashMap<>();
            variables.put("employeeName", employeeName);
            variables.put("type", type == 0 ? "奖励" : "惩罚");
            variables.put("title", title);
            variables.put("content", content);
            variables.put("status", "已提交，等待审批");
            context.setVariables(variables);
            
            String emailContent = templateEngine.process("reward-punishment-application", context);
            
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            helper.setFrom(emailConfig.getFrom());
            helper.setTo(to);
            helper.setSubject("奖惩申请已提交 - " + title);
            helper.setText(emailContent, true);
            
            mailSender.send(message);
            logger.info("奖惩申请邮件已发送至: {}", to);
            return Result.success("邮件发送成功");
        } catch (Exception e) {
            logger.error("发送奖惩申请邮件时发生异常: ", e);
            return Result.error("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送奖惩审批结果邮件
     * 
     * @param to            收件人邮箱地址
     * @param employeeName  员工姓名
     * @param type          奖惩类型（0表示奖励，非0表示惩罚）
     * @param title         奖惩申请标题
     * @param approveStatus 审批状态（1表示通过，其他值表示驳回）
     * @param remark        审批备注信息（可选）
     * @return 返回操作结果，包含成功或失败的信息
     */
    @Override
    public Result sendRewardPunishmentApproveMail(String to, String employeeName, Integer type, String title, Integer approveStatus, String remark) {
        try {
            Context context = new Context();
            Map<String, Object> variables = new HashMap<>();
            variables.put("employeeName", employeeName);
            variables.put("type", type == 0 ? "奖励" : "惩罚");
            variables.put("title", title);
            variables.put("status", approveStatus == 1 ? "已通过" : "已驳回");
            variables.put("approveStatus", approveStatus);
            variables.put("remark", remark != null ? remark : "");
            context.setVariables(variables);
            
            String emailContent = templateEngine.process("reward-punishment-approve", context);
            
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            helper.setFrom(emailConfig.getFrom());
            helper.setTo(to);
            String subject = approveStatus == 1 ? "奖惩申请已通过 - " : "奖惩申请已驳回 - ";
            helper.setSubject(subject + title);
            helper.setText(emailContent, true);
            
            mailSender.send(message);
            logger.info("奖惩审批结果邮件已发送至: {}", to);
            return Result.success("邮件发送成功");
        } catch (Exception e) {
            logger.error("发送奖惩审批结果邮件时发生异常: ", e);
            return Result.error("邮件发送失败：" + e.getMessage());
        }
    }
} 