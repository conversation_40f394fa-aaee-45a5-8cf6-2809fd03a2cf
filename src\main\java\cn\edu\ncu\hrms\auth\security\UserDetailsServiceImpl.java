package cn.edu.ncu.hrms.auth.security;

import cn.edu.ncu.hrms.auth.mapper.PermissionMapper;
import cn.edu.ncu.hrms.auth.mapper.RoleMapper;
import cn.edu.ncu.hrms.common.constant.RoleConstant;
import cn.edu.ncu.hrms.common.entity.Role;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import cn.edu.ncu.hrms.auth.service.UserService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义用户详情服务实现类
 */
@Service
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    @Lazy
    private UserService userService;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 1. 根据用户名查询用户信息
        User user = userService.getUserByUsername(username);
        if (user == null) {
            log.warn("登录/加载用户信息失败：用户不存在或已被停用，用户名: {}", username);
            throw new BusinessException("用户不存在或已被停用");
        }

        // 2. 根据用户ID查询用户角色列表和权限列表
        List<String> authorities = new ArrayList<>();
        if (user.getId() != null) {
            List<String> roleKeys = roleMapper.selectRolesByUserId(user.getId()).stream()
                    .map(Role::getRoleKey).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> permissionKeys = permissionMapper.selectPermKeysByUserId(user.getId());

            log.info("用户 {} 信息加载成功, 角色:{} 权限数量: {}", username, roleKeys, permissionKeys.size());

            // 为避免空权限问题，可以添加基础权限
            if (roleKeys == null || roleKeys.isEmpty()) {
                log.warn("用户 {} 没有任何角色，将添加基础角色", username);
                if (user.getIsAdmin()) {
                    roleKeys.add(RoleConstant.ROLE_ADMIN);
                } else {
                    roleKeys.add(RoleConstant.ROLE_EMPLOYEE);
                }
            }
            if (permissionKeys == null || permissionKeys.isEmpty()){
                log.warn("用户 {} 没有任何权限，将添加基础权限", username);
                if (user.getIsAdmin()) {
                    permissionKeys.add(RoleConstant.ADMIN_PERMISSION_KEY);
                } else {
                    permissionKeys.add(RoleConstant.EMPLOYEE_PERMISSION_KEY);
                }
            }
            authorities.addAll(roleKeys);
            authorities.addAll(permissionKeys);
        }

        // 3. 封装成 Spring Security 所需的 UserDetails 对象
        return new LoginUser(user, authorities);
    }
} 