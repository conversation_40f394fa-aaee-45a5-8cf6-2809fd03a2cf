package cn.edu.ncu.hrms.system.service.impl;

import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.service.EmployeeService;
import cn.edu.ncu.hrms.system.dto.EmployeePageQueryDTO;
import cn.edu.ncu.hrms.system.dto.EmployeeUpdateDTO;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import cn.edu.ncu.hrms.system.service.SystemEmployeeService;
import cn.edu.ncu.hrms.system.service.SystemUserService;
import cn.edu.ncu.hrms.system.vo.DashboardDistributionStatsVO;
import cn.edu.ncu.hrms.system.vo.DashboardOnboardingStatsVO;
import cn.edu.ncu.hrms.system.vo.EmployeeVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 系统员工管理服务实现类
 */
@Service
@Slf4j
public class SystemEmployeeServiceImpl implements SystemEmployeeService {

    private static final Logger logger = LoggerFactory.getLogger(SystemEmployeeServiceImpl.class);

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private EmployeeService employeeService;


    @Override
    public Result<List<EmployeeVO>> listAllEmployees() {
        logger.debug("获取所有员工列表");
        List<EmployeeVO> voList = employeeMapper.selectVOList();
        return Result.success(voList);
    }

    @Override
    public PageInfo<EmployeeVO> pageEmployees(EmployeePageQueryDTO queryDTO) {
        logger.info("分页查询员工, 条件: {}", queryDTO);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<EmployeeVO> voList = employeeMapper.selectByConditions(
                queryDTO.getName(), queryDTO.getWorkNo(), queryDTO.getDepartmentId(),
                queryDTO.getPositionId(), queryDTO.getPositionName(), queryDTO.getStatus()
        );

        if (voList != null && !voList.isEmpty()) {
            // 设置用户信息
            voList.forEach(this::setUserInfo);
            // 设置部门及上级部门名称
            voList.forEach(this::setDepartmentNames);
        }

        PageInfo<EmployeeVO> pageInfo = new PageInfo<>(voList);

        return pageInfo;
    }


    private EmployeeVO setUserInfo(EmployeeVO vo) {
        if (vo == null || vo.getId() == null) {
            return null;
        }
        Integer employeeId = vo.getId();
        User user = systemUserService.findUserByEmployeeId(employeeId);
        if (user != null) {
            vo.setUserId(user.getId());
            vo.setUsername(user.getUsername());
            vo.setIsAdmin(user.getIsAdmin());
        }
        return vo;
    }

    private EmployeeVO setDepartmentNames(EmployeeVO vo) {
        if (vo == null || vo.getId() == null) {
            return null;
        }
        if (vo.getDepartmentId() != null) {
            List<String> names = employeeService.getDepartmentAndParents(vo.getDepartmentId());
            if (names != null && !names.isEmpty()) {
                String departmentNames = "";
                for (int i = names.size() - 1; i > 0; i--) {
                    departmentNames  += names.get(i) + "/";
                }
                departmentNames += names.get(0);
                vo.setDepartmentNames(departmentNames);
            }
        }

        return vo;
    }

    @Override
    public Result<EmployeeVO> getEmployeeVOById(Integer id) {
        logger.debug("根据ID获取员工VO信息, ID: {}", id);
        if (id == null) {
            return Result.error("员工ID不能为空");
        }
        // 使用 Mapper 方法直接获取VO
        EmployeeVO vo = employeeMapper.selectVOById(id);
        if (vo != null) {
            return Result.success(vo);
        } else {
            logger.warn("未找到员工信息, ID: {}", id);
            return Result.error("员工不存在");
        }
    }

    @Override
    @Transactional
    public Result<Void> updateEmployee(EmployeeUpdateDTO updateDTO) {
        log.info("更新员工信息, ID: {}", updateDTO.getId());
        Integer id = updateDTO.getId();
        if (id == null) {
            return Result.error("更新失败：员工ID不能为空");
        }
        Employee existingEmployee = employeeMapper.selectById(id);
        if (existingEmployee == null || existingEmployee.getDelFlag()) { // 同时检查是否已逻辑删除
            log.warn("尝试更新不存在或已删除的员工, ID: {}", id);
            return Result.error("更新失败：员工不存在或已被删除");
        }
        String newIdCard = updateDTO.getIdCard();
        if (newIdCard != null && !newIdCard.equals(existingEmployee.getIdCard()) &&  employeeMapper.selectByIdCard(newIdCard) != null) {
            log.warn("身份证号重复, ID: {}", newIdCard);
            return Result.error("身份证号重复");
        }

        Employee employeeToUpdate = new Employee();
        BeanUtils.copyProperties(updateDTO, employeeToUpdate);
        employeeToUpdate.setUpdateTime(LocalDateTime.now());
        employeeToUpdate.setStatus(existingEmployee.getStatus()); // 保持原有状态
        employeeToUpdate.setDelFlag(existingEmployee.getDelFlag()); // 保持原有删除标记

        // 使用 mapper 更新
        int rows = employeeMapper.update(employeeToUpdate);
        if (rows > 0) {
            log.info("员工信息更新成功, ID: {}", id);
            return Result.success(null, "员工信息更新成功");
        } else {
            log.warn("更新员工信息数据库操作未影响行数, ID: {}. 可能数据未改变?", id);
            // 再次检查是否存在，以防并发删除
            Employee current = employeeMapper.selectById(id);
            if (current == null || current.getDelFlag()) {
                return Result.error("更新失败：员工已被删除");
            }
            return Result.success("系统异常，请稍后再试");
        }
    }


    @Override
    @Transactional
    public Result<Void> updateEmployeeStatus(Integer id, Boolean status) {
        logger.info("更新员工实体状态及关联用户状态, 员工ID: {}, 新状态: {}", id, status ? "在职" : "离职");
        if (id == null || status == null) {
            return Result.error("员工ID和状态不能为空");
        }
        Employee existingEmployee = employeeMapper.selectById(id);
        if (existingEmployee == null || existingEmployee.getDelFlag()) {
            logger.warn("尝试更新不存在或已删除的员工状态, ID: {}", id);
            return Result.error("员工不存在或已被删除");
        }

        // 1. 更新员工实体本身的状态
        Employee employeeToUpdate = new Employee();
        employeeToUpdate.setId(id);
        employeeToUpdate.setStatus(status);
        employeeToUpdate.setUpdateTime(LocalDateTime.now());
        int empUpdateRows = employeeMapper.update(employeeToUpdate);

        if (empUpdateRows <= 0) {
            logger.warn("更新员工 {} 状态为 {} 时数据库未影响行数", id, status);
            // 可能状态未改变，但仍需继续处理用户状态
            // 检查员工是否还存在
            Employee current = employeeMapper.selectById(id);
            if (current == null || current.getDelFlag()) {
                return Result.error("更新员工状态失败：员工已被删除");
            }
            // 如果状态确实没变，也继续尝试更新用户状态
        }
        logger.info("员工 {} 实体状态更新完成 (影响行数: {})", id, empUpdateRows);

        // 2. 删除关联的用户
        User associatedUser = systemUserService.findUserByEmployeeId(id);
        if (associatedUser != null) {
            Result result = systemUserService.deleteUser(associatedUser.getId());
            if (result.getCode() == Result.ERROR_CODE) {
                logger.error("员工 {} 关联的用户 {} 删除失败，错误信息: {}", id, associatedUser.getId(), result.getMsg());
                return Result.error("系统异常，请稍后再试");
            } else {
                logger.info("员工 {} 关联的用户 {} 已成功删除", id, associatedUser.getId());
                return Result.success("员工状态更新成功");
            }
        } else {
            logger.warn("未找到员工 {} 关联的用户账号，仅更新员工实体状态", id);
            if (empUpdateRows > 0) {
                return Result.success("员工状态更新成功（无关联用户）");
            } else {
                return Result.success("系统异常，请稍后再试");
            }
        }
    }


    @Override
    public Result<List<EmployeeVO>> getEmployeesByDepartmentId(Integer id) {
        logger.debug("获取部门员工列表");
        List<EmployeeVO> voList = employeeMapper.selectVOByDepartmentId(id);
        return Result.success(voList);
    }


    @Override
    public Result<DashboardOnboardingStatsVO> getDashboardOnboardingStats() {
        int currentYear = LocalDate.now().getYear();
        log.info("获取管理员仪表盘年度入职统计，年份: {}", currentYear);

        try {
            List<Map<String, Object>> monthlyData = employeeMapper.countMonthlyOnboarding(currentYear);

            // 将数据库结果转换为 Map<Integer, Long> (Month -> Count)
            Map<Integer, Long> countsMap = monthlyData.stream()
                    .collect(Collectors.toMap(
                            map -> ((Number) map.get("month")).intValue(),
                            map -> ((Number) map.get("count")).longValue()
                    ));

            // 构建包含12个月份的结果列表，缺失月份补0
            List<DashboardOnboardingStatsVO.MonthlyCount> monthlyCounts = IntStream.rangeClosed(1, 12)
                    .mapToObj(month -> new DashboardOnboardingStatsVO.MonthlyCount(
                            month,
                            countsMap.getOrDefault(month, 0L)
                    ))
                    .collect(Collectors.toList());

            DashboardOnboardingStatsVO vo = new DashboardOnboardingStatsVO();
            vo.setMonthlyCounts(monthlyCounts);

            return Result.success(vo);

        } catch (Exception e) {
            log.error("获取年度入职统计失败，年份: {}, 错误: ", currentYear, e);
            return Result.error("获取入职统计失败");
        }
    }

    @Override
    public Result<List<DashboardDistributionStatsVO>> getDashboardDistributionStats() {
        log.info("获取管理员仪表盘员工统计");
        List<Department> allDepartments = departmentMapper.selectList();
        List<DashboardDistributionStatsVO> VOs = allDepartments.stream().map(this::convertToDashboardDistributionStatsVO).collect(Collectors.toList());
        // 构建部门层级关系
        buildTree(VOs);
        // 递归统计员工数量
        for (DashboardDistributionStatsVO vo : VOs) {
            // 顶级部门才需要递归统计员工数量
            if (vo.getParentId() == null) {
                countEmployeesRecursively(vo);
            }

        }

        return Result.success(VOs);
    }

    private DashboardDistributionStatsVO convertToDashboardDistributionStatsVO(Department department) {
        if (department == null) {
            return null;
        }

        DashboardDistributionStatsVO vo = new DashboardDistributionStatsVO();
        vo.setDepartmentId(department.getId());
        vo.setDepartmentName(department.getName());
        vo.setParentId(department.getParentId());

        return vo;
    }

    private void buildTree(List<DashboardDistributionStatsVO> VOs) {
        Map<Integer,DashboardDistributionStatsVO> map = new HashMap<>();
        for (DashboardDistributionStatsVO vo : VOs) {
            if (vo != null) {
                map.put(vo.getDepartmentId(),vo);
            }
        }

        for (DashboardDistributionStatsVO vo : VOs) {
            if (vo != null && vo.getParentId() != null) {
                DashboardDistributionStatsVO parent = map.get(vo.getParentId());
                if (parent != null) {
                    parent.getChildren().add(vo);
                }
            }
        }

    }

    // 递归统计指定部门及其子部门员工数量的
    private void countEmployeesRecursively(DashboardDistributionStatsVO vo) {
        // 根据当前部门的 ID，从数据库中统计该部门的员工数量
        int count = employeeMapper.countByDepartmentId(vo.getDepartmentId());
        // 设置当前部门的员工数量
        vo.setEmployeeCount(count);
        // 遍历当前部门的子部门列表
        for (DashboardDistributionStatsVO child : vo.getChildren()) {
            // 递归调用自身，统计子部门的员工数量
            countEmployeesRecursively(child);
            // 将子部门的员工数量累加到当前部门的员工数量上
            vo.setEmployeeCount(vo.getEmployeeCount() + child.getEmployeeCount());
        }
    }

    @Override
    public Result<List<EmployeeVO>> getUnregisteredEmployeeList() {
        List<EmployeeVO> unregisteredEmployeeList = employeeMapper.selectUnregistered();
        return Result.success(unregisteredEmployeeList);
    }

    @Override
    public Result<List<Employee>> getEmployeesByDepartmentIdAndRole(Integer departmentId, String roleKey) {
        List<Employee> employees = employeeMapper.selectByDepartmentIdAndRoleKey(departmentId, roleKey);
        return Result.success(employees);
    }
}