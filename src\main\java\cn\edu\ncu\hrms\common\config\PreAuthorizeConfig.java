package cn.edu.ncu.hrms.common.config;

import cn.edu.ncu.hrms.auth.security.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.edu.ncu.hrms.common.constant.RoleConstant.ADMIN_PERMISSION_KEY;

@Slf4j
@Component("hrms")
public class PreAuthorizeConfig {
    

    public boolean hasAuthority(String authority){

        // 获取当前用户拥有的权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        List<String> permissions = loginUser.getPermissions();

        String[] parts = authority.split(":");
        // 一级权限
        String first = parts[0];
        // 二级权限
        String second = parts[0] + ":" + parts[1];
        
        // 拥有更高级的权限（管理员权限、一级权限、二级权限）或指定权限（三级权限）则校验通过
        if (permissions.contains(ADMIN_PERMISSION_KEY) || permissions.contains(first) || permissions.contains(second) || permissions.contains(authority)) {
            return true;
        }
        
        return false;
        
    }
}
