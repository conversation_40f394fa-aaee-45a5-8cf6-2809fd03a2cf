package cn.edu.ncu.hrms.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 注册参数DTO
 */
@Data
@ApiModel(description = "注册参数")
public class RegisterDTO {

    @NotBlank(message = "用户名不能为空")
    @ApiModelProperty(value = "用户名", required = true, example = "zhangsan")
    private String username;

    @NotBlank(message = "密码不能为空")
    @ApiModelProperty(value = "密码", required = true, example = "123456")
    private String password;

    @NotBlank(message = "员工工号不能为空")
    @ApiModelProperty(value = "员工工号", required = true, example = "EMP001")
    private String employeeId;
} 