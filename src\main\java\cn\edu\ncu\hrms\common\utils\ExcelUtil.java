package cn.edu.ncu.hrms.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Excel工具类
 */
@Slf4j
public class ExcelUtil {

    /**
     * 读取Excel文件
     *
     * @param file Excel文件
     * @param clazz 实体类
     * @param <T> 泛型
     * @return 实体列表
     * @throws IOException IO异常
     */
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clazz) throws IOException {
        return EasyExcel.read(file.getInputStream())
                .head(clazz)
                .sheet()
                .doReadSync();
    }

    /**
     * 读取Excel文件
     *
     * @param file
     * @param clazz
     * @param <T>
     * @return
     * @throws IOException
     */
    public static <T> List<T> readExcel(File file, Class<T> clazz) throws IOException {
        return EasyExcel.read(file)
                .head(clazz)
                .sheet()
                .doReadSync();
    }

    /**
     * 导出Excel到输出流
     *
     * @param outputStream 输出流 (例如 HttpServletResponse.getOutputStream())
     * @param data 数据列表
     * @param clazz 实体类
     * @param <T> 泛型
     * @throws IOException IO异常
     */
    public static <T> void writeExcel(OutputStream outputStream, List<T> data, Class<T> clazz) throws IOException {
        // 默认 sheetName 为 "Sheet1"，如果需要自定义可以添加参数
        writeExcel(outputStream, data, "Sheet1", clazz);
    }

    /**
     * 导出Excel到输出流 (带 Sheet 名称)
     *
     * @param outputStream 输出流
     * @param data 数据列表
     * @param sheetName 表格名称
     * @param clazz 实体类
     * @param <T> 泛型
     * @throws IOException IO异常
     */
    public static <T> void writeExcel(OutputStream outputStream, List<T> data, String sheetName, Class<T> clazz) throws IOException {
        ExcelWriter excelWriter = null;
        try {
            // 这里 指定使用哪个class写excel
            excelWriter = EasyExcel.write(outputStream, clazz).build();
            // 这里注意 如果同一个sheet只要创建一次
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
            // 去调用写入,这里传入 ১৯৭sheet会循环处理
            excelWriter.write(data, writeSheet);
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
            // EasyExcel 会自动关闭传入的 OutputStream，无需手动关闭
        }
    }



    /**
     * 基于模板导出Excel（通用方法）
     * @param dataList 数据列表
     * @param templatePath 模板路径（classpath相对路径）
     * @param fieldMapping 字段映射关系（key: 模板占位符, value: 数据字段名/自定义值）
     * @return 导出的字节数组
     */
    public static <T> byte[] exportWithTemplate(List<T> dataList,
                                                String templatePath,
                                                Map<String, Function<T, Object>> fieldMapping) {
        try (InputStream templateStream = ExcelUtil.class.getResourceAsStream(templatePath);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            if (templateStream == null) {
                throw new FileNotFoundException("模板文件未找到: " + templatePath);
            }

            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .withTemplate(templateStream)
                    .build();

            // 跳过表头行（第0行），从占位符行（第1行）开始填充
            WriteSheet writeSheet = EasyExcel.writerSheet()
                    .relativeHeadRowIndex(1)
                    .build();


            // 填充数据
            List<Map<String, Object>> mappedData = dataList.stream()
                    .map(data -> buildRowData(data, fieldMapping))
                    .collect(Collectors.toList());

            System.out.println("Mapped Data: " + mappedData);

            excelWriter.fill(mappedData, writeSheet);
            excelWriter.finish();

            return outputStream.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Excel导出失败", e);
        }
    }

    /**
     * 构建单行数据
     */
    private static <T> Map<String, Object> buildRowData(T data,
                                                        Map<String, Function<T, Object>> fieldMapping) {
        Map<String, Object> rowData = new LinkedHashMap<>();
        fieldMapping.forEach((placeholder, func) -> {
            Object value = func.apply(data);
            // 特殊类型处理
            if (value instanceof LocalDate) {
                rowData.put(placeholder, ((LocalDate) value).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
            } else if (value instanceof Boolean) {
                // rowData.put(placeholder, ((Boolean) value) ? "是" : "否");
            } else {
                rowData.put(placeholder, value);
            }
        });
        return rowData;
    }


} 