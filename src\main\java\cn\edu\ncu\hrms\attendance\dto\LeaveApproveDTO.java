package cn.edu.ncu.hrms.attendance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
@ApiModel(description = "审批请假申请数据传输对象")
public class LeaveApproveDTO {

    @NotBlank(message = "审批操作不能为空")
    @Pattern(regexp = "^(APPROVED|REJECTED)$", message = "审批状态必须是 APPROVED 或 REJECTED") // 使用常量或枚举更好
    @ApiModelProperty(value = "审批操作", required = true, example = "APPROVED")
    private String status;

    @ApiModelProperty(value = "审批意见")
    @Size(max = 500, message = "审批意见长度不能超过500个字符")
    private String comment;
} 