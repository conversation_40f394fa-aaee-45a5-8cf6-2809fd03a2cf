<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.employee.mapper.RewardPunishmentMapper">

    <!-- 通用字段列，供各查询语句复用 -->
    <sql id="Base_Column_List">
        id, employee_id, operator_id, type, title, content, amount, reward_date, remark, create_time, update_time
    </sql>

    <!-- 根据奖惩ID查询详情 -->
    <select id="selectById" resultType="cn.edu.ncu.hrms.common.entity.RewardPunishment">
        SELECT
        <include refid="Base_Column_List" />
        FROM reward_punishment
        WHERE id = #{id}
            AND del_flag = 0
    </select>

    <!-- 查询所有奖惩记录列表 -->
    <select id="selectList" resultType="cn.edu.ncu.hrms.common.entity.RewardPunishment">
        SELECT
        <include refid="Base_Column_List" />
        FROM reward_punishment
        WHERE del_flag = 0
        ORDER BY reward_date DESC, create_time DESC
    </select>

    <!-- 插入奖惩记录 -->
    <insert id="insert" parameterType="cn.edu.ncu.hrms.employee.dto.RewardPunishmentAddDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO reward_punishment (
            employee_id, operator_id, type, title, content, amount, reward_date, remark, create_time, update_time, del_flag
        )
        VALUES (
            #{employeeId}, #{operatorId}, #{type}, #{title}, #{content}, #{amount}, #{rewardDate}, #{remark}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新奖惩记录信息 -->
    <update id="update" parameterType="cn.edu.ncu.hrms.common.entity.RewardPunishment">
        UPDATE reward_punishment
        <set>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="rewardDate != null">reward_date = #{rewardDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
        AND del_flag = 0
    </update>

    <!-- 删除记录 -->
    <delete id="deleteById">
        UPDATE reward_punishment
        SET del_flag = 1
        WHERE id = #{id}
    </delete>

    <!-- 批量删除记录 -->
    <delete id="batchDelete">
        UPDATE reward_punishment
        SET del_flag = 1
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据员工ID查询 -->
    <select id="selectByEmployeeId" resultType="cn.edu.ncu.hrms.common.entity.RewardPunishment">
        SELECT
        <include refid="Base_Column_List" />
        FROM reward_punishment
        WHERE employee_id = #{employeeId}
            AND del_flag = 0
        ORDER BY reward_date DESC, create_time DESC
    </select>

    <!-- 获取奖惩类型统计数据 -->
    <select id="selectTypeStatistics" resultType="java.util.Map">
        SELECT
            type,
            COUNT(*) as count,
            SUM(amount) as totalAmount
        FROM reward_punishment
        GROUP BY type
    </select>

    <!-- 获取指定日期范围内的奖惩金额统计 -->
    <select id="selectAmountStatistics" resultType="java.util.Map">
        SELECT
            type,
            SUM(amount) as totalAmount,
            COUNT(*) as count
        FROM reward_punishment
        WHERE reward_date BETWEEN #{startDate} AND #{endDate}
            AND del_flag = 0
        GROUP BY type
    </select>

    <!-- 获取员工奖惩排行榜 -->
    <select id="selectEmployeeRanking" resultType="java.util.Map">
        SELECT
        rp.employee_id,
        e.name as employee_name,
        COUNT(rp.id) as count,
        SUM(rp.amount) as totalAmount
        FROM reward_punishment rp
        JOIN employee e ON rp.employee_id = e.id
        <where>
            del_flag = 0
            <if test="type != null">
                AND rp.type = #{type}
            </if>
        </where>
        GROUP BY rp.employee_id, e.name
        ORDER BY
        <choose>
            <when test="type != null and type == 0">
                totalAmount DESC, count DESC
            </when>
            <when test="type != null and type == 1">
                totalAmount ASC, count DESC
            </when>
            <otherwise>
                count DESC, totalAmount DESC
            </otherwise>
        </choose>
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据动态条件查询 (配合 PageHelper) -->
    <select id="selectByCondition" parameterType="cn.edu.ncu.hrms.employee.dto.RewardPunishmentQueryDTO" resultType="cn.edu.ncu.hrms.common.entity.RewardPunishment">
        SELECT
        rp.id, rp.employee_id, e.name as employee_name, rp.type, rp.title, rp.content, rp.amount, rp.reward_date,
        rp.remark, rp.create_time, rp.update_time
        FROM reward_punishment rp
        LEFT JOIN employee e ON rp.employee_id = e.id
        <where>
            rp.del_flag = 0
            AND
            e.del_flag = 0
            <if test="operatorId != null">
                AND rp.operator_id = #{operatorId}
            </if>
            <if test="type != null">
                AND rp.type = #{type}
            </if>
            <if test="workNo != null">
                AND e.work_no = #{workNo}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="startDate != null">
                AND rp.reward_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND rp.reward_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY rp.reward_date DESC, rp.create_time DESC
    </select>


    <select id="selectByEmployeeAndDateRange" resultType="cn.edu.ncu.hrms.common.entity.RewardPunishment">
        SELECT
        <include refid="Base_Column_List" />
        FROM reward_punishment
        WHERE employee_id = #{employeeId}
        AND reward_date BETWEEN #{startDate} AND #{endDate}
        AND del_flag = 0
        ORDER BY reward_date DESC, create_time DESC
    </select>

</mapper>