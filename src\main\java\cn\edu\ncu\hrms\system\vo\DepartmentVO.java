package cn.edu.ncu.hrms.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门信息视图对象 (VO)
 */
@Data
@ApiModel(value="DepartmentVO", description="部门信息视图对象")
public class DepartmentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门ID")
    private Integer id;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "上级部门ID")
    private Integer parentId;

    @ApiModelProperty(value = "部门领导ID")
    private Integer leaderId;

    // 需要从Employee中查询
    @ApiModelProperty(value = "部门领导姓名")
    private String leaderName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "子部门列表", notes = "用于构建部门树")
    private List<DepartmentVO> children;

    @ApiModelProperty(value = "上班打卡开始时间", example = "08:00")
    private String clockInStartTime;

    @ApiModelProperty(value = "上班打卡结束时间", example = "09:00")
    private String clockInEndTime;

    @ApiModelProperty(value = "迟到阈值（分钟）", example = "30")
    private Integer lateThreshold;

    @ApiModelProperty(value = "迟到扣款（元）", example = "50.00")
    private BigDecimal lateDeduction;

    @ApiModelProperty(value = "下班打卡开始时间", example = "17:00")
    private String clockOutStartTime;

    @ApiModelProperty(value = "下班打卡结束时间", example = "18:00")
    private String clockOutEndTime;

    @ApiModelProperty(value = "早退阈值（分钟）", example = "30")
    private Integer earlyThreshold;

    @ApiModelProperty(value = "早退扣款（元）", example = "50.00")
    private BigDecimal earlyDeduction;

    @ApiModelProperty(value = "打卡地点（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockLocation;

    @ApiModelProperty(value = "打卡范围（米）", example = "100")
    private Integer clockRadius;


} 