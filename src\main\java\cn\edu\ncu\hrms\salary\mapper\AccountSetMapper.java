package cn.edu.ncu.hrms.salary.mapper;

import cn.edu.ncu.hrms.salary.dto.AccountSetPageQueryDTO;
import cn.edu.ncu.hrms.common.entity.AccountSet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.List;

/**
 * 薪资账套规则Mapper接口
 */
@Mapper
public interface AccountSetMapper {

    /**
     * 根据ID查询薪资账套
     *
     * @param id 账套ID
     * @return 薪资账套信息
     */
    AccountSet selectById(Integer id);

    /**
     * 查询所有薪资账套列表
     *
     * @return 薪资账套列表
     */
    List<AccountSet> selectList();

    /**
     * 新增薪资账套
     *
     * @param accountSet 薪资账套信息
     * @return 影响行数
     */
    int insert(AccountSet accountSet);

    /**
     * 更新薪资账套
     *
     * @param accountSet 薪资账套信息
     * @return 影响行数
     */
    int update(AccountSet accountSet);

    /**
     * 删除薪资账套（逻辑删除）
     *
     * @param id 账套ID
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 根据基本工资范围查询薪资账套
     *
     * @param minSalary 最低工资
     * @param maxSalary 最高工资
     * @return 薪资账套列表
     */
    List<AccountSet> selectBySalaryRange(BigDecimal minSalary, BigDecimal maxSalary);

    /**
     * 模糊查询薪资账套（根据账套名称）
     *
     * @param keyword 关键字
     * @return 薪资账套列表
     */
    List<AccountSet> searchByName(String keyword);

    /**
     * 根据职位ID查询账套信息
     *
     * @param positionId 职位ID
     * @return 账套信息
     */
    AccountSet selectByPositionId(Integer positionId);

    /**
     * 根据名称查询账套 (用于重复校验)
     * @param name 账套名称
     * @return 账套实体
     */
    AccountSet selectByName(@Param("name") String name);

    /**
     * 查询所有未删除的账套
     * @return 账套列表
     */
    List<AccountSet> selectAll();

    /**
     * 根据条件查询账套列表 (配合 PageHelper 使用)
     * @param queryDTO 查询条件 DTO
     * @return 账套列表
     */
    List<AccountSet> selectByCondition(AccountSetPageQueryDTO queryDTO);
}