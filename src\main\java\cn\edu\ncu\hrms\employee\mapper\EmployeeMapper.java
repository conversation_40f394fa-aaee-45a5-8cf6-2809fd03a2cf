package cn.edu.ncu.hrms.employee.mapper;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.employee.dto.EmployeeQueryDTO;
import cn.edu.ncu.hrms.system.vo.EmployeeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 员工Mapper接口
 */
@Mapper
public interface EmployeeMapper {

    /**
     * 根据ID查询员工
     *
     * @param id 员工ID
     * @return 员工信息
     */
    Employee selectById(Integer id);

    /**
     * 根据工号查询员工
     *
     * @param workNo 工号
     * @return 员工信息
     */
    Employee selectByWorkNo(String workNo);

    /**
     * 根据身份证号查询员工
     *
     * @param idCard 身份证号
     * @return 员工信息
     */
    Employee selectByIdCard(String idCard);


    /**
     * 根据姓名查询所有员工
     *
     * @param name 员工姓名
     * @return 员工信息
     */
    List<Employee> selectListByName(String name);

    /**
     * 根据姓名查询最后一个创建的员工（包括已删除）
     * @param name
     * @return
     */
    Employee selectLastByName(String name);

    /**
     * 查询员工列表
     *
     * @return 员工列表
     */
    List<Employee> selectList();

    /**
     * 统计员工数量
     * @return
     */
    int count();

    /**
     * 新增员工
     *
     * @param employee 员工信息
     * @return 影响行数
     */
    int insert(Employee employee);

    /**
     * 更新员工
     *
     * @param employee 员工信息
     * @return 影响行数
     */
    int update(Employee employee);

    /**
     * 删除员工
     *
     * @param id 员工ID
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 批量删除员工
     *
     * @param ids 员工ID列表
     * @return 影响行数
     */
    int batchDelete(List<Integer> ids);

    /**
     * 根据部门ID查询员工列表
     *
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<Employee> selectByDepartmentId(Integer departmentId);

    /**
     * 根据职位ID查询员工列表
     *
     * @param positionId 职位ID
     * @return 员工列表
     */
    List<Employee> selectByPositionId(Integer positionId);

    /**
     * 根据HRBP员工ID查询员工列表
     *
     * @param hrbpId HRBP员工ID
     * @return 员工列表
     */
    List<Employee> selectByHrbpId(Integer hrbpId);

    /**
     * 更新员工状态（在职/离职）
     *
     * @param id 员工ID
     * @param status 状态（1在职 0离职）
     * @return 影响行数
     */
    int updateStatus(@Param("id") Integer id, @Param("status") Boolean status);

    /**
     * 模糊查询员工信息（根据姓名、工号、电话）
     *
     * @param keyword 关键字
     * @return 员工列表
     */
    List<Employee> search(String keyword);

    /**
     * 根据姓名模糊查询员工ID列表
     *
     * @param name 员工姓名
     * @return 员工ID列表
     */
    List<Integer> selectIdsByName(String name);

    /**
     * 查询所有员工
     */
    List<Employee> selectAll();

    /**
     * 根据员工姓名模糊查询员工列表
     */
    List<Employee> selectByNameLike(@Param("name") String name);

    /**
     * 统计部门员工数量
     */
    int countByDepartmentId(@Param("departmentId") Integer departmentId);

    /**
     * 统计职位员工数量
     */
    int countByPositionId(@Param("positionId") Integer positionId);

    /**
     * 分页查询员工列表
     */
    List<Employee> selectPage(
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("name") String name,
            @Param("departmentId") Long departmentId,
            @Param("position") String position,
            @Param("status") Integer status
    );

    /**
     * 查询符合条件的员工总数
     */
    int selectCount(
            @Param("name") String name,
            @Param("departmentId") Long departmentId,
            @Param("position") String position,
            @Param("status") Integer status
    );

    /**
     * 查询员工的部门领导ID（即直接上级）
     * @param employeeId 员工ID
     * @return 部门领导员工ID
     */
    Integer getDepartmentLeaderId(@Param("employeeId") Integer employeeId);


    /**
     * 查询指定领导的上级领导ID（自动向上查找一级）
     * @param leaderId 当前领导ID
     * @return 上级领导ID（可能为null）
     */
    Integer selectSuperiorLeaderId(@Param("leaderId") Integer leaderId);

    /**
     * 查询员工的HRBP ID
     * @param employeeId 员工ID
     * @return HRBP员工ID
     */
    Integer getHRBPId(@Param("employeeId") Integer employeeId);

    /**
     * 根据条件查询员工列表 (配合PageHelper, 返回带有关联信息的VO)
     *
     * @param name         姓名 (模糊)
     * @param workNo       工号 (精确)
     * @param departmentId 部门ID
     * @param positionId   职位ID
     * @param status       在职状态
     * @return 员工VO列表 (包含部门和职位名称)
     */
    List<EmployeeVO> selectByConditions(@Param("name") String name,
                                      @Param("workNo") String workNo,
                                      @Param("departmentId") Integer departmentId,
                                      @Param("positionId") Integer positionId,
                                      @Param("positionName") String positionName,
                                      @Param("status") Boolean status);

    /**
     * 查询员工视图对象列表 (包含部门和职位名称)
     * 
     * @return List<EmployeeVO> 员工视图对象列表
     */
    List<EmployeeVO> selectVOList();

    /**
     * 根据员工ID查询员工视图对象 (包含部门和职位名称)
     * 
     * @param id 员工ID
     * @return EmployeeVO 员工视图对象，未找到则返回 null
     */
    EmployeeVO selectVOById(@Param("id") Integer id);

    /**
     * 根据部门ID查询员工视图对象列表 (包含部门和职位名称)
     *
     * @param departmentId 部门ID
     * @return List<EmployeeVO> 员工视图对象列表
     */
    List<EmployeeVO> selectVOByDepartmentId(@Param("departmentId") Integer departmentId);

    /**
     * 统计指定年份各个月份的入职人数
     * @param year 年份
     * @return List<Map<String, Object>> 每个Map包含 "month" 和 "count"
     */
    List<Map<String, Object>> countMonthlyOnboarding(@Param("year") int year);

    /**
     * 查询未注册的员工列表
     * @return 员工视图对象列表
     */
    List<EmployeeVO> selectUnregistered();

    /**
     * 根据部门ID和角色标识获取员工列表
     * @param departmentId
     * @param roleKey
     * @return
     */
    List<Employee> selectByDepartmentIdAndRoleKey(@Param("departmentId") Integer departmentId, @Param("roleKey") String roleKey);


    /**
     * 分页条件查询员工列表（HR或领导）
     *
     * @param queryDTO
     * @return
     */
    List<Employee> selectEmployeeListByCondition(@Param("queryDTO") EmployeeQueryDTO queryDTO);


    /**
     * 批量插入员工
     * @param employees
     */
    void batchInsert(List<Employee> employees);


    /**
     * 根据姓名查询最后一条工号（FOR UPDATE加了排他锁，防止并发时查询到相同的工号）
     * @param name
     * @return
     */
    @Select("SELECT work_no FROM employee WHERE name = #{name} ORDER BY create_time DESC LIMIT 1 FOR UPDATE")
    String selectLastWorkNoForUpdate(String name);


    /**
     * 根据姓名拼音查询最大工号
     * @param namePinyin
     * @return
     */
    String selectMaxWorkNoByPinyin(@Param("namePinyin") String namePinyin, @Param("suffixStartPos") int suffixStartPos);


    /**
     * 判断员工是否为部门领导
     * @param employeeId
     * @return
     */
    Boolean isLeader(@Param("employeeId") Integer employeeId);

}