package cn.edu.ncu.hrms.system.controller;

import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.system.vo.DepartmentVO;
import cn.edu.ncu.hrms.system.dto.DepartmentAddDTO;
import cn.edu.ncu.hrms.system.dto.DepartmentUpdateDTO;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.service.SystemDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统部门管理控制器
 */
@RestController
@RequestMapping("/system/department")
@Api(tags = "系统部门管理接口")
public class SystemDepartmentController {

    @Autowired
    private SystemDepartmentService departmentService;

    /**
     * 获取部门列表 (扁平结构)
     */
    @GetMapping("/list")
    @ApiOperation("获取部门列表 (扁平结构)")
    @PreAuthorize("@hrms.hasAuthority('system:dept:list')")
    public Result<List<DepartmentVO>> getDepartmentList() {
        return departmentService.listDepartments();
    }

    /**
     * 获取部门树结构
     */
    @GetMapping("/tree")
    @ApiOperation("获取部门树结构")
    @PreAuthorize("@hrms.hasAuthority('system:dept:list')")
    public Result<List<DepartmentVO>> getDepartmentTree() {
        return departmentService.getDepartmentTree();
    }

    /**
     * 根据ID获取部门信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取部门信息")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:dept:query')")
    public Result<DepartmentVO> getDepartmentById(@PathVariable Integer id) {
        return departmentService.getDepartmentVOById(id);
    }

    /**
     * 创建部门
     */
    @PostMapping
    @ApiOperation("创建部门")
    @PreAuthorize("@hrms.hasAuthority('system:dept:add')")
    public Result<Void> createDepartment(@Valid @RequestBody DepartmentAddDTO addDTO) {
        return departmentService.createDepartment(addDTO);
    }

    /**
     * 更新部门信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新部门信息")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:dept:edit')")
    public Result<Void> updateDepartment(
            @PathVariable Integer id,
            @Valid @RequestBody DepartmentUpdateDTO updateDTO) {
        return departmentService.updateDepartment(id, updateDTO);
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除部门")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:dept:delete')")
    public Result<Void> deleteDepartment(@PathVariable Integer id) {
        return departmentService.deleteDepartment(id);
    }

    /**
     * 设置部门领导
     */
    @PutMapping("/{id}/leader/{leaderId}")
    @ApiOperation("设置部门领导")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "int", paramType = "path"),
            @ApiImplicitParam(name = "leaderId", value = "领导员工ID", required = true, dataType = "int", paramType = "path")
    })
    @PreAuthorize("@hrms.hasAuthority('system:dept:edit')")
    public Result<Void> setDepartmentLeader(@PathVariable Integer id, @PathVariable Integer leaderId) {
        return departmentService.setDepartmentLeader(id, leaderId);
    }

    /**
     * 获取部门下的员工列表
     */
    @GetMapping("/{id}/employees")
    @ApiOperation("获取部门下的员工列表")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:dept:query')")
    public Result<List<Employee>> getDepartmentEmployees(@PathVariable Integer id) {
        return departmentService.getDepartmentEmployees(id);
    }

    @GetMapping("/{id}/child")
    @ApiOperation("获取部门及其的子部门列表")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "int", paramType = "path")
    @PreAuthorize("@hrms.hasAuthority('system:dept:query')")
    public Result<List<Department>> getChildDepartments(@PathVariable Integer id) {
        return Result.success(departmentService.getChildDepartments(id));
    }

} 