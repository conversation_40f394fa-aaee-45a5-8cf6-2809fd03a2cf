package cn.edu.ncu.hrms.employee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 奖惩信息展示数据对象
 */
@ApiModel("奖惩信息展示")
@Data
public class RewardPunishmentVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖惩ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "员工ID", example = "101")
    private Integer employeeId;

    @ApiModelProperty(value = "员工姓名", example = "张三")
    private String employeeName;

    @ApiModelProperty(value = "员工工号", example = "zhangsan03")
    private String workNo;

    @ApiModelProperty(value = "操作人员工姓名", example = "李四")
    private String operatorName;

    @ApiModelProperty(value = "操作人员工工号", example = "lisi04")
    private String operatorWorkNo;

    @ApiModelProperty(value = "部门ID", example = "101")
    private Integer departmentId;

    @ApiModelProperty(value = "部门名称", example = "研发部")
    private String departmentName;

    @ApiModelProperty(value = "奖惩类型（0奖励 1惩罚）", allowableValues = "0,1", example = "0")
    private Integer type;

    @ApiModelProperty(value = "奖惩标题", example = "优秀员工奖励")
    private String title;

    @ApiModelProperty(value = "奖惩内容", example = "因表现优异，给予奖金奖励")
    private String content;

    @ApiModelProperty(value = "奖惩金额", example = "500.00")
    private BigDecimal amount;

    @ApiModelProperty(value = "奖惩日期", example = "2023-10-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate rewardDate;

    @ApiModelProperty(value = "备注", example = "无")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2023-10-01 10:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-10-01 12:00:00")
    private LocalDateTime updateTime;
} 