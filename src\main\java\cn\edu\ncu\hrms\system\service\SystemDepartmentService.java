package cn.edu.ncu.hrms.system.service;

import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.dto.DepartmentAddDTO;
import cn.edu.ncu.hrms.system.dto.DepartmentUpdateDTO;
import cn.edu.ncu.hrms.system.vo.DepartmentVO;

import java.util.List;

/**
 * 系统部门管理服务接口 (修改以匹配 Controller)
 */
public interface SystemDepartmentService {

    /**
     * 获取所有部门列表 (扁平结构)
     * @return Result<List<DepartmentVO>> 部门VO列表
     */
    Result<List<DepartmentVO>> listDepartments();

    /**
     * 获取部门树结构
     * @return Result<List<DepartmentVO>> 部门树VO列表
     */
    Result<List<DepartmentVO>> getDepartmentTree();

    /**
     * 根据ID获取部门信息
     * @param id 部门ID
     * @return Result<DepartmentVO> 部门VO
     */
    Result<DepartmentVO> getDepartmentVOById(Integer id);

    /**
     * 创建新部门
     * @param addDTO 部门创建信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> createDepartment(DepartmentAddDTO addDTO);

    /**
     * 更新部门信息
     * @param id 部门ID
     * @param updateDTO 部门更新信息 DTO
     * @return Result<Void> 操作结果
     */
    Result<Void> updateDepartment(Integer id, DepartmentUpdateDTO updateDTO);

    /**
     * 删除部门 (需要处理子部门和关联员工)
     * @param id 部门ID
     * @return Result<Void> 操作结果
     */
    Result<Void> deleteDepartment(Integer id);

    /**
     * 设置部门领导
     * @param id 部门ID
     * @param leaderId 领导员工ID
     * @return Result<Void> 操作结果
     */
    Result<Void> setDepartmentLeader(Integer id, Integer leaderId);

    /**
     * 获取部门下员工列表 (返回 Employee 实体)
     * @param id 部门ID
     * @return Result<List<Employee>> 员工实体列表
     */
    Result<List<Employee>> getDepartmentEmployees(Integer id);


    /**
     * 递归获取部门及其所有子部门（包括子部门的子部门）
     * @param departmentId
     * @return
     */
    List<Department> getChildDepartments(Integer departmentId);
}


