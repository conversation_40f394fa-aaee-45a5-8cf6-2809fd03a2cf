package cn.edu.ncu.hrms.employee.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * 审批状态转换器
 */
public class ApproveStatusConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) {
        String value = context.getReadCellData().getStringValue();
        if ("未审批".equals(value)) {
            return 0;
        } else if ("已通过".equals(value)) {
            return 1;
        } else if ("已驳回".equals(value)) {
            return 2;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        Integer value = context.getValue();
        String textValue = null;
        if (value != null) {
            if (value == 0) {
                textValue = "未审批";
            } else if (value == 1) {
                textValue = "已通过";
            } else if (value == 2) {
                textValue = "已驳回";
            }
        }
        return new WriteCellData<>(textValue);
    }
} 