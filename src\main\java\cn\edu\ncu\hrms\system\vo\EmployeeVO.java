package cn.edu.ncu.hrms.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 员工视图对象 VO
 */
@Data
@ApiModel("员工视图对象")
public class EmployeeVO {

    @ApiModelProperty("员工ID")
    private Integer id;

    @ApiModelProperty("工号")
    private String workNo;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("入职日期")
    private LocalDate entryDate;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("家庭住址")
    private String address;

    @ApiModelProperty("部门ID")
    private Integer departmentId;

    @ApiModelProperty("职位ID")
    private Integer positionId;

    @ApiModelProperty("HRBP员工ID")
    private Integer hrbpId;

    @ApiModelProperty("在职状态")
    private Boolean status;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    // --- 关联信息 --- 
    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty(value = "部门及其上级部门名称")
    private String departmentNames;

    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("职级")
    private Byte level;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "是否管理员")
    private Boolean isAdmin;
} 