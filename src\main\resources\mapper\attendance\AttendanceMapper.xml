<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.ncu.hrms.attendance.mapper.AttendanceMapper">

    <!-- 通用查询列 -->
    <sql id="Base_Column_List">
        id
        , employee_id, date,
        clock_in_time, clock_in_location, clock_in_status,
        clock_out_time, clock_out_location, clock_out_status,
        deduction, create_time, update_time, del_flag
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" resultType="attendance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attendance
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据员工和日期查询 -->
    <select id="selectByEmployeeAndDate" resultType="attendance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attendance
        WHERE employee_id = #{employeeId}
        AND date = #{date}
        AND del_flag = 0
    </select>

    <!-- 新增考勤记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO attendance (employee_id, date,
                                clock_in_time, clock_in_location, clock_in_status,
                                clock_out_time, clock_out_location, clock_out_status,
                                deduction, create_time, update_time, del_flag)
        VALUES (#{employeeId}, #{date},
                #{clockInTime}, #{clockInLocation}, #{clockInStatus},
                #{clockOutTime}, #{clockOutLocation}, #{clockOutStatus},
                #{deduction}, NOW(), NOW(), 0)
    </insert>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO attendance
        (employee_id, date, clock_in_time, clock_in_location, clock_in_status,
        clock_out_time, clock_out_location, clock_out_status, deduction,
        create_time, update_time, del_flag)
        VALUES
        <foreach collection="attendances" item="att" separator=",">
            (#{att.employeeId}, #{att.date},
            #{att.clockInTime}, #{att.clockInLocation}, #{att.clockInStatus},
            #{att.clockOutTime}, #{att.clockOutLocation}, #{att.clockOutStatus},
            #{att.deduction}, NOW(), NOW(), 0)
        </foreach>
    </insert>

    <!-- 更新考勤记录 -->
    <update id="update">
        UPDATE attendance
        <set>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="date != null">date = #{date},</if>
            <if test="clockInTime != null">clock_in_time = #{clockInTime},</if>
            <if test="clockInLocation != null">clock_in_location = #{clockInLocation},</if>
            <if test="clockInStatus != null">clock_in_status = #{clockInStatus},</if>
            <if test="clockOutTime != null">clock_out_time = #{clockOutTime},</if>
            <if test="clockOutLocation != null">clock_out_location = #{clockOutLocation},</if>
            <if test="clockOutStatus != null">clock_out_status = #{clockOutStatus},</if>
            <if test="deduction != null">deduction = #{deduction},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteById">
        UPDATE attendance
        SET del_flag    = null,
            update_time = NOW()
        WHERE id = #{id}
          AND del_flag = 0
    </update>

    <!-- 批量删除考勤记录 -->
    <update id="batchDelete">
        UPDATE attendance
        SET del_flag = null,
        update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>


    <update id="batchUpdate">
        <foreach collection="attendances" item="att" separator=";">
            UPDATE attendance
            <set>
                <if test="att.employeeId != null">employee_id = #{att.employeeId},</if>
                <if test="att.date != null">date = #{att.date},</if>
                <if test="att.clockInTime != null">clock_in_time = #{att.clockInTime},</if>
                <if test="att.clockInLocation != null">clock_in_location = #{att.clockInLocation},</if>
                <if test="att.clockInStatus != null">clock_in_status = #{att.clockInStatus},</if>
                <if test="att.clockOutTime != null">clock_out_time = #{att.clockOutTime},</if>
                <if test="att.clockOutLocation != null">clock_out_location = #{att.clockOutLocation},</if>
                <if test="att.clockOutStatus != null">clock_out_status = #{att.clockOutStatus},</if>
                <if test="att.deduction != null">deduction = #{att.deduction},</if>
                update_time = NOW()
            </set>
            WHERE id = #{att.id} AND del_flag = 0
        </foreach>
    </update>

    <!-- 条件查询 (配合 PageHelper) -->
    <select id="selectByCondition" resultType="attendance">
        SELECT a.*
        FROM attendance a
        JOIN employee e ON a.employee_id = e.id
        <where>
            a.del_flag = 0
            AND
            e.del_flag = 0
            <if test="query.departmentId != null">
                AND e.department_id = #{query.departmentId}
            </if>
            <if test="query.hrbpId != null">
                AND e.hrbp_id = #{query.hrbpId}
            </if>
            <if test="query.workNo != null">
                AND e.work_no = #{query.workNo}
            </if>
            <if test="query.startDate != null">
                AND date <![CDATA[>=]]> #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND date <![CDATA[<=]]> #{query.endDate}
            </if>
            <if test="query.clockInStatus != null">
                AND a.clock_in_status = #{query.clockInStatus}
            </if>
            <if test="query.clockOutStatus != null">
                AND a.clock_out_status = #{query.clockOutStatus}
            </if>
        </where>
        ORDER BY a.date DESC, a.employee_id ASC
    </select>

    <!-- 员工考勤统计 -->
    <select id="selectStatisticsByEmployeeAndDateRange" resultType="map">
        SELECT COUNT(CASE WHEN clock_in_status = 'LATE' THEN 1 END)                                  AS lateTimes,
               COUNT(CASE WHEN clock_out_status = 'EARLY' THEN 1 END)                                AS earlyTimes,
               COUNT(CASE WHEN clock_in_status = 'ABSENT' OR clock_out_status = 'ABSENT' THEN 1 END) AS absentDays,
               SUM(CASE
                       WHEN clock_in_status = 'LEAVE' AND clock_out_status = 'LEAVE' THEN 1
                       WHEN clock_in_status = 'LEAVE' OR clock_out_status = 'LEAVE' THEN 0.5
                       ELSE 0
                   END)                                                                              AS leaveDays,
               SUM(
                       CASE
                           -- 情况1：两个状态都正常（非空且不是 LEAVE/ABSENT）→ 1天
                           WHEN clock_in_status IN ('NORMAL', 'LATE', 'EARLY')
                               AND clock_out_status IN ('NORMAL', 'LATE', 'EARLY')
                               THEN 1

                           -- 情况2：一个为 LEAVE，另一个正常 → 0.5天
                           WHEN (clock_in_status = 'LEAVE' AND clock_out_status IN ('NORMAL', 'LATE', 'EARLY'))
                               OR (clock_out_status = 'LEAVE' AND clock_in_status IN ('NORMAL', 'LATE', 'EARLY'))
                               THEN 0.5

                           -- 其他情况 → 0天
                           ELSE 0
                           END
                   )                                                                                 AS actualWorkDays
        FROM attendance
        WHERE del_flag = 0
          AND employee_id = #{employeeId}
          AND date BETWEEN #{startDate}
          AND #{endDate}
    </select>

    <!-- 根据员工ID查询所有考勤记录 -->
    <select id="selectByEmployee" resultType="attendance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attendance
        WHERE employee_id = #{employeeId}
        ORDER BY date DESC
    </select>

    <!-- 根据员工ID和日期范围查询考勤记录 -->
    <select id="selectByEmployeeAndDateRange" resultType="attendance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attendance
        WHERE employee_id = #{employeeId}
        AND date BETWEEN #{startDate} AND #{endDate}
        AND del_flag = 0
        ORDER BY date DESC
    </select>

    <!-- 根据部门ID和日期查询考勤记录 -->
    <select id="selectByDepartment" resultType="attendance">
        SELECT a.*
        FROM attendance a
                 JOIN employee e ON a.employee_id = e.id
        WHERE e.department_id = #{departmentId}
          AND a.date = #{date}
          AND a.del_flag = 0
          AND e.del_flag = 0
        ORDER BY a.employee_id ASC
    </select>

    <!-- 统计今日考勤状态 -->
    <select id="countTodayAttendanceStatus" resultType="java.util.Map">
        SELECT COUNT(CASE WHEN clock_in_status = 'NORMAL' AND clock_out_status = 'NORMAL' THEN 1 END) AS normalCount,
               COUNT(CASE WHEN clock_in_status = 'LATE' THEN 1 END)                                   AS lateCount,
               COUNT(CASE WHEN clock_out_status = 'EARLY' THEN 1 END)                                 AS earlyLeaveCount,
               COUNT(CASE WHEN clock_in_status = 'ABSENT' OR clock_out_status = 'ABSENT' THEN 1 END)  AS absentCount,
               COUNT(CASE WHEN clock_in_status = 'LEAVE' OR clock_out_status = 'LEAVE' THEN 1 END)    AS leaveCount
        FROM attendance
        WHERE date = #{today}
          AND del_flag = 0
    </select>

    <!-- 查询员工指定月份的考勤记录列表 -->
    <select id="selectEmployeeRecordsByMonth" resultType="attendance">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attendance
        WHERE employee_id = #{employeeId}
        AND DATE_FORMAT(date, '%Y-%m') = #{yearMonth}
        AND del_flag = 0
        ORDER BY date ASC
    </select>

</mapper>