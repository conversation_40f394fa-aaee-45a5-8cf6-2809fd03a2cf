package cn.edu.ncu.hrms.common.kafka;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class KafkaProducerService {
    private final KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    public void sendMessage(String topic, String message) {
        kafkaTemplate.send(topic, message)
                .addCallback(
                        success -> System.out.println("消息发送成功：" + message),
                        failure -> System.err.println("消息发送失败：" + failure.getMessage())
                );
    }

    public void sendObjectMessage(String topic, Object object) {
        try {
            String json = objectMapper.writeValueAsString(object);
            sendMessage(topic, json);
        } catch (JsonProcessingException e) {
            log.error("对象序列化失败", e);
            throw new RuntimeException(e);
        }
    }
}
