<template>
  <div class="app-container">
    <div class="table-container">
      <div class="table-header">
        <div class="left">
          <h3>职位管理</h3>
        </div>
        <div class="right">
          <el-input
            v-model="queryParams.id"
            placeholder="请输入职位ID"
            clearable
            size="small"
            class="search-input"
            @keyup.enter.native="handleQuery"
          />
          <el-input
            v-model="queryParams.name"
            placeholder="请输入职位名称"
            clearable
            size="small"
            class="search-input"
            @keyup.enter.native="handleQuery"
          />
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button type="info" icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="positionList"
        border
      >
        <el-table-column prop="id" label="职位ID" align="center" min-width="80" />
        <el-table-column prop="name" label="职位名称" align="center" min-width="120" />
        <el-table-column prop="level" label="职级" align="center" min-width="80" />
        <el-table-column prop="accountId" label="账套ID" align="center" min-width="80" />
        <el-table-column label="关联账套" align="center" min-width="120">
          <template slot-scope="scope">
            <span>{{ getAccountName(scope.row.accountId) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" min-width="160" />
        <el-table-column label="操作" align="center" min-width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改信息</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除职位</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="table-footer">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加或修改职位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入职位名称" />
        </el-form-item>
        <el-form-item label="职级" prop="level">
          <el-input-number v-model="form.level" :min="5" :max="10" :step="1" controls-position="right" />
          <div class="form-tip">职级必须是5-10之间的整数</div>
        </el-form-item>
        <el-form-item label="账套" prop="accountId">
          <el-select v-model="form.accountId" placeholder="请选择关联账套" clearable>
            <el-option
              v-for="item in accountOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPositionPage, getPosition, addPosition, updatePosition, deletePosition } from '@/api/system/position'
import { listAccount } from '@/api/salary/account'

export default {
  name: 'Position',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 职位表格数据
      positionList: [],
      // 账套选项
      accountOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        name: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        level: undefined,
        accountId: undefined
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '职位名称不能为空', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '职位名称不能包含空格', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '职级不能为空', trigger: 'blur' },
          { type: 'number', min: 5, max: 10, message: '职级必须在5-10之间', trigger: 'blur' }
        ],
        accountId: [
          { required: true, message: '关联账套不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getAccountOptions()
  },
  methods: {
    /** 查询职位列表 */
    getList() {
      this.loading = true
      getPositionPage(this.queryParams).then(response => {
        if (response && response.code === 1 && response.data) {
          this.positionList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response && response.msg || '获取职位列表失败')
          this.positionList = []
          this.total = 0
        }
        this.loading = false
      }).catch(error => {
        this.$message.error(error.message || '获取职位列表失败')
        this.positionList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 获取账套名称 */
    getAccountName(accountId) {
      if (!accountId) return '未关联账套'
      const account = this.accountOptions.find(item => item.id === accountId)
      return account ? account.name : '未知账套'
    },

    /** 获取账套选项 */
    getAccountOptions() {
      listAccount().then(response => {
        if (response && response.code === 1) {
          this.accountOptions = response.data || []
        } else {
          this.$message.error(response && response.msg || '获取账套列表失败')
          this.accountOptions = []
        }
      }).catch(error => {
        this.$message.error(error.message || '获取账套列表失败')
        this.accountOptions = []
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        name: undefined
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加职位'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getPosition(row.id).then(response => {
        if (response && response.code === 1) {
          this.form = response.data || {}
        } else {
          this.$message.error(response && response.msg || '获取职位详情失败')
          this.form = { ...row }
        }
        this.open = true
        this.title = '修改职位'
      }).catch(error => {
        this.$message.error(error.message || '获取职位详情失败')
        this.form = { ...row }
        this.open = true
        this.title = '修改职位'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePosition(this.form.id, this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '修改失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '修改失败')
            })
          } else {
            addPosition(this.form).then(response => {
              if (response && response.code === 1) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
              } else {
                this.$message.error(response && response.msg || '新增失败')
              }
            }).catch(error => {
              this.$message.error(error.message || '新增失败')
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的职位?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return deletePosition(row.id)
      }).then(response => {
        if (response && response.code === 1) {
          this.getList()
          this.$message.success('删除成功')
        } else {
          this.$message.error(response && response.msg || '删除失败')
        }
      }).catch(error => {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除失败')
        }
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        level: 5, // 默认值为5
        accountId: undefined
      }
      this.resetForm('form')
    },
    /** 重置表单 */
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    // 分页页码变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
