package cn.edu.ncu.hrms.salary.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 更新薪资账套 DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("更新薪资账套数据")
public class AccountSetUpdateDTO extends AccountSetAddDTO {

    @ApiModelProperty(value = "账套ID")
    private Integer id;

} 