import axios from 'axios'
import store from '@/store'
import { Message } from 'element-ui'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // 基础地址，与后端接口前缀一致
  timeout: 30000, // 请求超时时间，增加到30秒
  // 添加重试配置
  retry: 1, // 重试次数
  retryDelay: 1000 // 重试间隔时间
})

// 创建一个全局变量，用于跟踪token过期状态
// 使用window对象确保它在整个应用中是全局的
window.tokenExpiredState = {
  // 是否已经显示过token过期消息
  messageShown: false,
  // 上次显示token过期消息的时间戳
  lastShownTimestamp: 0,
  // token过期消息的冷却时间（毫秒）
  cooldownPeriod: 3000
}

// 重置Token过期消息标记
export function resetTokenExpiredMessageFlag() {
  window.tokenExpiredState.messageShown = false
  window.tokenExpiredState.lastShownTimestamp = 0
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 如果存在token，请求头携带token
    if (store.getters.token) {
      config.headers.Authorization = `Bearer ${store.getters.token}`
    }

    // 为每个请求添加重试配置
    config.retry = config.retry || service.defaults.retry || 0
    config.retryDelay = config.retryDelay || service.defaults.retryDelay || 1000

    // 对于导出等大型操作，增加超时时间
    if (config.url && (config.url.includes('/export') || config.url.includes('/import'))) {
      config.timeout = 60000 // 60秒
    }

    return config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果是文件流或二进制数据，直接返回
    if (response.data instanceof Blob || response.config.responseType === 'arraybuffer' || response.config.responseType === 'blob') {
      // 检查是否有content-disposition头，可能包含文件名信息
      const contentDisposition = response.headers['content-disposition']
      if (contentDisposition) {
        console.log('Content-Disposition:', contentDisposition)
      }

      // 检查content-type头，确认文件类型
      const contentType = response.headers['content-type']
      if (contentType) {
        console.log('Content-Type:', contentType)
      }

      // 检查响应是否为JSON格式的错误消息 - 只对arraybuffer类型检查
      if (response.config.responseType === 'arraybuffer' && response.data.byteLength < 1000) {
        // 小于1KB的响应可能是错误消息，尝试解析
        try {
          const text = new TextDecoder().decode(response.data)
          if (text.includes('"code":0') || text.includes('"code": 0')) {
            // 尝试解析为JSON
            const jsonResponse = JSON.parse(text)
            console.warn('二进制响应包含错误信息:', jsonResponse)
            // 返回解析后的JSON，而不是二进制数据
            return jsonResponse
          }
        } catch (e) {
          // 解析失败，继续当作二进制数据处理
          console.log('小型二进制响应解析失败，继续当作文件处理')
        }
      }

      // 检查blob类型响应是否为空或过小
      if (response.data instanceof Blob && response.data.size < 100) {
        console.warn('Blob响应过小，可能不是有效文件:', response.data.size, '字节')
      }

      return response.data
    }

    // 返回原始响应，由业务代码处理
    return response.data
  },
  error => {
    // 处理401错误（未授权）或403错误（权限不足，可能是token过期）
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      const currentTime = Date.now()
      const tokenState = window.tokenExpiredState

      // 检查当前路由是否为登录页
      const isLoginPage = router.currentRoute.path === '/login'

      // 只有在未显示过Token过期消息，或者距离上次显示已经超过冷却时间时，且不在登录页时才显示
      if ((!tokenState.messageShown ||
          (currentTime - tokenState.lastShownTimestamp > tokenState.cooldownPeriod)) &&
          !isLoginPage) {

        // 更新状态
        tokenState.messageShown = true
        tokenState.lastShownTimestamp = currentTime

        // 显示消息
        Message({
          message: 'Token已过期，请重新登录',
          type: 'warning',
          duration: 3 * 1000
        })

        // 清除token并跳转到登录页
        store.dispatch('user/logout').then(() => {
          // 如果当前不在登录页，才跳转
          if (!isLoginPage) {
            router.push('/login')
          }

          // 登录页面加载完成后重置标记
          setTimeout(() => {
            resetTokenExpiredMessageFlag()
          }, 3000)
        })
      }

      // 自定义错误对象，避免显示默认的错误消息
      const customError = new Error()
      customError.isTokenExpired = true
      // 不返回任何错误消息，避免在控制台显示
      customError.message = ''
      return Promise.reject(customError)
    }

    // 处理其他错误（排除Token过期的自定义错误）
    if (!error.isTokenExpired) {
      // 只有非token过期的错误才显示消息
      Message({
        message: error.message || '请求失败',
        type: 'error',
        duration: 3 * 1000
      })

      // 在控制台记录非token过期的错误
      console.error('API请求错误:', error)
    }

    return Promise.reject(error)
  }
)

export default service
