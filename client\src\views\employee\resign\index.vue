<template>
  <div class="app-container">
    <div class="page-header">
      <h2>离职管理</h2>
      <div class="header-actions">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="self">个人申请</el-radio-button>
          <el-radio-button label="approval" v-if="hasApprovalPermission">离职审批</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 个人离职申请视图 -->
    <div v-if="viewMode === 'self'" class="resign-container">
      <el-card class="resign-card" shadow="hover">
        <div slot="header" class="card-header-self">
          <span><i class="el-icon-s-release"></i>我的离职申请</span>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAddResign"
            :disabled="hasActiveResign"
          >申请离职</el-button>
        </div>
        <el-table
          v-loading="loading"
          :data="personalResigns"
          border
          style="width: 100%"
        >
          <el-table-column label="申请ID" min-width="80" align="center">
            <template slot-scope="scope">
              {{ scope.row.id }}
            </template>
          </el-table-column>
          <el-table-column label="申请时间" min-width="180" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="离职日期" min-width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.resignDate }}
            </template>
          </el-table-column>
          <el-table-column label="离职原因" min-width="200" align="center">
            <template slot-scope="scope">
              {{ scope.row.reason }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-view"
                circle
                @click="handleViewResign(scope.row)"
                title="查看详情"
              ></el-button>
              <el-button
                v-if="scope.row.status === 'PENDING'"
                size="mini"
                type="warning"
                icon="el-icon-minus"
                circle
                @click="handleCancelResign(scope.row)"
                title="撤回申请"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 个人申请不需要分页 -->
      </el-card>
    </div>

    <!-- 离职审批视图 -->
    <div v-else-if="viewMode === 'approval'" class="approval-container">
      <el-card class="resign-card" shadow="hover">
        <div slot="header" class="card-header">
          <span>
            <i class="el-icon-s-release"></i>
            <!-- 获取用户角色信息 -->
            <span v-if="isHR">关联员工离职</span>
            <!-- 只有当用户没有HR角色但有Leader角色时才显示Leader标题 -->
            <span v-else-if="isLeader">部门员工离职</span>
            <span v-else>员工离职</span>
          </span>
          <div class="card-actions">
            <!-- 这里可以添加其他操作按钮 -->
          </div>
        </div>

        <div class="filter-form">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="工号">
              <el-input
                v-model="searchQuery"
                placeholder="输入员工工号"
                clearable
                prefix-icon="el-icon-document"
                style="width: 180px;"
                @keyup.enter.native="handleSearch"
              />
            </el-form-item>
            <el-form-item label="离职日期">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 250px;"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="statusFilter" placeholder="申请状态" clearable style="width: 140px;">
                <el-option label="待我审批" value="PENDING" />
                <el-option label="已审批" value="APPROVED" />
                <el-option label="已拒绝" value="REJECTED" />
                <el-option label="自动拒绝" value="AUTO_REJECTED" />
                <el-option label="已中止" value="TERMINATE" />
                <el-option label="已取消" value="CANCELED" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
          v-loading="loading"
          :data="approvalResigns"
          border
          stripe
          highlight-current-row
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column prop="id" label="申请ID" min-width="60" align="center">
            <template slot-scope="scope">
              {{ scope.row.id }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="申请时间" min-width="160" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.createTime">{{ scope.row.createTime }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="employeeWorkNo" label="工号" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.employeeWorkNo">{{ scope.row.employeeWorkNo }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="employeeName" label="姓名" min-width="60" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.employeeName">{{ scope.row.employeeName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="departmentName" label="部门" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="resignDate" label="离职日期" width="140" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.resignDate">{{ scope.row.resignDate }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="离职原因" min-width="250" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.reason">{{ scope.row.reason }}</span>
              <span v-else class="empty-text">暂无</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.isPendingByMe ? '待我审批' : getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="150" align="center">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  circle
                  @click="handleViewResign(scope.row)"
                  title="查看详情"
                ></el-button>
                <el-button
                  v-if="scope.row.status === 'PENDING' && scope.row.isPendingByMe"
                  size="mini"
                  type="danger"
                  icon="el-icon-close"
                  circle
                  @click="handleRejectResign(scope.row)"
                  title="拒绝申请"
                ></el-button>
                <el-button
                  v-if="scope.row.status === 'PENDING' && scope.row.isPendingByMe"
                  size="mini"
                  type="success"
                  icon="el-icon-check"
                  circle
                  @click="handleApproveResign(scope.row)"
                  title="通过申请"
                ></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleApprovalSizeChange"
            @current-change="handleApprovalCurrentChange"
            :current-page="approvalCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="approvalPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="approvalTotal"
          />
        </div>
      </el-card>
    </div>

    <!-- 离职申请对话框 -->
    <el-dialog title="离职申请" :visible.sync="resignDialogVisible" width="600px" :close-on-click-modal="false">
      <el-form ref="resignForm" :model="resignForm" :rules="resignRules" label-width="100px">
        <el-form-item label="离职日期" prop="resignDate">
          <el-date-picker
            v-model="resignForm.resignDate"
            type="date"
            placeholder="选择离职日期"
            style="width: 100%;"
            value-format="yyyy-MM-dd"
            :picker-options="resignDateOptions"
          />
        </el-form-item>
        <el-form-item label="离职原因" prop="reason">
          <el-input
            type="textarea"
            v-model="resignForm.reason"
            placeholder="请输入离职原因"
            :rows="4"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resignDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitResignForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 离职申请详情对话框 -->
    <el-dialog title="离职申请详情" :visible.sync="detailDialogVisible" width="700px" :close-on-click-modal="false">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请人">{{ resignDetail.employeeName }}</el-descriptions-item>
        <!-- 仅在审批视图下显示工号 -->
        <el-descriptions-item v-if="viewMode === 'approval'" label="工号">{{ resignDetail.employeeWorkNo }}</el-descriptions-item>
        <el-descriptions-item v-if="viewMode === 'approval'" label="部门">{{ resignDetail.departmentName }}</el-descriptions-item>
        <!-- 仅在审批视图下显示职位 -->
        <!-- <el-descriptions-item v-if="viewMode === 'approval'" label="职位">{{ resignDetail.positionName }}</el-descriptions-item> -->
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(resignDetail.status)">
            {{ getStatusText(resignDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请日期">{{ resignDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="离职日期">{{ resignDetail.resignDate }}</el-descriptions-item>
        <el-descriptions-item label="离职原因" :span="2">{{ resignDetail.reason }}</el-descriptions-item>
      </el-descriptions>

      <div class="approval-flow" v-if="resignDetail.approvalProcesses && resignDetail.approvalProcesses.length > 0">
        <h3>审批流程</h3>
        <div v-if="resignDetail.approvalProcesses.length === 1" class="single-approval">
          <!-- 单个审批节点的展示 -->
          <div class="single-approval-item">
            <div class="approver-info">
              <div class="approver-name">{{ resignDetail.approvalProcesses[0].approverName }}</div>
              <div class="approver-workno">{{ resignDetail.approvalProcesses[0].approverWorkNo }}</div>
            </div>
            <div class="approval-status-single">
              <el-tag :type="getStatusTagType(resignDetail.approvalProcesses[0].status)">
                {{ getApprovalDescription(resignDetail.approvalProcesses[0]) }}
              </el-tag>
            </div>
            <div v-if="resignDetail.approvalProcesses[0].comment" class="approval-comment-single">
              <div class="comment-label">审批意见：</div>
              <div class="comment-content">{{ resignDetail.approvalProcesses[0].comment }}</div>
            </div>
          </div>
        </div>
        <!-- 多个审批节点的展示 -->
        <div v-else class="multi-approval">
          <!-- <el-steps
            :active="getActiveStep(resignDetail)"
            finish-status="success"
            simple
            class="approval-steps"
          >
            <el-step
              v-for="(approval, index) in resignDetail.approvalProcesses"
              :key="index"
              :title="approval.approverName"
              :status="getApprovalStepStatus(approval)"
            />
          </el-steps> -->

          <div class="approval-details">
            <div
              v-for="(approval, index) in resignDetail.approvalProcesses"
              :key="index"
              class="approval-detail-item"
              :class="{'active': index === getActiveStep(resignDetail) }"
            >
              <div class="approval-detail-header">
                <span class="step-number">{{ index + 1 }}</span>
                <span class="approver-name">{{ approval.approverName }}</span>
              </div>
              <div class="approval-detail-content">
                <div class="approver-workno">{{ approval.approverWorkNo }}</div>
                <div class="approval-status-text">
                  <el-tag size="small" :type="getStatusTagType(approval.status)">
                    {{ getApprovalDescription(approval) }}
                  </el-tag>
                </div>
                <div v-if="approval.comment" class="approval-comment">
                  <div class="comment-label">审批意见：</div>
                  <div class="comment-content">{{ approval.comment }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="resignDetail.id" class="approval-flow-empty">
        <h3>审批流程</h3>
        <div class="empty-tip">
          <i class="el-icon-warning-outline"></i>
          <p>暂无审批流程数据</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
        <!-- 仅在审批视图下显示审批按钮 -->
        <template v-if="viewMode === 'approval'">
          <el-button
            v-if="resignDetail.status === 'PENDING' && resignDetail.isPendingByMe"
            type="danger"
            @click="handleRejectResign(resignDetail)"
          >拒 绝</el-button>
          <el-button
            v-if="resignDetail.status === 'PENDING' && resignDetail.isPendingByMe"
            type="primary"
            @click="handleApproveResign(resignDetail)"
          >通 过</el-button>
        </template>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog :title="approvalDialogTitle" :visible.sync="approvalDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
        <el-form-item label="审批意见" prop="comment">
          <el-input
            type="textarea"
            v-model="approvalForm.comment"
            placeholder="请输入审批意见"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitApprovalForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 使用Vuex中的用户角色信息，不再直接调用API
import {
  listPersonalResign,
  listResignPage,
  getResign,
  addResign,
  withdrawResign,
  approveResign,
  rejectResign
} from '@/api/employee/resign'
import { getApprovalProcess } from '@/api/common/approval'

export default {
  name: 'EmployeeResign',
  data() {
    return {
      // 视图模式：个人申请/离职审批
      viewMode: 'self',
      // 是否有审批权限
      hasApprovalPermission: false,
      // 是否为HR
      isHR: false,
      // 是否为领导
      isLeader: false,
      // 加载状态
      loading: false,
      // 个人离职申请
      personalResigns: [],
      // 是否有活跃的离职申请
      hasActiveResign: false,
      // 个人申请不需要分页
      // 审批离职申请
      approvalResigns: [],
      // 审批分页
      approvalCurrentPage: 1,
      approvalPageSize: 10,
      approvalTotal: 0,
      // 搜索参数
      searchQuery: '', // 工号搜索
      statusFilter: '',
      dateRange: [],
      // 离职申请对话框
      resignDialogVisible: false,
      resignForm: {
        resignDate: '',
        reason: ''
      },
      resignRules: {
        resignDate: [
          { required: true, message: '请选择离职日期', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入离职原因', trigger: 'blur' }
        ]
      },
      // 离职日期选择器配置
      resignDateOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期
        }
      },
      // 离职申请详情对话框
      detailDialogVisible: false,
      resignDetail: {},
      // 审批对话框
      approvalDialogVisible: false,
      approvalDialogTitle: '',
      approvalAction: '', // 'approve' 或 'reject'
      approvalForm: {
        resignId: null,
        comment: ''
      },
      approvalRules: {
        comment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ]
      },
      // 当前用户信息
      currentUser: {}
    }
  },
  created() {
    this.getUserRole()
    // 初始化时不立即加载数据，等待角色信息获取后再决定
  },
  methods: {
    // 获取用户角色信息 - 从Vuex中获取，不再调用API
    getUserRole() {
      // 从Vuex中获取用户角色信息
      const userInfo = this.$store.state.userInfo.userInfo || {}
      const roles = this.$store.state.userInfo.roles || []

      this.isHR = this.$store.getters['userInfo/hasRole']('ROLE_hr')
      this.isLeader = this.$store.getters['userInfo/hasRole']('ROLE_leader')
      this.hasApprovalPermission = this.isHR || this.isLeader
      this.currentUser = { id: userInfo.id, roles }

      console.log('从Vuex获取用户角色信息:', {
        isHR: this.isHR,
        isLeader: this.isLeader,
        hasApprovalPermission: this.hasApprovalPermission
      })


      // 根据权限决定默认视图和加载数据
      if (this.hasApprovalPermission) {
        // 有审批权限，默认显示审批视图
        this.viewMode = 'approval'
        this.$nextTick(() => {
          this.getApprovalResigns()
        })
      } else {
        // 无审批权限，显示个人申请视图
        this.viewMode = 'self'
        this.$nextTick(() => {
          this.getPersonalResigns()
        })
      }
    },

    // 重置筛选条件
    resetFilter() {
      this.searchQuery = ''
      this.statusFilter = ''
      this.dateRange = []
      this.handleSearch()
    },
    // 获取个人离职申请
    async getPersonalResigns() {
      try {
        this.loading = true
        const response = await listPersonalResign()

        if (response.code === 1) {
          this.personalResigns = response.data || []

          // 检查是否有活跃的离职申请
          this.hasActiveResign = this.personalResigns.some(item => item.status === 'PENDING')

          // 获取每个申请的审批流程
          await this.fetchApprovalProcesses()
        } else {
          this.$message.error(response.msg || '获取个人离职申请失败')
        }
        this.loading = false
      } catch (error) {
        console.error('获取个人离职申请失败', error)
        this.$message.error('获取个人离职申请失败')
        this.loading = false
      }
    },

    // 获取审批流程
    async fetchApprovalProcesses() {
      try {
        // 为每个离职申请获取审批流程
        for (let i = 0; i < this.personalResigns.length; i++) {
          const resign = this.personalResigns[i]
          if (!resign.approvalProcesses) {
            console.log('获取离职申请审批流程，申请ID:', resign.id)
            const response = await getApprovalProcess('RESIGN', resign.id)
            console.log('离职申请审批流程响应:', response)

            // 直接检查响应数据，不需要检查code
            if (response && Array.isArray(response)) {
              // 按照stepOrder排序
              const sortedProcesses = response.sort((a, b) => a.stepOrder - b.stepOrder)
              console.log('排序后的离职申请审批流程:', sortedProcesses)

              // 使用Vue.set确保响应式更新
              this.$set(this.personalResigns[i], 'approvalProcesses', sortedProcesses)
            } else if (response && response.code === 1 && Array.isArray(response.data)) {
              // 兼容标准返回格式
              const sortedProcesses = response.data.sort((a, b) => a.stepOrder - b.stepOrder)
              console.log('排序后的离职申请审批流程(标准格式):', sortedProcesses)

              // 使用Vue.set确保响应式更新
              this.$set(this.personalResigns[i], 'approvalProcesses', sortedProcesses)
            } else {
              console.warn('获取审批流程失败或数据格式不正确:', response)
            }
          }
        }
      } catch (error) {
        console.error('获取审批流程失败', error)
      }
    },
    // 获取审批离职申请
    async getApprovalResigns() {
      try {
        this.loading = true
        // 构建查询参数
        const params = {
          pageNum: this.approvalCurrentPage,
          pageSize: this.approvalPageSize,
          workNo: this.searchQuery || undefined,
          status: this.statusFilter || undefined,
          resignStartDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : undefined,
          resignEndDate: this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : undefined
        }

        const response = await listResignPage(params)

        if (response.code === 1) {
          this.approvalResigns = response.data.records || []
          this.approvalTotal = response.data.total || 0

          // 获取每个申请的审批流程
          for (let i = 0; i < this.approvalResigns.length; i++) {
            const resign = this.approvalResigns[i]
            if (!resign.approvalProcesses) {
              try {
                const processResponse = await getApprovalProcess('RESIGN', resign.id)
                if (processResponse && Array.isArray(processResponse)) {
                  // 按照stepOrder排序
                  const sortedProcesses = processResponse.sort((a, b) => a.stepOrder - b.stepOrder)
                  this.$set(this.approvalResigns[i], 'approvalProcesses', sortedProcesses)
                } else if (processResponse && processResponse.code === 1 && Array.isArray(processResponse.data)) {
                  // 兼容标准返回格式
                  const sortedProcesses = processResponse.data.sort((a, b) => a.stepOrder - b.stepOrder)
                  this.$set(this.approvalResigns[i], 'approvalProcesses', sortedProcesses)
                }
              } catch (err) {
                console.error(`获取申请ID ${resign.id} 的审批流程失败`, err)
              }
            }
          }
        } else {
          this.$message.error(response.msg || '获取审批离职申请失败')
        }
        this.loading = false
      } catch (error) {
        console.error('获取审批离职申请失败', error)
        this.$message.error('获取审批离职申请失败')
        this.loading = false
      }
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'AUTO_REJECTED': 'danger',
        'TERMINATE': 'info',
        'CANCELED': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        'NEW': 'info',
        'PENDING': 'warning',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'AUTO_REJECTED': 'danger',
        'TERMINATE': 'info',
        'CANCELED': 'info',
        'SKIPPED': 'info'
      }
      return statusMap[status] || 'info'
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待审批',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'AUTO_REJECTED': '自动拒绝',
        'TERMINATE': '已中止',
        'CANCELED': '已撤回'
      }
      return statusMap[status] || '未知'
    },
    // 获取审批描述
    getApprovalDescription(approval) {
      if (approval.status === 'PENDING') {
        return `待审批`
      } else if (approval.status === 'APPROVED') {
        return `已通过 ${approval.approveTime ? `(${this.formatDateTime(approval.approveTime)})` : ''}`
      } else if (approval.status === 'REJECTED') {
        return `已拒绝 ${approval.approveTime ? `(${this.formatDateTime(approval.approveTime)})` : ''}`
      } else if (approval.status === 'NEW') {
        return '未开始'
      } else if (approval.status === 'AUTO_REJECTED') {
        return '自动拒绝'
      } else if (approval.status === 'TERMINATE') {
        return '已终止'
      } else if (approval.status === 'CANCELED') {
        return '已撤回'
      }
      return ''
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';

      // 如果已经是格式化后的字符串，直接返回
      if (typeof dateTime === 'string' && dateTime.includes('-')) {
        return dateTime;
      }

      try {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
      } catch (e) {
        console.error('日期格式化错误', e);
        return dateTime;
      }
    },

    // 获取审批步骤状态
    getApprovalStepStatus(approval) {
      const statusMap = {
        'NEW': 'wait',
        'PENDING': 'process',
        'APPROVED': 'success',
        'REJECTED': 'error',
        'AUTO_REJECTED': 'error',
        'TERMINATE': 'info',
        'CANCELED': 'info',
        'SKIPPED': 'info'
      }
      return statusMap[approval.status] || 'wait'
    },
    // 获取当前活跃步骤
    getActiveStep(resign) {
      if (!resign.approvalProcesses || resign.approvalProcesses.length === 0) {
        return 0
      }

      if (resign.status === 'APPROVED') {
        return resign.approvalProcesses.length
      } else if (resign.status === 'REJECTED' || resign.status === 'AUTO_REJECTED') {
        // 找到拒绝的步骤
        const rejectIndex = resign.approvalProcesses.findIndex(a => a.status === 'REJECTED')
        return rejectIndex !== -1 ? rejectIndex + 1 : 0
      } else {
        // 找到第一个待审批的步骤
        const pendingIndex = resign.approvalProcesses.findIndex(a => a.status === 'PENDING')
        return pendingIndex !== -1 ? pendingIndex : 0
      }
    },

    // 计算步骤之间的间距
    calculateStepSpace(stepsCount) {
      if (!stepsCount) return '100px'

      // 根据步骤数量动态计算间距
      if (stepsCount <= 2) {
        return '200px' // 两个步骤，间距大一些
      } else if (stepsCount === 3) {
        return '120px' // 三个步骤，间距适中
      } else if (stepsCount === 4) {
        return '80px' // 四个步骤，间距小一些
      } else {
        return '60px' // 五个及以上步骤，间距最小
      }
    },
    // 判断当前用户是否可以审批
    canApprove(resign) {
      // 直接使用后端返回的isPendingByMe字段判断
      return resign.isPendingByMe === true
    },
    // 个人申请不需要分页处理
    // 处理审批页码变化
    handleApprovalCurrentChange(val) {
      this.approvalCurrentPage = val
      this.getApprovalResigns()
    },
    // 处理审批每页条数变化
    handleApprovalSizeChange(val) {
      this.approvalPageSize = val
      this.approvalCurrentPage = 1
      this.getApprovalResigns()
    },
    // 处理搜索
    handleSearch() {
      this.approvalCurrentPage = 1
      this.getApprovalResigns()
    },
    // 处理添加离职申请
    handleAddResign() {
      this.resignForm = {
        resignDate: '',
        reason: '',
        handover: ''
      }
      this.resignDialogVisible = true
    },
    // 处理查看离职申请
    async handleViewResign(row) {
      try {
        this.loading = true
        // 清空之前的数据
        this.resignDetail = {}

        // 获取离职申请详情
        const response = await getResign(row.id)
        console.log('离职申请详情响应:', response)

        if (response.code === 1) {
          this.resignDetail = response.data

          // 直接调用审批流程接口
          const hasApprovalProcess = await this.fetchApprovalProcessForDetail(row.id)
          console.log('是否获取到审批流程:', hasApprovalProcess)

          // 显示对话框
          this.detailDialogVisible = true

          // 如果没有获取到审批流程，再尝试一次
          if (!hasApprovalProcess) {
            setTimeout(async () => {
              console.log('重新尝试获取审批流程...')
              await this.fetchApprovalProcessForDetail(row.id)
            }, 500)
          }
        } else {
          this.$message.error(response.msg || '获取离职申请详情失败')
        }
        this.loading = false
      } catch (error) {
        console.error('获取离职申请详情失败', error)
        this.$message.error('获取离职申请详情失败')
        this.loading = false
      }
    },

    // 获取单个离职申请的审批流程
    async fetchApprovalProcessForDetail(resignId) {
      try {
        console.log('获取详情审批流程，申请ID:', resignId)
        const processResponse = await getApprovalProcess('RESIGN', resignId)
        console.log('详情审批流程响应:', processResponse)

        // 直接检查响应数据，不需要检查code
        if (processResponse && Array.isArray(processResponse)) {
          // 按照stepOrder排序
          const sortedProcesses = processResponse.sort((a, b) => a.stepOrder - b.stepOrder)
          console.log('排序后的详情审批流程:', sortedProcesses)

          if (sortedProcesses.length > 0) {
            // 使用Vue.set确保响应式更新
            this.$set(this.resignDetail, 'approvalProcesses', sortedProcesses)
            return true
          } else {
            console.warn('审批流程节点为空')
            return false
          }
        } else if (processResponse && processResponse.code === 1 && Array.isArray(processResponse.data)) {
          // 兼容标准返回格式
          const sortedProcesses = processResponse.data.sort((a, b) => a.stepOrder - b.stepOrder)
          console.log('排序后的详情审批流程(标准格式):', sortedProcesses)

          if (sortedProcesses.length > 0) {
            // 使用Vue.set确保响应式更新
            this.$set(this.resignDetail, 'approvalProcesses', sortedProcesses)
            return true
          } else {
            console.warn('审批流程节点为空(标准格式)')
            return false
          }
        } else {
          console.warn('获取审批流程失败或数据格式不正确:', processResponse)
          return false
        }
      } catch (error) {
        console.error('获取审批流程失败', error)
        return false
      }
    },
    // 处理撤回离职申请
    handleCancelResign(row) {
      this.$confirm('确定要撤回此离职申请吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await withdrawResign(row.id)
          if (response.code === 1) {
            this.$message.success('撤回成功')
            this.getPersonalResigns()
          } else {
            this.$message.error(response.msg || '撤回离职申请失败')
          }
        } catch (error) {
          console.error('撤回离职申请失败', error)
          this.$message.error('撤回离职申请失败')
        }
      }).catch(() => {
        // 取消撤回
      })
    },
    // 处理通过离职申请
    handleApproveResign(row) {
      this.approvalDialogTitle = '通过离职申请'
      this.approvalAction = 'approve'
      this.approvalForm = {
        resignId: row.id,
        comment: ''
      }
      this.approvalDialogVisible = true
    },
    // 处理拒绝离职申请
    handleRejectResign(row) {
      this.approvalDialogTitle = '拒绝离职申请'
      this.approvalAction = 'reject'
      this.approvalForm = {
        resignId: row.id,
        comment: ''
      }
      this.approvalDialogVisible = true
    },
    // 提交离职申请表单
    submitResignForm() {
      this.$refs.resignForm.validate(async valid => {
        if (valid) {
          try {
            // 准备提交的数据
            const submitData = {
              resignDate: this.resignForm.resignDate,
              reason: this.resignForm.reason
            }

            const response = await addResign(submitData)
            if (response.code === 1) {
              this.$message.success('离职申请提交成功')
              this.resignDialogVisible = false
              this.getPersonalResigns()
            } else {
              this.$message.error(response.msg || '离职申请提交失败')
            }
          } catch (error) {
            console.error('提交离职申请失败', error)
            this.$message.error('提交离职申请失败')
          }
        }
      })
    },
    // 提交审批表单
    submitApprovalForm() {
      this.$refs.approvalForm.validate(async valid => {
        if (valid) {
          try {
            // 准备提交的数据
            const submitData = {
              comment: this.approvalForm.comment,
              status: this.approvalAction === 'approve' ? 'APPROVED' : 'REJECTED'
            }

            let response
            if (this.approvalAction === 'approve') {
              response = await approveResign(this.approvalForm.resignId, submitData)
            } else {
              response = await rejectResign(this.approvalForm.resignId, submitData)
            }

            if (response.code === 1) {
              this.$message.success(this.approvalAction === 'approve' ? '已通过离职申请' : '已拒绝离职申请')
              this.approvalDialogVisible = false
              if (this.viewMode === 'approval') {
                this.getApprovalResigns()
              }
              this.detailDialogVisible = false
            } else {
              this.$message.error(response.msg || '提交审批失败')
            }
          } catch (error) {
            console.error('提交审批失败', error)
            this.$message.error('提交审批失败')
          }
        }
      })
    }
  },
  watch: {
    viewMode(newVal) {
      if (newVal === 'approval') {
        this.getApprovalResigns()
      } else {
        this.getPersonalResigns()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }
}

.resign-container {
  margin-top: 20px;
}



.resign-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-header-self {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 20px;

    span {
      font-size: 18px;
      font-weight: 600;

      i {
        margin-right: 5px;
        // color: #409EFF;
      }
    }

    .card-actions {
      display: flex;
      gap: 10px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 18px;
      font-weight: 600;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }

    .card-actions {
      display: flex;
      gap: 10px;
    }
  }


  .filter-form {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    height: 70px;
  }

  .empty-text {
    color: #909399;
    font-size: 13px;
  }
}

.approval-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.approval-flow, .approval-flow-empty {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;

  h3 {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.approval-flow-empty {
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    i {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 15px;
      animation: pulse 2s infinite;
    }

    p {
      color: #909399;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.approval-flow {
  .single-approval {
    padding: 25px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .single-approval-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .approver-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;

        .approver-name {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #303133;
        }

        .approver-workno {
          font-size: 14px;
          color: #606266;
          background-color: #f0f2f5;
          padding: 3px 12px;
          border-radius: 15px;
        }
      }

      .approval-status {
        margin-bottom: 20px;
        .el-tag {
          padding: 8px 15px;
          font-size: 14px;
          border-radius: 4px;
        }
      }


      .approval-status-single {
        .el-tag {
          font-size: 14px;
          border-radius: 4px;
        }
      }

      .approval-comment-single {
        background-color: #fff;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #409EFF;
        width: 100%;
        max-width: 500px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);

        .comment-label {
          font-weight: 600;
          margin-bottom: 10px;
          color: #303133;
          font-size: 15px;
        }

        .comment-content {
          color: #606266;
          line-height: 1.6;
          word-break: break-all;
          font-size: 14px;
        }
      }
    }
  }

  .multi-approval {
    width: 100%;

    .approval-steps {
      margin-bottom: 20px;
    }

    .approval-details {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      justify-content: space-between;

      .approval-detail-item {
        flex: 1;
        min-width: 200px;
        max-width: calc(33.33% - 10px);
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &.active {
          border-left: 4px solid #409EFF;
          background-color: #f0f7ff;
        }

        .approval-detail-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 10px;

          .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background-color: #409EFF;
            color: white;
            border-radius: 50%;
            font-size: 14px;
            margin-right: 10px;
          }

          .approver-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }

        .approval-detail-content {
          .approver-workno {
            display: inline-block;
            font-size: 13px;
            color: #606266;
            background-color: #f0f2f5;
            padding: 2px 10px;
            border-radius: 12px;
            margin-bottom: 10px;
          }

          .approval-status-text {
            margin-bottom: 10px;
          }

          .approval-comment {
            background-color: #fff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;

            .comment-label {
              font-weight: 500;
              margin-bottom: 5px;
              color: #303133;
              font-size: 13px;
            }

            .comment-content {
              color: #606266;
              line-height: 1.5;
              word-break: break-all;
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

/* 使用::v-deep替代/deep/，解决Sass编译错误 */
::v-deep .el-step__title {
  font-size: 14px;
  font-weight: 500;
}

::v-deep .el-step__description {
  padding: 0 5px;
  box-sizing: border-box;
  width: 100%;
}

::v-deep .el-step.is-horizontal {
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #e4e7ed;
    z-index: -1;
  }
}

::v-deep .el-step.is-vertical {
  .el-step__line {
    border-width: 2px;
  }

  .el-step__head {
    padding-bottom: 10px;
  }
}

::v-deep .el-step__head.is-wait {
  color: #909399;
  border-color: #909399;
}

::v-deep .el-step__head.is-process {
  color: #409EFF;
  border-color: #409EFF;
}

::v-deep .el-step__head.is-success {
  color: #67c23a;
  border-color: #67c23a;
}

::v-deep .el-step__head.is-error {
  color: #f56c6c;
  border-color: #f56c6c;
}

::v-deep .el-step__main {
  white-space: normal;
  text-align: center;
}

/* 简单步骤条样式优化 */
::v-deep .el-steps--simple {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px 0;

  .el-step__head {
    width: 8px;
    height: 8px;

    &.is-process {
      .el-step__icon.is-text {
        border: 2px solid #409EFF;
        background-color: #409EFF;
      }
    }

    &.is-wait {
      .el-step__icon.is-text {
        border: 2px solid #c0c4cc;
        background-color: #fff;
      }
    }

    &.is-success {
      .el-step__icon.is-text {
        border: 2px solid #67c23a;
        background-color: #67c23a;
      }
    }

    &.is-error {
      .el-step__icon.is-text {
        border: 2px solid #f56c6c;
        background-color: #f56c6c;
      }
    }
  }

  .el-step__icon {
    width: 8px;
    height: 8px;
    font-size: 12px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-step__title {
    font-size: 13px;

    &.is-process {
      font-weight: 600;
      color: #409EFF;
    }

    &.is-wait {
      color: #909399;
    }

    &.is-success {
      color: #67c23a;
    }

    &.is-error {
      color: #f56c6c;
    }
  }

  .el-step__arrow {
    margin: 0 8px;
  }
}
</style>