<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import webSocketClient from '@/utils/websocket'
import store from '@/store'

export default {
  name: 'App',
  created() {
    // 如果用户已登录，初始化WebSocket连接
    if (store.getters.token) {
      console.log('App.vue: 用户已登录，初始化WebSocket连接')
      setTimeout(() => {
        webSocketClient.connect()
      }, 1000) // 延迟1秒连接，确保用户信息已加载
    }
  },
  watch: {
    // 监听token变化，自动连接或断开WebSocket
    '$store.getters.token': {
      handler(newToken) {
        if (newToken) {
          // 用户登录，连接WebSocket
          console.log('App.vue: 检测到token变化，连接WebSocket')
          setTimeout(() => {
            webSocketClient.connect()
          }, 1000) // 延迟1秒连接，确保用户信息已加载
        } else {
          // 用户登出，断开WebSocket
          console.log('App.vue: 检测到token失效，断开WebSocket')
          webSocketClient.disconnect()
        }
      },
      immediate: true
    }
  }
}
</script>

<style>
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

#app {
  height: 100%;
}
</style>
