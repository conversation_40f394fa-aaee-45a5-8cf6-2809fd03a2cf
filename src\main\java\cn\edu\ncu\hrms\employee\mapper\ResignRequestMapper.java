package cn.edu.ncu.hrms.employee.mapper;

import cn.edu.ncu.hrms.common.entity.ResignRequest;
import cn.edu.ncu.hrms.employee.dto.ResignPageQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 离职申请mapper接口
 */
@Mapper
public interface ResignRequestMapper {
    /**
     * 根据ID查询离职申请
     *
     * @param id 离职申请ID
     * @return 离职申请信息
     */
    ResignRequest selectById(Integer id);

    /**
     * 查询员工的离职申请列表
     *
     * @param employeeId 员工ID
     * @return 离职申请列表
     */
    List<ResignRequest> selectByEmployeeId(Integer employeeId);

    /**
     * 查询员工在日期范围内的离职申请
     *
     * @param employeeId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 离职申请列表
     */
    List<ResignRequest> selectByEmployeeAndDateRange(@Param("employeeId") Integer employeeId,
                                                    @Param("startDate") LocalDate startDate,
                                                    @Param("endDate") LocalDate endDate);

    /**
     * 查询待审批的离职申请列表
     *
     * @param employeeId 员工ID
     * @return 待审批的离职申请列表
     */
    List<ResignRequest> selectPendingByEmployeeId(Integer employeeId);

    /**
     * 查询部门的离职申请列表
     *
     * @param departmentId 部门ID
     * @param status 申请状态
     * @return 离职申请列表
     */
    List<ResignRequest> selectByDepartment(@Param("departmentId") Integer departmentId,
                                          @Param("status") String status);

    /**
     * 分页查询离职申请
     *
     * @param params 查询参数
     * @return 离职申请列表
     */
    List<ResignRequest> selectPage(Map<String, Object> params);

    /**
     * 新增离职申请
     *
     * @param resignRequest 离职申请信息
     * @return 影响行数
     */
    int insert(ResignRequest resignRequest);

    /**
     * 更新离职申请
     *
     * @param resignRequest 离职申请信息
     * @return 影响行数
     */
    int update(ResignRequest resignRequest);

    /**
     * 删除离职申请
     *
     * @param id 离职申请ID
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 批量删除离职申请
     *
     * @param ids 离职申请ID列表
     * @return 影响行数
     */
    int batchDelete(List<Integer> ids);

    /**
     * 统计员工在指定年份的离职天数
     *
     * @param employeeId 员工ID
     * @param year 年份
     * @return 离职天数统计
     */
    Map<String, Object> countByYear(@Param("employeeId") Integer employeeId,
                                    @Param("year") Integer year);

    /**
     * 查询日期范围内有离职的员工ID列表
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 员工ID列表
     */
    List<Integer> selectEmployeesOnLeave(@Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate);
    /**
     * 更新离职申请的审批状态
     * @param id 申请ID
     * @param status 状态（PENDING/APPROVED/REJECTED）
     * @return 影响行数
     */
    int updateStatus(@Param("id") Integer id, @Param("status") String status);


    /**
     * 根据查询条件查询离职申请
     * @param queryDTO
     * @return
     */
    List<ResignRequest> selectByCondition(ResignPageQueryDTO queryDTO);


    /**
     * 根据日期查询离职申请
     * @param date
     * @return
     */
    List<ResignRequest> selectResignationsByDate(LocalDate date);


    /**
     * 查询今天离职的所有记录
     * @return
     */
    List<ResignRequest> selectResignationsForToday();

}
