package cn.edu.ncu.hrms.common.utils;

import cn.edu.ncu.hrms.auth.mapper.UserMapper;
import cn.edu.ncu.hrms.auth.security.LoginUser;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.entity.User;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 安全工具类，用于获取当前登录用户信息
 */
@Slf4j
@Component
public class SecurityUtil {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取当前登录用户名
     *
     * @return 当前登录用户名，如果未登录返回null
     */
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("用户未登录");
            return null;
        }
        // 获取用户名
        String username = authentication.getName();
        log.debug("当前登录用户: {}", username);
        return username;
    }

    /**
     * 获取当前登录用户信息
     * @return
     */
    public User getCurrentUser() {
        /*String username = getCurrentUsername();
        if (username == null) {
            return null;
        }

        // 查询用户信息
        User user = userMapper.selectByUsername(username);*/

        LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (loginUser.getUser() == null) {
            log.warn("用户信息不存在");
            return null;
        }

        log.debug("获取到当前登录用户: {}", loginUser.getUser());
        return loginUser.getUser();
    }


    /**
     * 获取当前登录用户关联的员工
     *
     * @return 当前员工ID，如果未登录或没有关联员工返回null
     */
    public Employee getCurrentEmployee() {
        // 获取当前登录用户
        User user = getCurrentUser();

        // 获取关联的员工ID
        Integer employeeId = user.getEmployeeId();
        if (employeeId == null) {
            log.warn("用户:{} 未关联员工", user.getUsername());
            return null;
        }

        Employee employee = employeeMapper.selectById(employeeId);
        if (employee == null) {
            log.warn("员工信息不存在，id = {}", employeeId);
            return null;
        }

        return employee;
    }
    
    /**
     * 获取当前登录用户关联的员工ID
     *
     * @return 当前员工ID，如果未登录或没有关联员工返回null
     */
    public Integer getCurrentEmployeeId() {
        Employee employee = getCurrentEmployee();
        if (employee != null) {
            return employee.getId();
        }
        return null;
    }


    /**
     * 获取当前登录用户关联的部门ID
     *
     * @return 当前部门ID，如果未登录或没有关联部门返回null
     */
    public Integer getCurrentDepartmentId() {
        // 获取关联的员工
        Employee employee = getCurrentEmployee();

        if (employee != null) {
            log.debug("当前登录用户关联的部门ID: {}", employee.getDepartmentId());
            return employee.getDepartmentId();
        }

        return null;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 当前用户ID，如果未登录返回null
     */
    public Integer getCurrentUserId() {
        /*String username = getCurrentUsername();
        if (username == null) {
            return null;
        }
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            log.warn("无法获取当前用户ID，用户不存在: {}", username);
            return null;
        }*/
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return null;
        }
        log.debug("获取到当前用户ID: {}", currentUser.getId());
        return currentUser.getId();
    }

    public List<String> getCurrentUserRoles() {
        String username = getCurrentUsername();
        if (username == null) {
            log.warn("无法获取当前用户角色，用户未登录");
            return null;
        }

        List<String> permissions = (List<String>) redisTemplate.opsForValue().get(String.format("permissions:%s", username));
        List<String> roles = permissions.stream().filter(permission -> permission.startsWith("ROLE_")).collect(Collectors.toList());

        return roles;
    }
} 