package cn.edu.ncu.hrms.attendance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 考勤记录VO
 */
@Data
@ApiModel(description = "考勤记录VO")
public class AttendanceVO {
    
    @ApiModelProperty(value = "考勤记录ID")
    private Integer id;
    
    @ApiModelProperty(value = "员工ID")
    private Integer employeeId;
    
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ApiModelProperty(value = "工号")
    private String workNo;
    
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    
    @ApiModelProperty(value = "考勤日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;
    
    @ApiModelProperty(value = "签到时间")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime clockInTime;

    @ApiModelProperty(value = "签到状态(NORMAL:正常, LATE:迟到, ABSENT:缺勤, LEAVE-请假)")
    private String clockInStatus;

    @ApiModelProperty(value = "签到位置（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockInLocation;

    @ApiModelProperty(value = "签退时间")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime clockOutTime;
    
    @ApiModelProperty(value = "签退状态(NORMAL:正常, EARLY:早退, ABSENT:缺勤, LEAVE-请假)")
    private String clockOutStatus;

    @ApiModelProperty(value = "签退位置（经纬度）", example = "[116.34351300000004, 40.060244]")
    private String clockOutLocation;

    @ApiModelProperty(value = "扣除金额", example = "0.00")
    private BigDecimal deduction;

} 