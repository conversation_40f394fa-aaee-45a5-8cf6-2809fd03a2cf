// 基础颜色
$blue: #409EFF; // 基础色调
$light-blue:#409EFF;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$orange:#fa7c4d;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;
$green1: #44b549;
$menuFontColor: #A7ACBA;   // 导航文字颜色
$fontColor: #383C4E; // 标题文本颜色
$secondFontColor: #697086; // 辅助文字颜色
$hoverFontColor: #fff; // hover辅助颜色

// 侧边栏
$menuText:#333333;
$menuActiveText:#409EFF;
$subMenuActiveText:#409EFF;

$menuBg: #ffffff;
$menuHover:#f0f7ff;

$subMenuBg:#ffffff;
$subMenuHover:#f0f7ff;

$sideBarWidth: 200px;

// 导出变量给JS使用
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
