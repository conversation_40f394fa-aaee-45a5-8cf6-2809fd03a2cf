package cn.edu.ncu.hrms.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 修改密码参数DTO
 */
@Data
@ApiModel(description = "修改密码参数")
public class PasswordChangeDTO {

    @NotBlank(message = "旧密码不能为空")
    @ApiModelProperty(value = "旧密码", required = true, example = "123456")
    private String oldPassword;

    @NotBlank(message = "新密码不能为空")
    @ApiModelProperty(value = "新密码", required = true, example = "654321")
    private String newPassword;
} 