package cn.edu.ncu.hrms.attendance.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 考勤统计VO
 */
@Data
@ApiModel(description = "考勤统计数据")
public class AttendanceStatisticsVO {
    
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    
    @ApiModelProperty(value = "开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    
    @ApiModelProperty(value = "结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "工作日天数")
    private Integer workDays;
    
    @ApiModelProperty(value = "应出勤天数")
    private Double shouldWorkDays;
    
    @ApiModelProperty(value = "实际出勤天数")
    private Double actualWorkDays;
    
    @ApiModelProperty(value = "迟到次数")
    private Integer lateTimes;
    
    @ApiModelProperty(value = "早退次数")
    private Integer earlyTimes;
    
    @ApiModelProperty(value = "缺勤天数")
    private Integer absentDays;
    
    @ApiModelProperty(value = "请假天数")
    private Double leaveDays;
    
    @ApiModelProperty(value = "出勤率(%)")
    private Double attendanceRate;
} 