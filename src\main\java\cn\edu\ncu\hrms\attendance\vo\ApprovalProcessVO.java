package cn.edu.ncu.hrms.attendance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@ApiModel(description = "审批流程节点视图")
public class ApprovalProcessVO {
    @ApiModelProperty(value = "审批步骤ID", required = true, example = "1101")
    private Integer id;

    @ApiModelProperty(value = "申请ID", required = true, example = "901")
    private Integer requestId;

    @ApiModelProperty(value = "申请类型", allowableValues = "LEAVE,RESIGN", example = "LEAVE")
    private String type;

    @ApiModelProperty(value = "审批人员工ID", required = true, example = "1002")
    private Integer approverId;

    @ApiModelProperty(value = "审批人姓名")
    private String approverName;

    @ApiModelProperty(value = "审批人工号")
    private String approverWorkNo;

    @ApiModelProperty(value = "审批顺序", required = true, example = "1")
    private Byte stepOrder;

    @ApiModelProperty(value = "审批状态", allowableValues = "NEW,PENDING,APPROVED,REJECTED,AUTO_REJECTED,TERMINATE,CANCELED", example = "PENDING")
    private String status;

    @ApiModelProperty(value = "审批状态名称")
    private String statusName;

    @ApiModelProperty(value = "审批意见", example = "同意申请")
    private String comment;

    @ApiModelProperty(value = "审批时间", example = "2025-01-15 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approveTime;

    @ApiModelProperty(value = "审批截止时间", required = true, example = "2025-01-18 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;
}
