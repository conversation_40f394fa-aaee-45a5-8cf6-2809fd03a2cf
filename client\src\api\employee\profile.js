import request from '@/utils/request'

// 获取我的档案信息
export function getMyProfile() {
  return request({
    url: '/employee/my-profile',
    method: 'get'
  })
}

// 更新我的档案信息
export function updateMyProfile(data) {
  return request({
    url: '/employee/my-profile',
    method: 'put',
    data: data
  })
}

// 获取员工详情
export function getEmployee(id) {
  return request({
    url: `/employee/${id}`,
    method: 'get'
  })
}

// HR分页查询员工列表
export function listEmployeesByHr(params) {
  return request({
    url: '/employee/page/hr',
    method: 'get',
    params
  })
}

// Leader分页查询员工列表
export function listEmployeesByLeader(params) {
  return request({
    url: '/employee/page/leader',
    method: 'get',
    params
  })
}

// 获取职位名称列表
export function getPositionNames() {
  return request({
    url: '/salary/position/name',
    method: 'get'
  })
}

// 添加员工
export function addEmployee(data) {
  return request({
    url: '/employee',
    method: 'post',
    data: data
  })
}

// 更新员工信息
export function updateEmployee(data) {
  return request({
    url: '/employee',
    method: 'put',
    data: data
  })
}

// 删除员工
export function deleteEmployee(id) {
  return request({
    url: '/employee',
    method: 'delete',
    data: { id }
  })
}

// 更新员工状态
export function updateEmployeeStatus(id, status) {
  return request({
    url: '/employee/status',
    method: 'put',
    data: { id, status }
  })
}

// 获取未注册用户的员工列表
export function getUnregisteredEmployees() {
  return request({
    url: '/employee/unregistered',
    method: 'get'
  })
}

// 导入员工数据
export function importEmployee(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/employee/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载员工导入模板
export function downloadTemplate() {
  return request({
    url: '/employee/template',
    method: 'get',
    responseType: 'blob', // 使用blob类型，因为后端直接返回文件流
    timeout: 30000 // 增加超时时间到30秒
  })
}

// 导出员工数据
export function exportEmployee(params) {
  return request({
    url: '/employee/export',
    method: 'get',
    params,
    responseType: 'arraybuffer', // 使用arraybuffer而不是blob
    headers: {
      'Accept': 'application/octet-stream' // 明确告诉服务器我们期望二进制数据
    }
  })
}
