package cn.edu.ncu.hrms.common.exception;

import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.result.Result;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常处理
     */
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.error("业务异常: {}", e.getMessage());
        return Result.error(e.getMessage());
    }

    /**
     * 权限异常处理
     */
    @ExceptionHandler(AccessDeniedException.class)
    public Result<Void> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String requestUrl = request.getRequestURL().toString();
        String requestMethod = request.getMethod();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication != null ? authentication.getName() : "未认证用户";
        log.error("权限异常，请求URL: {}, 请求方法: {}, 用户: {}, 异常信息: {}", requestUrl, requestMethod, username, e.getMessage(), e);
        return Result.error("权限不足，请联系管理员");
    }

    /**
     * 参数校验异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            // errorMsg.append(fieldError.getField()).append(": ").append(fieldError.getDefaultMessage()).append(", ");
            errorMsg.append(fieldError.getDefaultMessage()).append(", ");
        }
        String message = errorMsg.toString();
        if (message.length() > 2) {
            message = message.substring(0, message.length() - 2);
        }
        log.error("参数校验异常: {}", message);
        return Result.error(message);
    }

    /**
     * 全局异常处理
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常: ", e);
        return Result.error("系统繁忙，请稍后再试");
    }


    /**
     * 数据库唯一约束异常处理
     * 可以处理复合唯一约束，如：UNIQUE KEY unique_position_name_level (name,level,del_flag)
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public Result<Void> handleDuplicateKeyException(DuplicateKeyException e) {
        log.error("数据库唯一约束异常: {}", e.getMessage());

        // 解析异常信息获取冲突字段
        Throwable cause = e.getCause();
        if (cause instanceof SQLIntegrityConstraintViolationException) {
            String errorMessage = cause.getMessage();

            // MySQL 8.0的错误信息格式
            Pattern pattern = Pattern.compile("Duplicate entry '(.*?)' for key '(.+?\\.)?(.+?)'");
            Matcher matcher = pattern.matcher(errorMessage);

            if (matcher.find()) {
                String constraintName = matcher.group(3); // 获取约束名（不含表名前缀）
                String duplicateValue = matcher.group(1);

                // 解析复合唯一约束涉及的字段
                if (constraintName.startsWith("unique_")) {
                    // 示例：unique_position_name_level -> 提取name和level
                    String[] parts = constraintName.split("_");
                    if (parts.length >= 3) {
                        // 获取实体类名（如position）
                        String entityName = parts[1];

                        try {
                            // 构建实体类全路径
                            String className = "cn.edu.ncu.hrms.common.entity." +
                                    entityName.substring(0, 1).toUpperCase() +
                                    entityName.substring(1);
                            Class<?> entityClass = Class.forName(className);

                            // 收集约束涉及的字段
                            List<String> displayNames = new ArrayList<>();
                            for (int i = 2; i < parts.length; i++) {
                                String fieldName = parts[i];
                                /*if (!"del".equals(fieldName) && !"flag".equals(fieldName) &&
                                        !fieldName.contains("del")) {
                                    displayNames.add(getFieldDisplayName(entityClass, fieldName));
                                }*/
                                displayNames.add(getFieldDisplayName(entityClass, fieldName));
                            }

                            if (!displayNames.isEmpty()) {
                                String fieldsDesc = String.join("&", displayNames);
                                return Result.error("【" + fieldsDesc + "】存在相同的数据");
                            }
                        } catch (ClassNotFoundException ex) {
                            log.warn("无法找到实体类: {}", entityName, ex);
                        }
                    }
                }

                // 默认处理简单唯一约束
                String columnName = constraintName.replaceAll("^(unique_)?", "");
                return Result.error("数据冲突，字段 '" + columnName + "' 的值 '" + duplicateValue + "' 已存在");
            }
        }
        return Result.error("数据已存在，请检查输入内容");
    }

    /**
     * 数据库非空约束异常处理（增强版）
     */
    @ExceptionHandler(SQLIntegrityConstraintViolationException.class)
    public Result<Void> handleNotNullConstraintViolationException(SQLIntegrityConstraintViolationException e) {
        log.error("数据库非空约束异常: {}", e.getMessage());

        // MySQL非空约束错误码为1048
        if (e.getErrorCode() == 1048) {
            String errorMessage = e.getMessage();

            // 解析表名和字段名
            Pattern tablePattern = Pattern.compile("Table '(.+?\\.)?(.+?)'");
            Pattern columnPattern = Pattern.compile("Column '(.+?)' cannot be null");

            Matcher tableMatcher = tablePattern.matcher(errorMessage);
            Matcher columnMatcher = columnPattern.matcher(errorMessage);

            if (tableMatcher.find() && columnMatcher.find()) {
                String tableName = tableMatcher.group(2);
                String columnName = columnMatcher.group(1);

                try {
                    // 构建实体类名（根据表名转换，如position -> Position）
                    String entityName = tableName.substring(0, 1).toUpperCase() +
                            tableName.substring(1).toLowerCase();

                    // 构建实体类全路径（根据你的配置调整包路径）
                    String className = "cn.edu.ncu.hrms.common.entity." + entityName;
                    Class<?> entityClass = Class.forName(className);

                    // 获取字段显示名
                    String displayName = getFieldDisplayName(entityClass, columnName);
                    return Result.error("【" + displayName + "】不能为空");

                } catch (ClassNotFoundException ex) {
                    log.warn("无法找到实体类: {}", tableName, ex);
                    return Result.error("字段 '" + columnName + "' 不能为空");
                }
            }
        }
        return Result.error("数据库操作异常，请检查必填字段");
    }



    /**
     * 获取实体类字段的友好显示名称
     *
     * 该方法优先返回字段上的@ApiModelProperty注解中的value值作为显示名称，
     * 如果没有该注解则返回字段名本身
     *
     * @param entityClass 实体类的Class对象，不能为null
     * @param fieldName 要获取显示名称的字段名，不能为null或空
     * @return 字段的友好显示名称（优先使用@ApiModelProperty的value值，
     *         若无注解则返回原始字段名）
     *
     * @throws IllegalArgumentException 如果参数entityClass或fieldName为null
     *
     * 设计说明：
     * 1. 使用反射获取字段信息，性能开销在可接受范围内
     * 2. 捕获NoSuchFieldException保证方法健壮性
     * 3. 与Swagger的@ApiModelProperty注解集成，保持与API文档的一致性
     */
    private String getFieldDisplayName(Class<?> entityClass, String fieldName) {
        // 参数校验
        if (entityClass == null || fieldName == null || fieldName.isEmpty()) {
            log.error("参数entityClass和fieldName不能为null或空");
            return fieldName;
        }

        try {
            // 通过反射获取字段对象
            // getDeclaredField()可以获取类中声明的所有字段（包括private）
            Field field = entityClass.getDeclaredField(fieldName);

            // 获取字段上的@ApiModelProperty注解
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);

            // 如果存在注解且注解value不为空，则返回注解值
            if (annotation != null && !annotation.value().isEmpty()) {
                return annotation.value();
            }

            // 没有有效注解时返回原始字段名
            return fieldName;

        } catch (NoSuchFieldException e) {
            // 防御性编程：如果字段不存在（可能由于拼写错误或类版本不一致）
            // 返回原始字段名保证方法总有返回值
            return fieldName;
        }

    }
} 