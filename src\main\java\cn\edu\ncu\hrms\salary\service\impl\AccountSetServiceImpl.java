package cn.edu.ncu.hrms.salary.service.impl;

import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.salary.vo.AccountSetVO;
import cn.edu.ncu.hrms.salary.dto.AccountSetPageQueryDTO;
import cn.edu.ncu.hrms.salary.dto.AccountSetAddDTO;
import cn.edu.ncu.hrms.salary.dto.AccountSetUpdateDTO;
import cn.edu.ncu.hrms.common.entity.AccountSet;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.salary.mapper.AccountSetMapper;
import cn.edu.ncu.hrms.salary.service.AccountSetService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 薪资账套服务实现类
 */
@Service
@Slf4j
public class AccountSetServiceImpl implements AccountSetService {

    private static final Logger logger = LoggerFactory.getLogger(AccountSetServiceImpl.class);

    @Autowired
    private AccountSetMapper accountSetMapper;

    @Autowired
    private PositionMapper positionMapper;

    @Override
    @Transactional
    public Result<Void> addAccountSet(AccountSetAddDTO addDTO) {
        logger.info("新增账套，名称: {}", addDTO.getName());
        AccountSet exist = accountSetMapper.selectByName(addDTO.getName());
        if (exist != null) {
            logger.warn("新增账套失败：名称已存在 '{}'", addDTO.getName());
            return Result.error("账套名称已存在");
        }
        AccountSet accountSet = new AccountSet();
        accountSet.setName(addDTO.getName());
        accountSet.setBaseSalary(addDTO.getBaseSalary());
        accountSet.setPerformanceRate(addDTO.getPerformanceRate());
        accountSet.setInsuranceRate(addDTO.getInsuranceRate());

        LocalDateTime now = LocalDateTime.now();
        accountSet.setCreateTime(now);
        accountSet.setUpdateTime(now);
        accountSet.setDelFlag(false);

        int result = accountSetMapper.insert(accountSet);
        if (result > 0) {
            logger.info("账套新增成功: {}", accountSet.getName());
            return Result.success();
        } else {
            logger.error("账套新增失败: {}", addDTO.getName());
            return Result.error("新增失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Result<Void> updateAccountSet(Integer id, AccountSetUpdateDTO updateDTO) {
        logger.info("更新账套，ID: {}", id);
        updateDTO.setId(id);
        AccountSet existingAccountSet = accountSetMapper.selectById(id);
        if (existingAccountSet == null || existingAccountSet.getDelFlag()) {
            logger.warn("未找到或已删除的账套，ID: {}", id);
            return Result.error("账套不存在或已被删除");
        }

        AccountSet accountSetToUpdate = new AccountSet();
        BeanUtils.copyProperties(updateDTO, accountSetToUpdate);

        int result = accountSetMapper.update(accountSetToUpdate);

        if (result > 0) {
            logger.info("账套更新成功: {}", updateDTO.getName());
            return Result.success();
        } else {
            logger.error("账套更新失败，ID: {}", id);
            return Result.error("更新失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteAccountSet(Integer id) {
        logger.info("删除账套，ID: {}", id);
        AccountSet existingAccountSet = accountSetMapper.selectById(id);
        if (existingAccountSet == null || existingAccountSet.getDelFlag()) {
            logger.warn("未找到或已删除的账套，ID: {}", id);
            return Result.success(null, "账套不存在或已被删除");
        }

         if (positionMapper.selectByAccountId(id).size() > 0) {
             logger.warn("无法删除账套 ID: {}, 因为它被职位引用.", id);
             return Result.error("该账套与职位关联，请先解除关联");
         }

        int result = accountSetMapper.deleteById(id);

        if (result > 0) {
            logger.info("账套已标记为删除，ID: {}", id);
            return Result.success();
        } else {
            logger.error("删除账套失败，ID: {}. 可能原因：Mapper 问题?", id);
            return Result.error("系统异常，请联系管理员");
        }
    }

    @Override
    public Result<AccountSetVO> getAccountSetVOById(Integer id) {
        logger.debug("查询账套详情，ID: {}", id);
        AccountSet accountSet = accountSetMapper.selectById(id);
        if (accountSet == null || accountSet.getDelFlag()) {
            logger.warn("未找到或已删除的账套，ID: {}", id);
            return Result.error("未查询到有效账套");
        }
        AccountSetVO vo = convertToAccountSetVO(accountSet);
        logger.debug("成功查询账套详情，ID: {}", id);
        return Result.success(vo);
    }

    @Override
    public Result<PageInfo<AccountSetVO>> pageAccountSets(AccountSetPageQueryDTO queryDTO) {
        logger.info("分页查询账套，条件: {}", queryDTO);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<AccountSet> accountSets = accountSetMapper.selectByCondition(queryDTO);
        PageInfo<AccountSet> pageInfo = new PageInfo<>(accountSets);

        List<AccountSetVO> voList = accountSets.stream()
                .map(this::convertToAccountSetVO)
                .collect(Collectors.toList());

        PageInfo<AccountSetVO> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        voPageInfo.setList(voList);

        logger.info("账套分页查询成功，总条数: {}", voPageInfo.getTotal());
        return Result.success(voPageInfo);
    }

    @Override
    public Result<List<AccountSetVO>> listAllAccountSets() {
        logger.info("查询所有有效账套");
        List<AccountSet> accountSets = accountSetMapper.selectList();
        List<AccountSetVO> voList = accountSets.stream()
                .map(entity -> {
                    AccountSetVO vo = new AccountSetVO();
                    vo.setId(entity.getId());
                    vo.setName(entity.getName());
                    return vo;
                })
                .collect(Collectors.toList());
        logger.info("查询到 {} 个有效账套", voList.size());
        return Result.success(voList);
    }

    // --- 私有辅助方法 ---

    private AccountSetVO convertToAccountSetVO(AccountSet accountSet) {
        if (accountSet == null) {
            return null;
        }
        AccountSetVO vo = new AccountSetVO();
        BeanUtils.copyProperties(accountSet, vo);
        return vo;
    }
}
