{"ast": null, "code": "/**\n * Firefox浏览器兼容性处理工具\n * 专门解决Firefox在加载高德地图API时的兼容性问题\n */\n\n// 检测是否为Firefox浏览器\nexport function isFirefox() {\n  return navigator.userAgent.toLowerCase().indexOf('firefox') > -1;\n}\n\n// Firefox专用的高德地图API加载器\nexport function loadAmapForFirefox() {\n  return new Promise((resolve, reject) => {\n    // 如果不是Firefox，直接解析\n    if (!isFirefox()) {\n      resolve();\n      return;\n    }\n\n    // 如果AMap已经存在，直接解析\n    if (window.AMap) {\n      resolve();\n      return;\n    }\n    console.log('检测到Firefox浏览器，使用兼容性加载方案');\n\n    // 创建一个临时的错误处理器\n    const originalErrorHandler = window.onerror;\n    const originalUnhandledRejection = window.onunhandledrejection;\n\n    // 临时屏蔽高德地图相关错误\n    window.onerror = function (message, source, lineno, colno, error) {\n      if (source && (source.includes('webapi.amap.com') || source.includes('count?type=UIInit'))) {\n        console.warn('Firefox: 已忽略高德地图统计错误');\n        return true;\n      }\n      if (originalErrorHandler) {\n        return originalErrorHandler.call(this, message, source, lineno, colno, error);\n      }\n    };\n    window.onunhandledrejection = function (event) {\n      if (event.reason && event.reason.toString().includes('amap')) {\n        console.warn('Firefox: 已忽略高德地图Promise错误');\n        event.preventDefault();\n        return;\n      }\n      if (originalUnhandledRejection) {\n        return originalUnhandledRejection.call(this, event);\n      }\n    };\n\n    // 延迟加载高德地图API\n    setTimeout(() => {\n      const script = document.createElement('script');\n      script.type = 'text/javascript';\n      script.src = 'https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662&plugin=AMap.Scale,AMap.ToolBar,AMap.Geocoder,AMap.Geolocation';\n      script.async = true;\n      script.onload = () => {\n        console.log('Firefox: 高德地图API加载成功');\n        // 恢复原始错误处理器\n        setTimeout(() => {\n          window.onerror = originalErrorHandler;\n          window.onunhandledrejection = originalUnhandledRejection;\n        }, 2000);\n        resolve();\n      };\n      script.onerror = () => {\n        console.error('Firefox: 高德地图API加载失败');\n        // 恢复原始错误处理器\n        window.onerror = originalErrorHandler;\n        window.onunhandledrejection = originalUnhandledRejection;\n        reject(new Error('高德地图API加载失败'));\n      };\n      document.head.appendChild(script);\n    }, 500);\n  });\n}\n\n// Firefox专用的错误抑制器\nexport function suppressAmapErrorsForFirefox() {\n  if (!isFirefox()) {\n    return;\n  }\n\n  // 监听全局错误事件\n  window.addEventListener('error', function (event) {\n    const target = event.target || event.srcElement;\n\n    // 检查是否是高德地图相关的脚本错误\n    if (target && target.src && target.src.includes('webapi.amap.com')) {\n      console.warn('Firefox: 已抑制高德地图脚本错误');\n      event.preventDefault();\n      event.stopPropagation();\n      return false;\n    }\n\n    // 检查错误消息是否包含高德地图相关内容\n    if (event.message && (event.message.includes('webapi.amap.com') || event.message.includes('count?type=UIInit') || event.message.includes('AMap') || event.message.includes('amap'))) {\n      console.warn('Firefox: 已抑制高德地图相关错误:', event.message);\n      event.preventDefault();\n      event.stopPropagation();\n      return false;\n    }\n  }, true);\n\n  // 监听未处理的Promise拒绝\n  window.addEventListener('unhandledrejection', function (event) {\n    if (event.reason && event.reason.toString().toLowerCase().includes('amap')) {\n      console.warn('Firefox: 已抑制高德地图Promise错误:', event.reason);\n      event.preventDefault();\n    }\n  });\n  console.log('Firefox: 高德地图错误抑制器已启动');\n}\n\n// 初始化Firefox兼容性处理\nexport function initFirefoxCompatibility() {\n  if (isFirefox()) {\n    console.log('检测到Firefox浏览器，启动兼容性处理');\n    suppressAmapErrorsForFirefox();\n  }\n}\n\n// 检查高德地图API是否可用\nexport function checkAmapAvailability() {\n  return new Promise(resolve => {\n    const maxAttempts = 10;\n    let attempts = 0;\n    const checkInterval = setInterval(() => {\n      attempts++;\n      if (window.AMap) {\n        clearInterval(checkInterval);\n        resolve(true);\n        return;\n      }\n      if (attempts >= maxAttempts) {\n        clearInterval(checkInterval);\n        console.warn('高德地图API检查超时');\n        resolve(false);\n      }\n    }, 500);\n  });\n}", "map": {"version": 3, "names": ["isFirefox", "navigator", "userAgent", "toLowerCase", "indexOf", "loadAmapForFirefox", "Promise", "resolve", "reject", "window", "AMap", "console", "log", "originalError<PERSON><PERSON><PERSON>", "onerror", "originalUnhandledRejection", "onunhandledrejection", "message", "source", "lineno", "colno", "error", "includes", "warn", "call", "event", "reason", "toString", "preventDefault", "setTimeout", "script", "document", "createElement", "type", "src", "async", "onload", "Error", "head", "append<PERSON><PERSON><PERSON>", "suppressAmapErrorsForFirefox", "addEventListener", "target", "srcElement", "stopPropagation", "initFirefoxCompatibility", "checkAmapAvailability", "maxAttempts", "attempts", "checkInterval", "setInterval", "clearInterval"], "sources": ["D:/Desktop/GP/HRMS/client/src/utils/firefox-compatibility.js"], "sourcesContent": ["/**\n * Firefox浏览器兼容性处理工具\n * 专门解决Firefox在加载高德地图API时的兼容性问题\n */\n\n// 检测是否为Firefox浏览器\nexport function isFirefox() {\n  return navigator.userAgent.toLowerCase().indexOf('firefox') > -1\n}\n\n// Firefox专用的高德地图API加载器\nexport function loadAmapForFirefox() {\n  return new Promise((resolve, reject) => {\n    // 如果不是Firefox，直接解析\n    if (!isFirefox()) {\n      resolve()\n      return\n    }\n\n    // 如果AMap已经存在，直接解析\n    if (window.AMap) {\n      resolve()\n      return\n    }\n\n    console.log('检测到Firefox浏览器，使用兼容性加载方案')\n\n    // 创建一个临时的错误处理器\n    const originalErrorHandler = window.onerror\n    const originalUnhandledRejection = window.onunhandledrejection\n\n    // 临时屏蔽高德地图相关错误\n    window.onerror = function(message, source, lineno, colno, error) {\n      if (source && (source.includes('webapi.amap.com') || source.includes('count?type=UIInit'))) {\n        console.warn('Firefox: 已忽略高德地图统计错误')\n        return true\n      }\n      if (originalErrorHandler) {\n        return originalErrorHandler.call(this, message, source, lineno, colno, error)\n      }\n    }\n\n    window.onunhandledrejection = function(event) {\n      if (event.reason && event.reason.toString().includes('amap')) {\n        console.warn('Firefox: 已忽略高德地图Promise错误')\n        event.preventDefault()\n        return\n      }\n      if (originalUnhandledRejection) {\n        return originalUnhandledRejection.call(this, event)\n      }\n    }\n\n    // 延迟加载高德地图API\n    setTimeout(() => {\n      const script = document.createElement('script')\n      script.type = 'text/javascript'\n      script.src = 'https://webapi.amap.com/maps?v=2.0&key=a3299b78709563deb62714fce9c1b662&plugin=AMap.Scale,AMap.ToolBar,AMap.Geocoder,AMap.Geolocation'\n      script.async = true\n      \n      script.onload = () => {\n        console.log('Firefox: 高德地图API加载成功')\n        // 恢复原始错误处理器\n        setTimeout(() => {\n          window.onerror = originalErrorHandler\n          window.onunhandledrejection = originalUnhandledRejection\n        }, 2000)\n        resolve()\n      }\n\n      script.onerror = () => {\n        console.error('Firefox: 高德地图API加载失败')\n        // 恢复原始错误处理器\n        window.onerror = originalErrorHandler\n        window.onunhandledrejection = originalUnhandledRejection\n        reject(new Error('高德地图API加载失败'))\n      }\n\n      document.head.appendChild(script)\n    }, 500)\n  })\n}\n\n// Firefox专用的错误抑制器\nexport function suppressAmapErrorsForFirefox() {\n  if (!isFirefox()) {\n    return\n  }\n\n  // 监听全局错误事件\n  window.addEventListener('error', function(event) {\n    const target = event.target || event.srcElement\n    \n    // 检查是否是高德地图相关的脚本错误\n    if (target && target.src && target.src.includes('webapi.amap.com')) {\n      console.warn('Firefox: 已抑制高德地图脚本错误')\n      event.preventDefault()\n      event.stopPropagation()\n      return false\n    }\n\n    // 检查错误消息是否包含高德地图相关内容\n    if (event.message && (\n      event.message.includes('webapi.amap.com') ||\n      event.message.includes('count?type=UIInit') ||\n      event.message.includes('AMap') ||\n      event.message.includes('amap')\n    )) {\n      console.warn('Firefox: 已抑制高德地图相关错误:', event.message)\n      event.preventDefault()\n      event.stopPropagation()\n      return false\n    }\n  }, true)\n\n  // 监听未处理的Promise拒绝\n  window.addEventListener('unhandledrejection', function(event) {\n    if (event.reason && event.reason.toString().toLowerCase().includes('amap')) {\n      console.warn('Firefox: 已抑制高德地图Promise错误:', event.reason)\n      event.preventDefault()\n    }\n  })\n\n  console.log('Firefox: 高德地图错误抑制器已启动')\n}\n\n// 初始化Firefox兼容性处理\nexport function initFirefoxCompatibility() {\n  if (isFirefox()) {\n    console.log('检测到Firefox浏览器，启动兼容性处理')\n    suppressAmapErrorsForFirefox()\n  }\n}\n\n// 检查高德地图API是否可用\nexport function checkAmapAvailability() {\n  return new Promise((resolve) => {\n    const maxAttempts = 10\n    let attempts = 0\n\n    const checkInterval = setInterval(() => {\n      attempts++\n      \n      if (window.AMap) {\n        clearInterval(checkInterval)\n        resolve(true)\n        return\n      }\n\n      if (attempts >= maxAttempts) {\n        clearInterval(checkInterval)\n        console.warn('高德地图API检查超时')\n        resolve(false)\n      }\n    }, 500)\n  })\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASA,SAASA,CAAA,EAAG;EAC1B,OAAOC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAClE;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACnC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC;IACA,IAAI,CAACR,SAAS,CAAC,CAAC,EAAE;MAChBO,OAAO,CAAC,CAAC;MACT;IACF;;IAEA;IACA,IAAIE,MAAM,CAACC,IAAI,EAAE;MACfH,OAAO,CAAC,CAAC;MACT;IACF;IAEAI,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;;IAEtC;IACA,MAAMC,oBAAoB,GAAGJ,MAAM,CAACK,OAAO;IAC3C,MAAMC,0BAA0B,GAAGN,MAAM,CAACO,oBAAoB;;IAE9D;IACAP,MAAM,CAACK,OAAO,GAAG,UAASG,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;MAC/D,IAAIH,MAAM,KAAKA,MAAM,CAACI,QAAQ,CAAC,iBAAiB,CAAC,IAAIJ,MAAM,CAACI,QAAQ,CAAC,mBAAmB,CAAC,CAAC,EAAE;QAC1FX,OAAO,CAACY,IAAI,CAAC,sBAAsB,CAAC;QACpC,OAAO,IAAI;MACb;MACA,IAAIV,oBAAoB,EAAE;QACxB,OAAOA,oBAAoB,CAACW,IAAI,CAAC,IAAI,EAAEP,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,CAAC;MAC/E;IACF,CAAC;IAEDZ,MAAM,CAACO,oBAAoB,GAAG,UAASS,KAAK,EAAE;MAC5C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAACL,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC5DX,OAAO,CAACY,IAAI,CAAC,2BAA2B,CAAC;QACzCE,KAAK,CAACG,cAAc,CAAC,CAAC;QACtB;MACF;MACA,IAAIb,0BAA0B,EAAE;QAC9B,OAAOA,0BAA0B,CAACS,IAAI,CAAC,IAAI,EAAEC,KAAK,CAAC;MACrD;IACF,CAAC;;IAED;IACAI,UAAU,CAAC,MAAM;MACf,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,IAAI,GAAG,iBAAiB;MAC/BH,MAAM,CAACI,GAAG,GAAG,uIAAuI;MACpJJ,MAAM,CAACK,KAAK,GAAG,IAAI;MAEnBL,MAAM,CAACM,MAAM,GAAG,MAAM;QACpBzB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnC;QACAiB,UAAU,CAAC,MAAM;UACfpB,MAAM,CAACK,OAAO,GAAGD,oBAAoB;UACrCJ,MAAM,CAACO,oBAAoB,GAAGD,0BAA0B;QAC1D,CAAC,EAAE,IAAI,CAAC;QACRR,OAAO,CAAC,CAAC;MACX,CAAC;MAEDuB,MAAM,CAAChB,OAAO,GAAG,MAAM;QACrBH,OAAO,CAACU,KAAK,CAAC,sBAAsB,CAAC;QACrC;QACAZ,MAAM,CAACK,OAAO,GAAGD,oBAAoB;QACrCJ,MAAM,CAACO,oBAAoB,GAAGD,0BAA0B;QACxDP,MAAM,CAAC,IAAI6B,KAAK,CAAC,aAAa,CAAC,CAAC;MAClC,CAAC;MAEDN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,MAAM,CAAC;IACnC,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,4BAA4BA,CAAA,EAAG;EAC7C,IAAI,CAACxC,SAAS,CAAC,CAAC,EAAE;IAChB;EACF;;EAEA;EACAS,MAAM,CAACgC,gBAAgB,CAAC,OAAO,EAAE,UAAShB,KAAK,EAAE;IAC/C,MAAMiB,MAAM,GAAGjB,KAAK,CAACiB,MAAM,IAAIjB,KAAK,CAACkB,UAAU;;IAE/C;IACA,IAAID,MAAM,IAAIA,MAAM,CAACR,GAAG,IAAIQ,MAAM,CAACR,GAAG,CAACZ,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MAClEX,OAAO,CAACY,IAAI,CAAC,sBAAsB,CAAC;MACpCE,KAAK,CAACG,cAAc,CAAC,CAAC;MACtBH,KAAK,CAACmB,eAAe,CAAC,CAAC;MACvB,OAAO,KAAK;IACd;;IAEA;IACA,IAAInB,KAAK,CAACR,OAAO,KACfQ,KAAK,CAACR,OAAO,CAACK,QAAQ,CAAC,iBAAiB,CAAC,IACzCG,KAAK,CAACR,OAAO,CAACK,QAAQ,CAAC,mBAAmB,CAAC,IAC3CG,KAAK,CAACR,OAAO,CAACK,QAAQ,CAAC,MAAM,CAAC,IAC9BG,KAAK,CAACR,OAAO,CAACK,QAAQ,CAAC,MAAM,CAAC,CAC/B,EAAE;MACDX,OAAO,CAACY,IAAI,CAAC,uBAAuB,EAAEE,KAAK,CAACR,OAAO,CAAC;MACpDQ,KAAK,CAACG,cAAc,CAAC,CAAC;MACtBH,KAAK,CAACmB,eAAe,CAAC,CAAC;MACvB,OAAO,KAAK;IACd;EACF,CAAC,EAAE,IAAI,CAAC;;EAER;EACAnC,MAAM,CAACgC,gBAAgB,CAAC,oBAAoB,EAAE,UAAShB,KAAK,EAAE;IAC5D,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAACxB,WAAW,CAAC,CAAC,CAACmB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1EX,OAAO,CAACY,IAAI,CAAC,4BAA4B,EAAEE,KAAK,CAACC,MAAM,CAAC;MACxDD,KAAK,CAACG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EAEFjB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;AACtC;;AAEA;AACA,OAAO,SAASiC,wBAAwBA,CAAA,EAAG;EACzC,IAAI7C,SAAS,CAAC,CAAC,EAAE;IACfW,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC4B,4BAA4B,CAAC,CAAC;EAChC;AACF;;AAEA;AACA,OAAO,SAASM,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAIxC,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMwC,WAAW,GAAG,EAAE;IACtB,IAAIC,QAAQ,GAAG,CAAC;IAEhB,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCF,QAAQ,EAAE;MAEV,IAAIvC,MAAM,CAACC,IAAI,EAAE;QACfyC,aAAa,CAACF,aAAa,CAAC;QAC5B1C,OAAO,CAAC,IAAI,CAAC;QACb;MACF;MAEA,IAAIyC,QAAQ,IAAID,WAAW,EAAE;QAC3BI,aAAa,CAACF,aAAa,CAAC;QAC5BtC,OAAO,CAACY,IAAI,CAAC,aAAa,CAAC;QAC3BhB,OAAO,CAAC,KAAK,CAAC;MAChB;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}