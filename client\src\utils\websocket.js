/**
 * WebSocket客户端服务
 * 用于与后端WebSocket服务器建立连接，接收实时消息
 */

import store from '@/store'
import { Message, Notification } from 'element-ui'

class WebSocketClient {
  constructor() {
    this.socket = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000 // 3秒
    this.listeners = {}
    this.connected = false
    this.connecting = false
    this.token = null // 添加token属性
    this.heartbeatTimer = null // 心跳定时器
    this.heartbeatInterval = 30000 // 心跳间隔30秒
  }

  /**
   * 连接WebSocket服务器
   */
  connect() {
    if (this.connected || this.connecting) {
      console.log('WebSocket已连接或正在连接中')
      return
    }

    // 清除之前的心跳定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    this.connecting = true
    // 尝试多种方式获取用户ID
    let userId = null;

    // 1. 从store.getters.userId获取
    if (store.getters.userId) {
      userId = store.getters.userId;
      console.log('从store.getters.userId获取用户ID:', userId);
    }
    // 2. 从store.state.user.userInfo获取
    else if (store.state.user && store.state.user.userInfo && store.state.user.userInfo.id) {
      userId = store.state.user.userInfo.id;
      console.log('从store.state.user.userInfo获取用户ID:', userId);
    }
    // 3. 从store.state.userInfo获取
    else if (store.state.userInfo && store.state.userInfo.userInfo && store.state.userInfo.userInfo.id) {
      userId = store.state.userInfo.userInfo.id;
      console.log('从store.state.userInfo.userInfo获取用户ID:', userId);
    }
    // 4. 从localStorage获取
    else if (localStorage.getItem('userInfo')) {
      try {
        const userInfoStr = localStorage.getItem('userInfo');
        const userInfo = JSON.parse(userInfoStr);
        if (userInfo && userInfo.id) {
          userId = userInfo.id;
          console.log('从localStorage获取用户ID:', userId);
        }
      } catch (e) {
        console.error('解析localStorage中的userInfo失败:', e);
      }
    }

    // 如果仍然没有获取到用户ID，尝试从token中解析
    if (!userId && localStorage.getItem('token')) {
      try {
        const token = localStorage.getItem('token');
        // 解析JWT token (格式: header.payload.signature)
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        const payload = JSON.parse(jsonPayload);
        if (payload && payload.id) {
          userId = payload.id;
          console.log('从JWT token解析获取用户ID:', userId);
        }
      } catch (e) {
        console.error('解析token失败:', e);
      }
    }

    // 如果所有方法都失败，使用固定ID (仅用于测试)
    if (!userId) {
      // 使用固定ID作为最后的备选方案
      userId = 11; // 根据错误信息，使用11作为固定ID
      console.warn('无法获取用户ID，使用固定ID:', userId);
    }

    // 获取当前域名和端口，构建WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    const wsUrl = `${protocol}//${host}/api/ws/${userId}`

    console.log(`正在连接WebSocket: ${wsUrl}`)

    try {
      // 获取token用于认证
      const token = localStorage.getItem('token')
      this.token = token // 保存到实例变量，以便在其他地方使用

      // 创建WebSocket连接，使用原始URL（不添加token作为查询参数）
      // WebSocket不支持自定义HTTP头，所以我们需要在连接建立后通过消息发送token
      this.socket = new WebSocket(wsUrl)

      // 记录连接信息
      console.log(`WebSocket连接URL: ${wsUrl}`)

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (!this.connected) {
          console.warn('WebSocket连接超时，关闭连接')
          this.socket.close()
          this.connecting = false
        }
      }, 5000) // 5秒超时

      // 连接建立时的回调
      this.socket.onopen = () => {
        console.log('WebSocket连接已建立')
        this.connected = true
        this.connecting = false
        this.reconnectAttempts = 0

        // 清除连接超时定时器
        clearTimeout(connectionTimeout)

        // 发送认证消息
        if (this.token) {
          try {
            // 发送认证消息，包含完整的Bearer token
            this.socket.send(JSON.stringify({
              type: 'AUTH',
              token: `Bearer ${this.token}`
            }))
            console.log('已发送WebSocket认证消息')

            // 延迟500ms后再次发送一个心跳消息，确保连接正常
            setTimeout(() => {
              if (this.connected) {
                try {
                  this.socket.send(JSON.stringify({
                    type: 'PING',
                    timestamp: new Date().getTime()
                  }))
                  console.log('已发送WebSocket心跳消息')
                } catch (err) {
                  console.error('发送WebSocket心跳消息失败:', err)
                }
              }
            }, 500)

            // 设置定期发送心跳
            this.heartbeatTimer = setInterval(() => {
              if (this.connected) {
                try {
                  this.socket.send(JSON.stringify({
                    type: 'PING',
                    timestamp: new Date().getTime()
                  }))
                  console.log('已发送WebSocket定期心跳消息')
                } catch (err) {
                  console.error('发送WebSocket定期心跳消息失败:', err)
                  // 如果发送失败，可能连接已断开，尝试重连
                  clearInterval(this.heartbeatTimer)
                  this.heartbeatTimer = null
                  this.connected = false
                  this.connect()
                }
              } else {
                // 如果连接已断开，清除心跳定时器
                clearInterval(this.heartbeatTimer)
                this.heartbeatTimer = null
              }
            }, this.heartbeatInterval)
          } catch (e) {
            console.error('发送WebSocket认证消息失败:', e)
          }
        }
      }

      // 接收消息的回调
      this.socket.onmessage = this.handleMessage.bind(this)

      // 连接错误的回调
      this.socket.onerror = this.handleError.bind(this)

      // 连接关闭的回调
      this.socket.onclose = this.handleClose.bind(this)

      // 连接关闭的回调已在上面设置

      // 连接错误的回调已在上面设置
    } catch (error) {
      console.error('WebSocket初始化错误:', error)
      console.error('WebSocket初始化失败，URL:', wsUrl)
      console.error('用户ID:', userId)
      console.error('Token存在:', !!this.token)
      this.connecting = false

      // 如果是因为URL问题，尝试使用不同的URL格式
      if (error.message && error.message.includes('Invalid URL')) {
        console.log('尝试使用替代URL格式重新连接')
        try {
          // 尝试不同的URL格式 - 使用查询参数而不是路径参数
          const alternativeUrl = `${protocol}//${host}/api/ws?uid=${userId}`
          console.log(`尝试替代URL: ${alternativeUrl}`)

          // 不在URL中添加token，而是在连接后通过消息发送
          this.socket = new WebSocket(alternativeUrl)
          console.log('使用替代URL创建WebSocket连接成功')

          // 设置连接建立后的回调
          this.socket.onopen = () => {
            console.log('替代WebSocket连接已建立')
            this.connected = true
            this.connecting = false
            this.reconnectAttempts = 0

            // 发送认证消息
            if (this.token) {
              try {
                this.socket.send(JSON.stringify({
                  type: 'AUTH',
                  token: `Bearer ${this.token}`
                }))
                console.log('已通过替代连接发送WebSocket认证消息')
              } catch (e) {
                console.error('通过替代连接发送WebSocket认证消息失败:', e)
              }
            }
          }

          // 设置其他回调
          this.socket.onmessage = this.handleMessage.bind(this)
          this.socket.onerror = this.handleError.bind(this)
          this.socket.onclose = this.handleClose.bind(this)

        } catch (e) {
          console.error('使用替代URL创建WebSocket连接失败:', e)
          this.connecting = false
        }
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data)
      console.log('WebSocket收到消息:', data)

      // 处理认证响应
      if (data.type === 'AUTH_RESPONSE') {
        if (data.success) {
          console.log('WebSocket认证成功')
        } else {
          console.error('WebSocket认证失败:', data.message)
        }
        return
      }

      // 处理心跳响应
      if (data.type === 'PONG') {
        console.log('收到WebSocket心跳响应')
        return
      }

      // 根据消息类型分发给不同的监听器
      if (data.type && this.listeners[data.type]) {
        this.listeners[data.type].forEach(callback => {
          try {
            callback(data)
          } catch (err) {
            console.error('WebSocket消息处理错误:', err)
          }
        })
      }

      // 处理导入进度消息
      if (data.type === 'IMPORT_PROGRESS') {
        // 可以在这里添加全局进度处理逻辑
      }

      // 处理导入结果消息
      if (data.type === 'IMPORT_RESULT') {
        // 处理rowErrorMap - 确保它是普通JavaScript对象
        if (data.rowErrorMap) {
          try {
            // 使用JSON序列化和反序列化来去除Vue响应式
            const jsonString = JSON.stringify(data.rowErrorMap)
            data.rowErrorMap = JSON.parse(jsonString)
            console.log('WebSocket: 处理后的rowErrorMap:', data.rowErrorMap)
          } catch (e) {
            console.error('WebSocket: 处理rowErrorMap失败:', e)
          }
        }

        // 根据状态显示不同类型的通知
        const notificationType =
          data.status === 'SUCCESS' ? 'success' :
          data.status === 'PARTIAL_SUCCESS' ? 'warning' : 'error'

        Notification({
          title: data.status === 'SUCCESS' ? '导入成功' :
                data.status === 'PARTIAL_SUCCESS' ? '部分导入成功' : '导入失败',
          message: data.message,
          type: notificationType,
          duration: 5000
        })
      }
    } catch (err) {
      console.error('WebSocket消息解析错误:', err, event.data)
    }
  }

  /**
   * 处理连接错误
   */
  handleError(error) {
    console.error('WebSocket连接错误:', error)
    this.connecting = false

    // 记录详细错误信息
    console.error('WebSocket连接失败')
    console.error('Token存在:', !!this.token)
  }

  /**
   * 处理连接关闭
   */
  handleClose(event) {
    console.log('WebSocket连接已关闭', event)
    this.connected = false
    this.connecting = false

    // 清除心跳定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    // 记录关闭原因
    if (event.code) {
      console.log(`WebSocket关闭代码: ${event.code}, 原因: ${event.reason || '未提供'}`)
    }

    // 尝试重新连接
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`WebSocket将在${this.reconnectInterval / 1000}秒后尝试第${this.reconnectAttempts}次重连`)
      setTimeout(() => this.connect(), this.reconnectInterval)
    } else {
      console.warn('WebSocket重连次数已达上限，不再尝试重连')
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    // 清除心跳定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    if (this.socket && (this.connected || this.connecting)) {
      this.socket.close()
      this.connected = false
      this.connecting = false
      console.log('WebSocket连接已断开')
    }
  }

  /**
   * 添加消息类型监听器
   * @param {string} type 消息类型
   * @param {Function} callback 回调函数
   */
  addListener(type, callback) {
    if (!this.listeners[type]) {
      this.listeners[type] = []
    }
    this.listeners[type].push(callback)
    return () => this.removeListener(type, callback) // 返回移除监听器的函数
  }

  /**
   * 移除消息类型监听器
   * @param {string} type 消息类型
   * @param {Function} callback 回调函数
   */
  removeListener(type, callback) {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type].filter(cb => cb !== callback)
    }
  }

  /**
   * 检查WebSocket是否已连接
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.connected
  }
}

// 创建单例
const webSocketClient = new WebSocketClient()

export default webSocketClient
