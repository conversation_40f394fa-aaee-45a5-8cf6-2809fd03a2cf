package cn.edu.ncu.hrms.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 用户密码重置数据传输对象 (DTO)
 */
@Data
@ApiModel(value="UserPasswordResetDTO", description="用户密码重置数据传输对象")
public class UserPasswordResetDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "新密码", required = true, notes = "密码将在后端加密存储")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码长度必须在6到20之间")
    private String newPassword;

} 