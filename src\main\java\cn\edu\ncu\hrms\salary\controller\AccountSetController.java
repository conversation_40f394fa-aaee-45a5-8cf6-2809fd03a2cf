package cn.edu.ncu.hrms.salary.controller;

import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.salary.dto.AccountSetAddDTO;
import cn.edu.ncu.hrms.salary.dto.AccountSetPageQueryDTO;
import cn.edu.ncu.hrms.salary.dto.AccountSetUpdateDTO;
import cn.edu.ncu.hrms.salary.service.AccountSetService;
import cn.edu.ncu.hrms.salary.vo.AccountSetVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 薪资账套控制器
 */
@RestController
@RequestMapping("/salary/account")
@Api(tags = "薪资账套接口")
public class AccountSetController {

    @Autowired
    private AccountSetService accountSetService;

    /**
     * 创建薪资账套
     */
    @PostMapping
    @ApiOperation("创建薪资账套")
    @PreAuthorize("@hrms.hasAuthority('salary:accountset:add')")
    public Result<Void> addAccountSet(@Valid @RequestBody AccountSetAddDTO addDTO) {
        return accountSetService.addAccountSet(addDTO);
    }

    /**
     * 修改薪资账套
     */
    @PutMapping("/{id}")
    @ApiOperation("修改薪资账套")
    @PreAuthorize("@hrms.hasAuthority('salary:accountset:edit')")
    public Result<Void> updateAccountSet(
            @PathVariable @ApiParam("账套ID") Integer id,
            @Valid @RequestBody AccountSetUpdateDTO updateDTO) {
        return accountSetService.updateAccountSet(id, updateDTO);
    }

    /**
     * 删除薪资账套
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除薪资账套")
    @PreAuthorize("@hrms.hasAuthority('salary:accountset:delete')")
    public Result<Void> deleteAccountSet(@PathVariable @ApiParam("账套ID") Integer id) {
        return accountSetService.deleteAccountSet(id);
    }

    /**
     * 根据ID获取账套详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID获取账套详情")
    @PreAuthorize("@hrms.hasAuthority('salary:accountset:query')") // 权限：查看账套详情
    public Result<AccountSetVO> getAccountSetById(@PathVariable @ApiParam("账套ID") Integer id) {
        return accountSetService.getAccountSetVOById(id);
    }

    /**
     * 分页查询账套列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询账套列表")
    @PreAuthorize("@hrms.hasAuthority('salary:accountset:list')") // 权限：查询账套列表
    public Result<PageInfo<AccountSetVO>> getAccountSetList(@Valid AccountSetPageQueryDTO queryDTO) {
        return accountSetService.pageAccountSets(queryDTO);
    }

    /**
     * 获取所有账套列表 (用于下拉选择)
     */
    @GetMapping("/all")
    @ApiOperation("获取所有账套列表")
    @PreAuthorize("@hrms.hasAuthority('salary:accountset:list')") // 权限：查询账套列表 (用于下拉选择)
    public Result<List<AccountSetVO>> listAllAccountSets() {
        return accountSetService.listAllAccountSets();
    }
}
