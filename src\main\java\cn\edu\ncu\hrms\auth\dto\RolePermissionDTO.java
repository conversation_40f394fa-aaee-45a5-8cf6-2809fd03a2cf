package cn.edu.ncu.hrms.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色权限参数DTO
 */
@Data
@ApiModel(description = "角色权限参数")
public class RolePermissionDTO {

    @NotNull(message = "角色ID不能为空")
    @ApiModelProperty(value = "角色ID", required = true, example = "1")
    private Integer roleId;

    @NotEmpty(message = "权限ID列表不能为空")
    @ApiModelProperty(value = "权限ID列表", required = true)
    private List<Integer> permissionIds;
} 