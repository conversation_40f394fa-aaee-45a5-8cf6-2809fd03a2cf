package cn.edu.ncu.hrms.salary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 薪资报表视图对象
 */
@Data
@ApiModel("薪资报表视图对象")
public class SalaryReportVO {

    @ApiModelProperty("报表ID（不展示）")
    private Integer id;

    @ApiModelProperty("员工ID（不展示）")
    private Integer employeeId;

    @ApiModelProperty("账套ID（不展示）")
    private Integer accountId;

    @ApiModelProperty("所属月份(yyyy-MM)")
    private String month;

    @ApiModelProperty("基本工资")
    private BigDecimal baseSalary;

    @ApiModelProperty("考核系数")
    private BigDecimal adjustmentCoefficient;

    @ApiModelProperty("绩效奖金")
    private BigDecimal performanceBonus;

    @ApiModelProperty("工作奖金")
    private BigDecimal workBonus;

    @ApiModelProperty("工作罚款")
    private BigDecimal workFine;

    @ApiModelProperty("考勤罚款")
    private BigDecimal attendanceFine;

    @ApiModelProperty("社保扣除")
    private BigDecimal insurance;

    @ApiModelProperty("应发总额")
    private BigDecimal total;


    // ====== 关联查询 ======
    @ApiModelProperty("员工姓名（个人报表不展示）")
    private String employeeName;

    @ApiModelProperty("员工工号（个人报表不展示）")
    private String workNo;

    @ApiModelProperty("部门ID（不展示）")
    private Integer departmentId;

    @ApiModelProperty("部门名称（个人报表不展示）")
    private String departmentName;
}
