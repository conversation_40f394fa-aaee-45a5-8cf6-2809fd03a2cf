package cn.edu.ncu.hrms.common.service;

import cn.edu.ncu.hrms.common.result.Result;

/**
 * 邮件服务接口
 */
public interface EmailService {

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return Result
     */
    Result sendSimpleMail(String to, String subject, String content);

    /**
     * 发送HTML格式邮件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content HTML内容
     * @return Result
     */
    Result sendHtmlMail(String to, String subject, String content);

    /**
     * 发送奖惩申请通知邮件
     *
     * @param to 收件人邮箱
     * @param employeeName 员工姓名
     * @param type 奖惩类型（0奖励 1惩罚）
     * @param title 奖惩标题
     * @param content 奖惩内容
     * @return Result
     */
    Result sendRewardPunishmentApplicationMail(String to, String employeeName, Integer type, String title, String content);

    /**
     * 发送奖惩审批结果通知邮件
     *
     * @param to 收件人邮箱
     * @param employeeName 员工姓名
     * @param type 奖惩类型（0奖励 1惩罚）
     * @param title 奖惩标题
     * @param approveStatus 审批状态（1通过 2驳回）
     * @param remark 审批备注
     * @return Result
     */
    Result sendRewardPunishmentApproveMail(String to, String employeeName, Integer type, String title, Integer approveStatus, String remark);
} 