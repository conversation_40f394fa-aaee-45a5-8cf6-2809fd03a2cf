package cn.edu.ncu.hrms.employee.service.impl;

import cn.edu.ncu.hrms.common.dto.PageQueryDTO;
import cn.edu.ncu.hrms.common.entity.Department;
import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.entity.RewardPunishment;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.common.utils.ExcelUtil;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentAddDTO;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentQueryDTO;
import cn.edu.ncu.hrms.employee.dto.RewardPunishmentUpdateDTO;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.mapper.RewardPunishmentMapper;
import cn.edu.ncu.hrms.employee.service.RewardPunishmentService;
import cn.edu.ncu.hrms.employee.vo.RewardPunishmentVO;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 奖惩信息服务实现类
 */
@Service
@Slf4j
public class RewardPunishmentServiceImpl implements RewardPunishmentService {

    @Autowired
    private RewardPunishmentMapper rewardPunishmentMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private SecurityUtil securityUtil;

    @Override
    public Result<RewardPunishmentVO> getVOById(Long id) {
        RewardPunishment rewardPunishment = rewardPunishmentMapper.selectById(id);
        if (rewardPunishment == null) {
            return Result.error("奖惩记录不存在");
        }
        return Result.success(convertToRewardPunishmentVO(rewardPunishment));
    }

    @Override
    public PageInfo<RewardPunishmentVO> listVOsByEmployeeId(PageQueryDTO queryDTO){
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        if (currentEmployeeId == null) {
            return null;
        }
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<RewardPunishment> rewardPunishments = rewardPunishmentMapper.selectByEmployeeId(currentEmployeeId);
        List<RewardPunishmentVO> rewardPunishmentVOs = rewardPunishments.stream()
                                               .map(this::convertToRewardPunishmentVO)
                                               .collect(Collectors.toList());
        PageInfo<RewardPunishment> pageInfoSrc = new PageInfo<>(rewardPunishments);
        PageInfo<RewardPunishmentVO> pageInfoVO = new PageInfo<>(rewardPunishmentVOs);

        BeanUtils.copyProperties(pageInfoSrc, pageInfoVO, "list");
        return pageInfoVO;
    }

    @Override
    public PageInfo<RewardPunishmentVO> pageVOs(RewardPunishmentQueryDTO queryDTO) {
        log.info("分页查询奖惩信息，查询条件: {}", queryDTO);
        if (queryDTO.getOperatorId() ==  null) {
            Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
            queryDTO.setOperatorId(currentEmployeeId);
        }
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<RewardPunishment> list = rewardPunishmentMapper.selectByCondition(queryDTO);

        List<RewardPunishmentVO> voList = list.stream()
                                             .map(this::convertToRewardPunishmentVO)
                                             .collect(Collectors.toList());
        PageInfo<RewardPunishment> pageInfoSrc = new PageInfo<>(list);
        PageInfo<RewardPunishmentVO> pageInfoVO = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfoSrc, pageInfoVO, "list");
        return pageInfoVO;
    }

    @Override
    @Transactional
    public Result<Void> add(RewardPunishmentAddDTO addDTO) {
        log.info("新增奖惩信息，DTO: {}", addDTO);

        RewardPunishment rewardPunishment = new RewardPunishment();
        BeanUtils.copyProperties(addDTO, rewardPunishment);

        // 设置操作人员工ID
        Integer currentEmployeeId = securityUtil.getCurrentEmployeeId();
        rewardPunishment.setOperatorId(currentEmployeeId);

        LocalDateTime now = LocalDateTime.now();
        rewardPunishment.setCreateTime(now);
        rewardPunishment.setUpdateTime(now);


        int rows = rewardPunishmentMapper.insert(rewardPunishment);
        if (rows > 0) {
            log.info("奖惩信息添加成功，ID: {}", rewardPunishment.getId());
            return Result.success();
        } else {
            log.error("奖惩信息添加失败");
            return Result.error("添加失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> update(RewardPunishmentUpdateDTO updateDTO) {
        log.info("更新奖惩信息，DTO: {}", updateDTO);
        RewardPunishment existRewardPunishment = rewardPunishmentMapper.selectById(updateDTO.getId());
        if (existRewardPunishment == null) {
            return Result.error("奖惩记录不存在");
        }

        BeanUtils.copyProperties(updateDTO, existRewardPunishment);
        existRewardPunishment.setUpdateTime(LocalDateTime.now());

        int rows = rewardPunishmentMapper.update(existRewardPunishment);
        if (rows > 0) {
            log.info("奖惩信息更新成功，ID: {}", existRewardPunishment.getId());
            return Result.success();
        } else {
            log.error("奖惩信息更新失败，ID: {}", existRewardPunishment.getId());
            return Result.error("更新失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> delete(Long id) {
        log.info("删除奖惩信息，ID: {}", id);
        RewardPunishment existRewardPunishment = rewardPunishmentMapper.selectById(id);
        if (existRewardPunishment == null) {
            log.warn("尝试删除不存在的奖惩记录，ID: {}", id);
            return Result.success();
        }

        int rows = rewardPunishmentMapper.deleteById(id);
        if (rows > 0) {
            log.info("奖惩信息删除成功，ID: {}", id);
            return Result.success();
        } else {
            log.error("奖惩信息删除失败，ID: {}", id);
            return Result.error("删除失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("未选择要删除的记录");
        }
        log.info("批量删除奖惩信息，IDs: {}", ids);
        int rows = rewardPunishmentMapper.batchDelete(ids);
        if (rows > 0) {
            log.info("成功批量删除 {} 条奖惩记录", rows);
            return Result.success();
        } else {
            log.warn("批量删除奖惩记录影响行数为 0 (可能记录已被删除或不存在)");
            return Result.success();
        }
    }

    @Override
    public Result<?> getTypeStatistics() {
        List<Map<String, Object>> typeStats = rewardPunishmentMapper.selectTypeStatistics();
        
        Map<String, Object> result = new HashMap<>();
        long rewardCount = 0;
        long punishmentCount = 0;
        double rewardAmount = 0.0;
        double punishmentAmount = 0.0;
        
        for (Map<String, Object> stat : typeStats) {
            Integer type = (Integer) stat.get("type");
            Long count = (Long) stat.get("count");
            Double totalAmount = (Double) stat.get("totalAmount");
            
            if (type != null) {
                if (type == 0) {
                    rewardCount = (count != null) ? count : 0;
                    rewardAmount = (totalAmount != null) ? totalAmount : 0.0;
                } else if (type == 1) {
                    punishmentCount = (count != null) ? count : 0;
                    punishmentAmount = (totalAmount != null) ? totalAmount : 0.0;
                }
            }
        }
        
        result.put("rewardCount", rewardCount);
        result.put("punishmentCount", punishmentCount);
        result.put("totalCount", rewardCount + punishmentCount);
        result.put("rewardAmount", rewardAmount);
        result.put("punishmentAmount", punishmentAmount);
        
        return Result.success(result);
    }
    
    @Override
    public Result<?> getAmountStatistics(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            return Result.error("请提供有效的开始和结束日期");
        }
        log.info("查询金额统计，日期范围: {} - {}", startDate, endDate);
        
        List<Map<String, Object>> amountStats = rewardPunishmentMapper.selectAmountStatistics(startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        double rewardAmount = 0.0;
        double punishmentAmount = 0.0;
        
        for (Map<String, Object> stat : amountStats) {
            Integer type = (Integer) stat.get("type");
            Double amount = (Double) stat.get("totalAmount");
            
            if (type != null && amount != null) {
                if (type == 0) {
                    rewardAmount = amount;
                } else if (type == 1) {
                    punishmentAmount = amount;
                }
            }
        }
        
        result.put("rewardAmount", rewardAmount);
        result.put("punishmentAmount", punishmentAmount);
        result.put("netAmount", rewardAmount - punishmentAmount);
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        
        return Result.success(result);
    }
    
    @Override
    public Result<?> getEmployeeRanking(Integer type, Integer limit) {
        int effectiveLimit = (limit == null || limit <= 0) ? 10 : limit;
        log.info("查询员工排行榜，类型: {}, 限制: {}", type == null ? "总和" : (type == 0 ? "奖励" : "惩罚"), effectiveLimit);
        
        List<Map<String, Object>> rankingData = rewardPunishmentMapper.selectEmployeeRanking(type, effectiveLimit);
        return Result.success(rankingData);
    }
    
    @Override
    public void exportExcel(HttpServletResponse response, RewardPunishmentQueryDTO queryDTO) {
        log.info("导出奖惩信息Excel，查询条件: {}", queryDTO);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "奖惩信息_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            
            List<RewardPunishment> rewardPunishments = rewardPunishmentMapper.selectByCondition(queryDTO);
            
            List<RewardPunishmentVO> voList = rewardPunishments.stream()
                                                .map(this::convertToRewardPunishmentVO)
                                                .collect(Collectors.toList());
            
            ExcelUtil.writeExcel(response.getOutputStream(), voList, RewardPunishmentVO.class);
            
        } catch (IOException e) {
            log.error("导出奖惩信息Excel失败", e);
            throw new RuntimeException("导出Excel失败", e);
        } catch (Exception e) {
            log.error("导出奖惩信息Excel时发生意外错误", e);
            throw new RuntimeException("导出Excel时发生意外错误", e);
        }
    }
    
    @Override
    public void downloadTemplate(HttpServletResponse response) {
        log.info("下载奖惩信息导入模板");
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "奖惩信息导入模板";
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            
            List<RewardPunishment> templateData = new ArrayList<>();
            ExcelUtil.writeExcel(response.getOutputStream(), templateData, RewardPunishment.class);
            
        } catch (IOException e) {
            log.error("下载奖惩信息导入模板失败", e);
            throw new RuntimeException("下载模板失败", e);
        } catch (Exception e) {
            log.error("下载模板时发生意外错误", e);
            throw new RuntimeException("下载模板时发生意外错误", e);
        }
    }
    
    @Override
    @Transactional
    public Result<Void> importExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.error("导入文件不能为空");
        }
        log.info("开始导入奖惩信息Excel文件: {}", file.getOriginalFilename());
        
        try {
            List<RewardPunishment> list = ExcelUtil.readExcel(file, RewardPunishment.class);
            if (list.isEmpty()) {
                return Result.error("导入文件内容为空");
            }
            
            for (RewardPunishment rp : list) {
                if (rp.getEmployeeId() == null || rp.getType() == null || rp.getRewardDate() == null) {
                    log.error("导入失败：记录缺少必要信息 (EmployeeId, Type, RewardDate)");
                    return Result.error("导入失败：存在记录缺少必要信息");
                }
                rp.setCreateTime(LocalDateTime.now());
                rp.setUpdateTime(LocalDateTime.now());
                
                rewardPunishmentMapper.insert(rp);
            }
            log.info("成功导入 {} 条奖惩信息", list.size());
            return Result.success();
            
        } catch (IOException e) {
            log.error("读取导入的Excel文件失败", e);
            return Result.error("读取文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导入奖惩信息Excel时发生错误", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }


    private RewardPunishmentVO convertToRewardPunishmentVO(RewardPunishment entity) {
        if (entity == null) {
            return null;
        }
        RewardPunishmentVO vo = new RewardPunishmentVO();
        BeanUtils.copyProperties(entity, vo);

        if (entity.getEmployeeId() != null) {
            Employee employee = employeeMapper.selectById(entity.getEmployeeId());
            if (employee != null) {
                vo.setEmployeeId(entity.getEmployeeId());
                vo.setEmployeeName(employee.getName());
                vo.setWorkNo(employee.getWorkNo());
                Integer departmentId = employee.getDepartmentId();
                if (departmentId != null) {
                    Department department = departmentMapper.selectById(departmentId);
                    if (department != null) {
                        vo.setDepartmentId(departmentId);
                        vo.setDepartmentName(department.getName());
                    }
                }
            }
        }

        if (entity.getOperatorId() != null) {
            Employee operator = employeeMapper.selectById(entity.getOperatorId());
            if (operator != null) {
                vo.setOperatorName(operator.getName());
                vo.setOperatorWorkNo(operator.getWorkNo());
            }
        }

        return vo;
    }
}