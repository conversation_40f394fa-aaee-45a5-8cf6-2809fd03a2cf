package cn.edu.ncu.hrms.employee.kafka;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.common.exception.DataFormatException;
import cn.edu.ncu.hrms.common.kafka.KafkaProducerService;
import cn.edu.ncu.hrms.common.utils.SecurityUtil;
import cn.edu.ncu.hrms.common.websocket.WebSocketServer;
import cn.edu.ncu.hrms.employee.dto.EmployeeImportDTO;
import cn.edu.ncu.hrms.employee.dto.ImportTaskDTO;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.employee.service.EmployeeService;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.system.mapper.DepartmentMapper;
import cn.edu.ncu.hrms.system.service.SystemDepartmentService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.edu.ncu.hrms.common.constant.KafkaConstant.EMPLOYEE_IMPORT_TOPIC;

@Slf4j
@Component
public class BatchImportConsumer {

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private ObjectMapper objectMapper;


/*    @KafkaListener(topics = EMPLOYEE_IMPORT_TOPIC)
    @Transactional
    public void batchImport(String filePath, Acknowledgment ack,
                            @Header(value = KafkaHeaders.DELIVERY_ATTEMPT, required = false) Integer attempt) {
        log.debug("接收到批量导入员工Excel文件路径消息：{}", filePath);

        try {
            // 1. 读取文件
            File file = new File(filePath);
            List<EmployeeImportDTO> importData = EasyExcel.read(file)
                    .head(EmployeeImportDTO.class)
                    .sheet()
                    .doReadSync();


            // 2. 处理数据（校验、生成工号等）
            List<Employee> validEmployees = new ArrayList<>();
            Map<Integer, List<String>> rowErrorMap = new LinkedHashMap<>(); // 行号->错误列表 Map<EmployeeImportDTO, List<String>> errorMap = new HashMap<>();
            importData.forEach(data -> {
                List<String> errors = validateImportData(data);
                if (errors.isEmpty()) {
                    Employee employee = new Employee();
                    BeanUtils.copyProperties(data, employee);
                    Position position = positionMapper.selectByNameAndLevel(data.getPositionName(), data.getLevel());
                    employee.setPositionId(position.getId());
                    // TODO 并发时（批量插入过程有单个插入）生成工号可能重复
                    employee.setWorkNo(employeeService.generateWorkNo(employee.getName(),true));
                    validEmployees.add(employee);
                } else {
                    errorMap.put(data, errors);
                    log.warn("员工数据校验失败:{} , errors={}", data, errors);
                }

            });

            // 3. 批量插入合法数据
            if (!validEmployees.isEmpty()) {
                employeeService.batchInsert(validEmployees);
            }

            // 4. 清理临时文件
            file.delete();

            // TODO 后续扩展站内信功能，可以将错误信息发给操作者

            // 手动提交Offset
            ack.acknowledge();
        } catch (Exception e) {
            if (attempt == null) attempt = 1;
            if (attempt < 3) {
                // 最多重试3次
                log.warn("员工导入失败，第 {} 次重试，异常信息：{}", attempt, e);
                kafkaProducerService.sendMessage(EMPLOYEE_IMPORT_TOPIC, filePath);
            }
        }

    }*/


    @KafkaListener(topics = EMPLOYEE_IMPORT_TOPIC)
    @Transactional
    public void batchImport(@Payload String message, Acknowledgment ack) {

        ImportTaskDTO task = null;
        /*boolean shouldAck = false; // 控制消息确认
        boolean resultSent = false; // 控制结果发送*/
        // 使用数组包装变量，绕过final限制
        boolean[] resultSent = {false};

        try {
            // 解析消息
            task = objectMapper.readValue(message, ImportTaskDTO.class);

            String filePath = task.getFilePath();
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("文件不存在: {}", filePath);
                sendResultMessage(task.getUid(), "FAILED",
                        "文件上传失败", 0, 0, null);
                resultSent[0] = true;
                return;
            }

            log.info("开始处理员工批量导入任务，task: {}", task);


            Map<String, List<String>> rowErrorMap = new LinkedHashMap<>(); // 行号->错误列表
            List<Employee> validEmployees = new ArrayList<>(); // 合法员工列表
            AtomicInteger successCount = new AtomicInteger(0);// 成功数量
            ImportTaskDTO finalTask = task;

            // 读取文件(使用监听器模式逐行处理)
            EasyExcel.read(new File(task.getFilePath()))
                    .excelType(ExcelTypeEnum.XLSX) // 显式指定类型
                    //.head(EmployeeImportDTO.class)
                    .head(new ArrayList<>()) // 不指定DTO类
                    .sheet()
                    .registerReadListener(new AnalysisEventListener<Map<Integer, String>>() {

                        private final AtomicInteger processedRows = new AtomicInteger(0);
                        private int totalRows = 0; // 总行数（不含表头）

                        @Override
                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                            // 在读取表头后初始化总行数
                            this.totalRows = context.readSheetHolder().getApproximateTotalRowNumber() - 1; // 减去表头
                            sendProgressMessage(finalTask.getUid(), 0, "开始处理数据", totalRows, 0);
                        }

                        @Override
                        public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                            Integer rowIndex = context.readRowHolder().getRowIndex() + 1; //  当前行数
                            int currentRowNum = processedRows.incrementAndGet(); // 已处理行数
                            try {
                                // 转换为DTO对象
                                EmployeeImportDTO dto = convertRowToDto(rowData);

                                // 校验数据
                                List<String> errors = validateImportData(dto);

                                // 转换成实体类
                                if (errors.isEmpty()) {
                                    log.info("员工数据校验成功 row={}，data={}", rowIndex, dto);
                                    Employee employee = convertToEntity(dto);
                                    validEmployees.add(employee);
                                    successCount.incrementAndGet();
                                } else {
                                    rowErrorMap.put(rowIndex.toString(), errors);
                                    log.warn("员工数据校验失败 row={}，data={}， errors={}",rowIndex, rowData, errors);
                                }

                                // 每处理10行或最后一行时发送进度
                                if (currentRowNum % 10 == 0 || currentRowNum == totalRows) {
                                    int progress = (int) ((currentRowNum * 100.0) / totalRows);
                                    sendProgressMessage(
                                            finalTask.getUid(),
                                            progress,
                                            String.format("已处理 %d/%d", currentRowNum, totalRows),
                                            totalRows,
                                            successCount.get()
                                    );
                                }

                            } catch (DataFormatException e) {
                                rowErrorMap.put(rowIndex.toString(), e.getFieldErrors());
                                log.warn("行{}格式校验失败: {}", rowIndex, e.getMessage());
                            } catch (Exception e) {
                                rowErrorMap.put(rowIndex.toString(), Collections.singletonList("系统错误: " + e.getMessage()));
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            sendProgressMessage(finalTask.getUid(), 99, "正在保存", totalRows, successCount.get());
                            // 处理有效数据
                            if (!validEmployees.isEmpty()) {
                                /*try {
                                    employeeService.batchInsert(validEmployees);
                                    log.info("批量插入{}条员工数据成功", validEmployees.size());
                                } catch (Exception e) {
                                    // 抛出异常会触发事务回滚
                                    log.error("数据库插入异常", e);
                                    throw e;
                                }*/
                                employeeService.batchInsert(validEmployees);
                                log.info("批量插入{}条员工数据成功", validEmployees.size());
                            } else {
                                log.debug("没有有效员工数据需要处理");
                            }

                            // 发送最终结果
                            sendResultMessage(
                                    finalTask.getUid(),
                                    rowErrorMap.isEmpty() ? "SUCCESS" : "PARTIAL_SUCCESS",
                                    String.format("导入完成，成功 %d 条，失败 %d 条", successCount.get(), rowErrorMap.size()),
                                    totalRows,
                                    successCount.get(),
                                    rowErrorMap
                            );

                            resultSent[0] = true;

                        }
                    })
                    .doRead();


            log.debug("批量导入员工任务完成");
            System.out.println("测试");

        } catch (JsonProcessingException e) {
            log.error("消息反序列化失败: {}", message, e);
        } catch (Exception e) {
            log.error("员工批量导入处理失败, message={}", message, e);
            if (task != null && !resultSent[0]) {
                sendResultMessage(task.getUid(), "FAILED", "系统异常，导入失败", 0, 0, null);
            }
        } finally {
            // 清理临时文件
            if (task != null && task.getFilePath() != null) {
                try {
                    Files.deleteIfExists(Paths.get(task.getFilePath()));
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", task.getFilePath(), e);
                }
            }

            // 确认消息
            if (ack != null) {
                ack.acknowledge();
            }
        }

        log.debug("try-catch-finally完成");
    }


    /**
     * 将Excel行数据转换为DTO对象
     * @param rowData
     * @return
     */
    private EmployeeImportDTO convertRowToDto(Map<Integer, String> rowData) throws DataFormatException {
        EmployeeImportDTO dto = new EmployeeImportDTO();
        List<String> formatErrors = new ArrayList<>();

        // 1. 姓名（索引0）- 必须为非空字符串
        dto.setName(getStringValue(rowData, 0));

        // 2. 入职日期（索引1）- 必须为YYYY-MM-DD格式
        String entryDateStr = getStringValue(rowData, 1);
        if (StringUtils.isNotBlank(entryDateStr)) {
            try {
                dto.setEntryDate(LocalDate.parse(entryDateStr, DateTimeFormatter.ISO_DATE));
            } catch (DateTimeParseException e) {
                formatErrors.add(String.format("入职日期：格式错误，必须为YYYY-MM-DD，输入值：%s", entryDateStr));
            }
        }

        // 3. 性别（索引2）- 允许为空字符串
        dto.setGender(getStringValue(rowData, 2));

        // 4. 身份证号（索引3）- 允许为空字符串
        dto.setIdCard(getStringValue(rowData, 3));

        // 5. 联系电话（索引4）- 允许为空字符串
        dto.setPhone(getStringValue(rowData, 4));

        // 6. 邮箱（索引5）- 允许为空字符串
        dto.setEmail(getStringValue(rowData, 5));

        // 7. 家庭住址（索引6）- 允许为空字符串
        dto.setAddress(getStringValue(rowData, 6));

        // 8. 部门ID（索引7）- 必须为整数
        String deptIdStr = getStringValue(rowData, 7);
        if (StringUtils.isNotBlank(deptIdStr)) {
            try {
                dto.setDepartmentId(Integer.parseInt(deptIdStr));
            } catch (NumberFormatException e) {
                formatErrors.add(String.format("部门ID：格式错误，必须为整数，输入值：%s", deptIdStr));
            }
        }

        // 9. 职位名称（索引8）- 必须为非空字符串
        dto.setPositionName(getStringValue(rowData, 8));

        // 10. 职级（索引9）- 必须为整数
        String levelStr = getStringValue(rowData, 9);
        if (StringUtils.isNotBlank(levelStr)) {
            try {
                dto.setLevel(Integer.parseInt(levelStr));
            } catch (NumberFormatException e) {
                formatErrors.add(String.format("职级：格式错误，必须为整数，输入值：%s", levelStr));
            }
        }

        // 11. HR工号（索引10）- 必须为非空字符串
        dto.setHrWorkNo(getStringValue(rowData, 10));

        // 12. 在职状态（索引11）- 必须可转换成布尔值（在职/离职/是/否/1/0）
        String statusStr = getStringValue(rowData, 11);
        if (StringUtils.isNotBlank(statusStr)) {
            try {
                dto.setStatus(parseBoolean(statusStr));
            } catch (IllegalArgumentException e) {
                formatErrors.add(String.format("在职状态：格式错误，必须是在职/离职，输入值：%s", statusStr));
            }
        }

        // 如果有格式错误，抛出异常
        if (!formatErrors.isEmpty()) {
            throw new DataFormatException(formatErrors);
        }

        return dto;

    }

    // 辅助方法：解析布尔值（支持中文）
    private Boolean parseBoolean(String value) {
        if (StringUtils.isBlank(value)) return null;

        value = value.trim().toLowerCase();
        if (value.equals("在职") || value.equals("是") || value.equals("1")) {
            return true;
        } else if (value.equals("离职") || value.equals("否") || value.equals("0")) {
            return false;
        }
        throw new IllegalArgumentException("无效的布尔值");
    }

    // 辅助方法：安全获取字符串
    private String getStringValue(Map<Integer, String> rowData, int index) {
        return rowData.containsKey(index) ?
                StringUtils.trimToNull(rowData.get(index)) : null;
    }


    /**
     * 校验导入数据的业务规则
     * @param importDTO 要校验的DTO对象
     * @return 错误信息列表，空列表表示校验通过
     */
    private List<String> validateImportData(EmployeeImportDTO importDTO) {
        List<String> errors = new ArrayList<>();

        // 1. 必填字段校验
        validateRequiredFields(importDTO, errors);

        // 2. 格式校验
        validateFormats(importDTO, errors);

        // 3. 业务规则校验
        validateBusinessRules(importDTO, errors);

        return errors;
    }

    // 必填字段校验
    private void validateRequiredFields(EmployeeImportDTO dto, List<String> errors) {
        if (StringUtils.isBlank(dto.getName())) {
            errors.add("姓名不能为空");
        }

        if (dto.getEntryDate() == null) {
            errors.add("入职日期不能为空");
        }

        if (dto.getDepartmentId() == null) {
            errors.add("部门ID不能为空");
        }

        if (StringUtils.isBlank(dto.getPositionName())) {
            errors.add("职位名称不能为空");
        }

        if (dto.getLevel() == null) {
            errors.add("职级不能为空");
        }

        if (StringUtils.isBlank(dto.getHrWorkNo())) {
            errors.add("HR工号不能为空");
        }

        if (dto.getStatus() == null) {
            errors.add("在职状态不能为空");
        }
    }

    // 格式校验
    private void validateFormats(EmployeeImportDTO dto, List<String> errors) {
        // 姓名格式
        if (StringUtils.isNotBlank(dto.getName()) && !dto.getName().matches("^[\\u4e00-\\u9fa5]{2,10}$")) {
            errors.add("姓名必须是2-10个汉字");
        }

        // 手机号格式
        if (StringUtils.isNotBlank(dto.getPhone()) && !dto.getPhone().matches("^1[3-9]\\d{9}$")) {
            errors.add("手机号格式错误");
        }

        // 身份证格式
        if (StringUtils.isNotBlank(dto.getIdCard()) && !IdCardValidator.validate(dto.getIdCard())) {
            errors.add("身份证号格式错误");
        }

        // 邮箱格式
        if (StringUtils.isNotBlank(dto.getEmail()) && !dto.getEmail().matches("^\\w+@[a-zA-Z0-9]+\\.[a-zA-Z]{2,}$")) {
            errors.add("邮箱格式错误");
        }

        // 性别选项
        if (StringUtils.isNotBlank(dto.getGender()) && !Arrays.asList("男", "女", "其他").contains(dto.getGender())) {
            errors.add("性别只能是：男/女/其他");
        }

        // 职级范围
        if (dto.getLevel() != null && (dto.getLevel() < 5 || dto.getLevel() > 10)) {
            errors.add("职级必须是5-10的整数");
        }
    }

    // 业务规则校验
    private void validateBusinessRules(EmployeeImportDTO dto, List<String> errors) {
        // 部门存在性校验
        if (dto.getDepartmentId() != null && departmentMapper.selectById(dto.getDepartmentId()) == null) {
            errors.add("部门ID不存在: " + dto.getDepartmentId());
        }

        // 职位存在性校验
        if (StringUtils.isNotBlank(dto.getPositionName()) && dto.getLevel() != null) {
            Position position = positionMapper.selectByNameAndLevel(dto.getPositionName(), dto.getLevel());
            if (position == null) {
                errors.add("职位不存在: " + dto.getPositionName() + "-L" + dto.getLevel());
            }
        }

        // HR工号存在性校验
        if (StringUtils.isNotBlank(dto.getHrWorkNo()) && employeeMapper.selectByWorkNo(dto.getHrWorkNo()) == null) {
            errors.add("HR不存在: " + dto.getHrWorkNo());
        }

        // 身份证号唯一性校验
        if (StringUtils.isNotBlank(dto.getIdCard()) && employeeMapper.selectByIdCard(dto.getIdCard()) != null) {
            errors.add("身份证号已存在: " + dto.getIdCard());
        }
    }

    static class IdCardValidator {
        public static boolean validate(String idCard) {
            return idCard.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$");
        }
    }

    // 实体转换方法
    private Employee convertToEntity(EmployeeImportDTO dto) {
        Employee employee = new Employee();
        BeanUtils.copyProperties(dto, employee);

        // 查询职位信息
        Position position = positionMapper.selectByNameAndLevel(dto.getPositionName(), dto.getLevel());
        employee.setPositionId(position.getId());

        // 生成工号
        employee.setWorkNo(employeeService.generateWorkNo(employee.getName(), true));

        return employee;
    }

    // 发送进度消息
    private void sendProgressMessage(Integer operatorId, int progress, String message,
                                     int total, int success) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "IMPORT_PROGRESS");
        payload.put("progress", progress);
        payload.put("message", message);
        payload.put("total", total);
        payload.put("success", success);
        payload.put("timestamp", System.currentTimeMillis());

        webSocketServer.sendToUser(operatorId, JSON.toJSONString(payload));
    }

    // 发送结果消息
    private void sendResultMessage(Integer operatorId, String status, String message,
                                   int total, int successCount, Map<String, List<String>> rowErrorMap) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("type", "IMPORT_RESULT");
        payload.put("status", status);
        payload.put("message", message);
        payload.put("total", total);
        payload.put("successCount", successCount);
        if (rowErrorMap != null) {
            payload.put("rowErrorMap", rowErrorMap);
        }
        payload.put("timestamp", System.currentTimeMillis());

        webSocketServer.sendToUser(operatorId, JSON.toJSONString(payload));
    }

}
