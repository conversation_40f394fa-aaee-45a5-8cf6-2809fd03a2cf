package cn.edu.ncu.hrms.system.service;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.system.vo.DashboardDistributionStatsVO;
import cn.edu.ncu.hrms.system.vo.DashboardOnboardingStatsVO;
import cn.edu.ncu.hrms.system.dto.EmployeeAddDTO;
import cn.edu.ncu.hrms.system.dto.EmployeePageQueryDTO;
import cn.edu.ncu.hrms.system.dto.EmployeeUpdateDTO;
import cn.edu.ncu.hrms.system.vo.EmployeeVO;
import com.github.pagehelper.PageInfo; // Import PageInfo
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统员工管理服务接口
 */
public interface SystemEmployeeService {

    /**
     * 获取所有员工列表 (返回VO)
     * @return 员工VO列表结果
     */
    Result<List<EmployeeVO>> listAllEmployees();

    /**
     * 分页查询员工 (使用DTO和VO)
     * @param queryDTO 查询条件 DTO
     * @return 分页结果 (EmployeeVO)
     */
    PageInfo<EmployeeVO> pageEmployees(EmployeePageQueryDTO queryDTO);

    /**
     * 根据ID获取员工信息 (返回VO)
     * @param id 员工ID
     * @return 员工VO信息结果
     */
    Result<EmployeeVO> getEmployeeVOById(Integer id);


    /**
     * 更新员工信息
     * @param updateDTO
     * @return
     */
    @Transactional
    Result<Void> updateEmployee(EmployeeUpdateDTO updateDTO);


    /**
     * 更新员工在职状态
     * @param id
     * @param status
     * @return
     */
    @Transactional
    Result<Void> updateEmployeeStatus(Integer id, Boolean status);

    /**
     * 根据部门ID获取员工列表 (返回VO)
     * @param id
     * @return
     */
    Result<List<EmployeeVO>> getEmployeesByDepartmentId(Integer id);


    /**
     * 获取管理员仪表盘 - 年度入职统计
     * @return Result<DashboardOnboardingStatsVO>
     */
    Result<DashboardOnboardingStatsVO> getDashboardOnboardingStats();


    /**
     * 获取管理员仪表盘 - 员工分布统计
     * @return
     */
    Result<List<DashboardDistributionStatsVO>> getDashboardDistributionStats();

    /**
     * 获取未注册用户的员工列表 (返回VO)
     * @return
     */
    Result<List<EmployeeVO>> getUnregisteredEmployeeList();

    /**
     * 根据部门ID和角色标识获取员工列表
     * @param departmentId
     * @param roleKey
     * @return
     */
    Result<List<Employee>> getEmployeesByDepartmentIdAndRole(Integer departmentId, String roleKey);
}