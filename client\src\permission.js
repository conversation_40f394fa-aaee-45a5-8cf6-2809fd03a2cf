import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // 进度条
import 'nprogress/nprogress.css' // 进度条样式
import { getToken } from '@/utils/auth' // 从cookie获取token
import { asyncRoutes, constantRoutes } from '@/router'

NProgress.configure({ showSpinner: false }) // 进度条配置

// 白名单，不需要登录就可以访问的页面
const whiteList = ['/login', '/register', '/404']

// 路由前置守卫
router.beforeEach(async(to, from, next) => {
  // 开启进度条
  NProgress.start()

  // 判断是否有token
  const hasToken = getToken()

  if (hasToken) {
    // 有token
    if (to.path === '/login' || to.path === '/register') {
      // 如果已登录，跳转到首页
      next({ path: '/' })
      NProgress.done()
    } else {
      // 判断是否已获取用户信息
      const hasUserInfo = store.getters.userId
      if (hasUserInfo) {
        next()
      } else {
        try {
          // 获取用户信息 - 只在登录后调用一次
          const userInfo = await store.dispatch('user/getUserInfo')

          // 同时加载用户角色信息到全局存储 - 只在登录后调用一次
          // 这个信息会被缓存在 Vuex 中，不需要每次路由切换都重新获取
          await store.dispatch('userInfo/getUserInfo')

          // 根据用户角色过滤路由
          let accessRoutes = []

          // 如果是管理员，只显示管理员路由
          if (userInfo.isAdmin) {
            accessRoutes = asyncRoutes.filter(route => {
              return route.meta && route.meta.roles && route.meta.roles.includes('ROLE_admin')
            })
          } else {
            // 如果是员工，只显示员工路由
            const roleKeys = userInfo.roleKeys || []
            accessRoutes = asyncRoutes.filter(route => {
              if (!route.meta || !route.meta.roles) {
                return true
              }

              // 检查用户角色是否与路由所需角色有交集
              return route.meta.roles.some(role => roleKeys.includes(role))
            })
          }

          // 动态添加路由
          const allRoutes = [...constantRoutes, ...accessRoutes]
          store.commit('user/SET_ROUTES', allRoutes)

          // 添加路由到路由表
          accessRoutes.forEach(route => {
            router.addRoute(route)
          })

          // 重新导航到目标页面
          // hack方法，确保动态添加的路由已完成
          next({ ...to, replace: true })
        } catch (error) {
          // 获取用户信息失败，清除token并跳转到登录页
          await store.dispatch('user/logout')
          Message.error(error.message || '获取用户信息失败，请重新登录')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在白名单中，直接访问
      next()
    } else {
      // 不在白名单中，重定向到登录页
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

// 路由后置守卫
router.afterEach(() => {
  // 关闭进度条
  NProgress.done()
})

/**
 * 根据用户权限过滤路由
 * @param {Array} routes - 路由数组
 * @param {Array} permissions - 用户权限数组
 * @returns {Array} 过滤后的路由数组
 */
function filterAsyncRoutes(routes, permissions) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    // 如果没有meta或没有roles，或者用户拥有该路由所需角色，则保留该路由
    if (!tmp.meta || !tmp.meta.roles || hasPermission(permissions, tmp.meta.roles)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, permissions)
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * 判断用户是否拥有某个角色
 * @param {Array} permissions - 用户权限数组
 * @param {Array} roles - 路由所需角色数组
 * @returns {Boolean} 是否拥有权限
 */
function hasPermission(permissions, roles) {
  if (!roles) return true
  return roles.some(role => permissions.includes(role))
}