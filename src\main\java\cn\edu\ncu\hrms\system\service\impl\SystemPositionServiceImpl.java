package cn.edu.ncu.hrms.system.service.impl;

import cn.edu.ncu.hrms.common.entity.Employee;
import cn.edu.ncu.hrms.common.entity.Position;
import cn.edu.ncu.hrms.common.result.Result;
import cn.edu.ncu.hrms.salary.mapper.PositionMapper;
import cn.edu.ncu.hrms.employee.mapper.EmployeeMapper;
import cn.edu.ncu.hrms.system.dto.PositionAddDTO;
import cn.edu.ncu.hrms.system.dto.SystemPositionPageQueryDTO;
import cn.edu.ncu.hrms.system.dto.PositionUpdateDTO;
import cn.edu.ncu.hrms.system.service.SystemPositionService;
import cn.edu.ncu.hrms.system.vo.PositionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 职位管理服务实现类
 * 实现职位的增删改查、搜索、层级与员工关联等业务逻辑
 */
@Service
public class SystemPositionServiceImpl implements SystemPositionService {

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    /**
     * 获取所有职位列表
     * @return Result<List<PositionVO>> 职位VO列表
     */
    @Override
    public Result<List<PositionVO>> getPositionList(SystemPositionPageQueryDTO queryDTO) {
        List<Position> positions = positionMapper.selectListByCondition(queryDTO);
        List<PositionVO> positionVOs = positions.stream()
                .map(this::convertToPositionVO)
                .collect(Collectors.toList());
        return Result.success(positionVOs);
    }

    /**
     * 根据ID获取职位信息
     * @param id 职位ID
     * @return Result<PositionVO> 职位VO
     */
    @Override
    public Result<PositionVO> getPositionVOById(Integer id) {
        Position position = positionMapper.selectById(id);
        if (position == null || Boolean.TRUE.equals(position.getDelFlag())) {
            return Result.error("职位不存在");
        }
        return Result.success(convertToPositionVO(position));
    }

    /**
     * 创建新职位
     * @param addDTO 职位创建信息 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> createPosition(PositionAddDTO addDTO) {
        Position position = new Position();
        BeanUtils.copyProperties(addDTO, position);
        position.setCreateTime(LocalDateTime.now());
        position.setDelFlag(false);
        int result = positionMapper.insert(position);
        if (result > 0) {
            return Result.success();
        } else {
            return Result.error("创建职位失败");
        }
    }

    /**
     * 更新职位信息
     * @param id 职位ID
     * @param updateDTO 职位更新信息 DTO
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> updatePosition(Integer id, PositionUpdateDTO updateDTO) {
        Position existingPosition = positionMapper.selectById(id);
        if (existingPosition == null || Boolean.TRUE.equals(existingPosition.getDelFlag())) {
            return Result.error("职位不存在");
        }

        BeanUtils.copyProperties(updateDTO, existingPosition);
        existingPosition.setId(id);
        existingPosition.setUpdateTime(LocalDateTime.now());

        int result = positionMapper.update(existingPosition);
        if (result > 0) {
            return Result.success();
        } else {
            return Result.error("更新职位失败");
        }
    }

    /**
     * 删除职位
     * @param id 职位ID
     * @return Result<Void> 操作结果
     */
    @Override
    @Transactional
    public Result<Void> deletePosition(Integer id) {
        Position position = positionMapper.selectById(id);
        if (position == null || Boolean.TRUE.equals(position.getDelFlag())) {
            return Result.success();
        }

        long employeeCount = employeeMapper.countByPositionId(id);
        if (employeeCount > 0) {
            return Result.error("该职位下有关联员工，无法删除");
        }

        int result = positionMapper.deleteById(id);
        if (result > 0) {
            return Result.success();
        } else {
            return Result.error("删除职位失败");
        }
    }

    /**
     * 根据账套ID获取职位列表
     * @param accountId 账套ID
     * @return Result<List<PositionVO>> 职位VO列表
     */
    @Override
    public Result<List<PositionVO>> getPositionsByAccountId(Integer accountId) {
        List<Position> positions = positionMapper.selectByAccountId(accountId);
        List<PositionVO> positionVOs = positions.stream()
                .map(this::convertToPositionVO)
                .collect(Collectors.toList());
        return Result.success(positionVOs);
    }

    /**
     * 获取某职位下的员工列表 (暂未修改为 VO)
     * @param id 职位ID
     * @return Result<List<Employee>> 员工实体列表
     */
    @Override
    public Result<List<Employee>> getPositionEmployees(Integer id) {
        Position position = positionMapper.selectById(id);
        if (position == null || Boolean.TRUE.equals(position.getDelFlag())) {
            return Result.error("职位不存在");
        }
        List<Employee> employees = employeeMapper.selectByPositionId(id);
        return Result.success(employees);
    }

    /**
     * 将 Position 实体转换为 PositionVO
     * @param position Position 实体
     * @return PositionVO
     */
    private PositionVO convertToPositionVO(Position position) {
        if (position == null) {
            return null;
        }
        PositionVO positionVO = new PositionVO();
        BeanUtils.copyProperties(position, positionVO);
        return positionVO;
    }


    @Override
    public PageInfo<PositionVO> getPositionPage(SystemPositionPageQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<Position> positionList = positionMapper.selectListByCondition(queryDTO);
        PageInfo<Position> pageInfoPosition = new PageInfo<>(positionList);

        List<PositionVO> positionVOList = positionList.stream()
                .map(this::convertToPositionVO)
                .collect(Collectors.toList());

        PageInfo<PositionVO> pageInfoVO = new PageInfo<>();
        BeanUtils.copyProperties(pageInfoPosition, pageInfoVO);
        pageInfoVO.setList(positionVOList);

        return pageInfoVO;
    }
}
