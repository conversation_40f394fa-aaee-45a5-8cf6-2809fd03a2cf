package cn.edu.ncu.hrms.salary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 薪资账套视图对象
 */
@Data
@ApiModel("薪资账套视图对象")
public class AccountSetVO {

    @ApiModelProperty("账套ID")
    private Integer id;

    @ApiModelProperty("账套名称")
    private String name;

    @ApiModelProperty("基本工资")
    private BigDecimal baseSalary;

    @ApiModelProperty("绩效比例")
    private BigDecimal performanceRate;

    @ApiModelProperty("社保比例")
    private BigDecimal insuranceRate;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 